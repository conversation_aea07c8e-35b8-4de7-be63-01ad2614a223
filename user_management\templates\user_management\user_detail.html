{% extends "base.html" %}
{% load base_filters %}

{% block title %}User Details: {{ viewed_user.username }}{% endblock %}

{% block content %}
<div class="container my-5">
    <div class="row">
        <div class="col-md-12 mb-4">
            <a href="{% url 'user_list' %}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to User List
            </a>
        </div>
    </div>

    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="row">
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="mb-0">User Information</h3>
                </div>
                <div class="card-body">
                    <div class="text-center mb-4">
                        {% if viewed_user.profile.avatar %}
                        <img src="{{ viewed_user.profile.avatar.url }}" alt="{{ viewed_user.username }}'s avatar" class="img-fluid rounded-circle mb-3" style="max-width: 150px;">
                        {% else %}
                        <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 150px; height: 150px;">
                            <span class="display-4 text-secondary">{{ viewed_user.username.0|upper }}</span>
                        </div>
                        {% endif %}

                        <h4>{{ viewed_user.get_full_name|default:viewed_user.username }}</h4>
                        <p class="text-muted">@{{ viewed_user.username }}</p>

                        <span class="badge {% if viewed_user.is_active %}bg-success{% else %}bg-danger{% endif %} mb-2">
                            {% if viewed_user.is_active %}Active{% else %}Inactive{% endif %}
                        </span>

                        {% if viewed_user.is_staff %}
                        <span class="badge bg-info">Staff</span>
                        {% endif %}

                        {% if viewed_user.is_superuser %}
                        <span class="badge bg-warning">Admin</span>
                        {% endif %}
                    </div>

                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Email</span>
                            <span>{{ viewed_user.email }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Date Joined</span>
                            <span>{{ viewed_user.date_joined|date:"M d, Y" }}</span>
                        </li>
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Last Login</span>
                            <span>{{ viewed_user.last_login|date:"M d, Y H:i"|default:"Never" }}</span>
                        </li>
                    </ul>
                </div>
                {% if request.user.is_staff %}
                <div class="card-footer">
                    <div class="d-grid gap-2">
                        <button class="btn {% if viewed_user.is_active %}btn-warning{% else %}btn-success{% endif %} toggle-status"
                                data-user-id="{{ viewed_user.id }}"
                                data-current-status="{% if viewed_user.is_active %}active{% else %}inactive{% endif %}">
                            {% if viewed_user.is_active %}Deactivate User{% else %}Activate User{% endif %}
                        </button>
                    </div>
                </div>
                {% endif %}
            </div>

            {% if viewed_user.profile %}
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="mb-0">Profile Details</h3>
                </div>
                <div class="card-body">
                    <ul class="list-group">
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>First Name</span>
                            <span>{{ viewed_user.first_name }}</span>
                        </li>

                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Last Name</span>
                            <span>{{ viewed_user.last_name }}</span>
                        </li>

                        {% if viewed_user.profile.middle_name %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Middle Name</span>
                            <span>{{ viewed_user.profile.middle_name }}</span>
                        </li>
                        {% endif %}

                        {% if viewed_user.profile.birthday %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Birthday</span>
                            <span>{{ viewed_user.profile.birthday|date:"M d, Y" }}</span>
                        </li>
                        {% endif %}

                        {% if viewed_user.profile.sex %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Sex</span>
                            <span>{{ viewed_user.profile.get_sex_display }}</span>
                        </li>
                        {% endif %}

                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Phone Number</span>
                            <span>{{ viewed_user.profile.phone_number|default:"Not provided" }}</span>
                        </li>

                        {% if viewed_user.profile.address %}
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between align-items-center">
                                <span>Address</span>
                            </div>
                            <p class="mt-2 mb-0">{{ viewed_user.profile.address }}</p>
                        </li>
                        {% endif %}

                        {% if viewed_user.profile.website %}
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <span>Website</span>
                            <a href="{{ viewed_user.profile.website }}" target="_blank">{{ viewed_user.profile.website }}</a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>

            {% if has_pending_changes and request.user.is_staff %}
            <div id="pending-changes" class="card mb-4 border-warning">
                <div class="card-header bg-warning bg-opacity-10">
                    <h3 class="mb-0 text-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>Pending Profile Changes
                    </h3>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <p><strong>This user has requested the following profile changes that require your approval:</strong></p>
                    </div>

                    <ul class="list-group mb-4">
                        {% if pending_changes.email %}
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold">Email</span>
                            </div>
                            <div class="mt-2">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <span class="text-muted">Current:</span>
                                        <span class="ms-2">{{ pending_changes.email.current }}</span>
                                    </div>
                                    <div class="ms-3">
                                        <span class="text-warning">Requested:</span>
                                        <span class="ms-2 fw-bold">{{ pending_changes.email.pending }}</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        {% endif %}

                        {% if pending_changes.phone_number %}
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold">Phone Number</span>
                            </div>
                            <div class="mt-2">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <span class="text-muted">Current:</span>
                                        <span class="ms-2">{{ pending_changes.phone_number.current|default:"Not provided" }}</span>
                                    </div>
                                    <div class="ms-3">
                                        <span class="text-warning">Requested:</span>
                                        <span class="ms-2 fw-bold">{{ pending_changes.phone_number.pending }}</span>
                                    </div>
                                </div>
                            </div>
                        </li>
                        {% endif %}

                        {% if pending_changes.address %}
                        <li class="list-group-item">
                            <div class="d-flex justify-content-between">
                                <span class="fw-bold">Address</span>
                            </div>
                            <div class="mt-2">
                                <div class="row">
                                    <div class="col-md-6">
                                        <span class="text-muted">Current:</span>
                                        <p class="ms-2">{{ pending_changes.address.current|default:"Not provided" }}</p>
                                    </div>
                                    <div class="col-md-6">
                                        <span class="text-warning">Requested:</span>
                                        <p class="ms-2 fw-bold">{{ pending_changes.address.pending }}</p>
                                    </div>
                                </div>
                            </div>
                        </li>
                        {% endif %}
                    </ul>

                    <div class="d-flex justify-content-end">
                        <form method="post" action="{% url 'reject_profile_changes' user_id=viewed_user.id %}" class="me-2">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-outline-danger">
                                <i class="fas fa-times me-2"></i>Reject Changes
                            </button>
                        </form>
                        <form method="post" action="{% url 'approve_profile_changes' user_id=viewed_user.id %}">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>Approve Changes
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            {% endif %}
            {% endif %}
        </div>

        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="mb-0">Recent Activity</h3>
                </div>
                <div class="card-body">
                    {% if activities %}
                    <div class="list-group">
                        {% for activity in activities %}
                        <div class="list-group-item list-group-item-action">
                            <div class="d-flex w-100 justify-content-between">
                                <h5 class="mb-1">{{ activity.action }}</h5>
                                <small class="text-muted">{{ activity.timestamp|date:"M d, Y H:i" }}</small>
                            </div>
                            {% if activity.description %}
                            <p class="mb-1">{{ activity.description }}</p>
                            {% endif %}
                            <small class="text-muted">
                                IP: {{ activity.ip_address|default:"Not recorded" }}
                                {% if activity.user_agent %}
                                | Agent: {{ activity.user_agent }}
                                {% endif %}
                            </small>
                        </div>
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted">No activity records found.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const toggleButton = document.querySelector('.toggle-status');

        if (toggleButton) {
            toggleButton.addEventListener('click', function () {
                const userId = this.getAttribute('data-user-id');
                const currentStatus = this.getAttribute('data-current-status');
                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

                fetch(`/users/${userId}/toggle-status/`, {
                    method: 'POST',
                    headers: {
                        'X-CSRFToken': csrfToken,
                        'Content-Type': 'application/json'
                    },
                    credentials: 'same-origin'
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.status === 'success') {
                            // Update button text and class
                            if (data.is_active) {
                                this.textContent = 'Deactivate User';
                                this.classList.remove('btn-success');
                                this.classList.add('btn-warning');
                                this.setAttribute('data-current-status', 'active');
                                document.querySelector('.badge').textContent = 'Active';
                                document.querySelector('.badge').classList.remove('bg-danger');
                                document.querySelector('.badge').classList.add('bg-success');
                            } else {
                                this.textContent = 'Activate User';
                                this.classList.remove('btn-warning');
                                this.classList.add('btn-success');
                                this.setAttribute('data-current-status', 'inactive');
                                document.querySelector('.badge').textContent = 'Inactive';
                                document.querySelector('.badge').classList.remove('bg-success');
                                document.querySelector('.badge').classList.add('bg-danger');
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    });
            });
        }
    });
</script>
{% endblock %}

{% endblock %}