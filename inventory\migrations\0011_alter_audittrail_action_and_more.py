# Generated by Django 5.1.2 on 2025-02-14 16:56

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('inventory', '0010_alter_audittrail_object_id'),
    ]

    operations = [
        migrations.AlterField(
            model_name='audittrail',
            name='action',
            field=models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('delete', 'Delete'), ('add_quantity', 'Add Quantity')], max_length=20),
        ),
        migrations.AlterField(
            model_name='audittrail',
            name='content_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
    ]
