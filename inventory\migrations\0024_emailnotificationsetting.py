# Generated by Django 4.2.9 on 2025-04-04 01:38

from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('inventory', '0023_medicine_expiration_date'),
    ]

    operations = [
        migrations.CreateModel(
            name='EmailNotificationSetting',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('low_stock', 'Low Stock Alert'), ('expiring_soon', 'Expiring Soon Alert'), ('out_of_stock', 'Out of Stock Alert')], max_length=20)),
                ('is_enabled', models.BooleanField(default=True)),
                ('recipients', models.ManyToManyField(related_name='email_notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('notification_type',)},
            },
        ),
    ]
