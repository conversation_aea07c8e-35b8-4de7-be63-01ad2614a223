{% extends 'base.html' %}
{% load static %}
{% load base_filters %}
{% load custom_filters %}

{% block title %}Forecast Detail - {{ medicine.name }}{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <h1>{{ medicine.name }} Forecast Detail</h1>
            <p>This is a simplified template for debugging purposes.</p>
            
            <div class="card">
                <div class="card-header">
                    <h4>Basic Information</h4>
                </div>
                <div class="card-body">
                    <p><strong>Medicine:</strong> {{ medicine.name }}</p>
                    <p><strong>Current Stock:</strong> {{ medicine.quantity }}</p>
                    <p><strong>Reorder Level:</strong> {{ medicine.reorder_level }}</p>
                    <p><strong>Forecast Method:</strong> {{ forecast.forecast_method }}</p>
                    <p><strong>Predicted Quantity:</strong> {{ forecast.predicted_quantity }}</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    console.log("Simple template loaded successfully");
</script>
{% endblock %}
