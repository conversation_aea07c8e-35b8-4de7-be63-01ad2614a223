{% extends "admin/base_site.html" %}
{% load i18n admin_urls %}

{% block content %}
<p>Are you sure you want to reset 2FA for user "{{ user.username }}"?</p>
<p>This will remove their current 2FA settings and require them to set it up again.</p>

<form method="post">
    {% csrf_token %}
    <div>
        <input type="submit" value="Yes, reset 2FA" />
        <a href="{% url 'admin:auth_user_changelist' %}" class="button cancel-link">No, take me back</a>
    </div>
</form>
{% endblock %}