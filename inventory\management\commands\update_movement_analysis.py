from django.core.management.base import BaseCommand
from inventory.models import Medicine, MovementAnalysis, StockMovement
from django.db.models import Count
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Updates movement analysis for all medicines based on existing stock movements'

    def handle(self, *args, **options):
        self.stdout.write('Starting movement analysis update...')
        
        # Get all medicines
        medicines = Medicine.objects.all()
        total_medicines = medicines.count()
        
        self.stdout.write(f'Found {total_medicines} medicines to process')
        
        # Create stock movements from transactions if needed
        self.create_stock_movements_from_transactions()
        
        # Process each medicine
        updated_count = 0
        created_count = 0
        error_count = 0
        
        for i, medicine in enumerate(medicines, 1):
            try:
                # Get or create movement analysis
                movement_analysis, created = MovementAnalysis.objects.get_or_create(
                    medicine=medicine
                )
                
                if created:
                    created_count += 1
                    self.stdout.write(f'Created new movement analysis for {medicine.name}')
                
                # Update the analysis
                movement_analysis.update_analysis()
                updated_count += 1
                
                # Log progress
                if i % 10 == 0 or i == total_medicines:
                    self.stdout.write(f'Processed {i}/{total_medicines} medicines')
                
            except Exception as e:
                error_count += 1
                logger.error(f'Error updating movement analysis for {medicine.name}: {str(e)}')
                self.stdout.write(self.style.ERROR(f'Error processing {medicine.name}: {str(e)}'))
        
        # Update movement classes based on relative movement rates
        self.update_movement_classes()
        
        self.stdout.write(self.style.SUCCESS(
            f'Movement analysis update complete: {updated_count} updated, {created_count} created, {error_count} errors'
        ))
    
    def create_stock_movements_from_transactions(self):
        """Create stock movements for any transactions that don't have corresponding movements"""
        from inventory.models import Transaction
        from django.contrib.auth import get_user_model
        User = get_user_model()
        
        # Get transactions that might not have stock movements
        transactions = Transaction.objects.all()
        
        self.stdout.write(f'Checking {transactions.count()} transactions for missing stock movements')
        
        # Get admin user for performed_by field
        admin_user = User.objects.filter(is_staff=True).first()
        
        created_count = 0
        
        for transaction in transactions:
            # Check if a stock movement already exists for this transaction
            reference = f"Transaction #{transaction.id}"
            existing_movement = StockMovement.objects.filter(reference_number=reference).exists()
            
            if not existing_movement:
                try:
                    # Map transaction type to movement type
                    movement_type = 'purchase' if transaction.transaction_type == 'purchase' else 'sale'
                    
                    # For sales, quantity should be negative in stock movements
                    movement_quantity = transaction.quantity if transaction.transaction_type == 'purchase' else -transaction.quantity
                    
                    # Create the stock movement
                    StockMovement.objects.create(
                        medicine=transaction.medicine,
                        movement_date=transaction.transaction_date,
                        quantity=movement_quantity,
                        movement_type=movement_type,
                        reference_number=reference,
                        notes=f"Automatically created from {transaction.transaction_type} transaction",
                        brand=transaction.medicine.brand,
                        supplier=transaction.medicine.supplier,
                        performed_by=admin_user,
                        unit_price=transaction.medicine.price,
                        total_value=abs(movement_quantity) * transaction.medicine.price
                    )
                    created_count += 1
                    
                except Exception as e:
                    logger.error(f"Failed to create stock movement for transaction {transaction.id}: {str(e)}")
        
        self.stdout.write(f'Created {created_count} new stock movements from transactions')
    
    def update_movement_classes(self):
        """Update movement classes (A, B, C) based on relative movement rates"""
        # Get all movement analyses with movement
        analyses = MovementAnalysis.objects.exclude(monthly_movement_rate=0).order_by('-monthly_movement_rate')
        total = analyses.count()
        
        if total == 0:
            self.stdout.write('No medicines with movement found, skipping class assignment')
            return
        
        self.stdout.write(f'Updating movement classes for {total} medicines with activity')
        
        # Calculate thresholds for A, B, C classes
        a_threshold = int(total * 0.2)  # Top 20%
        b_threshold = int(total * 0.5)  # Next 30% (up to 50%)
        
        # Update classes
        for i, analysis in enumerate(analyses):
            if i < a_threshold:
                analysis.movement_class = 'A'
            elif i < b_threshold:
                analysis.movement_class = 'B'
            else:
                analysis.movement_class = 'C'
            analysis.save(update_fields=['movement_class'])
        
        self.stdout.write(f'Updated movement classes: {a_threshold} Class A, {b_threshold - a_threshold} Class B, {total - b_threshold} Class C')
