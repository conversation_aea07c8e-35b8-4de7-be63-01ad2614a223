{% extends 'base.html' %}
{% load static %}
{% load base_filters %}

{% block title %}Forecast Detail - {{ medicine.name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.css">
<link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="{% static 'css/forecast_detail.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    .card-header {
        padding: 1rem 1.5rem;
    }

    .card-body .table-responsive {
        margin-bottom: 1rem;
    }

    .card-body .chart-container {
        margin-top: 1rem;
    }

    .d-flex.align-items-center {
        gap: 0.5rem;
    }

    .badge {
        margin-right: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-11">
            <div class="card border-0 bg-primary bg-gradient text-white shadow-lg rounded-3">
                <div class="card-body py-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="display-6 mb-0">{{ medicine.name }}</h1>
                            <p class="mb-0 opacity-75">Forecast Analysis: {{ start_date|date:"Y-m-d" }} - {{ end_date|date:"Y-m-d" }}</p>
                        </div>
                        <div class="text-end">
                            {% if forecast.recommended_order_quantity > 0 %}
                            <div class="badge bg-warning text-dark fs-5 p-2 rounded-pill">
                                <i class="fas fa-shopping-cart me-1"></i> Order: {{ forecast.recommended_order_quantity|floatformat:0 }} pcs
                            </div>
                            {% else %}
                            <div class="badge bg-success fs-5 p-2 rounded-pill">
                                <i class="fas fa-check-circle me-1"></i> Stock Sufficient
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Section -->
    <div class="row g-4 mb-4">
        <div class="col-md-4">
            <div class="card border-0 shadow-sm rounded-3 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle p-3 bg-info bg-opacity-25 me-3">
                            <i class="fas fa-chart-bar text-info fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted mb-1">Actual Demand</h6>
                            <h3 class="mb-0">{{ actual_demand }} pcs</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm rounded-3 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle p-3 bg-success bg-opacity-25 me-3">
                            <i class="fas fa-cubes text-success fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted mb-1">Current Stock</h6>
                            <h3 class="mb-0">{{ current_quantity }} pcs</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card border-0 shadow-sm rounded-3 h-100">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="rounded-circle p-3 bg-warning bg-opacity-25 me-3">
                            <i class="fas fa-exclamation-triangle text-warning fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted mb-1">Reorder Level</h6>
                            <h3 class="mb-0">{{ reorder_level }} pcs</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Medicine Information Card -->
        <div class="col-xl-6">
            <div class="card border-0 shadow-sm rounded-3 h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary rounded-circle p-2 me-2">
                            <i class="fas fa-info-circle"></i>
                        </span>
                        <h4 class="mb-0">Medicine Information</h4>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-borderless">
                            <tbody>
                                <tr>
                                    <th class="ps-0"><i class="fas fa-prescription-bottle-alt text-primary me-2"></i>Medicine Name:</th>
                                    <td class="fw-bold">{{ medicine.name }}</td>
                                </tr>
                                <tr>
                                    <th class="ps-0"><i class="fas fa-tag text-primary me-2"></i>Brand:</th>
                                    <td>{{ medicine.brand|default:"Generic" }}</td>
                                </tr>
                                <tr>
                                    <th class="ps-0"><i class="fas fa-building text-primary me-2"></i>Supplier:</th>
                                    <td>{{ medicine.supplier|default:"Unknown" }}</td>
                                </tr>
                                {% if medicine.generic_medicine %}
                                <tr>
                                    <th class="ps-0"><i class="fas fa-pills text-primary me-2"></i>Generic Medicine:</th>
                                    <td>{{ medicine.generic_medicine.name }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th class="ps-0"><i class="fas fa-shopping-cart text-primary me-2"></i>Recommended Order:</th>
                                    <td>
                                        {% if forecast.recommended_order_quantity > 0 %}
                                        <span class="badge bg-warning text-dark px-3 py-2 rounded-pill">{{ forecast.recommended_order_quantity|floatformat:0 }} pcs</span>
                                        {% else %}
                                        <span class="badge bg-success px-3 py-2 rounded-pill">Sufficient</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th class="ps-0"><i class="fas fa-calendar-alt text-primary me-2"></i>Forecast Date:</th>
                                    <td>{{ forecast.forecast_date|date:"M d, Y" }}</td>
                                </tr>
                                <tr>
                                    <th class="ps-0"><i class="fas fa-box text-primary me-2"></i>Current Stock:</th>
                                    <td>{{ current_quantity }} pcs</td>
                                </tr>
                                <tr>
                                    <th class="ps-0"><i class="fas fa-exchange-alt text-primary me-2"></i>Transactions:</th>
                                    <td>{{ transaction_count }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quarterly Analysis -->
        <div class="col-xl-6">
            <div class="card border-0 shadow-sm rounded-3 h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-success rounded-circle p-2 me-2">
                            <i class="fas fa-chart-pie"></i>
                        </span>
                        <h4 class="mb-0">Quarterly Analysis</h4>
                    </div>
                </div>
                <div class="card-body">
                    {% if quarterly_analysis %}
                    <div class="table-responsive mb-4">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Quarter</th>
                                    <th>Average</th>
                                    <th>Std Dev</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for quarter, data in quarterly_analysis.seasonal_patterns.items %}
                                <tr>
                                    <td class="fw-bold">Q{{ quarter }}</td>
                                    <td>{{ data.mean|floatformat:0 }} pcs</td>
                                    <td>{{ data.std|floatformat:0 }}</td>
                                    <td>
                                        {% if quarter == quarterly_analysis.peak_quarter %}
                                        <span class="badge bg-success rounded-pill px-3 py-2"><i class="fas fa-arrow-up me-1"></i> Peak</span>
                                        {% elif quarter == quarterly_analysis.low_quarter %}
                                        <span class="badge bg-danger rounded-pill px-3 py-2"><i class="fas fa-arrow-down me-1"></i> Low</span>
                                        {% else %}
                                        <span class="badge bg-secondary rounded-pill px-3 py-2"><i class="fas fa-minus me-1"></i> Normal</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <div class="chart-container" style="height: 220px;">
                        <canvas id="quarterlyChart"></canvas>
                    </div>
                    {% else %}
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center text-muted">
                            <i class="fas fa-chart-area fs-1 mb-3"></i>
                            <p class="mb-0">No quarterly analysis data available</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Monthly Demand -->
        <div class="col-xl-6">
            <div class="card border-0 shadow-sm rounded-3 h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-info rounded-circle p-2 me-2">
                            <i class="fas fa-calendar-alt"></i>
                        </span>
                        <h4 class="mb-0">Monthly Demand</h4>
                    </div>
                </div>
                <div class="card-body">
                    {% if monthly_demand %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Month</th>
                                    <th>Quantity</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for month, quantity in monthly_demand.items %}
                                <tr>
                                    <td class="fw-bold">{{ month }}</td>
                                    <td>{{ quantity }} pcs</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center text-muted">
                            <i class="fas fa-calendar-times fs-1 mb-3"></i>
                            <p class="mb-0">No monthly demand data available</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="col-xl-6">
            <div class="card border-0 shadow-sm rounded-3 h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-warning rounded-circle p-2 me-2">
                            <i class="fas fa-exchange-alt"></i>
                        </span>
                        <h4 class="mb-0">Recent Transactions</h4>
                    </div>
                </div>
                <div class="card-body">
                    {% if transactions %}
                    <div class="table-responsive">
                        <table id="transactions-table" class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>Quantity</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in transactions %}
                                <tr>
                                    <td>{{ transaction.transaction_date|date:"M d, Y" }}</td>
                                    <td>{{ transaction.quantity }} pcs</td>
                                    <td>
                                        <span class="badge {% if transaction.transaction_type == 'IN' %}bg-success{% else %}bg-danger{% endif %} rounded-pill px-3 py-2">
                                            {% if transaction.transaction_type == 'IN' %}
                                            <i class="fas fa-arrow-down me-1"></i>
                                            {% else %}
                                            <i class="fas fa-arrow-up me-1"></i>
                                            {% endif %}
                                            {{ transaction.transaction_type }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center text-muted">
                            <i class="fas fa-clipboard fs-1 mb-3"></i>
                            <p class="mb-0">No transaction data available</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden element for chart data -->
{% if quarterly_analysis %}
<script id="quarterly-data" type="application/json">{{ quarterly_analysis.data|safe }}</script>
{% endif %}
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
<script src="{% static 'js/forecast_detail.js' %}"></script>
<script>
    $(document).ready(function () {
        $('#transactions-table').DataTable({
            pageLength: 5,
            lengthMenu: [5, 10, 25],
            order: [[0, 'desc']],
            dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
            language: {
                search: "_INPUT_",
                searchPlaceholder: "Search transactions..."
            },
        });

        // Initialize quarterly analysis chart if data is available
        {% if quarterly_analysis %}
        var ctx = document.getElementById('quarterlyChart').getContext('2d');
        var quarterlyData = JSON.parse(document.getElementById('quarterly-data').textContent);

        var labels = quarterlyData.map(function (item) { return item.quarter; });
        var data = quarterlyData.map(function (item) { return item.quantity; });

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Demand Quantity',
                    data: data,
                    backgroundColor: [
                        'rgba(75, 192, 192, 0.2)',
                        'rgba(153, 102, 255, 0.2)',
                        'rgba(255, 159, 64, 0.2)',
                        'rgba(255, 99, 132, 0.2)'
                    ],
                    borderColor: [
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                scales: {
                    y: {
                        beginAtZero: true
                    }
                },
                plugins: {
                    tooltip: {
                        callbacks: {
                            label: function (context) {
                                var label = context.dataset.label || '';
                                if (label) {
                                    label += ': ';
                                }
                                label += context.raw + ' pcs';
                                return label;
                            }
                        }
                    }
                }
            }
        });
        {% endif %}
    });






</script>
{% endblock %}
