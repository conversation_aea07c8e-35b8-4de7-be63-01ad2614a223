from datetime import datetime, timedelta
from decimal import Decimal
from django.utils import timezone

# Generate realistic medicine data
MEDICINE_DATA = [
    {
        'name': 'Amoxicillin',
        'description': 'Amoxicillin tablet - Antibiotics medication for bacterial infections',
        'reorder_level': 30,
        'reorder_quantity': 100,
        'category': 'Antibiotics',
        'medicine_type': 'tablet',
        'price': Decimal('15.50'),
        'expiration_date': (timezone.now().date() + timedelta(days=730))
    },
    {
        'name': 'Paracetamol',
        'description': 'Paracetamol tablet - Analgesics medication for pain and fever',
        'reorder_level': 50,
        'reorder_quantity': 200,
        'category': 'Analgesics',
        'medicine_type': 'tablet',
        'price': Decimal('5.75'),
        'expiration_date': (timezone.now().date() + timedelta(days=900))
    },
    {
        'name': 'Metformin',
        'description': 'Metformin tablet - Antidiabetic medication for type 2 diabetes',
        'reorder_level': 40,
        'reorder_quantity': 120,
        'category': 'Antidiabetic',
        'medicine_type': 'tablet',
        'price': Decimal('12.25'),
        'expiration_date': (timezone.now().date() + timedelta(days=800))
    },
    {
        'name': 'Atorvastatin',
        'description': 'Atorvastatin tablet - Cardiovascular medication for cholesterol management',
        'reorder_level': 35,
        'reorder_quantity': 90,
        'category': 'Cardiovascular',
        'medicine_type': 'tablet',
        'price': Decimal('25.00'),
        'expiration_date': (timezone.now().date() + timedelta(days=850))
    },
    {
        'name': 'Losartan',
        'description': 'Losartan tablet - Antihypertensive medication for blood pressure control',
        'reorder_level': 30,
        'reorder_quantity': 80,
        'category': 'Cardiovascular',
        'medicine_type': 'tablet',
        'price': Decimal('18.75'),
        'expiration_date': (timezone.now().date() + timedelta(days=780))
    },
    {
        'name': 'Omeprazole',
        'description': 'Omeprazole capsule - Gastrointestinal medication for acid reflux',
        'reorder_level': 25,
        'reorder_quantity': 70,
        'category': 'Gastrointestinal',
        'medicine_type': 'capsule',
        'price': Decimal('14.50'),
        'expiration_date': (timezone.now().date() + timedelta(days=820))
    },
    {
        'name': 'Cetirizine',
        'description': 'Cetirizine tablet - Antihistamines medication for allergies',
        'reorder_level': 45,
        'reorder_quantity': 150,
        'category': 'Antihistamines',
        'medicine_type': 'tablet',
        'price': Decimal('8.25'),
        'expiration_date': (timezone.now().date() + timedelta(days=750))
    },
    {
        'name': 'Salbutamol',
        'description': 'Salbutamol inhaler - Respiratory medication for asthma',
        'reorder_level': 20,
        'reorder_quantity': 50,
        'category': 'Respiratory',
        'medicine_type': 'inhaler',
        'price': Decimal('120.00'),
        'expiration_date': (timezone.now().date() + timedelta(days=700))
    },
    {
        'name': 'Metoprolol',
        'description': 'Metoprolol tablet - Cardiovascular medication for heart rhythm',
        'reorder_level': 30,
        'reorder_quantity': 80,
        'category': 'Cardiovascular',
        'medicine_type': 'tablet',
        'price': Decimal('16.50'),
        'expiration_date': (timezone.now().date() + timedelta(days=880))
    },
    {
        'name': 'Amlodipine',
        'description': 'Amlodipine tablet - Antihypertensive medication for blood pressure',
        'reorder_level': 35,
        'reorder_quantity': 90,
        'category': 'Cardiovascular',
        'medicine_type': 'tablet',
        'price': Decimal('15.75'),
        'expiration_date': (timezone.now().date() + timedelta(days=830))
    },
    {
        'name': 'Simvastatin',
        'description': 'Simvastatin tablet - Cardiovascular medication for cholesterol',
        'reorder_level': 30,
        'reorder_quantity': 80,
        'category': 'Cardiovascular',
        'medicine_type': 'tablet',
        'price': Decimal('22.50'),
        'expiration_date': (timezone.now().date() + timedelta(days=840))
    },
    {
        'name': 'Lisinopril',
        'description': 'Lisinopril tablet - Antihypertensive medication for blood pressure',
        'reorder_level': 35,
        'reorder_quantity': 90,
        'category': 'Cardiovascular',
        'medicine_type': 'tablet',
        'price': Decimal('17.25'),
        'expiration_date': (timezone.now().date() + timedelta(days=810))
    },
    {
        'name': 'Albuterol',
        'description': 'Albuterol inhaler - Respiratory medication for asthma',
        'reorder_level': 20,
        'reorder_quantity': 50,
        'category': 'Respiratory',
        'medicine_type': 'inhaler',
        'price': Decimal('110.00'),
        'expiration_date': (timezone.now().date() + timedelta(days=720))
    },
    {
        'name': 'Fluoxetine',
        'description': 'Fluoxetine capsule - Antidepressants medication for depression',
        'reorder_level': 25,
        'reorder_quantity': 60,
        'category': 'Antidepressants',
        'medicine_type': 'capsule',
        'price': Decimal('19.75'),
        'expiration_date': (timezone.now().date() + timedelta(days=790))
    },
    {
        'name': 'Sertraline',
        'description': 'Sertraline tablet - Antidepressants medication for anxiety',
        'reorder_level': 25,
        'reorder_quantity': 60,
        'category': 'Antidepressants',
        'medicine_type': 'tablet',
        'price': Decimal('21.50'),
        'expiration_date': (timezone.now().date() + timedelta(days=800))
    },
    {
        'name': 'Gabapentin',
        'description': 'Gabapentin capsule - Analgesics medication for nerve pain',
        'reorder_level': 30,
        'reorder_quantity': 80,
        'category': 'Analgesics',
        'medicine_type': 'capsule',
        'price': Decimal('18.25'),
        'expiration_date': (timezone.now().date() + timedelta(days=770))
    },
    {
        'name': 'Hydrochlorothiazide',
        'description': 'Hydrochlorothiazide tablet - Antihypertensive medication for blood pressure',
        'reorder_level': 35,
        'reorder_quantity': 90,
        'category': 'Cardiovascular',
        'medicine_type': 'tablet',
        'price': Decimal('14.75'),
        'expiration_date': (timezone.now().date() + timedelta(days=860))
    },
    {
        'name': 'Levothyroxine',
        'description': 'Levothyroxine tablet - Hormones medication for thyroid',
        'reorder_level': 40,
        'reorder_quantity': 100,
        'category': 'Hormones',
        'medicine_type': 'tablet',
        'price': Decimal('16.25'),
        'expiration_date': (timezone.now().date() + timedelta(days=870))
    },
    {
        'name': 'Prednisone',
        'description': 'Prednisone tablet - Hormones medication for inflammation',
        'reorder_level': 30,
        'reorder_quantity': 80,
        'category': 'Hormones',
        'medicine_type': 'tablet',
        'price': Decimal('12.75'),
        'expiration_date': (timezone.now().date() + timedelta(days=760))
    },
    {
        'name': 'Ibuprofen',
        'description': 'Ibuprofen tablet - Analgesics medication for pain and inflammation',
        'reorder_level': 50,
        'reorder_quantity': 150,
        'category': 'Analgesics',
        'medicine_type': 'tablet',
        'price': Decimal('7.50'),
        'expiration_date': (timezone.now().date() + timedelta(days=910))
    }
]
