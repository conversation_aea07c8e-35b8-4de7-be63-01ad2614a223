﻿{% extends 'base.html' %}
{% load static %}
{% load base_filters %}

{% block extra_css %}
<link href="{% static 'css/inventory.css' %}" rel="stylesheet">
<style>
    .table .table-header-gradient {
        background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%) !important;
    }

        .table .table-header-gradient th {
            color: white !important;
            background: transparent !important;
            padding: 1rem !important;
        }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-gray-800">Inventory List</h1>
                <div class="d-flex gap-2">
                    <a href="{% url 'medicine_list' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-pills me-2"></i>Medicines
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-header-gradient">
                        <tr>
                            <th>Name</th>
                            <th>Description</th>
                            <th>Quantity</th>
                            <th>Reorder Level</th>
                            <th>Reorder Quantity</th>
                            <th>Category</th>
                            <th>Price</th>
                            <th>Expiration Date</th>
                            <th>Stock Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for medicine in page_obj %}
                        <tr>
                            <td class="align-middle">
                                <span class="fw-medium text-primary">{{ medicine.name }}</span>
                            </td>
                            <td class="align-middle">{{ medicine.description|truncatechars:50 }}</td>
                            <td class="align-middle">
                                <span class="{% if medicine.quantity <= medicine.reorder_level %}text-danger fw-bold{% endif %}">
                                    {{ medicine.quantity }}
                                </span>
                            </td>
                            <td class="align-middle">{{ medicine.reorder_level }}</td>
                            <td class="align-middle">{{ medicine.reorder_quantity }}</td>
                            <td class="align-middle">
                                <span class="badge bg-primary bg-opacity-10 text-primary px-2 py-1 rounded-pill">
                                    {{ medicine.category }}
                                </span>
                            </td>
                            <td class="align-middle">₱{{ medicine.price }}</td>
                            <td class="align-middle">
                                {% if medicine.expiration_date %}
                                <span class="badge {% if medicine.is_expired %}bg-danger{% elif medicine.days_until_expiration <= 30 %}bg-warning text-dark{% else %}bg-success{% endif %} px-2 py-1">
                                    {{ medicine.expiration_date|date:"F d, Y" }}
                                    {% if medicine.is_expired %}
                                    (Expired)
                                    {% elif medicine.days_until_expiration <= 30 %}
                                    ({{ medicine.days_until_expiration }} days left)
                                    {% endif %}
                                </span>
                                {% else %}
                                <span class="badge bg-secondary px-2 py-1">Not Set</span>
                                {% endif %}
                            </td>
                            <td class="align-middle">
                                {% if medicine.quantity <= medicine.reorder_level|divide:2 %}
                                <a href="#" class="badge bg-danger text-white text-decoration-none" data-bs-toggle="modal" data-bs-target="#alertModal{{ medicine.pk }}-critical">
                                    <i class="fas fa-exclamation-triangle me-1"></i> Critical Low
                                </a>
                                {% elif medicine.quantity <= medicine.reorder_level %}
                                <a href="#" class="badge bg-warning text-dark text-decoration-none" data-bs-toggle="modal" data-bs-target="#alertModal{{ medicine.pk }}-reorder">
                                    <i class="fas fa-bell me-1"></i> Reorder
                                </a>
                                {% elif medicine.quantity <= medicine.reorder_level|multiply:2 %}
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i> Good Stock
                                </span>
                                {% else %}
                                <a href="#" class="badge bg-primary text-decoration-none" data-bs-toggle="modal" data-bs-target="#alertModal{{ medicine.pk }}-overstock">
                                    <i class="fas fa-info-circle me-1"></i> Overstock
                                </a>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="card-footer bg-light py-3">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link text-primary" href="?page=1">&laquo; First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link text-primary" href="?page={{ page_obj.previous_page_number }}">Previous</a>
                    </li>
                    {% endif %}

                    <li class="page-item active">
                        <span class="page-link border-primary">
                            Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}
                        </span>
                    </li>

                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link text-primary" href="?page={{ page_obj.next_page_number }}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link text-primary" href="?page={{ page_obj.paginator.num_pages }}">Last &raquo;</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>
</div>



{% for medicine in page_obj %}
    {% for alert in alerts|get_item:medicine.pk %}
<div class="modal fade" id="alertModal{{ medicine.pk }}-{{ alert.type }}" tabindex="-1" aria-labelledby="alertModalLabel{{ medicine.pk }}-{{ alert.type }}" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header {% if alert.type == 'critical' %}bg-danger{% elif alert.type == 'reorder' %}bg-warning{% elif alert.type == 'overstock' %}bg-primary{% endif %} text-white">
                <h5 class="modal-title" id="alertModalLabel{{ medicine.pk }}-{{ alert.type }}">
                    <i class="fas {% if alert.type == 'critical' %}fa-exclamation-triangle{% elif alert.type == 'reorder' %}fa-bell{% elif alert.type == 'overstock' %}fa-info-circle{% endif %} me-2"></i>
                    {% if alert.type == 'critical' %}Critical Low Alert{% elif alert.type == 'reorder' %}Reorder Alert{% elif alert.type == 'overstock' %}Overstock Alert{% endif %} for {{ medicine.name }}
                </h5>
                <button type="button" class="btn-close text-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert {% if alert.type == 'critical' %}alert-danger{% elif alert.type == 'reorder' %}alert-warning{% elif alert.type == 'overstock' %}alert-primary{% endif %} mb-4">
                    <h4 class="alert-heading fw-bold mb-3">
                        {{ alert.type|title }} Alert
                    </h4>
                    <p class="mb-3">{{ alert.message }}</p>
                    <hr>
                    <div class="mt-3">
                        <div class="row">
                            <div class="col-md-4">
                                <p class="mb-2"><strong>Current Quantity:</strong></p>
                                <h5 class="{% if alert.type == 'critical' %}text-danger{% elif alert.type == 'reorder' %}text-warning{% elif alert.type == 'overstock' %}text-primary{% endif %}">
                                    {{ medicine.quantity }}
                                </h5>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-2"><strong>Reorder Level:</strong></p>
                                <h5>{{ medicine.reorder_level }}</h5>
                            </div>
                            <div class="col-md-4">
                                <p class="mb-2"><strong>Reorder Quantity:</strong></p>
                                <h5>{{ medicine.reorder_quantity }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn {% if alert.type == 'critical' %}btn-danger{% elif alert.type == 'reorder' %}btn-warning{% elif alert.type == 'overstock' %}btn-primary{% endif %}">
                    Take Action
                </button>
            </div>
        </div>
    </div>
</div>
    {% endfor %}
{% endfor %}
{% endblock %}

{% block extra_js %}
<script src="{% static 'js/inventory.js' %}"></script>
{% endblock %}