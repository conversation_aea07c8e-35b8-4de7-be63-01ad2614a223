from django.shortcuts import render
from django.http import HttpResponse, JsonResponse
from django.contrib.auth.decorators import login_required
from django.utils import timezone
from .models import Medicine, Forecast
from .forecasting import get_forecasting_results
import json
import pandas as pd
import xlsxwriter
import io
import numpy as np

@login_required
def forecast_simulation(request, medicine_id=None):
    """
    View to demonstrate the forecasting simulation for a specific medicine
    """
    # Get the medicine if ID is provided, otherwise use the first medicine
    if medicine_id:
        medicine = Medicine.objects.get(id=medicine_id)
    else:
        medicine = Medicine.objects.first()
    
    # Get forecasting results for this medicine
    results = get_forecasting_results(medicine)
    
    # Return the results as JSON
    return JsonResponse(results)

@login_required
def download_forecast_simulation(request, medicine_id=None):
    """
    Generate an Excel file with the forecast simulation data
    """
    # Get the medicine if ID is provided, otherwise use the first medicine
    if medicine_id:
        medicine = Medicine.objects.get(id=medicine_id)
    else:
        medicine = Medicine.objects.first()
    
    # Get forecasting results for this medicine
    results = get_forecasting_results(medicine)
    
    # Create an in-memory output file
    output = io.BytesIO()
    
    # Create Excel workbook and worksheet
    workbook = xlsxwriter.Workbook(output)
    worksheet = workbook.add_worksheet('Forecast Simulation')
    
    # Add headers
    headers = ['Method', 'Accuracy', 'MAPE', 'MAE', 'RMSE']
    for col, header in enumerate(headers):
        worksheet.write(0, col, header)
    
    # Add data
    row = 1
    for method, metrics in results['methods'].items():
        worksheet.write(row, 0, method)
        worksheet.write(row, 1, metrics['accuracy'])
        worksheet.write(row, 2, metrics['mape'])
        worksheet.write(row, 3, metrics['mae'])
        worksheet.write(row, 4, metrics['rmse'])
        row += 1
    
    # Close the workbook
    workbook.close()
    
    # Set up the response
    output.seek(0)
    response = HttpResponse(
        output.read(),
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename=forecast_simulation_{medicine.name}.xlsx'
    
    return response
