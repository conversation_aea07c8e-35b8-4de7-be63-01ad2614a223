<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Verification - BMC MedForecast</title>
    
    <!-- Stylesheets -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            background-color: #f8f9fa;
            min-height: 100vh;
            display: flex;
            align-items: center;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        .card-title {
            color: #2c3e50;
            font-weight: 600;
        }
        .btn-primary {
            background-color: #2c3e50;
            border-color: #2c3e50;
        }
        .btn-primary:hover {
            background-color: #34495e;
            border-color: #34495e;
        }
        .btn-outline-secondary:hover {
            background-color: #95a5a6;
            border-color: #95a5a6;
        }
        .input-group-text {
            background-color: #f8f9fa;
            border-right: none;
        }
        .form-control {
            border-left: none;
        }
        .form-control:focus {
            border-color: #ced4da;
            box-shadow: none;
        }
        .logo {
            max-width: 150px;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5">
                <div class="text-center mb-4">
                    <h2 class="text-dark mb-4">BMC MedForecast</h2>
                </div>
                
                <div class="card">
                    <div class="card-body p-4">
                        <h3 class="card-title text-center mb-4">Email Verification</h3>
                        
                        {% if messages %}
                        <div class="messages mb-4">
                            {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                        
                        <div class="text-center mb-4">
                            <p class="text-muted">Please enter the 6-digit verification code sent to your email.</p>
                        </div>
                        
                        <form method="post" action="{% url 'verify_email' %}" class="needs-validation" novalidate>
                            {% csrf_token %}
                            <div class="form-group mb-4">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-key"></i>
                                    </span>
                                    <input type="text" 
                                           class="form-control" 
                                           id="verification_code" 
                                           name="verification_code" 
                                           placeholder="Enter verification code" 
                                           required 
                                           pattern="\d{6}"
                                           maxlength="6"
                                           inputmode="numeric"
                                           autocomplete="off">
                                    <div class="invalid-feedback">
                                        Please enter the 6-digit verification code.
                                    </div>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-check-circle me-2"></i>Verify Email
                                </button>
                                <a href="{% url 'resend_verification' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-paper-plane me-2"></i>Resend Code
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="text-center mt-4">
                    <a href="{% url 'login' %}" class="text-muted text-decoration-none">
                        <i class="fas fa-arrow-left me-2"></i>Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Form validation
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms).forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    form.classList.add('was-validated')
                }, false)
                
                // Add real-time validation for the code input
                const codeInput = form.querySelector('#verification_code')
                codeInput.addEventListener('input', function() {
                    this.value = this.value.replace(/[^\d]/g, '').substring(0, 6)
                })
            })
            
            // Auto-dismiss alerts after 5 seconds
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert')
                alerts.forEach(alert => {
                    const bsAlert = new bootstrap.Alert(alert)
                    bsAlert.close()
                })
            }, 5000)
        })()
    </script>
</body>
</html>