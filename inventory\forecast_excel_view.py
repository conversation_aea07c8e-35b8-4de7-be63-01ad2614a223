from django.http import HttpResponse
from django.contrib.auth.decorators import login_required
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils import get_column_letter
from django.utils import timezone
from datetime import timedelta
from .models import Medicine
from .forecasting import get_forecasting_results

@login_required
def forecast_excel_view(request):
    """Generate forecast Excel report"""
    # Get date range for forecast
    date_range = int(request.GET.get('date_range', 30))
    forecast_end_date = timezone.now() + timedelta(days=date_range)
    
    # Get all medicines
    medicines = Medicine.objects.all().order_by('name')
    
    # Create Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Forecast Report"
    
    # Define headers
    headers = ['Medicine Name', 'Category', 'Current Stock', 'Reorder Level', 'Predicted Demand', 'Recommended Order', 'Forecast Method']
    
    # Add title
    title = f"Forecast Report (Next {date_range} days)"
    ws['A1'] = title
    ws.merge_cells(f'A1:{get_column_letter(len(headers))}1')
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = Alignment(horizontal="center")
    
    # Add metadata
    ws['A2'] = f"Report Period: Next {date_range} days"
    ws['A3'] = f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}"
    ws['A4'] = f"Generated by: {request.user.get_full_name() or request.user.username}"
    
    # Add header row
    row_num = 6
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=row_num, column=col_num, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="2C3E50", end_color="2C3E50", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")
        cell.font = Font(bold=True, color="FFFFFF")
    
    # Add data rows
    row_num += 1
    total_predicted = 0
    total_recommended = 0
    
    for i, medicine in enumerate(medicines):
        # Get forecast data
        try:
            forecast_data = get_forecasting_results(medicine)
            predicted_demand = forecast_data.get('predicted_demand', 0)
            recommended_order = forecast_data.get('recommended_order_quantity', 0)
            forecast_method = forecast_data.get('model_used', 'N/A')
            
            total_predicted += predicted_demand
            total_recommended += recommended_order
            
            # Add row data
            ws.cell(row=row_num, column=1, value=medicine.name)
            ws.cell(row=row_num, column=2, value=medicine.category)
            ws.cell(row=row_num, column=3, value=medicine.quantity)
            ws.cell(row=row_num, column=4, value=medicine.reorder_level)
            ws.cell(row=row_num, column=5, value=round(predicted_demand, 1))
            ws.cell(row=row_num, column=6, value=round(recommended_order, 1))
            ws.cell(row=row_num, column=7, value=forecast_method)
            
            # Apply alternating row styling
            if i % 2 == 1:
                for col in range(1, len(headers) + 1):
                    ws.cell(row=row_num, column=col).fill = PatternFill(start_color="ECF0F1", end_color="ECF0F1", fill_type="solid")
            
            row_num += 1
        except Exception as e:
            print(f"Error generating forecast for {medicine.name}: {str(e)}")
            continue
    
    # Add total row
    row_num += 1
    ws.cell(row=row_num, column=1, value="Total")
    ws.cell(row=row_num, column=5, value=round(total_predicted, 1))
    ws.cell(row=row_num, column=6, value=round(total_recommended, 1))
    
    # Style total row
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=row_num, column=col)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="ECF0F1", end_color="ECF0F1", fill_type="solid")
    
    # Auto-adjust column widths
    for i, col in enumerate(ws.columns, 1):
        max_length = 0
        column = get_column_letter(i)
        for cell in col:
            if cell.value and not isinstance(cell, type(ws.merged_cells)):
                max_length = max(max_length, len(str(cell.value)))
        adjusted_width = max(max_length + 2, 10)
        ws.column_dimensions[column].width = adjusted_width
    
    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    response['Content-Disposition'] = f'attachment; filename="forecast_report_{timestamp}.xlsx"'
    wb.save(response)
    return response
