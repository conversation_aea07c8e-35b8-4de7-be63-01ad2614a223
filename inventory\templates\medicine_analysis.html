﻿{% extends 'base.html' %}
{% load static %}

{% block extra_css %}
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.css">
<style>
    /* Overall Layout Improvements */
    body {
        background-color: #f5f7fa;
        color: #2c3e50;
    }

    .dashboard-container {
        padding: 1.5rem;
        max-width: 1400px;
        margin: 0 auto;
    }

    .page-title {
        color: #2c3e50;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .period-indicator {
        color: #7f8c8d;
        font-size: 0.95rem;
        margin-bottom: 0.5rem;
    }

    /* Filters Section */
    .filters-container {
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        padding: 1.25rem;
        margin-bottom: 1.5rem;
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        align-items: flex-end;
    }

    .filter-group {
        flex: 1;
        min-width: 200px;
    }

    .filter-label {
        display: block;
        font-size: 0.85rem;
        font-weight: 600;
        color: #7f8c8d;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .filter-select {
        width: 100%;
        padding: 0.6rem 0.75rem;
        border: 1px solid #e9ecef;
        border-radius: 6px;
        background-color: #fff;
        color: #2c3e50;
        font-size: 0.95rem;
        transition: all 0.2s ease;
    }

        .filter-select:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
            outline: none;
        }

    .filter-button {
        background-color: #3498db;
        color: white;
        border: none;
        padding: 0.6rem 1.25rem;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
        min-width: 120px;
    }

        .filter-button:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }

    .filter-reset {
        background-color: #f8f9fa;
        color: #2c3e50;
        border: 1px solid #e9ecef;
        padding: 0.6rem 1.25rem;
        border-radius: 6px;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.2s ease;
    }

        .filter-reset:hover {
            background-color: #e9ecef;
        }

    /* Medicine Analysis Card */
    .analysis-card {
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
        margin-bottom: 2rem;
        overflow: hidden;
        transition: all 0.3s ease;
    }

        .analysis-card:hover {
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
        }

    .medicine-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 1rem 1.5rem;
        background-color: #f8f9fa;
        border-bottom: 1px solid #e9ecef;
    }

    .medicine-name {
        font-size: 1.5rem;
        font-weight: 600;
        color: #34495e;
        margin: 0;
    }

    .medicine-content {
        padding: 1.5rem;
    }

    /* No Data Message */
    .no-data-message {
        text-align: center;
        padding: 3rem;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .no-data-icon {
        font-size: 3rem;
        color: #e9ecef;
        margin-bottom: 1rem;
    }

    .no-data-text {
        font-size: 1.25rem;
        color: #7f8c8d;
        margin-bottom: 1.5rem;
    }

    /* Metric Cards */
    .metrics-row {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }

    .metric-card {
        padding: 1.25rem;
        border-radius: 10px;
        background: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        transition: transform 0.2s ease;
        border-left: 4px solid #3498db;
    }

        .metric-card:hover {
            transform: translateY(-3px);
        }

    .metric-title {
        font-size: 0.85rem;
        font-weight: 500;
        color: #7f8c8d;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .metric-value {
        font-size: 1.75rem;
        font-weight: 700;
        color: #2c3e50;
    }

    /* Trend Indicators */
    .trend-indicator {
        padding: 0.35rem 0.85rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 600;
        display: inline-flex;
        align-items: center;
    }

    .trend-up {
        background-color: #d4edda;
        color: #155724;
    }

        .trend-up::before {
            content: "↑ ";
            margin-right: 3px;
        }

    .trend-down {
        background-color: #f8d7da;
        color: #721c24;
    }

        .trend-down::before {
            content: "↓ ";
            margin-right: 3px;
        }

    .trend-neutral {
        background-color: #fff3cd;
        color: #856404;
    }

        .trend-neutral::before {
            content: "→ ";
            margin-right: 3px;
        }

    /* Charts Section */
    .charts-container {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
        gap: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .chart-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        overflow: hidden;
    }

    .chart-header {
        padding: 1rem 1.25rem;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .chart-title {
        font-size: 1rem;
        font-weight: 600;
        color: #34495e;
        margin: 0;
    }

    .chart-body {
        padding: 1.25rem;
        height: 300px;
    }

    /* Seasonal Patterns Table */
    .table-section {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        overflow: hidden;
        margin-bottom: 1.5rem;
    }

    .section-header {
        padding: 1rem 1.25rem;
        border-bottom: 1px solid #e9ecef;
    }

    .section-title {
        font-size: 1rem;
        font-weight: 600;
        color: #34495e;
        margin: 0;
    }

    .section-body {
        padding: 1.25rem;
    }

    .seasonal-table {
        width: 100%;
        border-collapse: collapse;
    }

        .seasonal-table th {
            background-color: #f8f9fa;
            color: #495057;
            font-weight: 600;
            text-align: left;
            padding: 0.75rem 1rem;
        }

        .seasonal-table td {
            padding: 0.75rem 1rem;
            border-top: 1px solid #e9ecef;
        }

        .seasonal-table tr:hover {
            background-color: #f8f9fa;
        }

    /* Recommendations Section */
    .recommendations-section {
        background: white;
        border-radius: 10px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        overflow: hidden;
    }

    .recommendation-list {
        padding: 0.5rem 0;
    }

    .recommendation-item {
        padding: 0.85rem 1.25rem;
        margin: 0.5rem 1rem;
        border-radius: 6px;
        background-color: #f8f9fa;
        border-left: 4px solid #3498db;
        transition: transform 0.2s ease;
    }

        .recommendation-item:hover {
            transform: translateX(3px);
            background-color: #edf2f7;
        }

    /* Loading Indicator */
    .loading-indicator {
        display: none;
        text-align: center;
        padding: 2rem;
    }

    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #3498db;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .charts-container {
            grid-template-columns: 1fr;
        }

        .medicine-header {
            flex-direction: column;
            align-items: flex-start;
        }

        .metric-card {
            margin-bottom: 1rem;
        }

        .filters-container {
            flex-direction: column;
            align-items: stretch;
        }

        .filter-group {
            width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <h2 class="page-title">Medicine Demand Pattern Analysis</h2>
    <p class="period-indicator" ({{ two_years_ago|date:"M d, Y" }} - {{ today|date:"M d, Y" }})</p>

   <!-- Filters Section -->
<div class="filters-container">
    <div class="filter-group">
        <label for="medicine-select" class="filter-label">Medicine</label>
        <select id="medicine-select" class="filter-select">
            <option value="">Select a Medicine</option>
            <option value="all">All Medicines</option>
            {% for medicine in all_medicines %}
            <option value="{{ medicine.id }}" {% if selected_medicine_id == medicine.id|stringformat:"s" %}selected{% endif %}>{{ medicine.name }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="filter-group">
        <label for="year-select" class="filter-label">Year</label>
        <select id="year-select" class="filter-select">
            <option value="">All Years</option>
            {% for year in available_years %}
            <option value="{{ year }}" {% if selected_year == year|stringformat:"s" %}selected{% endif %}>{{ year }}</option>
            {% endfor %}
        </select>
    </div>
    <div class="filter-group">
        <label for="quarter-select" class="filter-label">Quarter</label>
        <select id="quarter-select" class="filter-select">
            <option value="">All Quarters</option>
            <option value="1" {% if selected_quarter == "1" %}selected{% endif %}>Q1</option>
            <option value="2" {% if selected_quarter == "2" %}selected{% endif %}>Q2</option>
            <option value="3" {% if selected_quarter == "3" %}selected{% endif %}>Q3</option>
            <option value="4" {% if selected_quarter == "4" %}selected{% endif %}>Q4</option>
        </select>
    </div>
    <div class="filter-group" style="flex: 0 0 auto;">
        <button id="apply-filters" class="filter-button">Apply Filters</button>
    </div>
    <div class="filter-group" style="flex: 0 0 auto;">
        <button id="reset-filters" class="filter-reset">Reset</button>
    </div>
</div>
    <!-- Loading Indicator -->
    <div id="loading-indicator" class="loading-indicator">
        <div class="loading-spinner"></div>
        <p>Loading analysis data...</p>
    </div>

    <!-- No Data Message (initially hidden) -->
    <div id="no-data-message" class="no-data-message" style="display: none;">
        <div class="no-data-icon">📊</div>
        <div class="no-data-text">No data available for the selected filters</div>
        <button id="clear-filters-btn" class="filter-button">Clear Filters</button>
    </div>

    <!-- Analysis Cards Container -->
    <div id="analysis-container">
        {% if medicine_analyses %}
        {% for analysis in medicine_analyses %}
        <div class="analysis-card" data-medicine-id="{{ analysis.medicine.id }}">
            <div class="medicine-header">
                <h3 class="medicine-name">{{ analysis.medicine.name }}</h3>
                <div class="d-flex align-items-center">
                    <span class="trend-indicator {% if analysis.trend == 'increasing' %}trend-up{% elif analysis.trend == 'decreasing' %}trend-down{% else %}trend-neutral{% endif %} me-2">
                        {{ analysis.trend|title }}
                    </span>
                    {% if analysis.medicine.brand %}
                    <span class="badge bg-info bg-opacity-10 text-info me-2">
                        <i class="fas fa-tag me-1"></i>{{ analysis.medicine.brand }}
                    </span>
                    {% endif %}
                    {% if analysis.medicine.supplier %}
                    <span class="badge bg-primary bg-opacity-10 text-primary me-2">
                        <i class="fas fa-building me-1"></i>{{ analysis.medicine.supplier }}
                    </span>
                    {% endif %}
                </div>
            </div>

            <div class="medicine-content">
                <!-- Key Metrics -->
                <div class="metrics-row">
                    <div class="metric-card" style="border-left-color: #3498db;">
                        <div class="metric-title">Current Stock</div>
                        <div class="metric-value">{{ analysis.medicine.quantity }}</div>
                    </div>
                    <div class="metric-card" style="border-left-color: #9b59b6;">
                        <div class="metric-title">Peak Quarter</div>
                        <div class="metric-value">Q{{ analysis.peak_quarter }}</div>
                    </div>

                    <div class="metric-card" style="border-left-color: #f39c12;">
                        <div class="metric-title">Peak Month</div>
                        <div class="metric-value">{{ analysis.peak_month|default:"N/A" }}</div>
                    </div>
                </div>

                <!-- Charts -->
                <div class="charts-container">
                    <div class="chart-card">
                        <div class="chart-header">
                            <h5 class="chart-title">Quarterly Demand</h5>
                        </div>
                        <div class="chart-body">
                            <canvas id="quarterlyChart{{ analysis.medicine.id }}"></canvas>
                        </div>
                    </div>
                    <div class="chart-card">
                        <div class="chart-header">
                            <h5 class="chart-title">Monthly Average Demand</h5>
                        </div>
                        <div class="chart-body">
                            <canvas id="monthlyChart{{ analysis.medicine.id }}"></canvas>
                        </div>
                    </div>
                </div>

                <!-- Seasonal Patterns -->
                <div class="table-section">
                    <div class="section-header">
                        <h5 class="section-title">Seasonal Patterns</h5>
                    </div>
                    <div class="section-body">
                        <div class="table-responsive">
                            <table class="seasonal-table">
                                <thead>
                                    <tr>
                                        <th>Quarter</th>
                                        <th>Average Demand</th>
                                        <th>Min</th>
                                        <th>Max</th>
                                        <th>Standard Deviation</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for quarter in analysis.seasonal_patterns %}
                                    <tr>
                                        <td><strong>Q{{ quarter.quarter }}</strong></td>
                                        <td>{{ quarter.mean|floatformat:2 }}</td>
                                        <td>{{ quarter.min|floatformat:2 }}</td>
                                        <td>{{ quarter.max|floatformat:2 }}</td>
                                        <td>{{ quarter.std|floatformat:2 }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- Recommendations -->
                <div class="recommendations-section">
                    <div class="section-header">
                        <h5 class="section-title">Recommendations</h5>
                    </div>
                    <div class="recommendation-list">
                        {% for recommendation in analysis.recommendations %}
                        <div class="recommendation-item">
                            {{ recommendation }}
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        {% else %}
        <div id="initial-message" class="no-data-message">
            <div class="no-data-icon">🔍</div>
            <div class="no-data-text">Please select a medicine from the dropdown above to view analysis data</div>
        </div>
        {% endif %}
    </div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script>
    // Chart configuration with improved styling
    const chartOptions = {
        responsive: true,
        maintainAspectRatio: false,
        interaction: {
            mode: 'index',
            intersect: false,
        },
        plugins: {
            legend: {
                position: 'top',
                labels: {
                    boxWidth: 12,
                    usePointStyle: true,
                    pointStyle: 'circle'
                }
            },
            tooltip: {
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                titleColor: '#2c3e50',
                bodyColor: '#2c3e50',
                borderColor: '#e9ecef',
                borderWidth: 1,
                padding: 10,
                boxPadding: 5,
                usePointStyle: true,
                callbacks: {
                    label: function(context) {
                        return ' ' + context.dataset.label + ': ' + context.parsed.y;
                    }
                }
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: 'rgba(0, 0, 0, 0.05)'
                }
            }
        }
    };

    // Create charts for current analyses
    {% for analysis in medicine_analyses %}
    // Quarterly Demand Chart
    new Chart(document.getElementById('quarterlyChart{{ analysis.medicine.id }}'), {
        type: 'line',
        data: {
            labels: {{ analysis.quarterly_labels|safe }},
            datasets: [{
                label: 'Quarterly Demand',
                data: {{ analysis.quarterly_data|safe }},
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 2,
                pointBackgroundColor: '#ffffff',
                pointBorderColor: '#3498db',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6,
                tension: 0.3,
                fill: true
            }]
        },
        options: chartOptions
    });

    // Monthly Average Chart
    new Chart(document.getElementById('monthlyChart{{ analysis.medicine.id }}'), {
        type: 'bar',
        data: {
            labels: {{ analysis.monthly_labels|safe }},
            datasets: [{
                label: 'Monthly Average Demand',
                data: {{ analysis.monthly_data|safe }},
                backgroundColor: 'rgba(46, 204, 113, 0.7)',
                borderColor: 'rgba(46, 204, 113, 1)',
                borderWidth: 1,
                borderRadius: 5,
                hoverBackgroundColor: 'rgba(46, 204, 113, 0.9)'
            }]
        },
        options: chartOptions
    });
    {% endfor %}

    document.addEventListener('DOMContentLoaded', function () {
        // DOM elements
        const medicineSelect = document.getElementById('medicine-select');
        const yearSelect = document.getElementById('year-select');
        const quarterSelect = document.getElementById('quarter-select');
        const applyButton = document.getElementById('apply-filters');
        const resetButton = document.getElementById('reset-filters');
        const clearFiltersBtn = document.getElementById('clear-filters-btn');
        const analysisContainer = document.getElementById('analysis-container');
        const loadingIndicator = document.getElementById('loading-indicator');
        const noDataMessage = document.getElementById('no-data-message');
        const initialMessage = document.getElementById('initial-message');

        // Event listeners
        applyButton.addEventListener('click', applyFilters);
        resetButton.addEventListener('click', resetFilters);
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', resetFilters);
        }

        // Initialize view - show only selected medicine or initial message
        initializeView();

        function initializeView() {
            const analysisCards = document.querySelectorAll('.analysis-card');
            const selectedMedicine = medicineSelect.value;

            if (selectedMedicine && selectedMedicine !== '') {
                // A medicine is already selected (from URL parameters)
                if (selectedMedicine === 'all') {
                    // Show all medicines
                    analysisCards.forEach(card => {
                        card.style.display = 'block';
                    });
                    if (initialMessage) initialMessage.style.display = 'none';
                } else {
                    // Show only the selected medicine
                    let foundCard = false;
                    analysisCards.forEach(card => {
                        const cardMedicineId = card.getAttribute('data-medicine-id');
                        if (cardMedicineId === selectedMedicine) {
                            card.style.display = 'block';
                            foundCard = true;
                        } else {
                            card.style.display = 'none';
                        }
                    });

                    if (!foundCard && analysisCards.length > 0) {
                        // No matching card found, show no data message
                        noDataMessage.style.display = 'block';
                        if (initialMessage) initialMessage.style.display = 'none';
                    } else {
                        noDataMessage.style.display = 'none';
                    }
                }
            } else {
                // No medicine selected, hide all cards and show initial message
                analysisCards.forEach(card => {
                    card.style.display = 'none';
                });

                if (initialMessage) {
                    initialMessage.style.display = 'block';
                }
                noDataMessage.style.display = 'none';
            }
        }

        function applyFilters() {
            const selectedMedicine = medicineSelect.value;
            const selectedYear = yearSelect.value;
            const selectedQuarter = quarterSelect.value;

            // Show loading indicator
            loadingIndicator.style.display = 'block';

            // Build the URL with query parameters
            let url = window.location.pathname + '?';
            if (selectedMedicine) {
                url += 'medicine_id=' + selectedMedicine + '&';
            }
            if (selectedYear) {
                url += 'year=' + selectedYear + '&';
            }
            if (selectedQuarter) {
                url += 'quarter=' + selectedQuarter + '&';
            }

            // Remove trailing '&' if exists
            if (url.endsWith('&')) {
                url = url.slice(0, -1);
            }

            // Redirect to the filtered URL
            window.location.href = url;
        }

        function resetFilters() {
            // Redirect to the base URL without parameters
            window.location.href = window.location.pathname;
        }
    });
</script>
{% endblock %}