﻿{% extends 'base.html' %}
{% load base_filters %}
{% block content %}


<!-- Terminal Output with Improved Styling -->
    {% if terminal_output %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm rounded-3 overflow-hidden">
            <div class="card-header bg-light border-0 py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 text-secondary"><i class="bi bi-terminal me-2"></i>Terminal Output</h5>
                <button class="btn btn-sm btn-outline-secondary rounded-pill" id="copyTerminal" data-bs-toggle="tooltip" title="Copy to clipboard">
                    <i class="bi bi-clipboard me-1"></i>Copy
                </button>
            </div>
            <div class="card-body bg-dark text-light rounded-bottom p-0">
                <pre class="mb-0 p-3 terminal-text">{{ terminal_output }}</pre>
            </div>
        </div>
    </div>
</div>
    {% endif %}

<!-- Filter and Search Bar with Enhanced Design -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm rounded-3">
            <div class="card-body p-3">
                <div class="row g-3">
                    <div class="col-md-8">
                        <form method="get" action="" class="search-form">
                            <div class="input-group">
                                <span class="input-group-text bg-white border-end-0">
                                    <i class="bi bi-search text-muted"></i>
                                </span>
                                <input type="text" name="search" value="{{ search|default:'' }}" class="form-control border-start-0" placeholder="Search by medicine name, quantity..." id="searchInput">
                                <button type="submit" class="btn btn-primary px-4">Search</button>
                            </div>
                        </form>
                    </div>
                    <div class="col-md-4">
                        <div class="d-flex gap-2 justify-content-md-end">
                            <div class="d-flex gap-2">
                                <a href="{% url 'upload_transaction_form' %}" class="btn btn-primary px-4 rounded-pill d-flex align-items-center">
                                    <i class="bi bi-file-earmark-arrow-up me-2"></i>Upload
                                </a>
                                <a href="{% url 'create_transaction' %}" class="btn btn-success px-4 rounded-pill d-flex align-items-center">
                                    <i class="bi bi-plus-circle me-2"></i>New
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Transaction Card with Professional Design -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm rounded-3 overflow-hidden">
            <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                <h5 class="mb-0 fw-semibold">Transactions</h5>
                <span class="badge bg-primary rounded-pill">{{ transactions.paginator.count }} Total</span>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover align-middle mb-0" id="transactionTable">
                        <thead class="bg-light">
                            <tr>
                                <th class="px-4 py-3 text-secondary fw-semibold">
                                    <a href="#" class="text-secondary text-decoration-none d-flex align-items-center sort-column" data-sort="medicine">
                                        Medicine
                                        <i class="bi bi-sort ms-1"></i>
                                    </a>
                                </th>
                                <th class="py-3 text-secondary fw-semibold">
                                    <a href="#" class="text-secondary text-decoration-none d-flex align-items-center sort-column" data-sort="quantity">
                                        Quantity
                                        <i class="bi bi-sort ms-1"></i>
                                    </a>
                                </th>
                                <th class="py-3 text-secondary fw-semibold">
                                    <a href="#" class="text-secondary text-decoration-none d-flex align-items-center sort-column" data-sort="date">
                                        Transaction Date & Time
                                        <i class="bi bi-sort-down ms-1"></i>
                                    </a>
                                </th>
                                <th class="py-3 text-secondary fw-semibold">Status</th>
                                <th class="py-3 text-center text-secondary fw-semibold">View</th>
                            </tr>
                        </thead>
                        <tbody id="transactionTableBody">
                            {% for transaction in transactions %}
                            <tr class="border-bottom transaction-row" data-id="{{ transaction.pk }}">
                                <td class="px-4 py-3">
                                    <div class="d-flex align-items-center">
                                        <div class="medicine-icon me-3 d-flex align-items-center justify-content-center" style="width: 45px; height: 45px;">
                                            <div class="pill-capsule">
                                                <div class="pill-content">
                                                    <div class="pill-shine"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div>
                                            <div class="fw-medium">{{ transaction.medicine.name }}</div>
                                            <small class="text-muted">
                                                {% if transaction.medicine.brand %}{{ transaction.medicine.brand }}{% endif %}
                                                {% if transaction.medicine.brand and transaction.medicine.supplier %} | {% endif %}
                                                {% if transaction.medicine.supplier %}{{ transaction.medicine.supplier }}{% endif %}
                                            </small>
                                            <small class="text-muted d-block">{{ transaction.medicine.category|default:"Uncategorized" }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3">
                                    <span class="fw-medium {% if transaction.quantity < 0 %}text-danger{% elif transaction.quantity > 0 %}text-success{% endif %}">
                                        {% if transaction.quantity > 0 %}+{% endif %}{{ transaction.quantity }}
                                    </span>
                                    <small class="text-muted d-block">Units</small>
                                </td>
                                <td class="py-3" data-date="{{ transaction.transaction_date|date:'Y-m-d H:i' }}">
                                    <div>{{ transaction.transaction_date|date:"Y-m-d" }}</div>
                                    <small class="text-muted">{{ transaction.transaction_date|date:"H:i" }}</small>
                                </td>
                                <td class="py-3">
                                    {% if transaction.quantity > 0 %}
                                    <span class="badge bg-success-subtle text-success rounded-pill px-3 py-2">Inbound</span>
                                    {% elif transaction.quantity < 0 %}
                                    <span class="badge bg-danger-subtle text-danger rounded-pill px-3 py-2">Outbound</span>
                                    {% else %}
                                    <span class="badge bg-secondary-subtle text-secondary rounded-pill px-3 py-2">Adjustment</span>
                                    {% endif %}
                                </td>
                                <td class="py-3 text-center">
                                    <a href="{% url 'transaction_detail' transaction.pk %}"
                                       class="btn btn-primary btn-sm rounded-pill px-3"
                                       data-bs-toggle="tooltip"
                                       title="View Details">
                                        <i class="bi bi-eye me-1"></i> View
                                    </a>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="text-center py-5 text-muted">
                                    <div class="empty-state p-5">
                                        <div class="empty-icon rounded-circle bg-light d-flex align-items-center justify-content-center mx-auto mb-4" style="width: 80px; height: 80px;">
                                            <i class="bi bi-inbox display-6 text-secondary"></i>
                                        </div>
                                        <h4 class="text-secondary">No transactions found</h4>
                                        <p class="mb-4 text-muted">Create your first transaction to get started</p>
                                        <a href="{% url 'create_transaction' %}" class="btn btn-primary px-4 py-2 rounded-pill">
                                            <i class="bi bi-plus-circle me-2"></i> Create New Transaction
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <!-- Professional Pagination Component -->
            {% if transactions.paginator.num_pages > 1 %}
            <div class="card-footer bg-white border-0 py-4">
                <nav aria-label="Page navigation">
                    <ul class="pagination pagination-md justify-content-center mb-0 gap-2">
                        {% if transactions.has_previous %}
                        <li class="page-item">
                            <a class="page-link border-0 rounded-3 px-3 py-2 d-flex align-items-center"
                               href="?page=1{% if search %}&search={{ search }}{% endif %}{% if filter %}&filter={{ filter }}{% endif %}"
                               aria-label="First">
                                <i class="bi bi-chevron-double-left me-1"></i>
                                <span class="d-none d-sm-inline">First</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link border-0 rounded-3 px-3 py-2 d-flex align-items-center"
                               href="?page={{ transactions.previous_page_number }}{% if search %}&search={{ search }}{% endif %}{% if filter %}&filter={{ filter }}{% endif %}"
                               aria-label="Previous">
                                <i class="bi bi-chevron-left me-1"></i>
                                <span class="d-none d-sm-inline">Previous</span>
                            </a>
                        </li>
                        {% endif %}

                        <li class="page-item active">
                            <span class="page-link border-0 rounded-3 px-4 py-2">
                                Page <strong>{{ transactions.number }}</strong> of {{ transactions.paginator.num_pages }}
                            </span>
                        </li>

                        {% if transactions.has_next %}
                        <li class="page-item">
                            <a class="page-link border-0 rounded-3 px-3 py-2 d-flex align-items-center"
                               href="?page={{ transactions.next_page_number }}{% if search %}&search={{ search }}{% endif %}{% if filter %}&filter={{ filter }}{% endif %}"
                               aria-label="Next">
                                <span class="d-none d-sm-inline">Next</span>
                                <i class="bi bi-chevron-right ms-1"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link border-0 rounded-3 px-3 py-2 d-flex align-items-center"
                               href="?page={{ transactions.paginator.num_pages }}{% if search %}&search={{ search }}{% endif %}{% if filter %}&filter={{ filter }}{% endif %}"
                               aria-label="Last">
                                <span class="d-none d-sm-inline">Last</span>
                                <i class="bi bi-chevron-double-right ms-1"></i>
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>


<style>
    :root {
        --primary-color: #4f46e5;
        --primary-hover: #4338ca;
        --success-color: #10b981;
        --danger-color: #ef4444;
        --light-bg: #f9fafb;
        --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        --transition: all 0.2s ease-in-out;
    }

    .bg-gradient-light {
        background-image: linear-gradient(to right, #f9fafb, #f3f4f6);
    }

    .btn-primary {
        background-color: var(--primary-color);
        border-color: var(--primary-color);
        transition: var(--transition);
    }

        .btn-primary:hover {
            background-color: var(--primary-hover);
            border-color: var(--primary-hover);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(79, 70, 229, 0.2);
        }

    .text-primary {
        color: var(--primary-color) !important;
    }

    .bg-success-subtle {
        background-color: rgba(16, 185, 129, 0.15);
    }

    .bg-danger-subtle {
        background-color: rgba(239, 68, 68, 0.15);
    }

    .bg-secondary-subtle {
        background-color: rgba(107, 114, 128, 0.15);
    }

    .card {
        box-shadow: var(--card-shadow);
        transition: var(--transition);
    }

        .card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

    .table th {
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(79, 70, 229, 0.03);
    }

    .terminal-text {
        font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    }

    /* Additional responsive styles */
    @media (max-width: 767px) {
        .table-responsive {
            overflow-x: auto;
        }

        .terminal-text {
            font-size: 0.9rem;
        }
    }


    /* Professional Pill Icon Design */
    .medicine-icon {
        position: relative;
        width: 40px;
        height: 40px;
        background: #f8f9fa;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        overflow: hidden;
    }

        .medicine-icon:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

    .pill-capsule {
        width: 32px;
        height: 16px;
        position: relative;
        transform: rotate(-45deg);
    }

    .pill-content {
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--primary-color) 50%, #4a90e2 50%);
        border-radius: 8px;
        position: relative;
        overflow: hidden;
        box-shadow: inset 0 -2px 4px rgba(0,0,0,0.1);
    }

    .pill-shine {
        position: absolute;
        width: 100%;
        height: 100%;
        background: linear-gradient( 135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0.1) 30%, rgba(255,255,255,0) 50% );
        transform: translateY(-50%);
    }

    .pill-tablet {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1), inset 0 -2px 4px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .pill-tablet-line {
        position: absolute;
        width: 70%;
        height: 2px;
        background: rgba(0,0,0,0.1);
        top: 50%;
        left: 15%;
        transform: translateY(-50%);
    }

    .pill-tablet-shine {
        position: absolute;
        width: 12px;
        height: 6px;
        background: rgba(255,255,255,0.3);
        border-radius: 50%;
        top: 25%;
        left: 25%;
    }

    /* Pill variations - use different colors based on medicine category */
    .pill-antibiotic .pill-content {
        background: linear-gradient(135deg, #10b981 50%, #059669 50%);
    }

    .pill-painkiller .pill-content {
        background: linear-gradient(135deg, #f97316 50%, #ea580c 50%);
    }

    .pill-cardiovascular .pill-content {
        background: linear-gradient(135deg, #ef4444 50%, #dc2626 50%);
    }

    .pill-vitamin .pill-tablet {
        background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    }

    /* Optional: Add animation on hover */
    .medicine-icon:hover .pill-capsule {
        animation: wiggle 0.5s ease;
    }

    .medicine-icon:hover .pill-tablet {
        animation: pulse 1s infinite;
    }

    @keyframes wiggle {
        0%, 100% {
            transform: rotate(-45deg);
        }

        25% {
            transform: rotate(-40deg);
        }

        75% {
            transform: rotate(-50deg);
        }
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
        }

        50% {
            transform: scale(1.05);
        }
    }



</style>

<script>
    // Search functionality with debounce
    const searchInput = document.getElementById('searchInput');
    const table = document.getElementById('transactionTable');
    const tbody = document.getElementById('transactionTableBody');
    let sortDirection = 'desc';
    let lastSortedColumn = 'date'; // Default sort by date

    function updateURL(params) {
        const url = new URL(window.location);
        Object.entries(params).forEach(([key, value]) => {
            if (value) {
                url.searchParams.set(key, value);
            } else {
                url.searchParams.delete(key);
            }
        });
        window.location = url;
    }

    searchInput.addEventListener('input', debounce((e) => {
        updateURL({
            search: e.target.value,
            page: 1
        });
    }, 500));

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Sorting functionality
    function sortTable(column) {
        const rows = Array.from(tbody.getElementsByTagName('tr'));

        // Skip if there's only one row or empty state
        if (rows.length <= 1 || rows[0].getElementsByTagName('td')[0].colSpan > 1) {
            return;
        }

        // Update sort direction
        if (lastSortedColumn === column) {
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            sortDirection = 'desc';
        }
        lastSortedColumn = column;

        // Update sort icons and active state
        document.querySelectorAll('.sort-column').forEach(header => {
            header.classList.remove('sort-active');
            header.querySelector('i').className = 'bi bi-sort ms-1';
        });

        const currentHeader = document.querySelector(`[data-sort="${column}"]`);
        currentHeader.classList.add('sort-active');
        currentHeader.querySelector('i').className = `bi bi-sort-${sortDirection === 'asc' ? 'up' : 'down'} ms-1`;

        // Sort rows
        rows.sort((a, b) => {
            let aVal, bVal;

            switch (column) {
                case 'medicine':
                    aVal = a.cells[0].querySelector('.fw-medium').textContent.toLowerCase();
                    bVal = b.cells[0].querySelector('.fw-medium').textContent.toLowerCase();
                    break;
                case 'quantity':
                    aVal = parseInt(a.cells[1].querySelector('.fw-medium').textContent);
                    bVal = parseInt(b.cells[1].querySelector('.fw-medium').textContent);
                    break;
                case 'date':
                    aVal = new Date(a.cells[2].dataset.date);
                    bVal = new Date(b.cells[2].dataset.date);
                    break;
            }

            if (sortDirection === 'asc') {
                return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
            } else {
                return aVal < bVal ? 1 : aVal > bVal ? -1 : 0;
            }
        });

        // Reorder table rows
        rows.forEach(row => tbody.appendChild(row));
    }

    // Terminal copy functionality
    if (document.getElementById('copyTerminal')) {
        document.getElementById('copyTerminal').addEventListener('click', function () {
            const terminalText = document.querySelector('.terminal-text').textContent;
            navigator.clipboard.writeText(terminalText).then(() => {
                // Change button text temporarily
                const originalText = this.innerHTML;
                this.innerHTML = '<i class="bi bi-check me-1"></i>Copied!';
                setTimeout(() => {
                    this.innerHTML = originalText;
                }, 2000);
            });
        });
    }

    // Export button functionality
    if (document.getElementById('exportBtn')) {
        document.getElementById('exportBtn').addEventListener('click', function () {
            // This is a placeholder - in a real app, this would trigger a CSV/Excel export
            alert('Export functionality would be implemented here');
        });
    }

    // Row click functionality
    document.querySelectorAll('.transaction-row').forEach(row => {
        row.addEventListener('click', function (e) {
            // Don't trigger if clicking on a button or link
            if (e.target.tagName === 'A' || e.target.tagName === 'BUTTON' ||
                e.target.closest('a') || e.target.closest('button')) {
                return;
            }
            const transactionId = this.dataset.id;
            window.location.href = `{% url 'transaction_detail' 0 %}`.replace('0', transactionId);
        });
    });

    // Initialize tooltips and sorting
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                placement: 'top',
                boundary: 'window'
            });
        });

        // Add click event listeners to sortable columns
        document.querySelectorAll('.sort-column').forEach(header => {
            header.addEventListener('click', (e) => {
                e.preventDefault();
                const column = e.currentTarget.dataset.sort;
                sortTable(column);
            });
        });

        // Initial sort by date
        sortTable('date');
    });

    // Set initial values from URL params
    const urlParams = new URLSearchParams(window.location.search);
    searchInput.value = urlParams.get('search') || '';
</script>
{% endblock %}