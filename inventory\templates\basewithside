
{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Medicine Inventory Management System - Efficient medicine tracking and forecasting">
    <meta name="theme-color" content="#2c3e50">
    <title>{% block title %}Medicine Inventory Management System{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{% static 'img/favicon.svg' %}">
    <link rel="alternate icon" type="image/x-icon" href="{% static 'img/favicon.ico' %}">

    <!-- Preload critical assets -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style">

    <!-- CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">
    <link rel="stylesheet" href="{% static 'css/base.css' %}">
    {% block extra_css %}{% endblock %}

    <!-- Progressive Web App meta tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">

    <!-- Custom CSS for navigation styles -->
    <style>


        :root {
            --sidebar-width: 250px;
            --header-height: 60px;
            --transition-speed: 0.3s;
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --primary-gradient: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            --background-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            --surface-color: #ffffff;
            --text-color: #333333;
            --hover-color: rgba(52, 152, 219, 0.1);
        }

        /* ===== BASE STYLES ===== */
        body {
            overflow-x: hidden;
            width: 100%;
            position: relative;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.5;
        }

        /* ===== ANIMATIONS ===== */
        .animate {
            animation-duration: 0.3s;
            animation-fill-mode: both;
        }

        .slideIn {
            animation-name: slideIn;
        }

        @keyframes slideIn {
            0% {
                transform: translateY(-10px);
                opacity: 0;
            }

            100% {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* ===== NAVBAR STYLES ===== */
        .navbar {
            padding: 0.5rem 0;
            background: var(--primary-gradient);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            min-height: var(--header-height);
            z-index: 1020;
        }

            .navbar .container,
            .navbar .container-fluid {
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

        /* Brand Styling */
        .navbar-brand-container {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1;
        }

        .navbar-brand {
            color: #ffffff !important;
            font-weight: 700;
            font-size: 1.5rem;
            margin: 0;
            padding: 0.5rem 0;
            white-space: nowrap;
            z-index: 1;
        }

        .brand-text {
            font-size: 1.25rem;
            font-weight: 500;
        }

        /* Navigation Buttons */
        .navbar-toggler-custom,
        .navbar-toggler,
        .btn-outline-light {
            color: white;
            border: 1px solid rgba(255,255,255,0.2);
            padding: 0.375rem 0.75rem;
            transition: all 0.2s ease;
            background: transparent;
        }

            .navbar-toggler-custom:hover,
            .navbar-toggler:hover,
            .btn-outline-light:hover {
                background: rgba(255,255,255,0.1);
                border-color: rgba(255,255,255,0.3);
            }

        .navbar-toggler-custom {
            font-size: 1.25rem;
            padding: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
        }

        /* Navigation Items */
        .navbar-dark .navbar-nav .nav-link,
        .nav-link {
            color: rgba(255,255,255,0.9) !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
        }

            .navbar-dark .navbar-nav .nav-link:hover,
            .nav-link:hover {
                color: #ffffff !important;
                transform: translateY(-1px);
            }

        /* Navbar Collapse */
        .navbar-collapse {
            flex-basis: auto;
            flex-grow: 0;
        }

        /* ===== DROPDOWN STYLES ===== */
        .dropdown-menu {
            background: var(--surface-color);
            border: none;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 0.5rem 0;
        }

        .dropdown-item {
            color: var(--text-color);
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
            font-weight: 500;
        }

            .dropdown-item:hover {
                background: var(--hover-color);
                color: var(--primary-color);
            }

        /* Navbar-specific dropdown styles */
        .navbar .dropdown-menu {
            position: absolute;
            background: white;
            border: none;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-top: 0.5rem;
            min-width: 200px;
            padding: 0.5rem 0;
            z-index: 1031;
        }

        .navbar .dropdown-menu-end {
            right: 0;
            left: auto;
        }

        .navbar .dropdown-item {
            padding: 0.75rem 1.25rem;
            color: var(--primary-color);
            font-weight: 500;
            display: flex;
            align-items: center;
            transition: all 0.2s ease;
        }

            .navbar .dropdown-item:hover {
                background-color: rgba(52, 152, 219, 0.1);
                color: var(--secondary-color);
                transform: translateX(5px);
            }

            .navbar .dropdown-item i,
            .navbar .dropdown-item .fas {
                margin-right: 0.75rem;
                width: 20px;
                text-align: center;
            }

        /* Ensure dropdown toggle arrow works correctly */
        .navbar .dropdown-toggle::after {
            margin-left: 0.5rem;
            vertical-align: middle;
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-left: 0.3em solid transparent;
        }

        /* ===== SIDEBAR STYLES ===== */
        .sidebar {
            position: fixed;
            top: 0;
            left: calc(-1 * var(--sidebar-width));
            width: var(--sidebar-width);
            height: 100vh;
            background: var(--primary-gradient);
            transition: left var(--transition-speed) ease;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            z-index: 1030;
        }

            .sidebar.active {
                left: 0;
            }

        .sidebar-header {
            padding: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

            .sidebar-header .navbar-brand {
                color: white;
                font-weight: 700;
            }

        .sidebar-menu {
            list-style: none;
            padding: 1rem 0;
            margin: 0;
        }

        .sidebar-item {
            position: relative;
        }

            .sidebar-item a {
                display: block;
                padding: 0.75rem 1.5rem;
                color: rgba(255,255,255,0.9);
                text-decoration: none;
                transition: all 0.3s ease;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

                .sidebar-item a:hover,
                .sidebar-item a:focus {
                    color: white;
                    background-color: rgba(255,255,255,0.1);
                }

        .sidebar-divider {
            border-top: 1px solid rgba(255,255,255,0.1);
            margin: 1rem 0;
        }

        .sidebar-item button.btn-link {
            text-decoration: none;
            padding: 0.75rem 1.5rem;
            text-align: left;
            display: block;
            width: 100%;
            color: rgba(255,255,255,0.9);
            transition: all 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            background: transparent;
            border: none;
        }

            .sidebar-item button.btn-link:hover {
                color: white;
                background-color: rgba(255,255,255,0.1);
            }

        /* ===== CONTENT WRAPPER ===== */
        .content-wrapper {
            transition: margin-left var(--transition-speed);
            width: 100%;
            max-width: 100%;
            overflow-x: hidden;
            box-sizing: border-box;
        }

            .content-wrapper.shifted {
                margin-left: var(--sidebar-width);
                width: calc(100% - var(--sidebar-width));
            }

        /* ===== MAIN CONTENT AREA ===== */
        .main-content {
            background: transparent;
            padding: 2rem 1rem;
            min-height: calc(100vh - var(--header-height) - 56px); /* 56px is footer height */
            width: 100%;
            box-sizing: border-box;
        }

        /* ===== CARD STYLING ===== */
        .card {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            margin-bottom: 1.5rem;
            position: relative;
            z-index: 10;
            background: white;
            display: block;
            clear: both;
            max-width: 100%;
            border: none;
        }

        .card-header {
            background: var(--primary-gradient);
            color: #ffffff;
            border-bottom: none;
            padding: 1rem 1.25rem;
        }

        /* ===== FOOTER STYLING ===== */
        .footer {
            background: rgba(255, 255, 255, 0.9) !important;
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            padding: 1rem 0;
        }

        /* ===== BUTTON STYLING ===== */
        .btn {
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-gradient);
            border: none;
        }

            .btn-primary:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            }

        /* ===== ALERT STYLING ===== */
        .alert {
            border-radius: 0.5rem;
            border: none;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* ===== MESSENGER-STYLE NAVIGATION TOGGLE ===== */
        .nav-style-toggle {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1040;
            display: flex;
            flex-direction: column;
        }

        .nav-toggle-menu {
            position: absolute;
            bottom: 70px;
            right: 0;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 10px 0;
            min-width: 150px;
            opacity: 0;
            transform: translateY(10px) scale(0.95);
            transform-origin: bottom right;
            transition: all 0.2s ease;
            pointer-events: none;
        }

            .nav-toggle-menu.show {
                opacity: 1;
                transform: translateY(0) scale(1);
                pointer-events: auto;
            }

        .nav-toggle-menu-item {
            display: block;
            padding: 10px 15px;
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s ease;
            cursor: pointer;
        }

            .nav-toggle-menu-item:hover {
                background-color: rgba(0,0,0,0.05);
            }

            .nav-toggle-menu-item.active {
                background-color: rgba(52, 152, 219, 0.1);
                color: var(--secondary-color);
            }

        .nav-toggle-button {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: var(--primary-gradient);
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
        }

            .nav-toggle-button:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 15px rgba(0,0,0,0.25);
            }

            .nav-toggle-button:focus {
                outline: none;
            }

            .nav-toggle-button i {
                transition: transform 0.3s ease;
            }

            .nav-toggle-button.active i {
                transform: rotate(45deg);
            }

        /* ===== NAVIGATION MODES ===== */
        /* Header Navigation Mode */
        body.nav-header .navbar-brand-container,
        body.nav-header .navbar-brand {
            display: none !important;
        }

        body.nav-header .sidebar {
            display: none !important;
        }

        body.nav-header .content-wrapper {
            margin-left: 0 !important;
            width: 100% !important;
        }

        body.nav-header .navbar-collapse {
            display: flex !important;
            flex-direction: row;
            align-items: center;
        }

        body.nav-header .navbar-nav {
            flex-direction: row;
            align-items: center;
        }

            body.nav-header .navbar-nav .nav-item {
                display: inline-block;
            }

        /* Sidebar Navigation Mode */
        body.nav-sidebar .navbar-collapse {
            display: none !important;
        }

        /* ===== RESPONSIVE STYLES ===== */
        /* Extra small devices (phones, less than 576px) */
        @media (max-width: 575.98px) {
            /* Automatically switch to sidebar navigation on mobile */
            body.nav-header:not(.force-header) .navbar-brand-container,
            body.nav-header:not(.force-header) .navbar-brand {
                display: block !important;
            }

            body.nav-header:not(.force-header) .sidebar {
                display: block !important;
            }

            body.nav-header:not(.force-header) .navbar-collapse {
                display: none !important;
            }

            body.nav-header:not(.force-header)::after {
                content: '';
                display: block;
                clear: both;
            }

            body.nav-header:not(.force-header).nav-sidebar {
                display: block !important;
            }

            .brand-text {
                font-size: 1rem;
            }

            .container {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            .main-content {
                padding: 1rem 0.5rem;
            }

            .nav-toggle-button {
                width: 50px;
                height: 50px;
                font-size: 1.25rem;
            }

            .navbar-brand {
                font-size: 1.25rem;
            }
        }

        /* Small devices (landscape phones, 576px and up) */
        @media (min-width: 576px) and (max-width: 767.98px) {
            /* Automatically switch to sidebar navigation on small devices */
            body.nav-header:not(.force-header) .navbar-brand-container,
            body.nav-header:not(.force-header) .navbar-brand {
                display: block !important;
            }

            body.nav-header:not(.force-header) .sidebar {
                display: block !important;
            }

            body.nav-header:not(.force-header) .navbar-collapse {
                display: none !important;
            }

            body.nav-header:not(.force-header)::after {
                content: '';
                display: block;
                clear: both;
            }

            body.nav-header:not(.force-header).nav-sidebar {
                display: block !important;
            }

            .brand-text {
                font-size: 1.1rem;
            }

            .navbar-toggler-custom {
                padding: 0.25rem 0.5rem;
            }

            .main-content {
                padding: 1.5rem 1rem;
            }
        }

        /* Medium devices (tablets, 768px and up) */
        @media (min-width: 768px) and (max-width: 991.98px) {
            /* Automatically switch to sidebar navigation on tablets */
            body.nav-header:not(.force-header) .navbar-brand-container,
            body.nav-header:not(.force-header) .navbar-brand {
                display: block !important;
            }

            body.nav-header:not(.force-header) .sidebar {
                display: block !important;
            }

            body.nav-header:not(.force-header) .navbar-collapse {
                display: none !important;
            }

            body.nav-header:not(.force-header)::after {
                content: '';
                display: block;
                clear: both;
            }

            body.nav-header:not(.force-header).nav-sidebar {
                display: block !important;
            }

            .sidebar.active {
                width: 70%;
                max-width: 250px;
            }

            .main-content {
                padding: 1.75rem 1.25rem;
            }
        }

        /* Large devices (desktops, 992px and up) */
        @media (min-width: 992px) {
            .sidebar {
                left: -250px;
            }

                .sidebar.active {
                    left: 0;
                }

            .content-wrapper {
                transition: margin-left 0.3s ease;
            }

                .content-wrapper.shifted {
                    margin-left: 250px;
                }

            /* Ensure header navigation displays in one line */
            body.nav-header .navbar > .container,
            body.nav-header .navbar > .container-fluid {
                display: flex;
                flex-wrap: nowrap;
                align-items: center;
                justify-content: space-between;
            }

            body.nav-header .navbar-collapse {
                display: flex !important;
                flex-basis: auto;
            }

            body.nav-header .navbar-nav {
                flex-direction: row;
            }

                body.nav-header .navbar-nav .nav-link {
                    padding-right: 0.75rem;
                    padding-left: 0.75rem;
                }
        }

        /* Common responsive adjustments for mobile devices */
        @media (max-width: 991.98px) {
            .content-wrapper,
            .content-wrapper.shifted {
                width: 100%;
                margin-left: 0;
            }

            .sidebar.active {
                width: 80%;
                max-width: 300px;
                box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
            }

            /* Overlay when sidebar is active */
            body.sidebar-active::after {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1025;
            }

            /* Navbar collapse styling for mobile */
            .navbar-collapse {
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: var(--primary-gradient);
                padding: 1rem;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                z-index: 1000;
            }

            .navbar-nav {
                padding-top: 0.5rem;
            }

            .nav-link {
                padding: 0.75rem 1rem;
            }

            /* Ensure navbar items stay in one line on mobile in header mode */
            body.nav-header .navbar-collapse {
                flex-wrap: nowrap;
                justify-content: flex-end;
            }

            body.nav-header .navbar-nav {
                flex-wrap: nowrap;
            }

            /* Mobile responsiveness for dropdowns */
            .navbar .dropdown-menu {
                position: static;
                float: none;
                width: auto;
                margin-top: 0;
                background-color: transparent;
                border: 0;
                box-shadow: none;
                padding: 0 1rem;
            }

            .navbar .dropdown-item {
                color: rgba(255, 255, 255, 0.9);
                padding: 0.5rem 1rem;
            }

                .navbar .dropdown-item:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                    color: #ffffff;
                    transform: none;
                }
        }


    </style>
</head>

<body class="d-flex flex-column min-vh-100">
    <!-- Sidebar Navigation -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <a class="navbar-brand d-flex align-items-center" href="{% url 'home' %}">
                <i class="fas fa-capsules me-2"></i>
                <span>MedForecast</span>
            </a>
            <button id="closeSidebar" class="btn btn-link text-white d-lg-none">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <ul class="sidebar-menu">
            <li class="sidebar-item">


                <a href="{% url 'transaction_list' %}">
                    <i class="fas fa-exchange-alt me-2"></i>Transactions
                </a>
            </li>

            <li class="sidebar-item dropdown">
                <a href="#" class="dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#createSubmenu" aria-expanded="false">
                    <i class="fas fa-plus-circle me-2"></i>Create
                    <i class="fas fa-chevron-down ms-auto"></i>
                </a>
                <ul class="collapse sidebar-submenu" id="createSubmenu">
                    <li class="sidebar-item">
                        <a href="{% url 'create_transaction' %}">
                            <i class="fas fa-file-plus me-2"></i>New Transaction
                        </a>
                    </li>
                    <li class="sidebar-item">
                        <a href="{% url 'create_medicine' %}">
                            <i class="fas fa-capsules me-2"></i>New Medicine
                        </a>
                    </li>
                    <li class="sidebar-item">
                        <a href="{% url 'reports_dashboard_new' %}">
                            <i class="fas fa-chart-bar me-2"></i>Reports
                            <span class="badge bg-primary ms-2 small">New</span>
                        </a>
                    </li>
                </ul>




            <li class="sidebar-item">
                <a href="{% url 'inventory_list' %}">
                    <i class="fas fa-boxes me-2"></i>Inventory
                </a>
            </li>
            <li class="sidebar-item">
                <a href="{% url 'medicine_list' %}">
                    <i class="fas fa-list me-2"></i>Medicines
                </a>
            </li>
            <li class="sidebar-item">
                <a href="{% url 'forecast_list' %}">
                    <i class="fas fa-chart-line me-2"></i>Forecast
                </a>
            </li>
            <li class="sidebar-item">
                <a href="{% url 'medicine_analysis' %}">
                    <i class="fas fa-chart-bar me-2"></i>Medicine Analysis
                </a>
            </li>
            <li class="sidebar-item">
                <a href="{% url 'audit_trail_list' %}">
                    <i class="fas fa-history me-2"></i>Audit Trail
                </a>
            </li>
        </ul>
    </div>

    <!-- Main Content Wrapper -->
    <div class="content-wrapper" id="content">
        <!-- Header -->
        <header>
            <nav class="navbar navbar-dark" id="mainNavbar">
                <div class="container position-relative">
                    <!-- Left side controls -->
                    <div class="d-flex align-items-center">
                        <button id="sidebarToggle" class="btn navbar-toggler-custom me-2">
                            <i class="fas fa-bars"></i>
                        </button>

                        <!-- Navigation Style Toggle -->
                        <div class="nav-style-toggle d-none d-lg-flex">
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-light btn-sm" id="sidebarNavToggle" title="Sidebar Navigation">
                                    <i class="fas fa-columns"></i>
                                </button>
                                <button type="button" class="btn btn-outline-light btn-sm" id="headerNavToggle" title="Header Navigation">
                                    <i class="fas fa-bars"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Centered Brand -->
                    <div class="navbar-brand-container">
                        <div class="navbar-brand d-flex align-items-center justify-content-center">
                            <i class="fas fa-capsules me-2"></i>
                            <span class="brand-text">BMC MedForecast</span>
                        </div>
                    </div>



                    <!-- Navigation Menu -->
                    <div class="collapse navbar-collapse" id="navbarContent">
                        <ul class="navbar-nav ms-auto">
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'transaction_list' %}">
                                    <i class="fas fa-exchange-alt me-1"></i>Transactions
                                </a>
                            </li>

                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="createDropdown" role="button"
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-plus-circle me-1"></i>Create
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end animate slideIn" aria-labelledby="createDropdown">
                                    <li>
                                        <a class="dropdown-item" href="{% url 'create_transaction' %}">
                                            <i class="fas fa-file-plus me-2"></i>New Transaction
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'create_medicine' %}">
                                            <i class="fas fa-capsules me-2"></i>New Medicine
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'reports_dashboard_new' %}">
                                            <i class="fas fa-chart-bar me-2"></i>Reports
                                            <span class="badge bg-primary ms-2 small">New</span>
                                        </a>
                                    </li>
                                </ul>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'inventory_list' %}">
                                    <i class="fas fa-boxes me-1"></i>Inventory
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'medicine_list' %}">
                                    <i class="fas fa-list me-1"></i>Medicines
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'forecast_list' %}">
                                    <i class="fas fa-chart-line me-1"></i>Forecast
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'medicine_analysis' %}">
                                    <i class="fas fa-chart-bar me-1"></i>Medicine Analysis
                                </a>
                            </li>

                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'audit_trail_list' %}">
                                    <i class="fas fa-history me-1"></i>Audit Trail
                                </a>
                            </li>

                            {% if user.is_authenticated %}
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                   data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-user me-1"></i>{{ user.username }}
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end animate slideIn" aria-labelledby="userDropdown">
                                    <li>
                                        <form method="post" action="{% url 'logout' %}" class="dropdown-item p-0">
                                            {% csrf_token %}
                                            <button type="submit" class="dropdown-item text-danger">
                                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                            {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'login' %}">
                                    <i class="fas fa-sign-in-alt me-1"></i>Login
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </nav>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            {% if messages %}
            <div class="container mt-3">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            </div>
            {% endif %}

            {% block content %}{% endblock %}
        </main>

        <!-- Footer -->
        <footer class="footer mt-auto py-3 bg-light">
            <div class="container text-center">
                <span class="text-muted">© {% now "Y" %} BMC MedForecast. All rights reserved.</span>
            </div>
        </footer>
    </div>
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>

    <script>


        /**
         * Enhanced Navigation Toggle System
         * Provides both sidebar and header navigation options with persistent settings
         */

        document.addEventListener('DOMContentLoaded', function () {
            // Constants for localStorage keys
            const NAV_STYLE_KEY = 'navStyle';
            const TOGGLE_POSITION_KEY = 'navTogglePosition';
            const TOGGLE_VISIBLE_KEY = 'navToggleVisible';

            // Cache DOM elements
            const sidebar = document.getElementById('sidebar');
            const content = document.getElementById('content');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const closeSidebar = document.getElementById('closeSidebar');

            // === SIDEBAR TOGGLE FUNCTIONALITY ===
            function toggleSidebar() {
                if (!sidebar || !content) return;

                sidebar.classList.toggle('active');
                content.classList.toggle('shifted');

                // Add overlay only on mobile
                if (window.innerWidth < 992) {
                    document.body.classList.toggle('sidebar-active');
                }

                // If sidebar is now active, update UI accordingly
                if (sidebar.classList.contains('active')) {
                    addSidebarUserInfo();
                    setNavStyle('sidebar', true);
                }
            }

            // Set up event listeners for sidebar toggle
            if (sidebarToggle) {
                sidebarToggle.removeEventListener('click', toggleSidebar);
                sidebarToggle.addEventListener('click', toggleSidebar);
                sidebarToggle.style.display = 'block'; // Always visible
            }

            // Handle close sidebar button
            if (closeSidebar) {
                closeSidebar.addEventListener('click', function () {
                    closeSidebarMenu();
                });
            }

            function closeSidebarMenu() {
                if (!sidebar || !content) return;

                sidebar.classList.remove('active');
                content.classList.remove('shifted');
                document.body.classList.remove('sidebar-active');
            }

            // === NAVIGATION STYLE FUNCTIONS ===
            function setNavStyle(style, forceStyle = false) {
                // Store the selected style
                localStorage.setItem(NAV_STYLE_KEY, style);

                // Auto-switch to sidebar on mobile unless forced
                if (!forceStyle && window.innerWidth < 992 && style === 'header') {
                    style = 'sidebar';
                }

                // Update body classes
                document.body.classList.remove('nav-sidebar', 'nav-header');
                document.body.classList.add(`nav-${style}`);

                // Update toggle button state
                updateActiveToggleItem(style);

                // Handle navbar collapse
                const navbarCollapse = document.getElementById('navbarContent');
                if (navbarCollapse) {
                    if (style === 'header') {
                        navbarCollapse.classList.add('show');
                        navbarCollapse.style.display = 'block';
                        closeSidebarMenu();
                    } else {
                        navbarCollapse.classList.remove('show');
                    }
                }

                // Handle sidebar visibility
                if (sidebar && content) {
                    if (style === 'sidebar') {
                        sidebar.classList.add('active');
                        content.classList.add('shifted');
                        addSidebarUserInfo();
                    } else {
                        sidebar.classList.remove('active');
                        content.classList.remove('shifted');
                    }
                }

                // Always ensure toggle visibility
                if (sidebarToggle) {
                    sidebarToggle.style.display = 'block';
                }
            }

            // Update active toggle indicators
            function updateActiveToggleItem(style) {
                // Update messenger toggle menu items
                const sidebarItem = document.getElementById('sidebarNavToggle');
                const headerItem = document.getElementById('headerNavToggle');
                if (sidebarItem && headerItem) {
                    sidebarItem.classList.toggle('active', style === 'sidebar');
                    headerItem.classList.toggle('active', style === 'header');
                }

                // Update sidebar user info options
                const sidebarUserInfo = document.querySelector('.sidebar-user-info');
                if (sidebarUserInfo) {
                    const navOptions = sidebarUserInfo.querySelectorAll('.nav-option');
                    if (navOptions && navOptions.length >= 2) {
                        const headerNavOption = navOptions[0];
                        const sidebarNavOption = navOptions[1];

                        // Update colors based on active state
                        headerNavOption.style.color = style === 'header' ? '#4e73df' : 'rgba(255,255,255,0.8)';
                        headerNavOption.style.backgroundColor = style === 'header' ? 'rgba(78,115,223,0.1)' : 'transparent';
                        sidebarNavOption.style.color = style === 'sidebar' ? '#4e73df' : 'rgba(255,255,255,0.8)';
                        sidebarNavOption.style.backgroundColor = style === 'sidebar' ? 'rgba(78,115,223,0.1)' : 'transparent';
                    }
                }
            }

            // === TOGGLE VISIBILITY FUNCTIONS ===
            function hideNavToggle() {
                const toggle = document.getElementById('navStyleToggle');
                if (toggle) {
                    toggle.style.display = 'none';
                    localStorage.setItem(TOGGLE_VISIBLE_KEY, 'false');
                }
            }

            function showNavToggle() {
                const toggle = document.getElementById('navStyleToggle');
                if (toggle) {
                    toggle.style.display = 'flex';
                    localStorage.setItem(TOGGLE_VISIBLE_KEY, 'true');
                }
            }

            // === MESSENGER TOGGLE CREATION ===
            function createEnhancedMessengerToggle() {
                // Remove old toggle if it exists
                const oldToggle = document.querySelector('.nav-style-toggle');
                if (oldToggle) {
                    oldToggle.remove();
                }

                // Get stored position or use default
                const storedPosition = JSON.parse(localStorage.getItem(TOGGLE_POSITION_KEY)) || { bottom: '20px', right: '20px' };
                const isVisible = localStorage.getItem(TOGGLE_VISIBLE_KEY) !== 'false'; // Default to visible

                // Create toggle container
                const toggleContainer = document.createElement('div');
                toggleContainer.className = 'nav-style-toggle';
                toggleContainer.id = 'navStyleToggle';
                toggleContainer.style.bottom = storedPosition.bottom;
                toggleContainer.style.right = storedPosition.right;

                // Set initial visibility
                if (!isVisible) {
                    toggleContainer.style.display = 'none';
                }

                // Create toggle menu
                const toggleMenu = document.createElement('div');
                toggleMenu.className = 'nav-toggle-menu';
                toggleMenu.id = 'navToggleMenu';

                // Create toggle button
                const toggleButton = document.createElement('button');
                toggleButton.className = 'nav-toggle-button';
                toggleButton.innerHTML = '<i class="fas fa-bars"></i>';

                // Add menu items for navigation styles
                const menuItems = [
                    { id: 'sidebarNavToggle', icon: 'fa-bars', text: 'Sidebar Navigation', action: 'sidebar' },
                    { id: 'headerNavToggle', icon: 'fa-grip-lines', text: 'Header Navigation', action: 'header' },
                    { separator: true },
                    { id: 'hideToggleBtn', icon: 'fa-eye-slash', text: 'Hide Button', action: 'hide' }
                ];

                menuItems.forEach(item => {
                    if (item.separator) {
                        toggleMenu.appendChild(document.createElement('hr'));
                        return;
                    }

                    const menuItem = document.createElement('a');
                    menuItem.className = 'nav-toggle-menu-item';
                    menuItem.id = item.id;
                    menuItem.innerHTML = `<i class="fas ${item.icon} mr-2"></i> ${item.text}`;

                    menuItem.addEventListener('click', function () {
                        if (item.action === 'hide') {
                            hideNavToggle();
                        } else {
                            setNavStyle(item.action);
                        }
                        toggleMenu.classList.remove('show');
                        toggleButton.classList.remove('active');
                    });

                    toggleMenu.appendChild(menuItem);
                });

                // Toggle menu on button click
                toggleButton.addEventListener('click', function (e) {
                    if (!toggleContainer.classList.contains('dragging')) {
                        toggleButton.classList.toggle('active');
                        toggleMenu.classList.toggle('show');
                    }
                    e.stopPropagation();
                });

                // Add components to container and attach to body
                toggleContainer.appendChild(toggleMenu);
                toggleContainer.appendChild(toggleButton);
                document.body.appendChild(toggleContainer);

                // Make draggable
                makeDraggable(toggleContainer, toggleButton);

                // Update active toggle
                const storedNavStyle = localStorage.getItem(NAV_STYLE_KEY) || 'header';
                updateActiveToggleItem(storedNavStyle);

                // Add option to show toggle in user dropdown
                addShowToggleOption();
            }

            // === MAKE ELEMENT DRAGGABLE ===
            function makeDraggable(element, handle) {
                let isDragging = false;
                let offsetX, offsetY;
                let longPressTimer;
                const longPressDuration = 300; // ms to start dragging

                function startDragging() {
                    // Add dragging class for visual feedback
                    element.classList.add('dragging');

                    // Add tooltip for user feedback
                    if (!element.querySelector('.drag-tooltip')) {
                        const tooltip = document.createElement('div');
                        tooltip.className = 'drag-tooltip';
                        tooltip.textContent = 'Release to place';
                        tooltip.style.position = 'absolute';
                        tooltip.style.top = '-40px';
                        tooltip.style.left = '0';
                        tooltip.style.backgroundColor = 'rgba(0,0,0,0.7)';
                        tooltip.style.color = 'white';
                        tooltip.style.padding = '5px 10px';
                        tooltip.style.borderRadius = '5px';
                        tooltip.style.zIndex = '2000';
                        tooltip.style.whiteSpace = 'nowrap';
                        element.appendChild(tooltip);
                    }

                    isDragging = true;
                    handle.style.cursor = 'grabbing';

                    // Close menu if open
                    const toggleMenu = document.getElementById('navToggleMenu');
                    if (toggleMenu && toggleMenu.classList.contains('show')) {
                        toggleMenu.classList.remove('show');
                        handle.classList.remove('active');
                    }
                }

                // Mouse events for dragging
                function handleMouseDown(e) {
                    offsetX = e.clientX - element.getBoundingClientRect().left;
                    offsetY = e.clientY - element.getBoundingClientRect().top;
                    longPressTimer = setTimeout(() => startDragging(), longPressDuration);
                    e.preventDefault();
                }

                function handleMouseMove(e) {
                    if (!isDragging) {
                        // Cancel timer if moved without being in drag mode
                        if (longPressTimer && Math.abs(e.clientX - (element.getBoundingClientRect().left + offsetX)) > 5) {
                            clearTimeout(longPressTimer);
                        }
                        return;
                    }

                    const x = e.clientX - offsetX;
                    const y = e.clientY - offsetY;

                    // Calculate position relative to viewport
                    const viewportWidth = window.innerWidth;
                    const viewportHeight = window.innerHeight;

                    // Set position using right and bottom (fixed positioning)
                    element.style.right = `${viewportWidth - (x + element.offsetWidth)}px`;
                    element.style.bottom = `${viewportHeight - (y + element.offsetHeight)}px`;

                    e.preventDefault();
                }

                function handleMouseUp() {
                    // Clear the long press timer
                    if (longPressTimer) {
                        clearTimeout(longPressTimer);
                        longPressTimer = null;
                    }

                    if (isDragging) {
                        isDragging = false;
                        handle.style.cursor = '';

                        // Save position to localStorage
                        const position = {
                            right: element.style.right,
                            bottom: element.style.bottom
                        };
                        localStorage.setItem(TOGGLE_POSITION_KEY, JSON.stringify(position));

                        // Clean up
                        element.classList.remove('dragging');
                        const tooltip = element.querySelector('.drag-tooltip');
                        if (tooltip) {
                            tooltip.remove();
                        }
                    }
                }

                // Set up mouse event listeners
                handle.addEventListener('mousedown', handleMouseDown);
                document.addEventListener('mousemove', handleMouseMove);
                document.addEventListener('mouseup', handleMouseUp);

                // Set up touch event listeners for mobile
                handle.addEventListener('touchstart', function (e) {
                    const touch = e.touches[0];
                    offsetX = touch.clientX - element.getBoundingClientRect().left;
                    offsetY = touch.clientY - element.getBoundingClientRect().top;
                    longPressTimer = setTimeout(() => startDragging(), longPressDuration);
                    e.preventDefault();
                }, { passive: false });

                document.addEventListener('touchmove', function (e) {
                    if (!isDragging) {
                        if (longPressTimer && Math.abs(e.touches[0].clientX - (element.getBoundingClientRect().left + offsetX)) > 5) {
                            clearTimeout(longPressTimer);
                        }
                        return;
                    }

                    const touch = e.touches[0];
                    const x = touch.clientX - offsetX;
                    const y = touch.clientY - offsetY;

                    const viewportWidth = window.innerWidth;
                    const viewportHeight = window.innerHeight;

                    element.style.right = `${viewportWidth - (x + element.offsetWidth)}px`;
                    element.style.bottom = `${viewportHeight - (y + element.offsetHeight)}px`;

                    e.preventDefault();
                }, { passive: false });

                document.addEventListener('touchend', handleMouseUp);
            }

            // === USER INFO IN SIDEBAR ===
            function addSidebarUserInfo() {
                if (!sidebar) return;

                // Don't duplicate if it already exists
                if (document.querySelector('.sidebar-user-info')) {
                    return;
                }

                // Get username from the header dropdown if available
                let username = "User";
                const userDropdownToggle = document.getElementById('userDropdown');
                if (userDropdownToggle) {
                    const usernameText = userDropdownToggle.textContent.trim();
                    if (usernameText) {
                        username = usernameText;
                    }
                }

                // Create the user info container
                const userInfoContainer = document.createElement('div');
                userInfoContainer.className = 'sidebar-user-info';
                userInfoContainer.style.padding = '15px';
                userInfoContainer.style.borderTop = '1px solid rgba(255,255,255,0.1)';
                userInfoContainer.style.marginTop = 'auto'; // Push to bottom

                // Create user display with avatar
                const userDisplay = createUserDisplay(username);

                // Get current navigation style
                const currentNavStyle = localStorage.getItem(NAV_STYLE_KEY) || 'header';

                // Create navigation options container
                const navOptionsContainer = document.createElement('div');
                navOptionsContainer.className = 'nav-options-container mt-3';
                navOptionsContainer.style.display = 'none'; // Initially hidden
                navOptionsContainer.style.flexDirection = 'column';
                navOptionsContainer.style.gap = '8px';
                navOptionsContainer.style.padding = '10px';
                navOptionsContainer.style.backgroundColor = 'rgba(0,0,0,0.1)';
                navOptionsContainer.style.borderRadius = '5px';
                navOptionsContainer.style.marginTop = '10px';

                // Create options
                const headerNavOption = createNavOption('header', currentNavStyle);
                const sidebarNavOption = createNavOption('sidebar', currentNavStyle);
                const showToggleOption = createActionOption('Show Nav Toggle', 'fa-sliders-h', showNavToggle);
                const logoutOption = createLogoutOption();

                // Add all options to container
                navOptionsContainer.appendChild(headerNavOption);
                navOptionsContainer.appendChild(sidebarNavOption);
                navOptionsContainer.appendChild(showToggleOption);
                navOptionsContainer.appendChild(logoutOption);

                // Make user display toggle the options panel
                userDisplay.addEventListener('click', function () {
                    const isVisible = navOptionsContainer.style.display === 'flex';
                    navOptionsContainer.style.display = isVisible ? 'none' : 'flex';
                    userDisplay.querySelector('.dropdown-indicator').style.transform =
                        isVisible ? 'rotate(0deg)' : 'rotate(180deg)';
                });

                // Add elements to container
                userInfoContainer.appendChild(userDisplay);
                userInfoContainer.appendChild(navOptionsContainer);

                // Add to sidebar
                sidebar.appendChild(userInfoContainer);

                // Handle outside clicks
                document.addEventListener('click', function (e) {
                    if (navOptionsContainer.style.display === 'flex' &&
                        !navOptionsContainer.contains(e.target) &&
                        !userDisplay.contains(e.target)) {
                        navOptionsContainer.style.display = 'none';
                        userDisplay.querySelector('.dropdown-indicator').style.transform = 'rotate(0deg)';
                    }
                });
            }

            // Helper function to create user display with avatar
            function createUserDisplay(username) {
                const userDisplay = document.createElement('div');
                userDisplay.className = 'd-flex align-items-center';
                userDisplay.style.cursor = 'pointer';

                // Create avatar
                const userAvatar = document.createElement('div');
                userAvatar.className = 'user-avatar me-3';
                userAvatar.style.width = '40px';
                userAvatar.style.height = '40px';
                userAvatar.style.borderRadius = '50%';
                userAvatar.style.backgroundColor = '#4e73df';
                userAvatar.style.color = 'white';
                userAvatar.style.display = 'flex';
                userAvatar.style.alignItems = 'center';
                userAvatar.style.justifyContent = 'center';
                userAvatar.style.fontSize = '16px';
                userAvatar.style.fontWeight = 'bold';
                userAvatar.textContent = username.charAt(0).toUpperCase();

                // Create username display
                const usernameDisplay = document.createElement('div');
                usernameDisplay.className = 'user-name';
                usernameDisplay.style.color = 'white';
                usernameDisplay.style.fontWeight = 'bold';
                usernameDisplay.textContent = username;

                // Create dropdown indicator
                const dropdownIndicator = document.createElement('i');
                dropdownIndicator.className = 'fas fa-chevron-down ms-2 dropdown-indicator';
                dropdownIndicator.style.color = 'rgba(255,255,255,0.5)';
                dropdownIndicator.style.fontSize = '12px';
                dropdownIndicator.style.transition = 'transform 0.2s ease';

                // Assemble the user display
                userDisplay.appendChild(userAvatar);
                userDisplay.appendChild(usernameDisplay);
                userDisplay.appendChild(dropdownIndicator);

                return userDisplay;
            }

            // Helper function to create navigation option
            function createNavOption(type, currentStyle) {
                const isActive = currentStyle === type;
                const option = document.createElement('a');
                option.href = '#';
                option.className = 'nav-option d-flex align-items-center';
                option.style.color = isActive ? '#4e73df' : 'rgba(255,255,255,0.8)';
                option.style.textDecoration = 'none';
                option.style.padding = '8px 10px';
                option.style.borderRadius = '5px';
                option.style.backgroundColor = isActive ? 'rgba(78,115,223,0.1)' : 'transparent';

                // Set icon and text based on type
                const icon = type === 'header' ? 'fa-grip-lines' : 'fa-bars';
                const text = type === 'header' ? 'Header Navigation' : 'Sidebar Navigation';
                option.innerHTML = `<i class="fas ${icon} me-2"></i> ${text}`;

                // Add click handler
                option.addEventListener('click', function (e) {
                    e.preventDefault();
                    setNavStyle(type);
                    updateNavOptionStyles(type);
                });

                return option;
            }

            // Helper function to create action option
            function createActionOption(text, icon, actionFn) {
                const option = document.createElement('a');
                option.href = '#';
                option.className = 'd-flex align-items-center mt-2';
                option.style.color = 'rgba(255,255,255,0.8)';
                option.style.textDecoration = 'none';
                option.style.padding = '8px 10px';
                option.style.borderRadius = '5px';
                option.style.backgroundColor = 'transparent';
                option.innerHTML = `<i class="fas ${icon} me-2"></i> ${text}`;

                option.addEventListener('click', function (e) {
                    e.preventDefault();
                    actionFn();

                    // Hide options after selection
                    const container = option.closest('.nav-options-container');
                    if (container) {
                        container.style.display = 'none';
                        const indicator = document.querySelector('.dropdown-indicator');
                        if (indicator) {
                            indicator.style.transform = 'rotate(0deg)';
                        }
                    }
                });

                return option;
            }

            // Function to update nav option styles after selection
            function updateNavOptionStyles(style) {
                const navOptionsContainer = document.querySelector('.nav-options-container');
                if (!navOptionsContainer) return;

                const navOptions = navOptionsContainer.querySelectorAll('.nav-option');
                if (navOptions.length >= 2) {
                    const headerNavOption = navOptions[0];
                    const sidebarNavOption = navOptions[1];

                    headerNavOption.style.color = style === 'header' ? '#4e73df' : 'rgba(255,255,255,0.8)';
                    headerNavOption.style.backgroundColor = style === 'header' ? 'rgba(78,115,223,0.1)' : 'transparent';
                    sidebarNavOption.style.color = style === 'sidebar' ? '#4e73df' : 'rgba(255,255,255,0.8)';
                    sidebarNavOption.style.backgroundColor = style === 'sidebar' ? 'rgba(78,115,223,0.1)' : 'transparent';
                }

                // Hide options after selection
                navOptionsContainer.style.display = 'none';
                const indicator = document.querySelector('.dropdown-indicator');
                if (indicator) {
                    indicator.style.transform = 'rotate(0deg)';
                }
            }

            // Create a logout option
            function createLogoutOption() {
                const logoutOption = document.createElement('a');
                logoutOption.className = 'logout-option d-flex align-items-center mt-2';
                logoutOption.style.color = 'rgba(255,255,255,0.8)';
                logoutOption.style.textDecoration = 'none';
                logoutOption.style.padding = '8px 10px';
                logoutOption.style.borderRadius = '5px';
                logoutOption.style.backgroundColor = 'transparent';
                logoutOption.style.borderTop = '1px solid rgba(255,255,255,0.1)';
                logoutOption.style.paddingTop = '12px';
                logoutOption.style.marginTop = '8px';
                logoutOption.innerHTML = '<i class="fas fa-sign-out-alt me-2"></i> Logout';

                // Find the logout link in the header dropdown
                let logoutUrl = '/logout'; // Default fallback

                // Look for logout form first
                const headerLogoutForm = document.querySelector('form[action*="logout"]');
                if (headerLogoutForm) {
                    const logoutForm = headerLogoutForm.cloneNode(true);
                    logoutForm.style.display = 'none';
                    document.body.appendChild(logoutForm);

                    logoutOption.addEventListener('click', function (e) {
                        e.preventDefault();
                        logoutForm.submit();
                    });
                } else {
                    // Try to find a logout link
                    const headerLogoutLink = document.querySelector('a[href*="logout"], a.dropdown-item:contains("Logout"), a.dropdown-item:contains("Sign out")');
                    if (headerLogoutLink) {
                        logoutUrl = headerLogoutLink.getAttribute('href');

                        // Check if link has a click handler
                        const hasClickHandler = headerLogoutLink.getAttribute('onclick') ||
                            headerLogoutLink.getAttribute('data-toggle') === 'modal';

                        if (hasClickHandler) {
                            logoutOption.addEventListener('click', function (e) {
                                e.preventDefault();
                                headerLogoutLink.click();
                            });
                        } else {
                            logoutOption.href = logoutUrl;
                        }
                    } else {
                        // Last resort: set default logout URL
                        logoutOption.href = logoutUrl;
                    }
                }

                return logoutOption;
            }

            // Add show toggle option to user dropdown
            function addShowToggleOption() {
                const userDropdown = document.querySelector('[aria-labelledby="userDropdown"]');
                if (!userDropdown) return;

                // Create show toggle option
                const showToggleItem = document.createElement('li');
                const showToggleLink = document.createElement('a');
                showToggleLink.className = 'dropdown-item';
                showToggleLink.innerHTML = '<i class="fas fa-sliders-h me-2"></i>Show Nav Toggle';
                showToggleLink.href = '#';

                showToggleLink.addEventListener('click', function (e) {
                    e.preventDefault();
                    showNavToggle();
                });

                showToggleItem.appendChild(showToggleLink);

                // Add before logout option (usually the last item)
                userDropdown.insertBefore(showToggleItem, userDropdown.lastElementChild);
            }

            // === DOCUMENT LEVEL EVENT LISTENERS ===
            // Close sidebar when clicking outside on mobile
            document.addEventListener('click', function (e) {
                // Handle sidebar close on outside click
                if (window.innerWidth < 992 &&
                    document.body.classList.contains('sidebar-active') &&
                    sidebar && !sidebar.contains(e.target) &&
                    sidebarToggle && !sidebarToggle.contains(e.target)) {
                    closeSidebarMenu();
                }

                // Handle navigation toggle menu close
                const navToggleMenu = document.getElementById('navToggleMenu');
                const navToggleButton = document.querySelector('.nav-toggle-button');
                if (navToggleMenu &&
                    navToggleMenu.classList.contains('show') &&
                    !navToggleMenu.contains(e.target) &&
                    navToggleButton && !navToggleButton.contains(e.target)) {
                    navToggleMenu.classList.remove('show');
                    navToggleButton.classList.remove('active');
                }
            });

            // Handle window resize
            window.addEventListener('resize', function () {
                const currentStyle = localStorage.getItem(NAV_STYLE_KEY) || 'header';

                // Close sidebar on mobile
                if (window.innerWidth < 992 && sidebar && content) {
                    closeSidebarMenu();
                } else if (currentStyle === 'sidebar' && sidebar && content) {
                    // Keep sidebar open in desktop when in sidebar mode
                    sidebar.classList.add('active');
                    content.classList.add('shifted');
                    addSidebarUserInfo();
                }

                // Always ensure sidebar toggle is visible
                if (sidebarToggle) {
                    sidebarToggle.style.display = 'block';
                }
            });

            // === INITIALIZATION ===
            // Create toggle elements
            createEnhancedMessengerToggle();

            // Apply stored navigation style
            const storedNavStyle = localStorage.getItem(NAV_STYLE_KEY) || 'header';
            setNavStyle(storedNavStyle, true);

            // Initialize Bootstrap components if available
            if (typeof bootstrap !== 'undefined') {
                const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
                tooltipTriggerList.forEach(function (tooltipTriggerEl) {
                    return new bootstrap.Tooltip(tooltipTriggerEl);
                });

                const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
                popoverTriggerList.forEach(function (popoverTriggerEl) {
                    return new bootstrap.Popover(popoverTriggerEl);
                });
            }
        });




    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>

