/* <PERSON> and <PERSON><PERSON>yling */
.brand-link {
    padding: 1rem;
    text-align: center;
}

    .brand-link img {
        max-height: 40px;
    }

/* Navbar Customization */
.navbar-nav .nav-item {
    margin: 0 5px;
}

.navbar-nav .nav-link {
    padding: 0.5rem 1rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

    .navbar-nav .nav-link:hover {
        background: rgba(255, 255, 255, 0.1);
    }

/* Sidebar Styling */
.sidebar-dark-primary {
    background: linear-gradient(180deg, #2c3e50 0%, #3498db 100%);
}

.nav-sidebar .nav-item .nav-link {
    padding: 0.8rem 1rem;
    margin: 2px 0;
    border-radius: 4px;
}

    .nav-sidebar .nav-item .nav-link:hover {
        background: rgba(255, 255, 255, 0.1);
    }

/* Card Styling */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.card-header {
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
    background-color: rgba(0, 0, 0, 0.03);
}

/* Table Styling */
.table thead th {
    border-top: none;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Button Styling */
.btn {
    border-radius: 4px;
    padding: 0.375rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

/* Form Styling */
.form-control {
    border-radius: 4px;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
}

    .form-control:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

/* Footer Styling */
.main-footer {
    background: #fff;
    border-top: 1px solid #dee2e6;
    padding: 1rem;
    color: #6c757d;
    text-align: center;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

    ::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
