from django import template
from datetime import <PERSON>elta

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """Get an item from a dictionary using a variable key."""
    if isinstance(dictionary, dict):
        return dictionary.get(key)
    return None

@register.filter
def divide(value, arg):
    """Divides the value by the argument."""
    try:
        return float(value) / float(arg)
    except (ValueError, ZeroDivisionError, TypeError):
        return 0

@register.filter
def multiply(value, arg):
    """Multiplies the value by the argument."""
    try:
        return value * arg
    except (ValueError, TypeError):
        return None

@register.filter
def subtract(value, arg):
    """Subtracts the arg from the value."""
    try:
        return float(value) - float(arg)
    except (ValueError, TypeError):
        try:
            return int(value) - int(arg)
        except (ValueError, TypeError):
            return 0

@register.filter
def sub(value, arg):
    """Alias for subtract filter."""
    return subtract(value, arg)

@register.filter
def percentage(value, total):
    """Calculates percentage of value against total."""
    if total == 0:
        return 0
    return (value / total) * 100

@register.filter
def percentage_of(value, total):
    """Calculates percentage with error handling."""
    try:
        return (float(value) / float(total)) * 100
    except (ValueError, ZeroDivisionError, TypeError):
        return 0

@register.filter
def abs_value(value):
    """Returns the absolute value."""
    try:
        return abs(float(value))
    except (ValueError, TypeError):
        return value

@register.filter
def floatformat_with_commas(value, arg=2):
    """Formats a float with commas as thousand separators and specified decimal places."""
    try:
        value = float(value)
        arg = int(arg)
        formatted = f"{value:,.{arg}f}"
        return formatted
    except (ValueError, TypeError):
        return value

@register.filter
def add_days(value, days):
    """Adds specified number of days to a date."""
    try:
        return value + timedelta(days=int(days))
    except (ValueError, TypeError):
        return value

@register.filter(name='addclass')
def addclass(field, css):
    """
    Add CSS classes to form fields
    Usage: {{ field|addclass:"form-control" }}
    """
    return field.as_widget(attrs={"class": css})

@register.filter(name='field_type')
def field_type(field):
    """
    Return the field type
    Usage: {{ field|field_type }}
    """
    return field.field.widget.__class__.__name__

@register.filter(name='is_checkbox')
def is_checkbox(field):
    """
    Check if field is a checkbox
    Usage: {% if field|is_checkbox %}
    """
    return field.field.widget.__class__.__name__ == 'CheckboxInput'

@register.filter(name='is_password')
def is_password(field):
    """
    Check if field is a password input
    Usage: {% if field|is_password %}
    """
    return field.field.widget.__class__.__name__ == 'PasswordInput'
