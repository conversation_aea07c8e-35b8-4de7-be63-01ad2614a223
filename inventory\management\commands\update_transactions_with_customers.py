import random
from django.core.management.base import BaseCommand
from django.db import transaction
from django.utils import timezone
from inventory.models import Transaction
from user_management.models import Customer


class Command(BaseCommand):
    help = 'Update existing transactions with real customer data from the Customer model'

    def add_arguments(self, parser):
        parser.add_argument('--batch-size', type=int, default=1000,
                            help='Number of transactions to update in each batch')
        parser.add_argument('--limit', type=int, default=None,
                            help='Limit the number of transactions to update (default: all)')

    def handle(self, *args, **options):
        batch_size = options['batch_size']
        limit = options['limit']
        
        # Get all customers
        customers = list(Customer.objects.all())
        
        if not customers:
            self.stdout.write(self.style.ERROR('No customers found in the database. Cannot update transactions.'))
            return
        
        self.stdout.write(self.style.SUCCESS(f'Found {len(customers)} customers to use for updating transactions'))
        
        # Get all sale transactions
        sale_transactions = Transaction.objects.filter(transaction_type='sale')
        
        if limit:
            sale_transactions = sale_transactions[:limit]
        
        total_transactions = sale_transactions.count()
        self.stdout.write(self.style.SUCCESS(f'Found {total_transactions} sale transactions to update'))
        
        if total_transactions == 0:
            self.stdout.write(self.style.WARNING('No sale transactions found to update'))
            return
        
        # Update transactions in batches
        updated_count = 0
        
        for i in range(0, total_transactions, batch_size):
            batch = sale_transactions[i:i+batch_size]
            
            with transaction.atomic():
                for tx in batch:
                    # Select a random customer
                    customer = random.choice(customers)
                    
                    # Update transaction with customer data
                    tx.customer_name = customer.customer_name
                    tx.patient_number = customer.patient_number
                    tx.customer_type = customer.customer_type
                    tx.save(update_fields=['customer_name', 'patient_number', 'customer_type'])
                    
                    # Update customer's last transaction date if needed
                    if not customer.last_transaction_date or customer.last_transaction_date < tx.transaction_date:
                        customer.last_transaction_date = tx.transaction_date
                        customer.save(update_fields=['last_transaction_date'])
                    
                    updated_count += 1
            
            self.stdout.write(f'Updated {updated_count} of {total_transactions} transactions')
        
        self.stdout.write(self.style.SUCCESS(f'Successfully updated {updated_count} transactions with real customer data'))
