{% load base_filters %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - BMC MedForecast: Inventory Optimization and Monitoring Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #0143a3;
            --primary-light: #0077d4;
            --primary-dark: #013380;
            --secondary-color: #f8fafc;
            --accent-color: #10b981;
            --text-color: #1e293b;
            --text-light: #64748b;
            --border-radius: 12px;
            --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }

        /* Professional Medical Background Styles with Dual Images */
        body {
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
            background: none; /* Remove default background */
            color: var(--text-color);
        }

        /* First background (left side) */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            height: 100%;
            width: 55%; /* Slightly wider to create overlap */
            z-index: -2;
            background-image: url('https://img.freepik.com/free-photo/medicine-capsules-global-health-with-geometric-pattern-digital-remix_53876-126742.jpg');
            background-size: cover;
            background-position: center right;
            mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 80%, rgba(0, 0, 0, 0) 100%);
            -webkit-mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 80%, rgba(0, 0, 0, 0) 100%);
        }

        /* Second background (right side) */
        body::after {
            content: '';
            position: fixed;
            top: 0;
            right: 0;
            height: 100%;
            width: 55%; /* Slightly wider to create overlap */
            z-index: -2;
            background-image: url('https://img.freepik.com/free-photo/medical-banner-with-stethoscope_23-**********.jpg');
            background-size: cover;
            background-position: center left;
            mask-image: linear-gradient(to left, rgba(0, 0, 0, 1) 80%, rgba(0, 0, 0, 0) 100%);
            -webkit-mask-image: linear-gradient(to left, rgba(0, 0, 0, 1) 80%, rgba(0, 0, 0, 0) 100%);
        }

        /* Additional overlay for center blending */
        .background-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            z-index: -1;
        }

        /* Mobile optimization for backgrounds */
        @media (max-width: 768px) {
            body::before, body::after {
                opacity: 0.3; /* Reduce background image opacity on mobile */
            }
            .background-overlay {
                background-color: rgba(255, 255, 255, 0.85); /* Increase overlay opacity */
            }
            .medical-bg .medical-element {
                opacity: 0.4; /* Reduce opacity of floating medical elements */
            }
        }

        /* Enhanced Medical Background Elements */
        .medical-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
            pointer-events: none;
        }

        .medical-element {
            position: absolute;
            opacity: 0.15;
            filter: blur(1px);
            animation: float 15s infinite ease-in-out;
        }

        .stethoscope {
            top: 15%;
            left: 10%;
            width: 80px;
            height: 80px;
            background: url('https://cdn-icons-png.flaticon.com/512/809/809957.png') no-repeat center/contain;
            animation-delay: 0s;
        }

        .pill-bottle {
            top: 25%;
            right: 15%;
            width: 60px;
            height: 60px;
            background: url('https://cdn-icons-png.flaticon.com/512/3004/3004458.png') no-repeat center/contain;
            animation-delay: 1s;
        }

        .medical-cross {
            bottom: 20%;
            left: 20%;
            width: 70px;
            height: 70px;
            background: url('https://cdn-icons-png.flaticon.com/512/2966/2966327.png') no-repeat center/contain;
            animation-delay: 2s;
        }

        .dna {
            top: 40%;
            left: 30%;
            width: 90px;
            height: 90px;
            background: url('https://cdn-icons-png.flaticon.com/512/1021/1021606.png') no-repeat center/contain;
            animation-delay: 3s;
        }

        .prescription {
            bottom: 30%;
            right: 25%;
            width: 65px;
            height: 65px;
            background: url('https://cdn-icons-png.flaticon.com/512/1560/1560727.png') no-repeat center/contain;
            animation-delay: 4s;
        }

        .microscope {
            top: 60%;
            right: 10%;
            width: 85px;
            height: 85px;
            background: url('https://cdn-icons-png.flaticon.com/512/1625/1625728.png') no-repeat center/contain;
            animation-delay: 5s;
        }

        .heart-rate {
            top: 75%;
            left: 15%;
            width: 100px;
            height: 50px;
            background: url('https://cdn-icons-png.flaticon.com/512/2785/2785544.png') no-repeat center/contain;
            animation-delay: 6s;
        }

        .medical-vial {
            top: 10%;
            right: 30%;
            width: 55px;
            height: 55px;
            background: url('https://cdn-icons-png.flaticon.com/512/3004/3004466.png') no-repeat center/contain;
            animation-delay: 7s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0) rotate(0deg);
            }
            25% {
                transform: translateY(-10px) rotate(2deg);
            }
            50% {
                transform: translateY(0) rotate(0deg);
            }
            75% {
                transform: translateY(10px) rotate(-2deg);
            }
        }

        /* Brand Elements */
        .brand-container {
            margin-bottom: 1.5rem;
            animation: fadeIn 0.8s ease-in-out;
        }

        .system-name {
            color: #1a3c6e;
            font-weight: 700;
            font-size: 2.2rem;
            margin-bottom: 1rem;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.5px;
        }

        @media (min-width: 768px) {
            .desktop-layout .system-name {
                font-size: 1.8rem;
                margin-bottom: 0.5rem;
            }
        }

        /* Card Styling */
        .main-card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            background: rgba(255, 255, 255, 0.9);
            overflow: hidden;
            animation: slideUp 0.6s ease-out;
            backdrop-filter: blur(10px);
            max-height: 85vh; /* Limit height to avoid scrolling */
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        /* Desktop landscape layout */
        @media (min-width: 768px) {
            .desktop-layout .main-card {
                display: flex;
                flex-direction: row;
                max-width: 1300px;
                margin: 0 auto;
                max-height: none; /* Override max-height for desktop */
                height: auto;
            }

            .desktop-layout .card-left {
                flex: 0 0 25%;
                background: linear-gradient(135deg, #0143a3 0%, #0077d4 100%);
                color: white;
                padding: 1rem;
                display: flex;
                flex-direction: column;
                justify-content: center;
                position: relative;
                overflow: hidden;
            }

            .desktop-layout .card-left::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-image: url('https://img.freepik.com/free-photo/medicine-capsules-global-health-with-geometric-pattern-digital-remix_53876-126742.jpg');
                background-size: cover;
                background-position: center;
                opacity: 0.1;
                z-index: 0;
            }

            .desktop-layout .card-left-content {
                position: relative;
                z-index: 1;
            }

            .desktop-layout .card-right {
                flex: 0 0 75%;
                padding: 0;
                overflow-y: auto;
                max-height: 80vh;
            }

            .desktop-layout .card-header {
                border-top-left-radius: 0 !important;
                padding: 0.25rem;
                min-height: 10px;
                border-bottom: none;
            }

            .desktop-layout .card-body {
                padding: 0.5rem 1rem 0.5rem;
                max-height: none;
                padding-top: 0.5rem;
            }

            .desktop-layout .form-group {
                margin-bottom: 0.35rem;
            }

            .desktop-layout .form-label {
                margin-bottom: 0.2rem;
                font-size: 0.8rem;
            }

            .desktop-layout .form-control {
                padding: 0.35rem 0.6rem;
                font-size: 0.85rem;
                height: calc(1.5em + 0.7rem + 2px);
                line-height: 1.4;
            }

            .desktop-layout .icon-container {
                width: 24px;
                height: 24px;
                margin-right: 0.4rem;
            }

            .desktop-layout .icon-container i {
                font-size: 0.8rem;
            }

            .desktop-layout .form-text {
                font-size: 0.75rem;
                margin-top: 0.2rem;
            }

            .desktop-layout .card-footer {
                padding: 0.4rem 1rem;
                font-size: 0.85rem;
            }

            .desktop-layout .btn-primary {
                padding: 0.4rem 1rem;
                font-size: 0.9rem;
            }
        }

        .main-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 20px rgba(0, 0, 0, 0.06);
        }

        /* Touch-friendly active states for mobile */
        .main-card:active {
            transform: translateY(-3px); /* Smaller transform on touch */
            transition: transform 0.1s ease;
        }

        .card-header {
            background: linear-gradient(135deg, #0143a3 0%, #0077d4 100%);
            border-bottom: none;
            padding: 1.25rem 1.5rem;
            color: white;
            border-top-left-radius: 1.25rem !important;
            border-top-right-radius: 1.25rem !important;
        }

        .card-header h2 {
            font-size: 1.4rem;
            font-weight: 600;
            color: white;
            margin: 0;
        }

        .card-body {
            padding: 1.5rem;
            overflow-y: auto;
            max-height: calc(85vh - 130px); /* Adjust based on header and footer height */
        }

        .card-footer {
            background-color: #f8fafc;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            padding: 1rem 1.5rem;
        }

            .card-footer a {
                color: var(--primary-color);
                text-decoration: none;
                font-weight: 500;
                transition: all 0.2s;
            }

                .card-footer a:hover {
                    color: var(--primary-dark);
                    text-decoration: underline;
                }

        /* Form Elements */
        .form-group {
            margin-bottom: 1rem;
            position: relative;
        }

        .form-label {
            font-weight: 500;
            color: var(--text-color);
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            transition: all 0.2s;
            font-size: 0.9rem;
        }

        .form-control {
            padding: 0.7rem 0.9rem;
            border: 2px solid #e2e8f0;
            border-radius: var(--border-radius);
            font-size: 0.95rem;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
            transition: all 0.3s ease;
            background-color: #f8fafc;
        }

        .form-control:focus {
            border-color: var(--primary-light);
            background-color: white;
            box-shadow: 0 0 0 4px rgba(1, 67, 163, 0.15);
            transform: translateY(-2px);
        }

        .form-control.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        .form-control.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        .form-text {
            color: var(--text-light);
            font-size: 0.8rem;
            margin-top: 0.3rem;
            font-style: italic;
        }

        .text-danger {
            color: #e11d48 !important;
            font-size: 0.8rem;
            display: flex;
            align-items: center;
            margin-top: 0.3rem;
            animation: fadeIn 0.3s ease-in-out;
        }

        /* Alert Styles */
        .alert {
            border: none;
            border-radius: var(--border-radius);
            padding: 0.9rem 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            border-left: 4px solid;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        .alert-danger {
            background-color: rgba(220, 53, 69, 0.05);
            color: #b91c1c;
            border-left-color: #dc3545;
        }

        .alert-success {
            background-color: rgba(40, 167, 69, 0.05);
            color: #0f5132;
            border-left-color: #28a745;
        }

        .alert-warning {
            background-color: rgba(255, 193, 7, 0.05);
            color: #664d03;
            border-left-color: #ffc107;
        }

        .alert-info {
            background-color: rgba(23, 162, 184, 0.05);
            color: #055160;
            border-left-color: #17a2b8;
        }

        /* Button Styles */
        .btn-primary {
            background-color: var(--primary-color);
            border: none;
            padding: 0.8rem 1.5rem;
            font-weight: 600;
            font-size: 1rem;
            border-radius: var(--border-radius);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-primary:hover {
            background-color: var(--primary-dark);
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(1, 67, 163, 0.3);
        }

        .btn-primary:active {
            transform: translateY(-1px);
        }

        /* Enhanced loading animation */
        @keyframes buttonLoad {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        .btn-loading {
            background-size: 200% 200%;
            background-image: linear-gradient(45deg, #0143a3, #0077d4, #0143a3, #0077d4);
            animation: buttonLoad 2s ease infinite;
            color: white !important;
            position: relative;
            overflow: hidden;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }

        /* Icon Styling */
        .icon-container {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 28px;
            height: 28px;
            background: rgba(1, 67, 163, 0.1);
            border-radius: 8px;
            margin-right: 0.5rem;
            transition: all 0.3s ease;
        }

        .icon-container i {
            color: var(--primary-color);
            font-size: 0.9rem;
        }

        .form-label:hover .icon-container {
            background: rgba(1, 67, 163, 0.2);
            transform: scale(1.05);
        }

        /* Form Grid Layout */
        .form-row {
            display: flex;
            flex-wrap: wrap;
            margin-right: -0.5rem;
            margin-left: -0.5rem;
        }

        .form-col {
            flex: 0 0 100%;
            max-width: 100%;
            padding-right: 0.5rem;
            padding-left: 0.5rem;
        }

        /* More compact form for desktop */
        @media (min-width: 768px) {
            .desktop-layout .form-row {
                margin-bottom: 0.15rem;
            }

            .desktop-layout .form-col .form-group {
                margin-bottom: 0.25rem;
            }

            .desktop-layout .form-check {
                margin-bottom: 0.25rem;
                font-size: 0.8rem;
            }
        }

        @media (min-width: 576px) {
            .form-col-sm-6 {
                flex: 0 0 50%;
                max-width: 50%;
            }
        }

        /* Field validation feedback */
        .field-feedback {
            position: absolute;
            right: 0;
            top: 0;
            font-size: 0.8rem;
            color: #6c757d;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        @media (min-width: 768px) {
            .desktop-layout .field-feedback {
                font-size: 0.75rem;
            }

            .desktop-layout .text-danger {
                font-size: 0.75rem;
                margin-top: 0.2rem;
            }
        }

        .field-feedback.visible {
            opacity: 1;
        }

        .field-feedback.valid {
            color: #198754;
        }

        .field-feedback.invalid {
            color: #dc3545;
        }

        /* Password validation styling */
        .password-validation-container {
            background-color: rgba(248, 250, 252, 0.7);
            border-radius: 8px;
            padding: 10px;
            border-left: 3px solid #0143a3;
        }

        .password-requirement {
            font-size: 0.8rem;
            color: #64748b;
            transition: color 0.3s ease;
        }

        .password-requirement.valid {
            color: #198754;
        }

        .requirement-icon {
            color: #cbd5e1;
            transition: color 0.3s ease;
        }

        .password-requirement.valid .requirement-icon {
            color: #10b981;
        }

        /* Animations */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        @keyframes slideUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        @keyframes shake {
            0%, 100% {
                transform: translateX(0);
            }
            20%, 60% {
                transform: translateX(-5px);
            }
            40%, 80% {
                transform: translateX(5px);
            }
        }

        .shake {
            animation: shake 0.5s ease-in-out;
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(1, 67, 163, 0.4);
            }

            70% {
                box-shadow: 0 0 0 10px rgba(1, 67, 163, 0);
            }

            100% {
                box-shadow: 0 0 0 0 rgba(1, 67, 163, 0);
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .card-body, .card-footer {
                padding: 1rem;
            }

            .form-group {
                margin-bottom: 0.8rem;
            }

            .form-control {
                padding: 0.6rem 0.8rem;
                font-size: 0.9rem;
            }

            .btn-primary {
                padding: 0.7rem 1.2rem;
                font-size: 0.95rem;
            }

            .system-name {
                font-size: 1.8rem;
                margin-bottom: 0.8rem;
            }

            .card-header h2 {
                font-size: 1.2rem;
            }
        }

        /* Error message container */
        #register-error-container {
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="background-overlay"></div>
    <!-- Enhanced Medical Background Elements -->
    <div class="medical-bg">
        <div class="medical-element stethoscope"></div>
        <div class="medical-element pill-bottle"></div>
        <div class="medical-element medical-cross"></div>
        <div class="medical-element dna"></div>
        <div class="medical-element prescription"></div>
        <div class="medical-element microscope"></div>
        <div class="medical-element heart-rate"></div>
        <div class="medical-element medical-vial"></div>
    </div>

    <div class="container py-2">
        <div class="row justify-content-center">
            <div class="col-12 text-center mb-2">
                <h1 class="system-name">
                    <i class="fas fa-capsules me-2"></i>
                    BMC MedForecast
                </h1>
            </div>
            <!-- Desktop layout (landscape) -->
            <div class="col-md-10 col-lg-10 col-xl-10 d-none d-md-block desktop-layout">
                <!-- Main Card -->
                <div class="main-card">
                    <div class="card-left">
                        <div class="card-left-content">
                            <div class="text-center mb-1">
                                <i class="fas fa-heartbeat pulse" style="font-size: 2rem;"></i>
                            </div>
                            <h4 class="mb-1">Welcome to BMC MedForecast</h4>
                            <h5 class="mb-1 text-white-50">Create Your Account</h5>
                            <div class="mt-1">
                                <div class="d-flex align-items-center mb-1" style="font-size: 0.8rem;">
                                    <i class="fas fa-check-circle me-1"></i>
                                    <span>Real-time inventory tracking</span>
                                </div>
                                <div class="d-flex align-items-center mb-1" style="font-size: 0.8rem;">
                                    <i class="fas fa-check-circle me-1"></i>
                                    <span>Advanced forecasting algorithms</span>
                                </div>
                                <div class="d-flex align-items-center" style="font-size: 0.8rem;">
                                    <i class="fas fa-check-circle me-1"></i>
                                    <span>Comprehensive reporting tools</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="card-right">
                    <div class="card-header">
                        <!-- Empty header for desktop view -->
                    </div>

                    <div class="card-body">
                        <div id="register-error-container"></div>

                        <!-- Registration Form -->
                        <form method="post" novalidate class="needs-validation" id="registrationForm">
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle fa-lg me-3"></i>
                                <div>
                                    {% for error in form.non_field_errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            <!-- Username field - always full width -->
                            <div class="form-group">
                                <label for="{{ form.username.id_for_label }}" class="form-label">
                                    <div class="icon-container">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    {{ form.username.label }}
                                </label>
                                {{ form.username|addclass:"form-control" }}
                                <div class="field-feedback" id="username-feedback"></div>
                                {% if form.username.errors %}
                                <div class="text-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    {% for error in form.username.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                {% if form.username.help_text %}
                                <small class="form-text">{{ form.username.help_text }}</small>
                                {% endif %}
                            </div>

                            <!-- Name fields in a row -->
                            <div class="form-row">
                                <!-- First Name -->
                                <div class="form-col form-col-sm-6">
                                    <div class="form-group">
                                        <label for="{{ form.first_name.id_for_label }}" class="form-label">
                                            <div class="icon-container">
                                                <i class="fas fa-user-tag"></i>
                                            </div>
                                            {{ form.first_name.label }}
                                        </label>
                                        {{ form.first_name|addclass:"form-control" }}
                                        {% if form.first_name.errors %}
                                        <div class="text-danger">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            {% for error in form.first_name.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Last Name -->
                                <div class="form-col form-col-sm-6">
                                    <div class="form-group">
                                        <label for="{{ form.last_name.id_for_label }}" class="form-label">
                                            <div class="icon-container">
                                                <i class="fas fa-user-tag"></i>
                                            </div>
                                            {{ form.last_name.label }}
                                        </label>
                                        {{ form.last_name|addclass:"form-control" }}
                                        {% if form.last_name.errors %}
                                        <div class="text-danger">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            {% for error in form.last_name.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Middle name and Email in a row -->
                            <div class="form-row">
                                <!-- Middle name field -->
                                <div class="form-col form-col-sm-6">
                                    <div class="form-group">
                                        <label for="{{ form.middle_name.id_for_label }}" class="form-label">
                                            <div class="icon-container">
                                                <i class="fas fa-user-tag"></i>
                                            </div>
                                            {{ form.middle_name.label }}
                                        </label>
                                        {{ form.middle_name|addclass:"form-control" }}
                                        {% if form.middle_name.errors %}
                                        <div class="text-danger">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            {% for error in form.middle_name.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Email field -->
                                <div class="form-col form-col-sm-6">
                                    <div class="form-group">
                                        <label for="{{ form.email.id_for_label }}" class="form-label">
                                            <div class="icon-container">
                                                <i class="fas fa-envelope"></i>
                                            </div>
                                            {{ form.email.label }}
                                        </label>
                                        {{ form.email|addclass:"form-control" }}
                                        <div class="field-feedback" id="email-feedback"></div>
                                        {% if form.email.errors %}
                                        <div class="text-danger">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            {% for error in form.email.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        {% if form.email.help_text %}
                                        <small class="form-text">{{ form.email.help_text }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Birthday and Gender in a row -->
                            <div class="form-row">
                                <!-- Birthday -->
                                <div class="form-col form-col-sm-6">
                                    <div class="form-group">
                                        <label for="{{ form.birthday.id_for_label }}" class="form-label">
                                            <div class="icon-container">
                                                <i class="fas fa-birthday-cake"></i>
                                            </div>
                                            {{ form.birthday.label }}
                                        </label>
                                        {{ form.birthday|addclass:"form-control" }}
                                        <div class="field-feedback" id="birthday-feedback"></div>
                                        {% if form.birthday.errors %}
                                        <div class="text-danger">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            {% for error in form.birthday.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Gender -->
                                <div class="form-col form-col-sm-6">
                                    <div class="form-group">
                                        <label for="{{ form.sex.id_for_label }}" class="form-label">
                                            <div class="icon-container">
                                                <i class="fas fa-venus-mars"></i>
                                            </div>
                                            {{ form.sex.label }}
                                        </label>
                                        {{ form.sex|addclass:"form-control" }}
                                        {% if form.sex.errors %}
                                        <div class="text-danger">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            {% for error in form.sex.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Phone number -->
                            <div class="form-group">
                                <label for="{{ form.phone_number.id_for_label }}" class="form-label">
                                    <div class="icon-container">
                                        <i class="fas fa-phone"></i>
                                    </div>
                                    {{ form.phone_number.label }}
                                </label>
                                {{ form.phone_number|addclass:"form-control" }}
                                <div class="field-feedback" id="phone-feedback"></div>
                                {% if form.phone_number.errors %}
                                <div class="text-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    {% for error in form.phone_number.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                <small class="form-text">Enter a valid Philippine phone number (e.g., +639123456789)</small>
                            </div>

                            <!-- Password fields in a row -->
                            <div class="form-row">
                                <!-- Password -->
                                <div class="form-col form-col-sm-6">
                                    <div class="form-group">
                                        <label for="{{ form.password1.id_for_label }}" class="form-label">
                                            <div class="icon-container">
                                                <i class="fas fa-lock"></i>
                                            </div>
                                            {{ form.password1.label }}
                                        </label>
                                        {{ form.password1|addclass:"form-control" }}
                                        <div class="field-feedback" id="password-feedback"></div>
                                        {% if form.password1.errors %}
                                        <div class="text-danger">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            {% for error in form.password1.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <div class="password-validation-container mt-2" style="display: none;">
                                            <small class="d-block mb-1">Password requirements:</small>
                                            <div class="password-requirement d-flex align-items-center mb-1">
                                                <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                                <small>At least 8 characters</small>
                                            </div>
                                            <div class="password-requirement d-flex align-items-center mb-1">
                                                <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                                <small>Contains lowercase letters</small>
                                            </div>
                                            <div class="password-requirement d-flex align-items-center mb-1">
                                                <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                                <small>Contains uppercase letters</small>
                                            </div>
                                            <div class="password-requirement d-flex align-items-center mb-1">
                                                <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                                <small>Contains numbers</small>
                                            </div>
                                            <div class="password-requirement d-flex align-items-center mb-1">
                                                <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                                <small>Not entirely numeric</small>
                                            </div>
                                            <div class="password-requirement d-flex align-items-center">
                                                <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                                <small>Not similar to personal information</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Confirm Password -->
                                <div class="form-col form-col-sm-6">
                                    <div class="form-group">
                                        <label for="{{ form.password2.id_for_label }}" class="form-label">
                                            <div class="icon-container">
                                                <i class="fas fa-lock"></i>
                                            </div>
                                            {{ form.password2.label }}
                                        </label>
                                        {{ form.password2|addclass:"form-control" }}
                                        <div class="field-feedback" id="password2-feedback"></div>
                                        {% if form.password2.errors %}
                                        <div class="text-danger">
                                            <i class="fas fa-exclamation-circle me-2"></i>
                                            {% for error in form.password2.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>

                            <!-- Terms Agreement -->
                            <div class="form-check mb-1 mt-0">
                                <input class="form-check-input" type="checkbox" id="agreeTerms" required>
                                <label class="form-check-label" for="agreeTerms" style="font-size: 0.8rem;">
                                    I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                                </label>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid gap-2 mt-1">
                                <button type="submit" class="btn btn-primary py-1" id="registerButton">
                                    <i class="fas fa-user-plus me-2"></i>Create Account
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Card Footer -->
                    <div class="card-footer text-center">
                        <p class="mb-0">
                            Already have an account?
                            <a href="{% url 'login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Sign in
                            </a>
                        </p>
                    </div>
                    </div>
                </div>

                <!-- Copyright Footer -->
                <div class="text-center mt-2 text-muted small">
                    <p class="mb-0" style="font-size: 0.75rem;">2025 BMC MedForecast. All rights reserved.</p>
                </div>
            </div>

            <!-- Mobile layout (vertical) -->
            <div class="col-md-10 col-lg-8 col-xl-6 d-block d-md-none">
                <!-- Main Card -->
                <div class="main-card">
                    <div class="card-header">
                        <h2 class="text-center">
                            <i class="fas fa-user-plus me-2"></i>Create Your Account
                        </h2>
                    </div>

                    <div class="card-body">
                        <div id="register-error-container-mobile"></div>

                        <!-- Registration Form -->
                        <form method="post" novalidate class="needs-validation" id="registrationFormMobile">
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle fa-lg me-3"></i>
                                <div>
                                    {% for error in form.non_field_errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                            </div>
                            {% endif %}

                            {% for field in form %}
                            <div class="form-group">
                                <label for="{{ field.id_for_label }}_mobile" class="form-label">
                                    <div class="icon-container">
                                        <i class="fas fa-{% if field.name == 'username' %}user{% elif field.name == 'email' %}envelope{% elif field.name == 'password1' or field.name == 'password2' %}lock{% elif field.name == 'first_name' %}user-tag{% elif field.name == 'last_name' %}user-tag{% elif field.name == 'birthday' %}birthday-cake{% elif field.name == 'sex' %}venus-mars{% elif field.name == 'phone_number' %}phone{% else %}info-circle{% endif %}"></i>
                                    </div>
                                    {{ field.label }}
                                </label>
                                {{ field|addclass:"form-control" }}
                                <div class="field-feedback" id="{{ field.name }}-feedback-mobile"></div>
                                {% if field.errors %}
                                <div class="text-danger">
                                    <i class="fas fa-exclamation-circle me-2"></i>
                                    {% for error in field.errors %}
                                    {{ error }}
                                    {% endfor %}
                                </div>
                                {% endif %}
                                {% if field.help_text %}
                                <small class="form-text">{{ field.help_text }}</small>
                                {% endif %}
                                {% if field.name == 'phone_number' %}
                                <small class="form-text">Enter a valid Philippine phone number (e.g., +639123456789)</small>
                                {% endif %}
                                {% if field.name == 'password1' %}
                                <div class="password-validation-container password-validation-container-mobile mt-2" style="display: none;">
                                    <small class="d-block mb-1">Password requirements:</small>
                                    <div class="password-requirement d-flex align-items-center mb-1">
                                        <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                        <small>At least 8 characters</small>
                                    </div>
                                    <div class="password-requirement d-flex align-items-center mb-1">
                                        <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                        <small>Contains lowercase letters</small>
                                    </div>
                                    <div class="password-requirement d-flex align-items-center mb-1">
                                        <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                        <small>Contains uppercase letters</small>
                                    </div>
                                    <div class="password-requirement d-flex align-items-center mb-1">
                                        <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                        <small>Contains numbers</small>
                                    </div>
                                    <div class="password-requirement d-flex align-items-center mb-1">
                                        <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                        <small>Not entirely numeric</small>
                                    </div>
                                    <div class="password-requirement d-flex align-items-center">
                                        <i class="fas fa-check-circle me-2 requirement-icon"></i>
                                        <small>Not similar to personal information</small>
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                            {% endfor %}

                            <!-- Terms Agreement -->
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="checkbox" id="agreeTermsMobile" required>
                                <label class="form-check-label" for="agreeTermsMobile" style="font-size: 0.85rem;">
                                    I agree to the <a href="#">Terms of Service</a> and <a href="#">Privacy Policy</a>
                                </label>
                            </div>

                            <!-- Submit Button -->
                            <div class="d-grid gap-2 mt-2">
                                <button type="submit" class="btn btn-primary" id="registerButtonMobile">
                                    <i class="fas fa-user-plus me-2"></i>Create Account
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- Card Footer -->
                    <div class="card-footer text-center">
                        <p class="mb-0">
                            Already have an account?
                            <a href="{% url 'login' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Sign in
                            </a>
                        </p>
                    </div>
                </div>

                <!-- Copyright Footer -->
                <div class="text-center mt-2 text-muted small">
                    <p class="mb-0" style="font-size: 0.75rem;">2025 BMC MedForecast. All rights reserved.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const form = document.getElementById('registrationForm');
            const submitButton = document.getElementById('registerButton');
            const errorContainer = document.getElementById('register-error-container');

            // Field elements
            const usernameInput = document.getElementById('{{ form.username.id_for_label }}');
            const emailInput = document.getElementById('{{ form.email.id_for_label }}');
            const birthdayInput = document.getElementById('{{ form.birthday.id_for_label }}');
            const phoneInput = document.getElementById('{{ form.phone_number.id_for_label }}');
            const passwordInput = document.getElementById('{{ form.password1.id_for_label }}');
            const password2Input = document.getElementById('{{ form.password2.id_for_label }}');

            // Feedback elements
            const usernameFeedback = document.getElementById('username-feedback');
            const emailFeedback = document.getElementById('email-feedback');
            const birthdayFeedback = document.getElementById('birthday-feedback');
            const phoneFeedback = document.getElementById('phone-feedback');
            const passwordFeedback = document.getElementById('password-feedback');
            const password2Feedback = document.getElementById('password2-feedback');

            // Helper function to show error message
            function showError(message) {
                errorContainer.innerHTML = `
                    <div class="alert alert-danger alert-dismissible fade show">
                        <i class="fas fa-exclamation-circle me-2"></i>${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                `;
            }

            // Helper function to clear error messages
            function clearErrors() {
                errorContainer.innerHTML = '';
            }

            // Helper function to show field feedback
            function showFieldFeedback(element, message, isValid) {
                if (element) {
                    element.textContent = message;
                    element.classList.add('visible');
                    if (isValid) {
                        element.classList.add('valid');
                        element.classList.remove('invalid');
                    } else {
                        element.classList.add('invalid');
                        element.classList.remove('valid');
                    }
                }
            }

            // Helper function to clear field feedback
            function clearFieldFeedback(element) {
                if (element) {
                    element.textContent = '';
                    element.classList.remove('visible', 'valid', 'invalid');
                }
            }

            // Username validation
            usernameInput.addEventListener('blur', async function() {
                const username = this.value.trim();
                if (!username) {
                    showFieldFeedback(usernameFeedback, 'Username is required', false);
                    return;
                }

                if (username.length < 3) {
                    showFieldFeedback(usernameFeedback, 'Username must be at least 3 characters', false);
                    return;
                }

                // Check if username exists (you would need an API endpoint for this)
                try {
                    const response = await fetch(`/check-username/?username=${encodeURIComponent(username)}`);
                    const data = await response.json();

                    if (data.exists) {
                        showFieldFeedback(usernameFeedback, 'Username already taken', false);
                        this.classList.add('is-invalid');
                        this.classList.remove('is-valid');
                    } else {
                        showFieldFeedback(usernameFeedback, 'Username available', true);
                        this.classList.add('is-valid');
                        this.classList.remove('is-invalid');
                    }
                } catch (error) {
                    console.error('Error checking username:', error);
                }
            });

            // Email validation
            emailInput.addEventListener('blur', async function() {
                const email = this.value.trim();
                if (!email) {
                    showFieldFeedback(emailFeedback, 'Email is required', false);
                    return;
                }

                // Enhanced email validation
                // First check for basic format
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email)) {
                    showFieldFeedback(emailFeedback, 'Invalid email format', false);
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                    return;
                }

                // More strict validation to catch errors like extra characters after domain
                const strictEmailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                if (!strictEmailRegex.test(email)) {
                    showFieldFeedback(emailFeedback, 'Invalid email format - check for extra characters', false);
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                    return;
                }

                // Check for valid domains (Google, Yahoo, etc.)
                const validDomains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'aol.com', 'icloud.com', 'protonmail.com', 'mail.com'];
                const domain = email.split('@')[1].toLowerCase();

                // Check if domain exactly matches one of our valid domains
                const isValidDomain = validDomains.some(validDomain => domain === validDomain);

                if (!isValidDomain) {
                    showFieldFeedback(emailFeedback, 'Please use a common email provider', false);
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                    return;
                }

                // Check if email exists (you would need an API endpoint for this)
                try {
                    const response = await fetch(`/check-email/?email=${encodeURIComponent(email)}`);
                    const data = await response.json();

                    if (data.exists) {
                        showFieldFeedback(emailFeedback, 'Email already registered', false);
                        this.classList.add('is-invalid');
                        this.classList.remove('is-valid');
                    } else {
                        const provider = domain.split('.')[0];
                        showFieldFeedback(emailFeedback, `Valid ${provider.charAt(0).toUpperCase() + provider.slice(1)} email`, true);
                        this.classList.add('is-valid');
                        this.classList.remove('is-invalid');
                    }
                } catch (error) {
                    console.error('Error checking email:', error);
                }
            });

            // Birthday validation
            birthdayInput.addEventListener('change', function() {
                validateBirthday(this, birthdayFeedback);
            });

            // Function to validate birthday
            function validateBirthday(inputElement, feedbackElement) {
                const birthday = new Date(inputElement.value);
                const today = new Date();

                if (!inputElement.value) {
                    showFieldFeedback(feedbackElement, 'Birthday is required', false);
                    inputElement.classList.add('is-invalid');
                    inputElement.classList.remove('is-valid');
                    return;
                }

                // Calculate age
                let age = today.getFullYear() - birthday.getFullYear();
                const monthDiff = today.getMonth() - birthday.getMonth();
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthday.getDate())) {
                    age--;
                }

                // Check if date is in the future
                if (birthday > today) {
                    showFieldFeedback(feedbackElement, 'Birthday cannot be in the future', false);
                    inputElement.classList.add('is-invalid');
                    inputElement.classList.remove('is-valid');
                    return;
                }

                // Check if year is before 1970
                if (birthday.getFullYear() < 1970) {
                    showFieldFeedback(feedbackElement, 'Year must be 1970 or later', false);
                    inputElement.classList.add('is-invalid');
                    inputElement.classList.remove('is-valid');
                    return;
                }

                // Check if year is after 2007
                if (birthday.getFullYear() > 2007) {
                    showFieldFeedback(feedbackElement, 'Year must be 2007 or earlier', false);
                    inputElement.classList.add('is-invalid');
                    inputElement.classList.remove('is-valid');
                    return;
                }

                // Valid age
                showFieldFeedback(feedbackElement, `Age: ${age} years`, true);
                inputElement.classList.add('is-valid');
                inputElement.classList.remove('is-invalid');
            }

            // Phone number validation (Philippine format)
            phoneInput.addEventListener('blur', function() {
                const phone = this.value.trim();
                if (!phone) {
                    // Phone is optional, so no error if empty
                    clearFieldFeedback(phoneFeedback);
                    this.classList.remove('is-invalid', 'is-valid');
                    return;
                }

                // Philippine phone number format: +639XXXXXXXXX or 09XXXXXXXXX
                const phoneRegex = /^(\+639|09)\d{9}$/;
                if (!phoneRegex.test(phone)) {
                    showFieldFeedback(phoneFeedback, 'Invalid Philippine phone number', false);
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                } else {
                    showFieldFeedback(phoneFeedback, 'Valid Philippine phone number', true);
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                }
            });

            // Password validation
            passwordInput.addEventListener('input', function() {
                const password = this.value;

                // Show password requirements when user starts typing
                const validationContainer = document.querySelector('.password-validation-container');
                if (validationContainer) {
                    if (password.length > 0) {
                        validationContainer.style.display = 'block';
                    } else {
                        validationContainer.style.display = 'none';
                    }
                }

                // Get all requirement elements
                const requirements = document.querySelectorAll('.password-requirement');

                // Validate each requirement
                // 1. Length requirement
                const lengthValid = password.length >= 8;
                updateRequirement(requirements[0], lengthValid);

                // 2. Lowercase requirement
                const lowercaseValid = /[a-z]/.test(password);
                updateRequirement(requirements[1], lowercaseValid);

                // 3. Uppercase requirement
                const uppercaseValid = /[A-Z]/.test(password);
                updateRequirement(requirements[2], uppercaseValid);

                // 4. Number requirement
                const numberValid = /\d/.test(password);
                updateRequirement(requirements[3], numberValid);

                // 5. Not entirely numeric
                const notNumericValid = !/^\d+$/.test(password) || password.length === 0;
                updateRequirement(requirements[4], notNumericValid);

                // 6. Not similar to personal info (simplified check)
                const personalInfoValid = password.length >= 8; // Simplified check
                updateRequirement(requirements[5], personalInfoValid);

                // Check password strength
                let strength = 0;
                let feedback = '';

                if (lengthValid) strength += 1;
                if (lowercaseValid && uppercaseValid) strength += 1;
                if (numberValid) strength += 1;
                if (password.match(/[^a-zA-Z\d]/)) strength += 1;

                switch (strength) {
                    case 0:
                        feedback = 'Very weak password';
                        this.classList.add('is-invalid');
                        this.classList.remove('is-valid');
                        break;
                    case 1:
                        feedback = 'Weak password';
                        this.classList.add('is-invalid');
                        this.classList.remove('is-valid');
                        break;
                    case 2:
                        feedback = 'Fair password';
                        this.classList.add('is-invalid');
                        this.classList.remove('is-valid');
                        break;
                    case 3:
                        feedback = 'Good password';
                        this.classList.add('is-valid');
                        this.classList.remove('is-invalid');
                        break;
                    case 4:
                        feedback = 'Strong password';
                        this.classList.add('is-valid');
                        this.classList.remove('is-invalid');
                        break;
                }

                showFieldFeedback(passwordFeedback, feedback, strength >= 3);

                // Update password confirmation validation if it has a value
                if (password2Input.value) {
                    password2Input.dispatchEvent(new Event('input'));
                }
            });

            // Helper function to update requirement validation
            function updateRequirement(requirementElement, isValid) {
                if (isValid) {
                    requirementElement.classList.add('valid');
                } else {
                    requirementElement.classList.remove('valid');
                }
            }

            // Password confirmation validation
            password2Input.addEventListener('input', function() {
                const password = passwordInput.value;
                const confirmPassword = this.value;

                if (confirmPassword === '') {
                    clearFieldFeedback(password2Feedback);
                    this.classList.remove('is-invalid', 'is-valid');
                    return;
                }

                if (password === confirmPassword) {
                    showFieldFeedback(password2Feedback, 'Passwords match', true);
                    this.classList.add('is-valid');
                    this.classList.remove('is-invalid');
                } else {
                    showFieldFeedback(password2Feedback, 'Passwords do not match', false);
                    this.classList.add('is-invalid');
                    this.classList.remove('is-valid');
                }
            });

            // Form submission
            form.addEventListener('submit', function(event) {
                // Clear any previous errors
                clearErrors();

                // Basic form validation
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                    showError('Please fill out all required fields correctly.');
                    form.classList.add('was-validated');
                    return;
                }

                // Check for any invalid fields
                const invalidFields = form.querySelectorAll('.is-invalid');
                if (invalidFields.length > 0) {
                    event.preventDefault();
                    showError('Please correct the highlighted errors before submitting.');
                    invalidFields[0].focus();
                    invalidFields[0].classList.add('shake');
                    setTimeout(() => {
                        invalidFields[0].classList.remove('shake');
                    }, 500);
                    return;
                }

                // Check terms agreement
                const agreeTerms = document.getElementById('agreeTerms');
                if (!agreeTerms.checked) {
                    event.preventDefault();
                    showError('You must agree to the Terms of Service and Privacy Policy.');
                    agreeTerms.focus();
                    return;
                }

                // If all validations pass, show loading state
                submitButton.disabled = true;
                submitButton.classList.add('btn-loading');
                submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';
            });

            // Enhanced input interactions
            document.querySelectorAll('.form-control').forEach(input => {
                // Set initial state for prefilled inputs
                if (input.value) {
                    input.classList.add('has-value');
                }

                // Focus effects
                input.addEventListener('focus', () => {
                    input.classList.add('focused');
                    input.parentElement.classList.add('input-focused');
                });

                // Blur effects
                input.addEventListener('blur', () => {
                    input.classList.remove('focused');
                    input.parentElement.classList.remove('input-focused');

                    if (input.value) {
                        input.classList.add('has-value');
                    } else {
                        input.classList.remove('has-value');
                    }
                });
            });

            // Mobile form handling
            const mobileForm = document.getElementById('registrationFormMobile');
            if (mobileForm) {
                const mobileSubmitButton = document.getElementById('registerButtonMobile');
                const mobileErrorContainer = document.getElementById('register-error-container-mobile');

                // Get mobile form fields
                const mobileInputs = mobileForm.querySelectorAll('.form-control');

                // Add validation to mobile form fields
                mobileInputs.forEach(input => {
                    const fieldName = input.getAttribute('name');
                    const feedbackElement = document.getElementById(`${fieldName}-feedback-mobile`);

                    if (fieldName === 'username') {
                        input.addEventListener('blur', async function() {
                            const username = this.value.trim();
                            if (!username) {
                                showFieldFeedback(feedbackElement, 'Username is required', false);
                                return;
                            }

                            if (username.length < 3) {
                                showFieldFeedback(feedbackElement, 'Username must be at least 3 characters', false);
                                return;
                            }

                            try {
                                const response = await fetch(`/check-username/?username=${encodeURIComponent(username)}`);
                                const data = await response.json();

                                if (data.exists) {
                                    showFieldFeedback(feedbackElement, 'Username already taken', false);
                                    this.classList.add('is-invalid');
                                    this.classList.remove('is-valid');
                                } else {
                                    showFieldFeedback(feedbackElement, 'Username available', true);
                                    this.classList.add('is-valid');
                                    this.classList.remove('is-invalid');
                                }
                            } catch (error) {
                                console.error('Error checking username:', error);
                            }
                        });
                    } else if (fieldName === 'email') {
                        input.addEventListener('blur', async function() {
                            const email = this.value.trim();
                            if (!email) {
                                showFieldFeedback(feedbackElement, 'Email is required', false);
                                return;
                            }

                            // Basic email validation
                            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                            if (!emailRegex.test(email)) {
                                showFieldFeedback(feedbackElement, 'Invalid email format', false);
                                this.classList.add('is-invalid');
                                this.classList.remove('is-valid');
                                return;
                            }

                            // More strict validation to catch errors like extra characters after domain
                            const strictEmailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
                            if (!strictEmailRegex.test(email)) {
                                showFieldFeedback(feedbackElement, 'Invalid email format - check for extra characters', false);
                                this.classList.add('is-invalid');
                                this.classList.remove('is-valid');
                                return;
                            }

                            const validDomains = ['gmail.com', 'yahoo.com', 'outlook.com', 'hotmail.com', 'aol.com', 'icloud.com', 'protonmail.com', 'mail.com'];
                            const domain = email.split('@')[1].toLowerCase();
                            // Check if domain exactly matches one of our valid domains
                            const isValidDomain = validDomains.some(validDomain => domain === validDomain);

                            if (!isValidDomain) {
                                showFieldFeedback(feedbackElement, 'Please use a common email provider', false);
                                this.classList.add('is-invalid');
                                this.classList.remove('is-valid');
                                return;
                            }

                            try {
                                const response = await fetch(`/check-email/?email=${encodeURIComponent(email)}`);
                                const data = await response.json();

                                if (data.exists) {
                                    showFieldFeedback(feedbackElement, 'Email already registered', false);
                                    this.classList.add('is-invalid');
                                    this.classList.remove('is-valid');
                                } else {
                                    const provider = domain.split('.')[0];
                                    showFieldFeedback(feedbackElement, `Valid ${provider.charAt(0).toUpperCase() + provider.slice(1)} email`, true);
                                    this.classList.add('is-valid');
                                    this.classList.remove('is-invalid');
                                }
                            } catch (error) {
                                console.error('Error checking email:', error);
                            }
                        });
                    } else if (fieldName === 'birthday') {
                        input.addEventListener('change', function() {
                            validateBirthday(this, feedbackElement);
                        });
                    } else if (fieldName === 'phone_number') {
                        input.addEventListener('blur', function() {
                            const phone = this.value.trim();
                            if (!phone) {
                                clearFieldFeedback(feedbackElement);
                                this.classList.remove('is-invalid', 'is-valid');
                                return;
                            }

                            const phoneRegex = /^(\+639|09)\d{9}$/;
                            if (!phoneRegex.test(phone)) {
                                showFieldFeedback(feedbackElement, 'Invalid Philippine phone number', false);
                                this.classList.add('is-invalid');
                                this.classList.remove('is-valid');
                            } else {
                                showFieldFeedback(feedbackElement, 'Valid Philippine phone number', true);
                                this.classList.add('is-valid');
                                this.classList.remove('is-invalid');
                            }
                        });
                    } else if (fieldName === 'password1') {
                        input.addEventListener('input', function() {
                            const password = this.value;

                            // Show password requirements when user starts typing
                            const mobileValidationContainer = mobileForm.querySelector('.password-validation-container-mobile');
                            if (mobileValidationContainer) {
                                if (password.length > 0) {
                                    mobileValidationContainer.style.display = 'block';
                                } else {
                                    mobileValidationContainer.style.display = 'none';
                                }
                            }

                            // Get all mobile requirement elements
                            const mobileRequirements = mobileForm.querySelectorAll('.password-requirement');
                            if (mobileRequirements.length > 0) {
                                // Validate each requirement
                                // 1. Length requirement
                                const lengthValid = password.length >= 8;
                                updateRequirement(mobileRequirements[0], lengthValid);

                                // 2. Lowercase requirement
                                const lowercaseValid = /[a-z]/.test(password);
                                updateRequirement(mobileRequirements[1], lowercaseValid);

                                // 3. Uppercase requirement
                                const uppercaseValid = /[A-Z]/.test(password);
                                updateRequirement(mobileRequirements[2], uppercaseValid);

                                // 4. Number requirement
                                const numberValid = /\d/.test(password);
                                updateRequirement(mobileRequirements[3], numberValid);

                                // 5. Not entirely numeric
                                const notNumericValid = !/^\d+$/.test(password) || password.length === 0;
                                updateRequirement(mobileRequirements[4], notNumericValid);

                                // 6. Not similar to personal info (simplified check)
                                const personalInfoValid = password.length >= 8; // Simplified check
                                updateRequirement(mobileRequirements[5], personalInfoValid);
                            }

                            let strength = 0;
                            let feedback = '';

                            if (password.length >= 8) strength += 1;
                            if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength += 1;
                            if (password.match(/\d/)) strength += 1;
                            if (password.match(/[^a-zA-Z\d]/)) strength += 1;

                            switch (strength) {
                                case 0:
                                    feedback = 'Very weak password';
                                    this.classList.add('is-invalid');
                                    this.classList.remove('is-valid');
                                    break;
                                case 1:
                                    feedback = 'Weak password';
                                    this.classList.add('is-invalid');
                                    this.classList.remove('is-valid');
                                    break;
                                case 2:
                                    feedback = 'Fair password';
                                    this.classList.add('is-invalid');
                                    this.classList.remove('is-valid');
                                    break;
                                case 3:
                                    feedback = 'Good password';
                                    this.classList.add('is-valid');
                                    this.classList.remove('is-invalid');
                                    break;
                                case 4:
                                    feedback = 'Strong password';
                                    this.classList.add('is-valid');
                                    this.classList.remove('is-invalid');
                                    break;
                            }

                            showFieldFeedback(feedbackElement, feedback, strength >= 3);

                            // Update password confirmation validation if it has a value
                            const password2 = mobileForm.querySelector('[name="password2"]');
                            if (password2 && password2.value) {
                                const event = new Event('input');
                                password2.dispatchEvent(event);
                            }
                        });
                    } else if (fieldName === 'password2') {
                        input.addEventListener('input', function() {
                            const password1 = mobileForm.querySelector('[name="password1"]');
                            const password = password1 ? password1.value : '';
                            const confirmPassword = this.value;

                            if (confirmPassword === '') {
                                clearFieldFeedback(feedbackElement);
                                this.classList.remove('is-invalid', 'is-valid');
                                return;
                            }

                            if (password === confirmPassword) {
                                showFieldFeedback(feedbackElement, 'Passwords match', true);
                                this.classList.add('is-valid');
                                this.classList.remove('is-invalid');
                            } else {
                                showFieldFeedback(feedbackElement, 'Passwords do not match', false);
                                this.classList.add('is-invalid');
                                this.classList.remove('is-valid');
                            }
                        });
                    }
                });

                // Mobile form submission
                mobileForm.addEventListener('submit', function(event) {
                    // Clear any previous errors
                    mobileErrorContainer.innerHTML = '';

                    // Basic form validation
                    if (!mobileForm.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                        mobileErrorContainer.innerHTML = `
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="fas fa-exclamation-circle me-2"></i>Please fill out all required fields correctly.
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        `;
                        mobileForm.classList.add('was-validated');
                        return;
                    }

                    // Check for any invalid fields
                    const invalidFields = mobileForm.querySelectorAll('.is-invalid');
                    if (invalidFields.length > 0) {
                        event.preventDefault();
                        mobileErrorContainer.innerHTML = `
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="fas fa-exclamation-circle me-2"></i>Please correct the highlighted errors before submitting.
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        `;
                        invalidFields[0].focus();
                        invalidFields[0].classList.add('shake');
                        setTimeout(() => {
                            invalidFields[0].classList.remove('shake');
                        }, 500);
                        return;
                    }

                    // Check terms agreement
                    const agreeTermsMobile = document.getElementById('agreeTermsMobile');
                    if (!agreeTermsMobile.checked) {
                        event.preventDefault();
                        mobileErrorContainer.innerHTML = `
                            <div class="alert alert-danger alert-dismissible fade show">
                                <i class="fas fa-exclamation-circle me-2"></i>You must agree to the Terms of Service and Privacy Policy.
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        `;
                        agreeTermsMobile.focus();
                        return;
                    }

                    // If all validations pass, show loading state
                    mobileSubmitButton.disabled = true;
                    mobileSubmitButton.classList.add('btn-loading');
                    mobileSubmitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Account...';
                });
            }
        });
    </script>
</body>
</html>