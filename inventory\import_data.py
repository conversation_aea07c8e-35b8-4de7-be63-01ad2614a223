﻿import os
import sys
import pandas as pd
import random
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
from django.utils import timezone
from django.db import transaction
import names  # pip install names

# Setup Django environment
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.append(project_root)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Pinv.settings')
import django
django.setup()

from inventory.models import Medicine, Transaction

def format_decimal(value):
    try:
        dec_value = Decimal(str(value))
        return dec_value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
    except:
        return Decimal('0.00')

def generate_random_date(start_date, end_date):
    time_between_dates = end_date - start_date
    days_between_dates = time_between_dates.days
    random_number_of_days = random.randrange(days_between_dates)
    random_date = start_date + timedelta(days=random_number_of_days)
    return timezone.make_aware(datetime.combine(random_date, datetime.min.time()))

def generate_patient_number():
    year = str(timezone.now().year)
    sequence = str(random.randint(1000, 9999))
    return f"P{year}-{sequence}"

def import_medicines():
    # Realistic categories and descriptions
    medicine_data = {
        'Antibiotics': [
            "Broad-spectrum antibiotic for bacterial infections",
            "First-line treatment for respiratory infections",
            "Treatment for skin and soft tissue infections"
        ],
        'Analgesics': [
            "Pain reliever and fever reducer",
            "Strong pain medication for moderate to severe pain",
            "Non-narcotic pain reliever"
        ],
        'Antidiabetic': [
            "Oral medication for type 2 diabetes",
            "Blood sugar control medication",
            "Long-acting insulin for diabetes management"
        ],
        'Cardiovascular': [
            "Blood pressure medication",
            "Cholesterol-lowering medication",
            "Heart rhythm regulation medication"
        ],
        'Respiratory': [
            "Bronchodilator for asthma treatment",
            "Allergy and sinus medication",
            "Cough suppressant and expectorant"
        ],
        'Gastrointestinal': [
            "Acid reflux and ulcer treatment",
            "Anti-diarrheal medication",
            "Digestive health supplement"
        ],
        'Supplements': [
            "Daily multivitamin supplement",
            "Calcium and vitamin D supplement",
            "Iron supplement for anemia"
        ],
        'Anti-inflammatory': [
            "Non-steroidal anti-inflammatory drug",
            "Joint pain and arthritis medication",
            "Topical anti-inflammatory cream"
        ]
    }

    try:
        # Read the backup CSV
        csv_path = os.path.join(current_dir, '..', 'backup', 'medicines_backup_20250329_200259.csv')
        df = pd.read_csv(csv_path)
        
        df['amount'] = pd.to_numeric(df['amount'], errors='coerce').fillna(0)
        df['quantity'] = pd.to_numeric(df['quantity'], errors='coerce').fillna(0)
        
        unique_medicines = df['medicine'].unique()
        medicines_created = 0
        
        for med_name in unique_medicines:
            med_data = df[df['medicine'] == med_name]
            
            # Calculate realistic price (between ₱5 and ₱500)
            base_price = random.uniform(5, 500)
            avg_price = format_decimal(base_price)
            
            # Select random category and matching description
            category = random.choice(list(medicine_data.keys()))
            description = random.choice(medicine_data[category])
            
            # Generate realistic quantities
            initial_quantity = random.randint(200, 2000)
            reorder_level = int(initial_quantity * 0.2)  # 20% of initial quantity
            reorder_quantity = int(initial_quantity * 0.5)  # 50% of initial quantity
            
            # Generate realistic expiration date (1-3 years from now)
            current_date = timezone.now().date()
            exp_date = current_date + timedelta(days=random.randint(365, 1095))
            
            try:
                with transaction.atomic():
                    medicine = Medicine.objects.create(
                        name=med_name,
                        description=description,
                        quantity=initial_quantity,
                        reorder_level=reorder_level,
                        reorder_quantity=reorder_quantity,
                        category=category,
                        price=avg_price,
                        expiration_date=exp_date
                    )
                    
                    # Create realistic transactions
                    batch_size = 100
                    transactions = []
                    remaining_quantity = initial_quantity
                    
                    for _ in range(random.randint(10, 30)):  # Generate 10-30 transactions
                        # Ensure we don't create transactions that would result in negative inventory
                        max_transaction_qty = min(remaining_quantity, random.randint(1, 50))
                        if max_transaction_qty <= 0:
                            break
                            
                        quantity = random.randint(1, max_transaction_qty)
                        remaining_quantity -= quantity
                        
                        # Generate realistic transaction date (within last 30 days)
                        transaction_date = timezone.now() - timedelta(
                            days=random.randint(1, 30),
                            hours=random.randint(0, 23),
                            minutes=random.randint(0, 59)
                        )
                        
                        # Generate realistic customer data
                        customer_type = random.choice(['in_patient', 'out_patient'])
                        customer_name = names.get_full_name()
                        patient_number = generate_patient_number()
                        
                        # Calculate total amount based on price and quantity
                        total_amount = format_decimal(float(medicine.price) * quantity)
                        
                        transactions.append(
                            Transaction(
                                medicine=medicine,
                                transaction_date=transaction_date,
                                quantity=quantity,
                                transaction_type='sale',
                                customer_type=customer_type,
                                customer_name=customer_name,
                                patient_number=patient_number,
                                total_amount=total_amount
                            )
                        )
                        
                        if len(transactions) >= batch_size:
                            Transaction.objects.bulk_create(transactions)
                            transactions = []
                    
                    if transactions:
                        Transaction.objects.bulk_create(transactions)
                    
                    # Update final quantity
                    medicine.quantity = remaining_quantity
                    medicine.save()
                    
                    medicines_created += 1
                    print(f"Created medicine: {med_name}")
                    print(f"Category: {category}")
                    print(f"Current inventory: {remaining_quantity}")
                    print(f"Price: ₱{avg_price}")
                    print("-" * 50)
                
            except Exception as e:
                print(f"Error creating medicine {med_name}: {str(e)}")
                continue
        
        print(f"\nTotal medicines created: {medicines_created}")
        
    except Exception as e:
        print(f"Error reading CSV file: {str(e)}")

if __name__ == "__main__":
    import_medicines()