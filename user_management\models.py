import os
from datetime import datetime, timezone
from PIL import Image, ImageDraw, ImageFont
from django.db import models
from django.contrib.auth.models import User
from django.conf import settings
from django.utils import timezone
from django.core.validators import RegexValidator
from django.dispatch import receiver
from django.db.models.signals import post_save
from django.db.models import signals
import random

phone_regex = RegexValidator(
    regex=r'^(\+639|09)\d{9}$',
    message="Please enter a valid Philippine phone number (e.g., +639123456789 or 09123456789)."
)

class UserProfile(models.Model):
    GENDER_CHOICES = [
        ('M', 'Male'),
        ('F', 'Female'),
        ('O', 'Other')
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='profile')
    first_name = models.CharField(max_length=100)
    middle_name = models.CharField(max_length=100, blank=True, null=True)
    last_name = models.CharField(max_length=100)
    birthday = models.DateField(null=True, blank=True)
    sex = models.CharField(max_length=1, choices=GENDER_CHOICES, null=True, blank=True)
    totp_secret = models.CharField(max_length=32, null=True, blank=True)
    totp_verified = models.BooleanField(default=False)
    phone_number = models.CharField(validators=[phone_regex], max_length=17, blank=True)
    address = models.TextField(blank=True, null=True)
    email_verified = models.BooleanField(default=False)
    email_verification_token = models.CharField(max_length=100, blank=True, null=True)
    email_verification_token_created = models.DateTimeField(null=True, blank=True)
    profile_picture = models.ImageField(
        upload_to='profile_pics/',
        null=True,
        blank=True,
        max_length=255
    )

    # Pending fields for approval-based editing
    pending_email = models.EmailField(blank=True, null=True)
    pending_phone_number = models.CharField(validators=[phone_regex], max_length=17, blank=True, null=True)
    pending_address = models.TextField(blank=True, null=True)
    pending_changes_date = models.DateTimeField(null=True, blank=True)

    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)
    last_login = models.DateTimeField(null=True, blank=True)

    def get_initials(self):
        """Return the user's initials based on first and last name."""
        initials = ""
        if self.first_name:
            initials += self.first_name[0].upper()
        if self.last_name:
            initials += self.last_name[0].upper()
        return initials if initials else "?"

    def get_full_name(self):
        """Return the full name, with middle name if available."""
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}".strip()
        return f"{self.first_name} {self.last_name}".strip()

    def generate_initial_avatar(self):
        """Generates an avatar with the user's initials"""
        # First fallback: Return a CSS-based default avatar path
        default_avatar = 'default/avatar.png'

        try:
            # If user has no profile or username, return default avatar
            if not self.user or not self.user.username:
                return default_avatar

            # Create a new image with a random background color
            colors = ['#1abc9c', '#2ecc71', '#3498db', '#9b59b6', '#34495e',
                     '#16a085', '#27ae60', '#2980b9', '#8e44ad', '#2c3e50']
            bg_color = random.choice(colors)

            img = Image.new('RGB', (500, 500), bg_color)
            draw = ImageDraw.Draw(img)

            # Try to load fonts in order of preference
            try:
                font = ImageFont.truetype('arial.ttf', 250)
            except IOError:
                try:
                    font = ImageFont.truetype('DejaVuSans.ttf', 250)
                except IOError:
                    return default_avatar  # Return default avatar if no suitable fonts

            initials = self.get_initials()
            if not initials or initials == "?":
                return default_avatar

            # Get text bounding box
            bbox = draw.textbbox((0, 0), initials, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            # Calculate center position
            x = (500 - text_width) // 2
            y = (500 - text_height) // 2

            # Draw text
            draw.text((x, y), initials, fill='white', font=font)

            # Save the image with shorter filename
            filename = f'generated_{self.user.id}.png'
            path = os.path.join(settings.MEDIA_ROOT, 'profile_pics', filename)

            try:
                os.makedirs(os.path.dirname(path), exist_ok=True)
                img.save(path, 'PNG')
                return f'profile_pics/{filename}'
            except:
                return default_avatar  # Return default avatar if saving fails

        except Exception as e:
            return default_avatar  # Return default avatar for any other errors

    def save(self, *args, **kwargs):
        if not self.profile_picture:
            self.profile_picture = self.generate_initial_avatar()
        super().save(*args, **kwargs)

    def delete_profile_picture(self):
        """Safely delete the profile picture and generate a new initial avatar"""
        if self.profile_picture:
            storage = self.profile_picture.storage
            path = self.profile_picture.path

            # Generate new avatar before deleting the old one
            new_avatar = self.generate_initial_avatar()

            # Only delete if it's not the default avatar and not the new avatar
            if (storage.exists(path) and
                'default/avatar.html' not in str(self.profile_picture) and
                path != os.path.join(settings.MEDIA_ROOT, new_avatar)):
                storage.delete(path)

            self.profile_picture = new_avatar
            self.save()

    def __str__(self):
        return f"{self.get_full_name()} ({self.user.username})"

    class Meta:
        verbose_name = 'User Profile'
        verbose_name_plural = 'User Profiles'
        ordering = ['last_name', 'first_name']

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create a UserProfile for every new User."""
    if created:
        UserProfile.objects.create(
            user=instance,
            first_name=instance.first_name or '',
            last_name=instance.last_name or ''
        )

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Update UserProfile when User is updated."""
    signals.post_save.disconnect(save_user_profile, sender=User)

    try:
        profile, created = UserProfile.objects.get_or_create(user=instance)

        if profile.first_name != instance.first_name or profile.last_name != instance.last_name:
            profile.first_name = instance.first_name
            profile.last_name = instance.last_name
            profile.save()

        if instance.first_name != profile.first_name or instance.last_name != profile.last_name:
            instance.first_name = profile.first_name
            instance.last_name = profile.last_name
            instance.save()
    finally:
        signals.post_save.connect(save_user_profile, sender=User)

class UserActivity(models.Model):
    """Track user activities in the system"""
    ACTION_CHOICES = [
        ('LOGIN', 'User Login'),
        ('LOGOUT', 'User Logout'),
        ('PROFILE_UPDATE', 'Profile Update'),
        ('PASSWORD_CHANGE', 'Password Change'),
        ('OTHER', 'Other Activity')
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='activities')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    description = models.TextField(blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.CharField(max_length=255, blank=True)
    timestamp = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-timestamp']
        verbose_name = 'User Activity'
        verbose_name_plural = 'User Activities'

    def __str__(self):
        return f"{self.user.username} - {self.action} - {self.timestamp}"

class UserPermissionGroup(models.Model):
    """Custom permission groups for users"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True)
    users = models.ManyToManyField(User, related_name='custom_groups')
    created_at = models.DateTimeField(auto_now_add=True)
    modified_at = models.DateTimeField(auto_now=True)

    can_view_inventory = models.BooleanField(default=False)
    can_edit_inventory = models.BooleanField(default=False)
    can_view_reports = models.BooleanField(default=False)
    can_manage_users = models.BooleanField(default=False)
    can_approve_orders = models.BooleanField(default=False)

    class Meta:
        ordering = ['name']
        verbose_name = 'User Permission Group'
        verbose_name_plural = 'User Permission Groups'

    def __str__(self):
        return self.name

class UserNotification(models.Model):
    """System notifications for users"""
    NOTIFICATION_TYPES = [
        ('INFO', 'Information'),
        ('WARNING', 'Warning'),
        ('ERROR', 'Error'),
        ('SUCCESS', 'Success')
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField(max_length=255)
    message = models.TextField()
    notification_type = models.CharField(max_length=10, choices=NOTIFICATION_TYPES, default='INFO')
    is_read = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    read_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        ordering = ['-created_at']
        verbose_name = 'User Notification'
        verbose_name_plural = 'User Notifications'

    def __str__(self):
        return f"{self.user.username} - {self.title}"

    def mark_as_read(self):
        if not self.is_read:
            self.is_read = True
            self.read_at = timezone.now()
            self.save()

class PasswordResetOTP(models.Model):
    OTP_TYPES = [
        ('email', 'Email OTP'),
        ('totp', 'Time-based OTP'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE)
    otp = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)
    is_used = models.BooleanField(default=False)
    expires_at = models.DateTimeField()
    otp_type = models.CharField(max_length=5, choices=OTP_TYPES, default='email')

    def is_valid(self):
        now = timezone.now()
        return not self.is_used and now <= self.expires_at

    def mark_as_used(self):
        self.is_used = True
        self.save()

class ArchivedUser(models.Model):
    username = models.CharField(max_length=150)
    email = models.EmailField()
    first_name = models.CharField(max_length=150, blank=True)
    last_name = models.CharField(max_length=150, blank=True)
    profile_data = models.JSONField(default=dict)
    archived_at = models.DateTimeField(auto_now_add=True)
    archived_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True)

    class Meta:
        ordering = ['-archived_at']
        verbose_name = 'Archived User'
        verbose_name_plural = 'Archived Users'

    def __str__(self):
        return f"{self.username} (Archived on {self.archived_at})"

class Customer(models.Model):
    """Stores information about customers for transactions"""
    CUSTOMER_TYPES = [
        ('in_patient', 'In Patient'),
        ('out_patient', 'Out Patient'),
    ]

    customer_name = models.CharField(max_length=255)
    patient_number = models.CharField(max_length=255, unique=True, blank=True, null=True)
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_TYPES, default='out_patient')
    phone_number = models.CharField(validators=[phone_regex], max_length=17, blank=True, null=True)
    email = models.EmailField(blank=True, null=True)
    address = models.TextField(blank=True, null=True)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_transaction_date = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.customer_name} ({self.patient_number or 'No Patient Number'})"

    class Meta:
        ordering = ['customer_name']
        verbose_name = 'Customer'
        verbose_name_plural = 'Customers'
        indexes = [
            models.Index(fields=['customer_name']),
            models.Index(fields=['patient_number']),
        ]