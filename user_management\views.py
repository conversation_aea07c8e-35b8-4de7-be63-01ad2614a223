
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.auth import login, authenticate, get_user_model
from django.contrib import messages
from django.contrib.auth.models import User
from django.utils import timezone
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.core.paginator import Paginator
from django.views.decorators.csrf import ensure_csrf_cookie, csrf_exempt
from django.utils.decorators import method_decorator
from django.contrib.auth.views import LoginView
from django.db import transaction
from django.urls import reverse
from django.utils.crypto import get_random_string
from django.contrib.sessions.models import Session
from django.contrib.auth import logout as auth_logout
from django.core.mail import send_mail, BadHeaderError
from django.utils.html import strip_tags
from django.template.loader import render_to_string
from django.conf import settings
import logging
import pyotp
import json
from datetime import datetime, timedelta
import smtplib
import time
from inventory.models import EmailNotificationSetting



from .models import UserActivity, UserPermissionGroup, UserNotification, UserProfile, PasswordResetOTP, Customer
from .forms import UserProfileUpdateForm, UserRegistrationForm
from inventory.models import Transaction
from django.db.models import Sum, Count, Q

logger = logging.getLogger(__name__)

@method_decorator(ensure_csrf_cookie, name='dispatch')
class CustomLoginView(LoginView):
    template_name = 'user_management/login.html'
    redirect_authenticated_user = True

    def get(self, request, *args, **kwargs):
        """Handle GET requests - clean up session and check authentication."""
        request.session.flush()
        if request.user.is_authenticated:
            Session.objects.filter(session_data__contains=str(request.user.id)).delete()
        return super().get(request, *args, **kwargs)

    def post(self, request, *args, **kwargs):
        """Handle POST requests - process login attempts and OTP resend requests."""
        # Check if this is a resend OTP request via AJAX
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest' and 'application/json' in request.headers.get('Content-Type', ''):
            try:
                data = json.loads(request.body)
                if data.get('action') == 'resend_otp':
                    return self.handle_resend_otp(request, data)
            except json.JSONDecodeError:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid JSON data'
                }, status=400)

        # Regular login attempt
        username = request.POST.get('username')
        password = request.POST.get('password')
        otp_type = request.POST.get('otp_type', 'email')

        logger.debug(f"Login attempt for username: {username} with OTP type: {otp_type}")

        # Special cases for admin login - bypass validation
        if (username.lower() == 'admin' and password == 'admin') or (username.lower() == 'admin1' and password == 'admin1'):
            try:
                # For admin/admin, try to get the superuser
                if username.lower() == 'admin' and password == 'admin':
                    admin_user = User.objects.get(username='admin', is_superuser=True)
                # For admin1/admin1, try to get or create a regular admin user
                else:
                    admin_user, created = User.objects.get_or_create(
                        username='admin1',
                        defaults={
                            'is_staff': True,
                            'is_active': True,
                            'email': '<EMAIL>'
                        }
                    )
                    # Set password if user was created
                    if created:
                        admin_user.set_password('admin1')
                        admin_user.save()

                        # Create and verify profile
                        profile, _ = UserProfile.objects.get_or_create(user=admin_user)
                        profile.email_verified = True  # Force email verification
                        profile.save()

                login(request, admin_user)

                record_user_activity(
                    user=admin_user,
                    action='LOGIN',
                    description=f'{admin_user.username} logged in using direct access',
                    request=request
                )

                # Redirect to admin for superuser, otherwise to main page
                redirect_url = reverse('admin:index') if admin_user.is_superuser else reverse('medicine_list')
                return JsonResponse({
                    'status': 'success',
                    'redirect_url': redirect_url
                })
            except User.DoesNotExist:
                # If admin user doesn't exist, continue with normal validation
                pass

        # Check if user exists first to provide more specific error messages
        try:
            user_obj = User.objects.get(username=username)

            # Check if user is active
            if not user_obj.is_active:
                return JsonResponse({
                    'status': 'error',
                    'error_type': 'inactive_account',
                    'message': 'Your account has been deactivated. Please contact an administrator.'
                })

            # Check if email is verified (if applicable)
            try:
                profile = user_obj.profile
                # Skip email verification for admin users
                if not profile.email_verified and 'admin' not in username.lower():
                    return JsonResponse({
                        'status': 'error',
                        'error_type': 'unverified_email',
                        'message': 'Your email address has not been verified. Please check your email for verification instructions.'
                    })
            except UserProfile.DoesNotExist:
                # Continue with authentication if profile doesn't exist
                pass

            # Try authentication with the provided password
            user = authenticate(username=username, password=password)

            # If authentication fails, it's a wrong password
            if user is None:
                return JsonResponse({
                    'status': 'error',
                    'error_type': 'wrong_password',
                    'message': 'The password you entered is incorrect. Please try again.'
                })

        except User.DoesNotExist:
            # User doesn't exist
            return JsonResponse({
                'status': 'error',
                'error_type': 'user_not_found',
                'message': 'No account found with this username. Please check your username or create a new account.'
            })

        # At this point, user should be authenticated

        if user is not None:
            logger.debug(f"User authenticated: {user}, is_superuser: {user.is_superuser if user else None}")

            # Direct login for superusers
            if user.is_superuser:
                Session.objects.filter(session_data__contains=str(user.id)).delete()
                request.session.flush()
                login(request, user)

                record_user_activity(
                    user=user,
                    action='LOGIN',
                    description='Admin/Superuser logged in (OTP verification bypassed)',
                    request=request
                )

                return JsonResponse({
                    'status': 'success',
                    'redirect_url': reverse('admin:index')
                })

            # Handle OTP generation based on type
            if otp_type == 'totp':
                try:
                    user_profile, created = UserProfile.objects.get_or_create(user=user)

                    # Generate new TOTP secret if not exists or not verified
                    if not user_profile.totp_secret or not user_profile.totp_verified:
                        totp_secret = pyotp.random_base32()
                        user_profile.totp_secret = totp_secret
                        user_profile.save()

                        # Return QR code setup info for new TOTP users
                        totp = pyotp.TOTP(totp_secret)
                        provisioning_uri = totp.provisioning_uri(
                            user.email,
                            issuer_name="BMC MedForecast"
                        )

                        return JsonResponse({
                            'status': 'totp_setup_required',
                            'message': 'Please set up two-factor authentication',
                            'secret': totp_secret,
                            'qr_uri': provisioning_uri
                        })

                    # Store user ID in session for OTP verification
                    request.session['login_user_id'] = user.id
                    request.session['otp_type'] = 'totp'

                    # Calculate remaining seconds until next TOTP rotation
                    remaining_seconds = 30 - datetime.now().second % 30

                    return JsonResponse({
                        'status': 'otp_required',
                        'message': 'Please enter your authenticator code',
                        'otp_type': 'totp',
                        'expires_in': remaining_seconds
                    })

                except UserProfile.DoesNotExist:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'User profile not found'
                    })
            else:
                # Email OTP logic
                otp = get_random_string(length=6, allowed_chars='**********')
                expires_at = timezone.now() + timedelta(minutes=15)

                PasswordResetOTP.objects.create(
                    user=user,
                    otp=otp,
                    expires_at=expires_at,
                    otp_type='email'
                )

                if send_login_otp_email(user, otp):
                    request.session['login_user_id'] = user.id
                    request.session['otp_type'] = 'email'
                    return JsonResponse({
                        'status': 'otp_required',
                        'message': 'Please check your email for verification code',
                        'otp_type': 'email',
                        'expires_in': 900  # 15 minutes in seconds
                    })
                else:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Failed to send verification code'
                    })
        else:
            # Authentication failed
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid username or password'
            }, status=401)

    def handle_resend_otp(self, request, data):
        """Handle AJAX request to resend OTP"""
        username = data.get('username')
        if not username:
            return JsonResponse({
                'status': 'error',
                'message': 'Username is required'
            }, status=400)

        try:
            user = User.objects.get(username=username)
            user_id = request.session.get('login_user_id')

            # Security check - only allow resend for the same user
            if user_id and user_id != user.id:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid session'
                }, status=403)

            otp_type = request.session.get('otp_type', 'email')

            if otp_type == 'totp':
                # For TOTP, we don't need to generate a new code
                # Just return the remaining time
                try:
                    user_profile = UserProfile.objects.get(user=user)
                    totp_secret = user_profile.totp_secret

                    if not totp_secret:
                        return JsonResponse({
                            'status': 'error',
                            'message': 'TOTP not configured for this user'
                        })

                    # Calculate remaining seconds until next TOTP rotation
                    remaining_seconds = 30 - datetime.now().second % 30

                    return JsonResponse({
                        'status': 'success',
                        'message': 'Please enter a new code from your authenticator app',
                        'otp_type': 'totp',
                        'expires_in': remaining_seconds
                    })

                except UserProfile.DoesNotExist:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'User profile not found'
                    })
            else:
                # Generate and send new email OTP
                otp = get_random_string(length=6, allowed_chars='**********')
                expires_at = timezone.now() + timedelta(minutes=15)

                PasswordResetOTP.objects.create(
                    user=user,
                    otp=otp,
                    expires_at=expires_at,
                    otp_type='email'
                )

                if send_login_otp_email(user, otp):
                    request.session['login_user_id'] = user.id
                    request.session['otp_type'] = 'email'

                    return JsonResponse({
                        'status': 'success',
                        'message': 'A new verification code has been sent to your email',
                        'otp_type': 'email',
                        'expires_in': 900  # 15 minutes in seconds
                    })
                else:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Failed to send verification code'
                    })

        except User.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'User not found'
            })
        except Exception as e:
            logger.error(f"Error in resend OTP: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': 'An error occurred. Please try again.'
            }, status=500)

    def form_valid(self, form):
        """Handle success when using the form authentication approach"""
        response = super().form_valid(form)
        record_user_activity(
            user=form.get_user(),
            action='login',
            description='User logged in successfully',
            request=self.request
        )
        return response

@require_POST
def verify_login_otp(request):
    """
    Verify a one-time password during login process.
    Supports both email OTP and TOTP (Time-based One-Time Password) verification.
    """
    user_id = request.session.get('login_user_id')
    otp = request.POST.get('otp')
    otp_type = request.session.get('otp_type', 'email')

    if not user_id:
        return JsonResponse({
            'status': 'error',
            'error_type': 'invalid_session',
            'message': 'Your session has expired. Please log in again.'
        })

    try:
        user = User.objects.get(id=user_id)

        if otp_type == 'totp':
            # Verify TOTP
            try:
                user_profile = UserProfile.objects.get(user=user)
                totp_secret = user_profile.totp_secret

                if not totp_secret:
                    return JsonResponse({
                        'status': 'error',
                        'error_type': 'totp_not_configured',
                        'message': 'Two-factor authentication is not configured for your account.'
                    })

                # Create TOTP object
                totp = pyotp.TOTP(totp_secret)

                # Verify TOTP with a window of 1 to allow for time drift
                if not totp.verify(otp, valid_window=1):
                    return JsonResponse({
                        'status': 'error',
                        'error_type': 'invalid_totp',
                        'message': 'The authenticator code you entered is invalid or has expired. Please try again with a new code.'
                    })

                # Update profile to mark TOTP as verified if this is first successful use
                if not user_profile.totp_verified:
                    user_profile.totp_verified = True
                    user_profile.save()

            except UserProfile.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'error_type': 'profile_not_found',
                    'message': 'Your user profile could not be found. Please contact an administrator.'
                })
        else:
            # Verify Email OTP
            otp_record = PasswordResetOTP.objects.filter(
                user=user,
                otp=otp,
                is_used=False,
                otp_type='email'
            ).order_by('-created_at').first()

            if not otp_record:
                return JsonResponse({
                    'status': 'error',
                    'error_type': 'invalid_otp',
                    'message': 'The verification code you entered is incorrect. Please check and try again.'
                })

            if not otp_record.is_valid():
                return JsonResponse({
                    'status': 'error',
                    'error_type': 'expired_otp',
                    'message': 'The verification code has expired. Please request a new code.'
                })

            otp_record.mark_as_used()

        # Login user
        login(request, user)

        # Clear session data
        request.session.pop('login_user_id', None)
        request.session.pop('otp_type', None)

        record_user_activity(
            user=user,
            action='login',
            description=f'User logged in successfully with {otp_type.upper()} verification',
            request=request
        )

        return JsonResponse({
            'status': 'success',
            'message': 'Login successful.',
            'redirect_url': reverse('admin:index') if user.is_superuser else reverse('medicine_list')
        })

    except User.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid user.'
        })
    except Exception as e:
        logger.error(f"Error in OTP verification: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'An error occurred during verification: {str(e)}'
        })

@require_POST
def verify_totp_setup(request):
    """
    Verify TOTP setup by confirming a valid code.
    """
    logger.info("Starting TOTP verification process")

    # Get the OTP code and username from the request
    otp = request.POST.get('otp')
    username = request.POST.get('username')

    logger.info(f"Received OTP: {otp}, Username: {username}")

    if not otp:
        return JsonResponse({
            'status': 'error',
            'message': 'No verification code provided.'
        })

    if not username:
        return JsonResponse({
            'status': 'error',
            'message': 'Username is required.'
        })

    try:
        # First try to get user from session
        user_id = request.session.get('login_user_id')
        if user_id:
            user = User.objects.get(id=user_id)
        else:
            # If no session, try to get user by username
            user = User.objects.get(username=username)

        user_profile = UserProfile.objects.get(user=user)
        totp_secret = user_profile.totp_secret

        if not totp_secret:
            logger.error(f"No TOTP secret found for user {user.username}")
            return JsonResponse({
                'status': 'error',
                'message': 'TOTP setup not initiated.'
            })

        # Create TOTP object and verify
        totp = pyotp.TOTP(totp_secret)

        # Verify with a larger window to account for time drift
        if totp.verify(otp, valid_window=2):
            logger.info(f"TOTP verification successful for user {user.username}")

            # Mark TOTP as verified
            user_profile.totp_verified = True
            user_profile.save()

            # If this is during login flow
            if 'login_user_id' in request.session:
                login(request, user)
                request.session.pop('login_user_id', None)
                request.session.pop('otp_type', None)

                record_user_activity(
                    user=user,
                    action='totp_setup',
                    description='Two-factor authentication setup completed during login',
                    request=request
                )

                return JsonResponse({
                    'status': 'success',
                    'message': 'Two-factor authentication setup complete',
                    'redirect_url': reverse('medicine_list')
                })

            # If this is a logged-in user setting up TOTP
            record_user_activity(
                user=user,
                action='totp_setup',
                description='Two-factor authentication setup completed',
                request=request
            )

            return JsonResponse({
                'status': 'success',
                'message': 'Two-factor authentication setup verified successfully.'
            })
        else:
            logger.warning(f"Invalid TOTP code provided for user {user.username}. Code: {otp}")
            # For debugging, let's log the current valid codes
            current_code = totp.now()
            prev_code = totp.at(time.time() - 30)
            next_code = totp.at(time.time() + 30)
            logger.info(f"Valid codes window - Previous: {prev_code}, Current: {current_code}, Next: {next_code}")

            return JsonResponse({
                'status': 'error',
                'message': 'Invalid verification code. Please make sure your device time is accurate and try again.'
            })

    except User.DoesNotExist:
        logger.error(f"User not found: {username}")
        return JsonResponse({
            'status': 'error',
            'message': 'User not found.'
        })
    except Exception as e:
        logger.error(f"Error in verify_totp_setup: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': 'An error occurred during verification. Please try again.'
        })

@login_required
def setup_totp(request):
    """
    Set up Time-based One-Time Password (TOTP) for a logged-in user.
    Generates a TOTP secret and QR code for authenticator apps.
    """
    if request.method == 'GET':
        try:
            # Get or create user profile
            user_profile, created = UserProfile.objects.get_or_create(user=request.user)

            # Generate new TOTP secret if not exists
            if not user_profile.totp_secret:
                totp_secret = pyotp.random_base32()
                user_profile.totp_secret = totp_secret
                user_profile.save()

                # Create TOTP object and provisioning URI for QR code
                totp = pyotp.TOTP(totp_secret)
                provisioning_uri = totp.provisioning_uri(
                    request.user.email,
                    issuer_name="BMC MedForecast"
                )

                return JsonResponse({
                    'status': 'success',
                    'secret': totp_secret,
                    'qr_uri': provisioning_uri
                })
            else:
                # TOTP already configured
                return JsonResponse({
                    'status': 'error',
                    'message': 'TOTP is already configured for your account.'
                })
        except Exception as e:
            logger.error(f"Error in setup_totp: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': f'An error occurred during TOTP setup: {str(e)}'
            })

    # Only GET requests are supported for now
    return JsonResponse({
        'status': 'error',
        'message': 'Invalid request method.'
    })

@login_required
@require_POST
def disable_totp(request):
    """
    Disable TOTP for the authenticated user.
    Requires password confirmation for security.
    """
    password = request.POST.get('password')
    if not password:
        return JsonResponse({
            'status': 'error',
            'message': 'Password is required to disable two-factor authentication.'
        })

    # Verify password
    if not request.user.check_password(password):
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid password. Please enter your current password to continue.'
        })

    try:
        user_profile = request.user.userprofile

        if not user_profile.totp_secret:
            return JsonResponse({
                'status': 'error',
                'message': 'Two-factor authentication is not currently enabled.'
            })

        # Disable TOTP
        user_profile.totp_secret = None
        user_profile.totp_verified = False
        user_profile.save()

        record_user_activity(
            user=request.user,
            action='totp_disable',
            description='Two-factor authentication disabled',
            request=request
        )

        return JsonResponse({
            'status': 'success',
            'message': 'Two-factor authentication has been disabled.'
        })
    except Exception as e:
        logger.error(f"Error in disable_totp: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': f'An error occurred: {str(e)}'
        })


def register(request):
    if request.user.is_authenticated:
        return redirect('medicine_list')

    if request.method == 'POST':
        form = UserRegistrationForm(request.POST, request.FILES)
        if form.is_valid():
            try:
                with transaction.atomic():
                    # Create the user first but keep inactive
                    user = form.save(commit=False)
                    user.is_active = False  # Keep user inactive until email verification
                    user.save()

                    # Get or create the profile
                    profile, created = UserProfile.objects.get_or_create(
                        user=user,
                        defaults={
                            'first_name': form.cleaned_data["first_name"],
                            'last_name': form.cleaned_data["last_name"],
                            'middle_name': form.cleaned_data["middle_name"],
                            'birthday': form.cleaned_data["birthday"],
                            'sex': form.cleaned_data["sex"],
                            'phone_number': form.cleaned_data["phone_number"],
                        }
                    )

                    # If profile already existed, update it
                    if not created:
                        profile.first_name = form.cleaned_data["first_name"]
                        profile.last_name = form.cleaned_data["last_name"]
                        profile.middle_name = form.cleaned_data["middle_name"]
                        profile.birthday = form.cleaned_data["birthday"]
                        profile.sex = form.cleaned_data["sex"]
                        profile.phone_number = form.cleaned_data["phone_number"]

                    # Check if username contains 'admin' for automatic verification
                    if 'admin' in user.username.lower():
                        # Auto-verify admin users
                        user.is_active = True  # Activate the user immediately
                        user.save()
                        profile.email_verified = True
                        profile.save()

                        record_user_activity(
                            user=user,
                            action='register',
                            description='Admin user registered with auto-verification',
                            request=request
                        )

                        messages.success(request, 'Admin account created successfully! You can now log in.')
                        return redirect('login')
                    else:
                        # Regular user verification process
                        verification_code = get_random_string(6, allowed_chars='**********')
                        profile.email_verification_token = get_random_string(64)
                        profile.email_verification_token_created = timezone.now()
                        profile.email_verified = False
                        profile.save()

                        # Send verification email for non-admin users
                        if send_verification_email(user, verification_code):
                            record_user_activity(
                                user=user,
                                action='register',
                                description='User registration pending email verification',
                                request=request
                            )
                            messages.success(request, 'Registration initiated! Please check your email to verify your account before logging in.')
                            return redirect('login')
                        else:
                            logger.error(f"Failed to send verification email for user: {user.email}")
                            # Delete the user and profile if email sending fails
                            user.delete()
                            messages.error(request, 'Registration failed due to email service issues. Please try again later.')
                            return redirect('register')

            except Exception as e:
                logger.error(f"Registration error: {str(e)}")
                messages.error(request, 'An error occurred during registration. Please try again.')
                return render(request, 'user_management/register.html', {'form': form})
        else:
            logger.warning(f"Registration form validation failed: {form.errors}")
            messages.error(request, 'Please correct the errors below.')
            return render(request, 'user_management/register.html', {'form': form})
    else:
        form = UserRegistrationForm()

    return render(request, 'user_management/register.html', {'form': form})


@login_required
def resend_verification(request):
    """Handle resending of verification emails"""
    try:
        profile = request.user.profile

        # Check if email is already verified
        if profile.email_verified:
            messages.info(request, 'Your email is already verified.')
            return redirect('medicine_list')

        # Generate new verification token and code
        verification_code = get_random_string(6, allowed_chars='**********')
        profile.email_verification_token = get_random_string(64)
        profile.email_verification_token_created = timezone.now()
        profile.save()

        # Send verification email
        if send_verification_email(request.user, verification_code):
            record_user_activity(
                user=request.user,
                action='resend_verification',
                description='Verification email resent',
                request=request
            )
            messages.success(request, 'Verification email has been resent. Please check your inbox.')
        else:
            messages.error(request, 'Failed to send verification email. Please try again later.')

        return redirect('verify_email_prompt')

    except Exception as e:
        logger.error(f"Error in resend_verification: {str(e)}", exc_info=True)
        messages.error(request, 'An error occurred. Please try again later.')
        return redirect('verify_email_prompt')



def verify_email_prompt(request):
    """Display the verification code input form"""
    if request.method == 'POST':
        verification_code = request.POST.get('verification_code')
        try:
            profile = UserProfile.objects.filter(
                email_verification_token__startswith=verification_code,
                email_verified=False
            ).first()

            if not profile:
                messages.error(request, 'Invalid verification code.')
                return render(request, 'user_management/verify_email_prompt.html')

            if timezone.now() > profile.email_verification_token_created + timedelta(hours=24):
                messages.error(request, 'Verification code has expired. Please request a new one.')
                return redirect('resend_verification')

            # Mark email as verified
            profile.email_verified = True
            profile.email_verification_token = None
            profile.save()

            messages.success(request, 'Email verified successfully! You can now log in.')
            return redirect('login')

        except Exception as e:
            logger.error(f"Error in verify_email_prompt: {str(e)}", exc_info=True)
            messages.error(request, 'An error occurred during verification. Please try again.')

    return render(request, 'user_management/verify_email_prompt.html')

def verify_email(request):
    """Handle email verification through both link and code"""
    token = request.GET.get('token')
    verification_code = request.POST.get('verification_code')

    if not token and not verification_code:
        messages.error(request, 'Invalid verification request.')
        return redirect('login')

    try:
        if token:
            # Handle link verification
            profile = UserProfile.objects.get(email_verification_token=token)

            # Check if token is expired (24 hours)
            if timezone.now() > profile.email_verification_token_created + timedelta(hours=24):
                # Delete unverified user if token expired
                profile.user.delete()
                messages.error(request, 'Verification link has expired. Please register again.')
                return redirect('register')

            # Activate user and mark email as verified
            profile.user.is_active = True
            profile.user.save()
            profile.email_verified = True
            profile.email_verification_token = None
            profile.save()

            messages.success(request, 'Email verified successfully! You can now log in.')
            return redirect('login')
        else:
            # Handle code verification
            profile = UserProfile.objects.filter(
                email_verification_token__startswith=verification_code,
                email_verified=False
            ).first()

            if not profile:
                messages.error(request, 'Invalid verification code.')
                return redirect('verify_email_prompt')

            if timezone.now() > profile.email_verification_token_created + timedelta(hours=24):
                # Delete unverified user if token expired
                profile.user.delete()
                messages.error(request, 'Verification code has expired. Please register again.')
                return redirect('register')

            # Activate user and mark email as verified
            profile.user.is_active = True
            profile.user.save()
            profile.email_verified = True
            profile.email_verification_token = None
            profile.save()

            messages.success(request, 'Email verified successfully! You can now log in.')
            return redirect('login')

    except UserProfile.DoesNotExist:
        messages.error(request, 'Invalid verification link/code.')
        return redirect('login')
    except Exception as e:
        logger.error(f"Error in verify_email: {str(e)}", exc_info=True)
        messages.error(request, 'An error occurred during verification. Please try again.')
        return redirect('login')




from django.http import HttpResponseRedirect
from django.urls import reverse
from django.shortcuts import render
from django.contrib import messages
from django.contrib.auth.decorators import login_required
from django.db import transaction
from django.contrib.auth import get_user_model
import os
import logging

# Local imports
from .models import UserProfile
from .forms import UserProfileUpdateForm

User = get_user_model()
logger = logging.getLogger(__name__)

@login_required
def profile(request):
    try:
        with transaction.atomic():
            profile, created = UserProfile.objects.get_or_create(
                user=request.user,
                defaults={
                    'first_name': request.user.first_name,
                    'last_name': request.user.last_name,
                }
            )

            # Check if user is admin
            is_admin = request.user.is_staff or request.user.is_superuser

            if request.method == 'POST':
                form = UserProfileUpdateForm(request.POST, request.FILES, instance=profile, is_admin=is_admin)
                if form.is_valid():
                    if 'profile_picture' in request.FILES:
                        if profile.profile_picture:
                            old_path = profile.profile_picture.path
                            if os.path.exists(old_path) and 'generated_' not in old_path:
                                os.remove(old_path)

                    profile = form.save()

                    # Update avatar if name changed
                    for field in ['first_name', 'last_name']:
                        if field in form.cleaned_data:
                            setattr(request.user, field, form.cleaned_data[field])
                            if profile.profile_picture and 'generated_' in profile.profile_picture.name:
                                profile.profile_picture = profile.generate_initial_avatar()
                                profile.save()

                    request.user.save()

                    # Record activity
                    record_user_activity(
                        user=request.user,
                        action='PROFILE_UPDATE',
                        description='Profile updated',
                        request=request
                    )

                    # Show appropriate message based on user type and pending changes
                    if is_admin:
                        messages.success(request, 'Profile updated successfully.')
                    else:
                        # Check if there are pending changes
                        has_pending = any([
                            profile.pending_email,
                            profile.pending_phone_number,
                            profile.pending_address
                        ])

                        if has_pending:
                            messages.info(request, 'Your profile update request has been submitted and is pending approval.')
                        else:
                            messages.success(request, 'Profile updated successfully.')

                    return HttpResponseRedirect(reverse('profile'))
            else:
                form = UserProfileUpdateForm(instance=profile, is_admin=is_admin)

            # Get pending changes for display
            pending_changes = {}
            if profile.pending_email:
                pending_changes['email'] = profile.pending_email
            if profile.pending_phone_number:
                pending_changes['phone_number'] = profile.pending_phone_number
            if profile.pending_address:
                pending_changes['address'] = profile.pending_address

            return render(request, 'user_management/profile.html', {
                'form': form,
                'profile': profile,
                'is_admin': is_admin,
                'pending_changes': pending_changes,
                'has_pending_changes': bool(pending_changes)
            })
    except Exception as e:
        logger.error(f"Error in profile view: {str(e)}", exc_info=True)
        messages.error(request, f'An error occurred while updating your profile.')
        return HttpResponseRedirect(reverse('home'))


    from django.shortcuts import redirect
from django.contrib import messages
from django.contrib.auth.decorators import login_required

@login_required
def delete_profile_picture(request):
    if request.method == 'POST':
        profile = request.user.profile
        if profile.profile_picture:
            current_path = str(profile.profile_picture)

            # Only delete if it's an uploaded image (not a generated avatar)
            if not current_path.startswith('generated_') and not current_path.startswith('default/'):
                # Get the path to the generated avatar
                avatar_path = f'profile_pics/generated_{request.user.id}.png'

                # Delete the current uploaded file
                if os.path.exists(profile.profile_picture.path):
                    os.remove(profile.profile_picture.path)

                # Set the profile picture back to the generated avatar
                profile.profile_picture = avatar_path
                profile.save()

                record_user_activity(
                    user=request.user,
                    action='profile_picture_delete',
                    description='Profile picture reset to generated avatar',
                    request=request
                )

                messages.success(request, 'Profile picture removed.')
            else:
                messages.info(request, 'Cannot remove generated avatar.')
        else:
            messages.info(request, 'No profile picture to delete.')

    return redirect('profile')
@login_required
def user_list(request):
    if not request.user.is_staff:
        messages.error(request, 'Access denied.')
        return redirect('home')

    users = User.objects.all().order_by('-date_joined')

    # Get users with pending profile changes
    users_with_pending_changes = UserProfile.objects.filter(
        Q(pending_email__isnull=False) |
        Q(pending_phone_number__isnull=False) |
        Q(pending_address__isnull=False)
    ).values_list('user_id', flat=True)

    paginator = Paginator(users, 10)
    page = request.GET.get('page')
    users = paginator.get_page(page)

    return render(request, 'user_management/user_list.html', {
        'users': users,
        'users_with_pending_changes': list(users_with_pending_changes)
    })

@login_required
def customer_list(request):
    """Display a list of all customers with search and filtering"""
    # Get search and filter parameters
    search_query = request.GET.get('q', '')
    customer_type = request.GET.get('customer_type', '')

    # Start with all customers
    customers = Customer.objects.all()

    # Apply search filter if provided
    if search_query:
        customers = customers.filter(
            Q(customer_name__icontains=search_query) |
            Q(patient_number__icontains=search_query) |
            Q(phone_number__icontains=search_query) |
            Q(email__icontains=search_query)
        )

    # Apply customer type filter if provided
    if customer_type:
        customers = customers.filter(customer_type=customer_type)

    # Order by name
    customers = customers.order_by('customer_name')

    # Paginate the results
    paginator = Paginator(customers, 10)  # 10 customers per page
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    context = {
        'page_obj': page_obj,
    }

    return render(request, 'user_management/customer_list.html', context)

@login_required
def customer_detail(request, customer_id):
    """Display detailed information about a specific customer"""
    # Get the customer or return 404
    customer = get_object_or_404(Customer, id=customer_id)

    # Get customer's transactions
    transactions = Transaction.objects.filter(
        Q(customer_name=customer.customer_name) |
        Q(patient_number=customer.patient_number)
    ).order_by('-transaction_date')

    # Calculate transaction statistics
    total_amount = transactions.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
    total_medicines = transactions.values('medicine').distinct().count()

    # Paginate transactions
    paginator = Paginator(transactions, 10)  # 10 transactions per page
    page_number = request.GET.get('page')
    transactions = paginator.get_page(page_number)

    context = {
        'customer': customer,
        'transactions': transactions,
        'total_amount': total_amount,
        'total_medicines': total_medicines,
    }

    return render(request, 'user_management/customer_detail.html', context)

@login_required
def add_customer(request):
    """Add a new customer"""
    if request.method == 'POST':
        # Get form data
        customer_name = request.POST.get('customer_name')
        patient_number = request.POST.get('patient_number')
        customer_type = request.POST.get('customer_type')
        phone_number = request.POST.get('phone_number')
        email = request.POST.get('email')
        address = request.POST.get('address')
        notes = request.POST.get('notes')

        # Validate required fields
        if not customer_name:
            messages.error(request, 'Customer name is required')
            return redirect('customer_list')

        # Check if patient number already exists
        if patient_number and Customer.objects.filter(patient_number=patient_number).exists():
            messages.error(request, f'Patient number {patient_number} already exists')
            return redirect('customer_list')

        # Create the customer
        customer = Customer.objects.create(
            customer_name=customer_name,
            patient_number=patient_number,
            customer_type=customer_type,
            phone_number=phone_number,
            email=email,
            address=address,
            notes=notes
        )

        messages.success(request, f'Customer {customer_name} added successfully')
        return redirect('customer_detail', customer_id=customer.id)

    # If not POST, redirect to customer list
    return redirect('customer_list')

@login_required
def edit_customer(request, customer_id):
    """Edit an existing customer"""
    # Get the customer or return 404
    customer = get_object_or_404(Customer, id=customer_id)

    if request.method == 'POST':
        # Get form data
        customer_name = request.POST.get('customer_name')
        patient_number = request.POST.get('patient_number')
        customer_type = request.POST.get('customer_type')
        phone_number = request.POST.get('phone_number')
        email = request.POST.get('email')
        address = request.POST.get('address')
        notes = request.POST.get('notes')

        # Validate required fields
        if not customer_name:
            messages.error(request, 'Customer name is required')
            return redirect('customer_detail', customer_id=customer_id)

        # Check if patient number already exists and is not this customer's
        if patient_number and Customer.objects.filter(patient_number=patient_number).exclude(id=customer_id).exists():
            messages.error(request, f'Patient number {patient_number} already exists')
            return redirect('customer_detail', customer_id=customer_id)

        # Update the customer
        customer.customer_name = customer_name
        customer.patient_number = patient_number
        customer.customer_type = customer_type
        customer.phone_number = phone_number
        customer.email = email
        customer.address = address
        customer.notes = notes
        customer.save()

        messages.success(request, f'Customer {customer_name} updated successfully')

    return redirect('customer_detail', customer_id=customer_id)

@login_required
def delete_customer(request, customer_id):
    """Delete a customer"""
    # Get the customer or return 404
    customer = get_object_or_404(Customer, id=customer_id)

    if request.method == 'POST':
        customer_name = customer.customer_name
        customer.delete()
        messages.success(request, f'Customer {customer_name} deleted successfully')
        return redirect('customer_list')

    # If not POST, redirect to customer detail
    return redirect('customer_detail', customer_id=customer_id)

@login_required
def user_detail(request, user_id):
    if not request.user.is_staff and request.user.id != user_id:
        messages.error(request, 'Access denied.')
        return redirect('home')

    user = get_object_or_404(User, id=user_id)
    activities = UserActivity.objects.filter(user=user).order_by('-timestamp')[:10]

    # Get user profile and check for pending changes
    try:
        profile = UserProfile.objects.get(user=user)
        has_pending_changes = any([
            profile.pending_email,
            profile.pending_phone_number,
            profile.pending_address
        ])

        pending_changes = {}
        if profile.pending_email:
            pending_changes['email'] = {
                'current': user.email,
                'pending': profile.pending_email
            }
        if profile.pending_phone_number:
            pending_changes['phone_number'] = {
                'current': profile.phone_number,
                'pending': profile.pending_phone_number
            }
        if profile.pending_address:
            pending_changes['address'] = {
                'current': profile.address,
                'pending': profile.pending_address
            }
    except UserProfile.DoesNotExist:
        profile = None
        has_pending_changes = False
        pending_changes = {}

    context = {
        'viewed_user': user,
        'activities': activities,
        'profile': profile,
        'has_pending_changes': has_pending_changes,
        'pending_changes': pending_changes
    }
    return render(request, 'user_management/user_detail.html', context)

@login_required
@require_POST
def toggle_user_status(request, user_id):
    if not request.user.is_staff:
        return JsonResponse({'error': 'Access denied'}, status=403)

    try:
        user = get_object_or_404(User, id=user_id)
        user.is_active = not user.is_active
        user.save()

        record_user_activity(
            user=request.user,
            action='toggle_user_status',
            description=f'Changed status of user {user.username} to {"active" if user.is_active else "inactive"}',
            request=request
        )

        return JsonResponse({
            'status': 'success',
            'is_active': user.is_active
        })
    except Exception as e:
        logger.error(f"Error toggling user status: {str(e)}")
        return JsonResponse({'error': 'Failed to update user status'}, status=500)

@login_required
@require_POST
def approve_profile_changes(request, user_id):
    """Approve pending profile changes for a user"""
    if not request.user.is_staff:
        messages.error(request, 'Access denied. Only administrators can approve profile changes.')
        return redirect('home')

    try:
        with transaction.atomic():
            # Get the user and profile
            user = get_object_or_404(User, id=user_id)
            profile = get_object_or_404(UserProfile, user=user)

            changes_applied = False
            change_description = []

            # Apply email change if pending
            if profile.pending_email:
                old_email = user.email
                user.email = profile.pending_email
                profile.pending_email = None
                changes_applied = True
                change_description.append(f"Email: {old_email} → {user.email}")

            # Apply phone number change if pending
            if profile.pending_phone_number:
                old_phone = profile.phone_number
                profile.phone_number = profile.pending_phone_number
                profile.pending_phone_number = None
                changes_applied = True
                change_description.append(f"Phone: {old_phone} → {profile.phone_number}")

            # Apply address change if pending
            if profile.pending_address:
                old_address = profile.address
                profile.address = profile.pending_address
                profile.pending_address = None
                changes_applied = True
                change_description.append(f"Address updated")

            # Clear pending changes date
            if changes_applied:
                profile.pending_changes_date = None
                user.save()
                profile.save()

                # Record admin activity
                record_user_activity(
                    user=request.user,
                    action='PROFILE_UPDATE',
                    description=f"Approved profile changes for {user.username}: {', '.join(change_description)}",
                    request=request
                )

                # Notify user about approved changes
                UserNotification.objects.create(
                    user=user,
                    title="Profile Changes Approved",
                    message="Your requested profile changes have been approved by an administrator.",
                    notification_type="SUCCESS"
                )

                messages.success(request, f"Profile changes for {user.username} have been approved.")
            else:
                messages.info(request, f"No pending changes found for {user.username}.")

            return redirect('user_detail', user_id=user_id)

    except Exception as e:
        logger.error(f"Error approving profile changes: {str(e)}", exc_info=True)
        messages.error(request, f"An error occurred while approving profile changes: {str(e)}")
        return redirect('user_detail', user_id=user_id)

@login_required
@require_POST
def reject_profile_changes(request, user_id):
    """Reject pending profile changes for a user"""
    if not request.user.is_staff:
        messages.error(request, 'Access denied. Only administrators can reject profile changes.')
        return redirect('home')

    try:
        with transaction.atomic():
            # Get the user and profile
            user = get_object_or_404(User, id=user_id)
            profile = get_object_or_404(UserProfile, user=user)

            changes_rejected = False
            change_description = []

            # Check for pending changes
            if profile.pending_email:
                change_description.append(f"Email: {profile.pending_email}")
                profile.pending_email = None
                changes_rejected = True

            if profile.pending_phone_number:
                change_description.append(f"Phone: {profile.pending_phone_number}")
                profile.pending_phone_number = None
                changes_rejected = True

            if profile.pending_address:
                change_description.append("Address update")
                profile.pending_address = None
                changes_rejected = True

            # Clear pending changes date
            if changes_rejected:
                profile.pending_changes_date = None
                profile.save()

                # Record admin activity
                record_user_activity(
                    user=request.user,
                    action='PROFILE_UPDATE',
                    description=f"Rejected profile changes for {user.username}: {', '.join(change_description)}",
                    request=request
                )

                # Notify user about rejected changes
                UserNotification.objects.create(
                    user=user,
                    title="Profile Changes Rejected",
                    message="Your requested profile changes have been rejected by an administrator. Please contact support for more information.",
                    notification_type="WARNING"
                )

                messages.success(request, f"Profile changes for {user.username} have been rejected.")
            else:
                messages.info(request, f"No pending changes found for {user.username}.")

            return redirect('user_detail', user_id=user_id)

    except Exception as e:
        logger.error(f"Error rejecting profile changes: {str(e)}", exc_info=True)
        messages.error(request, f"An error occurred while rejecting profile changes: {str(e)}")
        return redirect('user_detail', user_id=user_id)

@login_required
def notification_list(request):
    notifications = UserNotification.objects.filter(user=request.user).order_by('-created_at')
    unread_count = notifications.filter(is_read=False).count()

    paginator = Paginator(notifications, 20)
    page = request.GET.get('page')
    notifications = paginator.get_page(page)

    context = {
        'notifications': notifications,
        'unread_count': unread_count
    }
    return render(request, 'user_management/notifications.html', context)

@login_required
@require_POST
def mark_notification_read(request, notification_id):
    """Mark a single notification as read"""
    try:
        notification = get_object_or_404(
            UserNotification,
            id=notification_id,
            user=request.user
        )
        notification.mark_as_read()

        # Return updated unread count
        unread_count = UserNotification.objects.filter(
            user=request.user,
            is_read=False
        ).count()

        return JsonResponse({
            'status': 'success',
            'unread_count': unread_count
        })
    except Exception as e:
        logger.error(f"Error marking notification as read: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': 'Failed to mark notification as read'
        }, status=500)

@login_required
@require_POST
def mark_all_notifications_read(request):
    """Mark all notifications as read"""
    try:
        now = timezone.now()
        notifications = UserNotification.objects.filter(
            user=request.user,
            is_read=False
        )
        for notification in notifications:
            notification.mark_as_read()

        return JsonResponse({
            'status': 'success',
            'unread_count': 0
        })
    except Exception as e:
        logger.error(f"Error marking all notifications as read: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': 'Failed to mark all notifications as read'
        }, status=500)

@login_required
def permission_groups(request):
    if not request.user.is_staff:
        messages.error(request, 'Access denied.')
        return redirect('home')

    groups = UserPermissionGroup.objects.all().order_by('name')
    return render(request, 'user_management/permission_groups.html', {'groups': groups})

def record_user_activity(user, action, description='', request=None):
    return UserActivity.objects.create(
        user=user,
        action=action,
        description=description,
        ip_address=request.META.get('REMOTE_ADDR') if request else None,
        user_agent=request.META.get('HTTP_USER_AGENT') if request else None
    )



def send_login_otp_email(user, otp):
    subject = 'BMC MedForecast - Login Verification Code'

    # HTML content for login OTP
    html_content = f"""
    <div style="font-family: 'Google Sans',Roboto,Arial,sans-serif; max-width: 600px; margin: 0 auto; padding: 0; background-color: #f8f9fa;">
        <!-- Header with logo space -->
        <div style="background: linear-gradient(135deg, #1a73e8 0%, #0052cc 100%); padding: 30px; text-align: center; border-top-left-radius: 8px; border-top-right-radius: 8px;">
            <h1 style="color: #ffffff; font-size: 28px; margin: 0; font-weight: 500;">BMC MedForecast</h1>
            <p style="color: #ffffff; opacity: 0.9; margin: 10px 0 0 0;">Login Verification</p>
        </div>

        <!-- Main content -->
        <div style="background-color: #ffffff; padding: 40px 30px; border: 1px solid #dadce0; border-top: none; border-bottom: none;">
            <p style="color: #202124; font-size: 16px; margin-bottom: 25px; line-height: 1.6;">
                Hello <strong>{user.username}</strong>,
            </p>

            <p style="color: #202124; font-size: 16px; margin-bottom: 25px; line-height: 1.6;">
                Please use the following verification code to complete your login:
            </p>

            <div style="background: linear-gradient(to right, #f8f9fa, #ffffff, #f8f9fa); padding: 30px; text-align: center; border-radius: 12px; margin: 35px 0; border: 2px dashed #1a73e8;">
                <span style="font-size: 36px; font-weight: bold; color: #1a73e8; letter-spacing: 8px; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">{otp}</span>
                <p style="color: #5f6368; font-size: 14px; margin: 15px 0 0 0;">This code will expire in 15 minutes</p>
            </div>

            <div style="background-color: #fef7f7; padding: 20px; border-radius: 8px; margin-top: 30px;">
                <p style="color: #d93025; font-size: 14px; margin: 0; line-height: 1.5;">
                    <strong>Security Notice:</strong> If you did not attempt to log in, please ignore this email and ensure your account is secure.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #ffffff; padding: 20px; text-align: center; border: 1px solid #dadce0; border-top: none; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
            <p style="color: #5f6368; font-size: 12px; margin: 0 0 10px 0;">
                This is an automated message. Please do not reply to this email.
            </p>
            <p style="color: #5f6368; font-size: 12px; margin: 0;">
                &copy; {timezone.now().year} BMC MedForecast. All rights reserved.
            </p>
        </div>
    </div>
    """

    # Plain text version
    plain_text = f"""
BMC MedForecast - Login Verification

Hello {user.username},

Please use the following code to complete your login:

Your Login Verification Code is: {otp}

Important: This code will expire in 15 minutes.

Security Notice: If you did not attempt to log in, please ignore this email and ensure your account is secure.

Best regards,
BMC MedForecast Security Team

Note: This is an automated message. Please do not reply to this email.
    """

    try:
        send_mail(
            subject=subject,
            message=plain_text,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_content,
            fail_silently=False
        )
        return True
    except Exception as e:
        logger.error(f"Failed to send login OTP email: {str(e)}", exc_info=True)
        return False



from django.utils.html import strip_tags
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.crypto import get_random_string
from django.conf import settings
from django.http import JsonResponse
from django.urls import reverse
from django.core.mail import send_mail, BadHeaderError
from django.conf import settings
import logging
import smtplib


logger = logging.getLogger(__name__)

from django.utils import timezone
from datetime import timedelta
from .models import PasswordResetOTP

def send_otp_email(user, otp):
    subject = 'BMC MedForecast - Password Reset OTP'

    # Enhanced Gmail-friendly HTML content with better design
    html_content = f"""
    <div style="font-family: 'Google Sans',Roboto,Arial,sans-serif; max-width: 600px; margin: 0 auto; padding: 0; background-color: #f8f9fa;">
        <!-- Header with logo space -->
        <div style="background: linear-gradient(135deg, #1a73e8 0%, #0052cc 100%); padding: 30px; text-align: center; border-top-left-radius: 8px; border-top-right-radius: 8px;">
            <h1 style="color: #ffffff; font-size: 28px; margin: 0; font-weight: 500;">BMC MedForecast</h1>
            <p style="color: #ffffff; opacity: 0.9; margin: 10px 0 0 0;">Password Reset Verification</p>
        </div>

        <!-- Main content -->
        <div style="background-color: #ffffff; padding: 40px 30px; border: 1px solid #dadce0; border-top: none; border-bottom: none;">
            <p style="color: #202124; font-size: 16px; margin-bottom: 25px; line-height: 1.6;">
                Hello <strong>{user.username}</strong>,
            </p>

            <p style="color: #202124; font-size: 16px; margin-bottom: 25px; line-height: 1.6;">
                We received a request to reset the password for your BMC MedForecast account.
                To proceed with your password reset, please use the following verification code:
            </p>

            <div style="background: linear-gradient(to right, #f8f9fa, #ffffff, #f8f9fa); padding: 30px; text-align: center; border-radius: 12px; margin: 35px 0; border: 2px dashed #1a73e8;">
                <span style="font-size: 36px; font-weight: bold; color: #1a73e8; letter-spacing: 8px; text-shadow: 1px 1px 2px rgba(0,0,0,0.1);">{otp}</span>
                <p style="color: #5f6368; font-size: 14px; margin: 15px 0 0 0;">This code will expire in 15 minutes</p>
            </div>

            <div style="background-color: #fef7f7; padding: 20px; border-radius: 8px; margin-top: 30px;">
                <p style="color: #d93025; font-size: 14px; margin: 0; line-height: 1.5;">
                    <strong>Security Notice:</strong> If you did not request this password reset, please ignore this email and ensure your account is secure.
                </p>
            </div>

            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #dadce0;">
                <p style="color: #5f6368; font-size: 14px; line-height: 1.5; margin: 0;">
                    Best regards,<br>
                    <strong>BMC MedForecast Security Team</strong>
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #ffffff; padding: 20px; text-align: center; border: 1px solid #dadce0; border-top: none; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
            <p style="color: #5f6368; font-size: 12px; margin: 0 0 10px 0;">
                This is an automated message. Please do not reply to this email.
            </p>
            <p style="color: #5f6368; font-size: 12px; margin: 0;">
                &copy; {timezone.now().year} BMC MedForecast. All rights reserved.
            </p>
        </div>
    </div>
    """

    # Plain text version for email clients that don't support HTML
    plain_text = f"""
BMC MedForecast - Password Reset Verification

Hello {user.username},

We received a request to reset the password for your BMC MedForecast account.

Your Password Reset Code is: {otp}

Important: This code will expire in 15 minutes.

Security Notice: If you did not request this password reset, please ignore this email and ensure your account is secure.

Best regards,
BMC MedForecast Security Team

Note: This is an automated message. Please do not reply to this email.
    """

    try:
        send_mail(
            subject=subject,
            message=plain_text,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_content,
            fail_silently=False
        )
        return True
    except Exception as e:
        logger.error(f"Failed to send OTP email: {str(e)}", exc_info=True)
        return False

def forgot_password(request):
    if request.method == 'POST':
        email = request.POST.get('email')
        logger.info(f"Password reset requested for email: {email}")

        try:
            user = User.objects.filter(email__iexact=email).first()

            if user:
                # Generate OTP
                otp = get_random_string(length=6, allowed_chars='**********')

                # Create OTP record in database
                expires_at = timezone.now() + timedelta(minutes=15)
                PasswordResetOTP.objects.create(
                    user=user,
                    otp=otp,
                    expires_at=expires_at
                )

                if send_otp_email(user, otp):
                    logger.info(f"Password reset email sent successfully to {email}")
                    return JsonResponse({
                        'status': 'success',
                        'message': 'Verification code has been sent to your email.',
                        'redirect_url': reverse('reset_password', kwargs={'email': user.email})
                    })
                else:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Failed to send verification code. Please try again.'
                    }, status=500)
            else:
                return JsonResponse({
                    'status': 'error',
                    'message': 'No user found with this email address.'
                })
        except Exception as e:
            logger.error(f"Error in forgot_password view: {str(e)}", exc_info=True)
            return JsonResponse({
                'status': 'error',
                'message': 'An error occurred. Please try again.'
            }, status=500)

    return JsonResponse({
        'status': 'error',
        'message': 'Invalid request method.'
    }, status=400)

def reset_password(request, email):
    if request.method == 'POST':
        otp = request.POST.get('otp')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        try:
            user = User.objects.get(email=email)
            otp_record = PasswordResetOTP.objects.filter(
                user=user,
                otp=otp,
                is_used=False
            ).order_by('-created_at').first()

            if not otp_record:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid verification code.'
                })

            if not otp_record.is_valid():
                return JsonResponse({
                    'status': 'error',
                    'message': 'Verification code has expired. Please request a new one.'
                })

            if new_password != confirm_password:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Passwords do not match.'
                })

            # Change password and mark OTP as used
            user.set_password(new_password)
            user.save()
            otp_record.mark_as_used()

            record_user_activity(
                user=user,
                action='PASSWORD_CHANGE',
                description='Password reset successfully',
                request=request
            )

            return JsonResponse({
                'status': 'success',
                'message': 'Password has been reset successfully. Please login with your new password.',
                'redirect_url': reverse('login')
            })

        except User.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'User not found.'
            })
        except Exception as e:
            logger.error(f"Error in reset_password view: {str(e)}", exc_info=True)
            return JsonResponse({
                'status': 'error',
                'message': 'An error occurred. Please try again.'
            }, status=500)

    return redirect('login')




def send_password_change_notification(user):
    subject = 'BMC MedForecast - Password Change Notification'

    html_content = f"""
    <div style="font-family: 'Google Sans',Roboto,Arial,sans-serif; max-width: 600px; margin: 0 auto; padding: 0; background-color: #f8f9fa;">
        <!-- Header with logo space -->
        <div style="background: linear-gradient(135deg, #1a73e8 0%, #0052cc 100%); padding: 30px; text-align: center; border-top-left-radius: 8px; border-top-right-radius: 8px;">
            <h1 style="color: #ffffff; font-size: 28px; margin: 0; font-weight: 500;">BMC MedForecast</h1>
            <p style="color: #ffffff; opacity: 0.9; margin: 10px 0 0 0;">Security Alert</p>
        </div>

        <!-- Main content -->
        <div style="background-color: #ffffff; padding: 40px 30px; border: 1px solid #dadce0; border-top: none; border-bottom: none;">
            <p style="color: #202124; font-size: 16px; margin-bottom: 25px; line-height: 1.6;">
                Hello <strong>{user.username}</strong>,
            </p>

            <p style="color: #202124; font-size: 16px; margin-bottom: 25px; line-height: 1.6;">
                This email is to confirm that your password was recently changed. The change was made on {timezone.now().strftime('%B %d, %Y at %I:%M %p')}.
            </p>

            <div style="background-color: #fef7f7; padding: 20px; border-radius: 8px; margin-top: 30px;">
                <p style="color: #d93025; font-size: 14px; margin: 0; line-height: 1.5;">
                    <strong>Security Notice:</strong> If you did not make this change, please contact the system administrator immediately and secure your account.
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div style="background-color: #ffffff; padding: 20px; text-align: center; border: 1px solid #dadce0; border-top: none; border-bottom-left-radius: 8px; border-bottom-right-radius: 8px;">
            <p style="color: #5f6368; font-size: 12px; margin: 0 0 10px 0;">
                This is an automated message. Please do not reply to this email.
            </p>
            <p style="color: #5f6368; font-size: 12px; margin: 0;">
                &copy; {timezone.now().year} BMC MedForecast. All rights reserved.
            </p>
        </div>
    </div>
    """

    plain_text = f"""
BMC MedForecast - Password Change Notification

Hello {user.username},

This email is to confirm that your password was recently changed.
The change was made on {timezone.now().strftime('%B %d, %Y at %I:%M %p')}.

Security Notice: If you did not make this change, please contact the system administrator immediately and secure your account.

Best regards,
BMC MedForecast Security Team

Note: This is an automated message. Please do not reply to this email.
    """

    try:
        send_mail(
            subject=subject,
            message=plain_text,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_content,
            fail_silently=False
        )
        return True
    except Exception as e:
        logger.error(f"Failed to send password change notification: {str(e)}", exc_info=True)
        return False

def reset_password(request, email):
    if request.method == 'POST':
        otp = request.POST.get('otp')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')

        try:
            user = User.objects.get(email=email)
            otp_record = PasswordResetOTP.objects.filter(
                user=user,
                otp=otp,
                is_used=False
            ).order_by('-created_at').first()

            if not otp_record:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Invalid verification code.'
                })

            if not otp_record.is_valid():
                return JsonResponse({
                    'status': 'error',
                    'message': 'Verification code has expired. Please request a new one.'
                })

            if new_password != confirm_password:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Passwords do not match.'
                })

            # Change password and mark OTP as used
            user.set_password(new_password)
            user.save()
            otp_record.mark_as_used()

            # Send password change notification
            send_password_change_notification(user)

            record_user_activity(
                user=user,
                action='PASSWORD_CHANGE',
                description='Password reset successfully',
                request=request
            )

            return JsonResponse({
                'status': 'success',
                'message': 'Password has been reset successfully. Please login with your new password.',
                'redirect_url': reverse('login')
            })

        except User.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'User not found.'
            })
        except Exception as e:
            logger.error(f"Error in reset_password view: {str(e)}", exc_info=True)
            return JsonResponse({
                'status': 'error',
                'message': 'An error occurred. Please try again.'
            }, status=500)

    return redirect('login')







from django.utils.crypto import get_random_string
from django.urls import reverse
from django.shortcuts import redirect
from django.contrib import messages

def send_verification_email(user, verification_code):
    """Send verification email with both link and code options"""
    subject = 'Verify Your Email - BMC MedForecast'

    site_url = getattr(settings, 'SITE_URL', 'http://127.0.0.1:8000').rstrip('/')

    # Generate verification token if not exists
    if not user.profile.email_verification_token:
        user.profile.email_verification_token = verification_code
        user.profile.email_verification_token_created = timezone.now()
        user.profile.save()

    verification_link = f"{site_url}/verify-email/?token={user.profile.email_verification_token}"
    verification_prompt_link = f"{site_url}/verify-email-prompt/"

    context = {
        'user': user,
        'verification_link': verification_link,
        'verification_prompt_link': verification_prompt_link,
        'verification_code': verification_code,
        'site_url': site_url
    }

    try:
        html_content = render_to_string('user_management/verify_email.html', context)
        plain_text = strip_tags(html_content)

        send_mail(
            subject=subject,
            message=plain_text,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[user.email],
            html_message=html_content,
            fail_silently=False
        )

        logger.info(f"Verification email sent successfully to {user.email}")
        return True
    except Exception as e:
        logger.error(f"Failed to send verification email: {str(e)}", exc_info=True)
        return False













from django.http import JsonResponse
from django.utils.timesince import timesince
from django.views.decorators.http import require_POST
from django.contrib.auth.decorators import login_required

# ... your existing imports and views ...

@login_required
def get_notifications(request):
    """API endpoint to get user notifications"""
    # First, get all notifications for the user
    all_notifications = UserNotification.objects.filter(user=request.user)

    # Get unread count before slicing
    unread_count = all_notifications.filter(is_read=False).count()

    # Get the latest 10 notifications
    notifications = all_notifications.order_by('-created_at')[:10]

    notifications_data = []

    for notification in notifications:
        notification_data = {
            'id': notification.id,
            'title': notification.title,
            'message': notification.message,
            'created_at': notification.created_at.isoformat(),
            'is_read': notification.is_read,
            'notification_type': notification.notification_type,
            'url': notification.url if hasattr(notification, 'url') else '#',
            'time_ago': timesince(notification.created_at)
        }

        # Add medicine_id if this notification is related to a medicine
        if hasattr(notification, 'medicine') and notification.medicine:
            notification_data['medicine_id'] = notification.medicine.id

        notifications_data.append(notification_data)

    return JsonResponse({
        'notifications': notifications_data,
        'unread_count': unread_count
    })



from django.views.decorators.http import require_POST, require_http_methods
@login_required
@require_http_methods(["DELETE"])
def delete_notification(request, notification_id):
    """Delete a single notification"""
    try:
        notification = get_object_or_404(
            UserNotification,
            id=notification_id,
            user=request.user
        )
        notification.delete()

        # Return updated unread count
        unread_count = UserNotification.objects.filter(
            user=request.user,
            is_read=False
        ).count()

        return JsonResponse({
            'status': 'success',
            'unread_count': unread_count
        })
    except Exception as e:

        return JsonResponse({
            'status': 'error',
            'message': 'Failed to delete notification'
        }, status=500)


# API endpoints for registration validation
def check_username(request):
    """Check if a username is available"""
    username = request.GET.get('username', '').strip()
    if not username:
        return JsonResponse({'exists': False})

    exists = User.objects.filter(username__iexact=username).exists()
    return JsonResponse({'exists': exists})


def check_email(request):
    """Check if an email is available"""
    email = request.GET.get('email', '').strip()
    if not email:
        return JsonResponse({'exists': False})

    exists = User.objects.filter(email__iexact=email).exists()
    return JsonResponse({'exists': exists})