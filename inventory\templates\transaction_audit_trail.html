{% extends 'base.html' %}

{% block content %}
<h1>Audit Trail</h1>
<a href="{% url 'home' %}">Back to Home</a>
<style>
    table {
        width: 100%;
        border-collapse: collapse;
    }

    th, td {
        border: 1px solid #ddd;
        padding: 8px;
    }

    th {
        background-color: #f2f2f2;
        text-align: left;
    }
</style>
<table>
    <thead>
        <tr>
            <th>Audit Trail ID</th>
            <th>Content Type</th>
            <th>Object ID</th>
            <th>Action</th>
            <th>Created At</th>
        </tr>
    </thead>
    <tbody>
        {% for audit_trail in audit_trails %}
        <tr>
            <td>{{ audit_trail.pk }}</td>
            <td>{{ audit_trail.content_type }}</td>
            <td>{{ audit_trail.object_id }}</td>
            <td>{{ audit_trail.get_action_display }}</td>
            <td>{{ audit_trail.created_at }}</td>
        </tr>
        {% endfor %}
    </tbody>
</table>
{% endblock %}