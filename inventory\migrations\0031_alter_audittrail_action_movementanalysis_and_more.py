# Generated by Django 5.2 on 2025-04-26 17:02

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0030_populate_generic_medicines'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterField(
            model_name='audittrail',
            name='action',
            field=models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('delete', 'Delete'), ('add_quantity', 'Add Quantity'), ('stock_movement', 'Stock Movement')], max_length=20),
        ),
        migrations.CreateModel(
            name='MovementAnalysis',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('monthly_movement_rate', models.FloatField(default=0, help_text='Average units moved per month')),
                ('quarterly_movement_rate', models.FloatField(default=0, help_text='Average units moved per quarter')),
                ('annual_movement_rate', models.FloatField(default=0, help_text='Average units moved per year')),
                ('movement_class', models.CharField(choices=[('A', 'Fast-moving (top 20%)'), ('B', 'Medium-moving (middle 30%)'), ('C', 'Slow-moving (bottom 50%)'), ('D', 'No movement (no transactions in 90 days)')], default='C', max_length=1)),
                ('last_movement_date', models.DateTimeField(blank=True, null=True)),
                ('days_since_last_movement', models.IntegerField(blank=True, null=True)),
                ('total_quantity_moved', models.IntegerField(default=0)),
                ('movement_frequency', models.FloatField(default=0, help_text='Average number of movements per month')),
                ('movement_trend', models.CharField(blank=True, help_text='Increasing, Decreasing, Stable, or Volatile', max_length=20, null=True)),
                ('trend_percentage', models.FloatField(blank=True, help_text='Percentage change in movement rate over the last 3 months', null=True)),
                ('top_brand', models.CharField(blank=True, max_length=255, null=True)),
                ('top_supplier', models.CharField(blank=True, max_length=255, null=True)),
                ('has_seasonality', models.BooleanField(default=False)),
                ('peak_season', models.CharField(blank=True, max_length=20, null=True)),
                ('medicine', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='movement_analysis', to='inventory.medicine')),
            ],
        ),
        migrations.CreateModel(
            name='StockMovement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('movement_date', models.DateTimeField(default=django.utils.timezone.now)),
                ('quantity', models.IntegerField(help_text='Positive for additions, negative for reductions')),
                ('movement_type', models.CharField(choices=[('purchase', 'Purchase from supplier'), ('sale', 'Sale to customer'), ('adjustment', 'Inventory adjustment'), ('return', 'Return to supplier'), ('expired', 'Expired medicine removal'), ('transfer', 'Internal transfer')], max_length=20)),
                ('reference_number', models.CharField(blank=True, help_text='Invoice, PO, or reference number', max_length=50, null=True)),
                ('notes', models.TextField(blank=True, help_text='Additional information about this movement')),
                ('brand', models.CharField(blank=True, help_text='Brand name of the medicine', max_length=255)),
                ('supplier', models.CharField(blank=True, help_text='Supplier or manufacturer', max_length=255)),
                ('unit_price', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('total_value', models.DecimalField(decimal_places=2, default=0.0, max_digits=10)),
                ('audit_trail', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='inventory.audittrail')),
                ('medicine', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_movements', to='inventory.medicine')),
                ('performed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-movement_date'],
                'indexes': [models.Index(fields=['medicine', 'movement_date'], name='inventory_s_medicin_81e229_idx'), models.Index(fields=['movement_type'], name='inventory_s_movemen_018f99_idx'), models.Index(fields=['brand'], name='inventory_s_brand_8740bb_idx'), models.Index(fields=['supplier'], name='inventory_s_supplie_e88e5d_idx')],
            },
        ),
    ]
