# Generated by Django 4.2.9 on 2025-04-03 13:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('user_management', '0006_userprofile_totp_verified'),
    ]

    operations = [
        migrations.CreateModel(
            name='ArchivedUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.Char<PERSON>ield(max_length=150)),
                ('email', models.EmailField(max_length=254)),
                ('first_name', models.Char<PERSON>ield(blank=True, max_length=150)),
                ('last_name', models.CharField(blank=True, max_length=150)),
                ('profile_data', models.JSONField(default=dict)),
                ('archived_at', models.DateT<PERSON><PERSON><PERSON>(auto_now_add=True)),
                ('archived_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Archived User',
                'verbose_name_plural': 'Archived Users',
                'ordering': ['-archived_at'],
            },
        ),
    ]
