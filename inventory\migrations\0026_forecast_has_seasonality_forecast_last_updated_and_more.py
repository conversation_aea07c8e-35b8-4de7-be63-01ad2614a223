# Generated by Django 4.2.9 on 2025-04-13 14:36

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0025_medicine_medicine_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='forecast',
            name='has_seasonality',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='forecast',
            name='last_updated',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='forecast',
            name='peak_period',
            field=models.CharField(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='forecast',
            name='seasonal_pattern',
            field=models.Char<PERSON>ield(blank=True, max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='forecast',
            name='trend_direction',
            field=models.CharField(blank=True, max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='forecast',
            name='trend_strength',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='forecast',
            name='forecast_method',
            field=models.CharField(blank=True, choices=[('arima', 'ARIMA'), ('linear', 'Linear Regression'), ('polynomial', 'Polynomial Regression'), ('moving_avg', 'Moving Average'), ('holtwinters', 'Holt-Winters'), ('ensemble', 'Ensemble')], max_length=50, null=True),
        ),
        migrations.AddIndex(
            model_name='forecast',
            index=models.Index(fields=['forecast_method'], name='inventory_f_forecas_936829_idx'),
        ),
        migrations.AddIndex(
            model_name='forecast',
            index=models.Index(fields=['accuracy'], name='inventory_f_accurac_39b2b0_idx'),
        ),
    ]
