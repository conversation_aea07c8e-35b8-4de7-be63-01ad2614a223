from django.http import HttpResponse
from django.contrib.auth.decorators import login_required
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from django.utils import timezone

@login_required
def simple_excel_view(request):
    """Generate a simple Excel file"""
    # Create workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Simple Excel"
    
    # Add title
    ws['A1'] = "Simple Excel Report"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = Alignment(horizontal="center")
    
    # Add data
    ws['A3'] = "Generated on:"
    ws['B3'] = timezone.now().strftime('%Y-%m-%d %H:%M')
    
    ws['A4'] = "Generated by:"
    ws['B4'] = request.user.get_full_name() or request.user.username
    
    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    response['Content-Disposition'] = f'attachment; filename="simple_excel_{timestamp}.xlsx"'
    wb.save(response)
    return response
