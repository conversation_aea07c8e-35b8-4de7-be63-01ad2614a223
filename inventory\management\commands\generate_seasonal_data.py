import random
import math
from datetime import datetime, timedelta
from decimal import Decimal

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from inventory.models import Medicine, Transaction, EmailNotificationSetting
from django.db.models import Q
import logging

logger = logging.getLogger(__name__)

# Customer types
CUSTOMER_TYPES = ['in_patient', 'out_patient', 'emergency', 'pharmacy']

# Customer names (realistic Filipino names)
CUSTOMER_NAMES = [
    "Juan Dela Cruz", "Maria Santos", "<PERSON>", "<PERSON> Lim", "<PERSON>",
    "<PERSON> Mendoza", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
]

def generate_patient_number():
    """Generate a realistic patient number"""
    year = random.randint(2023, 2025)
    number = random.randint(10000, 99999)
    return f"P-{year}-{number}"

def create_transaction(medicine, date, transaction_type, quantity):
    """Create a transaction for a medicine"""
    try:
        # For purchases, use a default customer type and name
        if transaction_type == 'purchase':
            customer_name = "Medical Supplier Inc."
            patient_number = None
            customer_type = 'pharmacy'  # Use pharmacy as default for purchases
        else:  # For sales
            customer_name = random.choice(CUSTOMER_NAMES)
            patient_number = generate_patient_number()
            customer_type = random.choice(CUSTOMER_TYPES)
        
        # Calculate total amount based on medicine price
        total_amount = quantity * medicine.price
        
        # Create the transaction
        transaction = Transaction.objects.create(
            medicine=medicine,
            transaction_date=date,
            quantity=quantity,
            transaction_type=transaction_type,
            customer_name=customer_name,
            patient_number=patient_number,
            customer_type=customer_type,
            total_amount=total_amount
        )
        
        # Update medicine quantity
        if transaction_type == 'purchase':
            medicine.quantity += quantity
        else:  # sale
            medicine.quantity = max(0, medicine.quantity - quantity)
        
        medicine.save(update_fields=['quantity'])  # Only update quantity field to avoid notifications
        
        return transaction
    except Exception as e:
        logger.error(f"Error creating transaction for {medicine.name}: {str(e)}")
        return None

def generate_seasonal_pattern(base_demand, date, medicine):
    """Generate a seasonal pattern based on the medicine category and date"""
    month = date.month
    category = medicine.category.lower() if medicine.category else ""
    
    # Different categories have different seasonal patterns
    if "respiratory" in category or "antihistamine" in category or medicine.name in ["Cetirizine", "Salbutamol"]:
        # Respiratory/allergy medicines peak in spring (March-May) and fall (Sep-Nov)
        if month in [3, 4, 5, 9, 10, 11]:
            return base_demand * random.uniform(1.3, 1.8)
        return base_demand * random.uniform(0.7, 1.0)
    
    elif "analgesic" in category or "antibiotic" in category or medicine.name in ["Paracetamol", "Ibuprofen", "Amoxicillin"]:
        # Pain/fever/infection medicines peak in winter (Dec-Feb) and summer (Jun-Aug)
        if month in [12, 1, 2, 6, 7, 8]:
            return base_demand * random.uniform(1.2, 1.6)
        return base_demand * random.uniform(0.8, 1.1)
    
    elif "antidiabetic" in category or "cardiovascular" in category or medicine.name in ["Metformin", "Atorvastatin"]:
        # Chronic disease medicines have stable demand with slight increase after holidays
        if month in [1, 2, 7, 8]:
            return base_demand * random.uniform(1.1, 1.3)
        return base_demand * random.uniform(0.9, 1.1)
    
    # Default pattern with slight randomness
    seasonal_factor = 1.0 + 0.2 * math.sin(month * math.pi / 6)
    return base_demand * seasonal_factor * random.uniform(0.9, 1.1)

class Command(BaseCommand):
    help = 'Generate seasonal transaction data for existing medicines'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing transactions before generating new data',
        )
        parser.add_argument(
            '--start-date',
            type=str,
            default='2023-01-01',
            help='Start date for transactions (YYYY-MM-DD)',
        )
        parser.add_argument(
            '--end-date',
            type=str,
            default='2025-06-01',
            help='End date for transactions (YYYY-MM-DD)',
        )
        parser.add_argument(
            '--density',
            type=float,
            default=0.7,
            help='Density of transactions (0.0-1.0)',
        )

    def handle(self, *args, **options):
        # Parse date arguments
        try:
            start_date = datetime.strptime(options['start_date'], '%Y-%m-%d')
            start_date = timezone.make_aware(start_date)
            
            end_date = datetime.strptime(options['end_date'], '%Y-%m-%d')
            end_date = timezone.make_aware(end_date)
            
            density = min(1.0, max(0.1, options['density']))
        except ValueError:
            self.stdout.write(self.style.ERROR('Invalid date format. Use YYYY-MM-DD.'))
            return
        
        # Clear existing transactions if requested
        if options['clear']:
            self.stdout.write(self.style.WARNING('Clearing existing transactions...'))
            Transaction.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing transactions cleared.'))
            
            # Reset all medicine quantities to zero
            Medicine.objects.all().update(quantity=0)
            self.stdout.write(self.style.SUCCESS('Reset all medicine quantities to zero.'))
        
        # Get all medicines
        medicines = list(Medicine.objects.all())
        if not medicines:
            self.stdout.write(self.style.ERROR('No medicines found in the database. Please add medicines first.'))
            return
        
        self.stdout.write(self.style.SUCCESS(f'Found {len(medicines)} medicines in the database.'))
        
        # Temporarily disable email notifications
        original_settings = {}
        try:
            # Store original notification settings
            for setting in EmailNotificationSetting.objects.all():
                original_settings[setting.id] = setting.is_enabled
                setting.is_enabled = False
                setting.save()
            
            self.stdout.write(self.style.WARNING('Email notifications temporarily disabled during data generation.'))
            
            # Generate transactions from start_date to end_date
            current_date = start_date
            transaction_count = 0
            purchase_count = 0
            sale_count = 0
            
            # First, create initial stock for all medicines
            self.stdout.write(self.style.NOTICE('Creating initial stock for all medicines...'))
            for medicine in medicines:
                initial_stock = random.randint(50, 200)
                create_transaction(
                    medicine=medicine,
                    date=start_date - timedelta(days=1),
                    transaction_type='purchase',
                    quantity=initial_stock
                )
            
            # Use transaction atomic to speed up the process
            with transaction.atomic():
                while current_date <= end_date:
                    # For each day, create some transactions
                    for medicine in medicines:
                        # Determine if we create a transaction today (based on density)
                        if random.random() < density:
                            # Determine transaction type (80% sales, 20% purchases)
                            transaction_type = 'sale' if random.random() < 0.8 else 'purchase'
                            
                            # Base quantity
                            base_quantity = random.randint(5, 15)
                            
                            # Apply seasonal pattern
                            adjusted_quantity = int(generate_seasonal_pattern(
                                base_quantity, 
                                current_date,
                                medicine
                            ))
                            
                            # For purchases, make larger quantities
                            if transaction_type == 'purchase':
                                adjusted_quantity *= random.randint(3, 6)
                                purchase_count += 1
                            else:
                                sale_count += 1
                            
                            # Create the transaction with a random time during business hours
                            trans_time = timedelta(
                                hours=random.randint(8, 17),
                                minutes=random.randint(0, 59),
                                seconds=random.randint(0, 59)
                            )
                            
                            trans = create_transaction(
                                medicine=medicine,
                                date=current_date + trans_time,
                                transaction_type=transaction_type,
                                quantity=adjusted_quantity
                            )
                            
                            if trans:
                                transaction_count += 1
                    
                    # Move to next day
                    current_date += timedelta(days=1)
                    
                    # Print progress every month
                    if current_date.day == 1:
                        self.stdout.write(f"Generated transactions for {current_date.strftime('%B %Y')}")
            
            self.stdout.write(self.style.SUCCESS(f"Data generation complete!"))
            self.stdout.write(f"Created {transaction_count} transactions ({purchase_count} purchases, {sale_count} sales)")
            self.stdout.write(f"Updated stock levels for {len(medicines)} medicines")
            
            # Show current stock levels for a few medicines
            sample_size = min(5, len(medicines))
            sample_medicines = random.sample(medicines, sample_size)
            
            self.stdout.write("\nCurrent stock levels for sample medicines:")
            for med in sample_medicines:
                self.stdout.write(f"{med.name}: {med.quantity} units")
                
        finally:
            # Restore original notification settings
            for setting_id, is_enabled in original_settings.items():
                try:
                    setting = EmailNotificationSetting.objects.get(id=setting_id)
                    setting.is_enabled = is_enabled
                    setting.save()
                except EmailNotificationSetting.DoesNotExist:
                    pass
            
            self.stdout.write(self.style.SUCCESS('Email notification settings restored.'))
