# Generated by Django 4.2.9 on 2025-04-02 13:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_management', '0003_passwordresetotp'),
    ]

    operations = [
        migrations.AddField(
            model_name='passwordresetotp',
            name='otp_type',
            field=models.CharField(choices=[('email', 'Email OTP'), ('totp', 'Time-based OTP')], default='email', max_length=5),
        ),
    ]
