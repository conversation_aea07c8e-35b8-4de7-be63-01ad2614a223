﻿from django.db import models
from django.contrib.contenttypes.fields import GenericForeign<PERSON>ey
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.contrib.auth import get_user_model
from user_management.models import UserNotification
import logging

logger = logging.getLogger(__name__)

User = get_user_model()

class EmailNotificationSetting(models.Model):
    """Settings for email notifications"""
    LOW_STOCK = 'low_stock'
    EXPIRING_SOON = 'expiring_soon'
    OUT_OF_STOCK = 'out_of_stock'
    PRICE_CHANGE = 'price_change'

    NOTIFICATION_TYPES = [
        (LOW_STOCK, 'Low Stock Alert'),
        (EXPIRING_SOON, 'Expiring Soon Alert'),
        (OUT_OF_STOCK, 'Out of Stock Alert'),
        (PRICE_CHANGE, 'Price Change Alert'),
    ]

    notification_type = models.Char<PERSON>ield(max_length=20, choices=NOTIFICATION_TYPES)
    is_enabled = models.<PERSON><PERSON>anField(default=True)
    recipients = models.ManyToManyField(User, related_name='email_notifications')

    class Meta:
        unique_together = ['notification_type']

    def __str__(self):
        return f"{self.get_notification_type_display()} ({self.is_enabled})"

def create_low_stock_notification(medicine):
    """Create in-app notifications for low stock alerts"""
    try:
        notification_setting = EmailNotificationSetting.objects.get(
            notification_type=EmailNotificationSetting.LOW_STOCK,
            is_enabled=True
        )

        UserNotification.objects.bulk_create([
            UserNotification(
                user_id=user_id,
                title=f"Low Stock Alert: {medicine.name}",
                message=f"Current stock ({medicine.quantity}) is below reorder level ({medicine.reorder_level})",
                notification_type='WARNING'
            ) for user_id in notification_setting.recipients.values_list('id', flat=True)
        ])

        logger.info(f"Low stock in-app notifications created for {medicine.name}")
    except EmailNotificationSetting.DoesNotExist:
        pass
    except Exception as e:
        logger.error(f"Failed to create low stock notifications: {str(e)}")

def create_out_of_stock_notification(medicine):
    """Create in-app notifications for out of stock alerts"""
    try:
        notification_setting = EmailNotificationSetting.objects.get(
            notification_type=EmailNotificationSetting.OUT_OF_STOCK,
            is_enabled=True
        )

        UserNotification.objects.bulk_create([
            UserNotification(
                user_id=user_id,
                title=f"Out of Stock Alert: {medicine.name}",
                message=f"{medicine.name} is completely out of stock and needs immediate reordering.",
                notification_type='DANGER'
            ) for user_id in notification_setting.recipients.values_list('id', flat=True)
        ])

        logger.info(f"Out of stock notifications created for {medicine.name}")
    except EmailNotificationSetting.DoesNotExist:
        pass
    except Exception as e:
        logger.error(f"Failed to create out of stock notifications: {str(e)}")

def create_price_change_notification(medicine, old_price, new_price):
    """Create in-app notifications for medicine price changes"""
    logger.info(f"Creating price change notification for {medicine.name}: old price={old_price}, new price={new_price}")
    try:
        notification_setting = EmailNotificationSetting.objects.get(
            notification_type=EmailNotificationSetting.PRICE_CHANGE,
            is_enabled=True
        )
        logger.info(f"Found notification setting with {notification_setting.recipients.count()} recipients")

        UserNotification.objects.bulk_create([
            UserNotification(
                user_id=user_id,
                title=f"Price Change Alert: {medicine.name}",
                message=f"The price of {medicine.name} has been updated from ₱{old_price} to ₱{new_price}.",
                notification_type='INFO'
            ) for user_id in notification_setting.recipients.values_list('id', flat=True)
        ])

        logger.info(f"Price change notifications created for {medicine.name}")
    except EmailNotificationSetting.DoesNotExist:
        pass
    except Exception as e:
        logger.error(f"Failed to create price change notifications: {str(e)}")

def create_expiring_medicine_notification(medicine):
    """Create in-app notifications for medicines that are expiring soon or already expired"""
    if not medicine.expiration_date:
        return

    days_until_expiry = medicine.days_until_expiration()

    try:
        notification_setting = EmailNotificationSetting.objects.get(
            notification_type=EmailNotificationSetting.EXPIRING_SOON,
            is_enabled=True
        )

        # Create notifications only if expiring soon (within 30 days) or already expired
        if days_until_expiry is not None and days_until_expiry <= 30:
            if days_until_expiry < 0:
                # Already expired
                notification_type = 'DANGER'
                title = f"Expired Medicine: {medicine.name}"
                message = f"{medicine.name} has expired on {medicine.expiration_date.strftime('%Y-%m-%d')}. Please dispose properly."
            else:
                # Expiring soon
                notification_type = 'WARNING'
                title = f"Expiring Soon: {medicine.name}"
                message = f"{medicine.name} will expire in {days_until_expiry} days on {medicine.expiration_date.strftime('%Y-%m-%d')}."

            UserNotification.objects.bulk_create([
                UserNotification(
                    user_id=user_id,
                    title=title,
                    message=message,
                    notification_type=notification_type
                ) for user_id in notification_setting.recipients.values_list('id', flat=True)
            ])

            logger.info(f"Expiration notifications created for {medicine.name}")
    except EmailNotificationSetting.DoesNotExist:
        pass
    except Exception as e:
        logger.error(f"Failed to create expiration notifications: {str(e)}")


class GenericMedicine(models.Model):
    """
    Represents a generic medicine category (e.g., Paracetamol) regardless of brand or supplier.
    Used for forecasting purposes.
    """
    name = models.CharField(max_length=255, unique=True)
    category = models.CharField(max_length=255)
    description = models.TextField(blank=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "Generic Medicine"
        verbose_name_plural = "Generic Medicines"
        ordering = ['name']

class Medicine(models.Model):
    MEDICINE_TYPES = [
        ('tablet', 'Tablet'),
        ('capsule', 'Capsule'),
        ('syrup', 'Syrup'),
        ('injection', 'Injection'),
        ('cream', 'Cream/Ointment'),
        ('drops', 'Drops'),
        ('inhaler', 'Inhaler'),
        ('powder', 'Powder'),
        ('suppository', 'Suppository'),
        ('patch', 'Patch'),
        ('other', 'Other')
    ]

    name = models.CharField(max_length=255)
    description = models.TextField()
    quantity = models.IntegerField()
    reorder_level = models.IntegerField()
    reorder_quantity = models.IntegerField()
    category = models.CharField(max_length=255)
    medicine_type = models.CharField(max_length=20, choices=MEDICINE_TYPES, default='tablet', help_text="Type of medicine formulation")
    price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00, help_text="Price in Philippine Peso (₱)")
    expiration_date = models.DateField(null=True, blank=True, help_text="Medicine expiration date")

    # New fields for brand and supplier tracking
    generic_medicine = models.ForeignKey(GenericMedicine, on_delete=models.CASCADE, related_name='variants', null=True, blank=True,
                                        help_text="The generic medicine category this specific medicine belongs to")
    brand = models.CharField(max_length=255, blank=True, help_text="Brand name (e.g., Tylenol, Panadol)")
    supplier = models.CharField(max_length=255, blank=True, help_text="Supplier or manufacturer (e.g., Johnson & Johnson, GlaxoSmithKline)")

    def send_low_stock_notification(self):
        """Send email notification when stock is low"""
        if self.quantity <= self.reorder_level:
            try:
                notification_setting = EmailNotificationSetting.objects.get(
                    notification_type=EmailNotificationSetting.LOW_STOCK,
                    is_enabled=True
                )

                recipient_emails = notification_setting.recipients.filter(
                    is_active=True
                ).values_list('email', flat=True)

                if recipient_emails:
                    subject = f'Low Stock Alert: {self.name}'

                    html_message = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body, table, td {{
            font-family: Arial, Helvetica, sans-serif !important;
        }}

        body {{
            margin: 0;
            padding: 0;
            background-color: #f2f2f2;
            width: 100%;
        }}

        .email-content {{
            line-height: 1.5;
        }}
    </style>
    <!--[if mso]>
    <style type="text/css">
        body, table, td {{
            font-family: Arial, Helvetica, sans-serif !important;
        }}
    </style>
    <![endif]-->
</head>
<body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #f2f2f2; width: 100%;">
    <!-- Preheader text (invisible but shows in inbox preview) -->
    <div style="display: none; max-height: 0px; overflow: hidden;">
        Low stock alert: {self.name} - Current quantity: {self.quantity}
    </div>

    <center style="width: 100%; background-color: #f2f2f2; padding: 20px 0;">
        <!-- Outlook wrapper -->
        <!--[if mso]>
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="600" align="center">
        <tr>
        <td>
        <![endif]-->

    <center style="width: 100%; background-color: #f2f2f2; padding: 20px 0;">
        <!-- Outlook wrapper -->
        <!--[if mso]>
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="600" align="center">
        <tr>
        <td>
        <![endif]-->

        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
            <!-- Top gradient bar -->
            <tr>
                <td align="center" valign="top" style="padding: 0;">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td style="background: #2c3e50; background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%); height: 8px; font-size: 0; line-height: 0;">&nbsp;</td>
                        </tr>
                    </table>
                </td>
            </tr>

            <!-- Header -->
            <tr>
                <td align="center" style="padding: 30px 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
                    <h1 style="color: #2c3e50; font-size: 24px; margin: 0; font-weight: 600; line-height: 28px;">LOW STOCK ALERT</h1>
                    <p style="color: #3498db; margin: 10px 0 0 0; font-size: 18px; font-weight: 500; line-height: 22px;">{self.name}</p>
                </td>
            </tr>

            <!-- Stock Information Alert -->
            <tr>
                <td style="padding: 20px;">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8f9fa; border-radius: 4px; border-left: 4px solid #3498db;">
                        <tr>
                            <td style="padding: 15px;">
                                <h2 style="margin: 0; color: #2c3e50; font-size: 18px; line-height: 22px;">Stock Level Critical</h2>
                                <p style="margin: 10px 0 0 0; color: #333333; line-height: 20px;">
                                    Immediate restocking required
                                </p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <!-- Stock Details - made responsive -->
            <tr>
                <td style="padding: 0 20px;">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <!-- Current Stock -->
                            <td width="50%" style="padding: 10px; vertical-align: top;">
                                <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8f9fa; border-radius: 4px; border: 1px solid #e0e0e0;">
                                    <tr>
                                        <td style="padding: 15px; text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #e74c3c; line-height: 28px;">{self.quantity}</div>
                                            <div style="color: #666666; font-size: 14px; line-height: 18px;">Current Stock</div>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                            <!-- Reorder Level -->
                            <td width="50%" style="padding: 10px; vertical-align: top;">
                                <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8f9fa; border-radius: 4px; border: 1px solid #e0e0e0;">
                                    <tr>
                                        <td style="padding: 15px; text-align: center;">
                                            <div style="font-size: 24px; font-weight: bold; color: #3498db; line-height: 28px;">{self.reorder_level}</div>
                                            <div style="color: #666666; font-size: 14px; line-height: 18px;">Reorder Level</div>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <!-- Additional Information -->
            <tr>
                <td style="padding: 20px;">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f8f9fa; border-radius: 4px;">
                        <tr>
                            <td style="padding: 15px;">
                                <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #ffffff;">
                                    <tr>
                                        <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">
                                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td style="color: #666666; line-height: 20px;">Category:</td>
                                                    <td style="text-align: right; color: #333333; line-height: 20px;">{self.category}</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 8px 0; border-bottom: 1px solid #dee2e6;">
                                            <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                                <tr>
                                                    <td style="color: #666666; line-height: 20px;">Recommended Order:</td>
                                                    <td style="text-align: right; color: #333333; line-height: 20px;">{self.reorder_quantity}</td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <!-- Footer -->
            <tr>
                <td style="padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
                    <p style="margin: 0; color: #666666; font-size: 14px; line-height: 18px;">
                        BMC MedForecast
                    </p>
                    <p style="margin: 8px 0 0 0; color: #666666; font-size: 12px; line-height: 16px;">
                        This is an automated inventory alert from BMC MedForecast
                    </p>
                </td>
            </tr>

            <!-- Bottom gradient bar -->
            <tr>
                <td align="center" valign="top" style="padding: 0;">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td style="background: #2c3e50; background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%); height: 8px; font-size: 0; line-height: 0;">&nbsp;</td>
                        </tr>
                    </table>
                </td>
            </tr>
        </table>

        <!--[if mso]>
        </td>
        </tr>
        </table>
        <![endif]-->
    </center>
</body>
</html>
                    """

                    send_mail(
                        subject=subject,
                        message='',
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=list(recipient_emails),
                        html_message=html_message,
                        fail_silently=False
                    )

                    # Create in-app notifications using the utility function
                    create_low_stock_notification(self)

                    logger.info(f"Low stock notification sent for {self.name} to {len(recipient_emails)} recipients")

            except EmailNotificationSetting.DoesNotExist:
                pass
            except Exception as e:
                logger.error(f"Failed to send email notification: {str(e)}")

    def send_price_change_notification(self, old_price, new_price):
        """Send email notification when medicine price changes"""
        logger.info(f"Sending price change notification email for {self.name}: old price={old_price}, new price={new_price}")
        try:
            notification_setting = EmailNotificationSetting.objects.get(
                notification_type=EmailNotificationSetting.PRICE_CHANGE,
                is_enabled=True
            )
            logger.info(f"Found notification setting with {notification_setting.recipients.count()} recipients")

            recipient_emails = notification_setting.recipients.filter(
                is_active=True
            ).values_list('email', flat=True)

            if recipient_emails:
                subject = f'Price Change Alert: {self.name}'

                # Define specific colors for price change alerts
                colors = {
                    'primary': '#3498db',      # Blue for information
                    'secondary': '#5dade2',    # Lighter blue
                    'background': '#ebf5fb',   # Very light blue
                    'border': '#3498db',       # Blue border
                }

                # Determine if price increased or decreased
                price_change_type = "increased" if new_price > old_price else "decreased"
                price_change_icon = "↑" if new_price > old_price else "↓"
                price_change_color = "#dc3545" if new_price > old_price else "#28a745"  # Red for increase, green for decrease

                html_message = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body, table, td {{
            font-family: Arial, Helvetica, sans-serif !important;
        }}
        .email-container {{
            max-width: 600px;
            margin: auto;
        }}
        .button {{
            background-color: {colors['primary']};
            border: none;
            color: white;
            padding: 12px 24px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 4px 2px;
            cursor: pointer;
            border-radius: 4px;
        }}
    </style>
</head>
<body style="margin: 0; padding: 0; background-color: #f9f9f9;">
    <center class="email-container">
        <!--[if mso]>
        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="600" align="center">
        <tr>
        <td>
        <![endif]-->

        <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="max-width: 600px; border-radius: 8px; overflow: hidden; margin-top: 20px; margin-bottom: 20px; box-shadow: 0 4px 6px rgba(0,0,0,0.1);">
            <!-- Top gradient bar -->
            <tr>
                <td style="background: linear-gradient(135deg, {colors['primary']} 0%, {colors['secondary']} 100%);
                           height: 8px; border-radius: 8px 8px 0 0;">&nbsp;</td>
            </tr>

            <!-- Header -->
            <tr>
                <td style="background-color: {colors['background']}; padding: 20px; text-align: center; border-bottom: 1px solid {colors['border']};">
                    <h1 style="margin: 0; color: {colors['primary']}; font-size: 24px;">Price Change Alert</h1>
                </td>
            </tr>

            <!-- Content -->
            <tr>
                <td style="background-color: white; padding: 20px;">
                    <p style="font-size: 16px; line-height: 1.5; color: #333;">
                        The price of <strong>{self.name}</strong> has {price_change_type}.
                    </p>

                    <table role="presentation" cellspacing="0" cellpadding="0" border="0" width="100%" style="margin: 20px 0; border: 1px solid #eee; border-radius: 4px; overflow: hidden;">
                        <tr style="background-color: #f8f9fa;">
                            <th style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">Medicine</th>
                            <th style="padding: 12px; text-align: right; border-bottom: 1px solid #eee;">Old Price</th>
                            <th style="padding: 12px; text-align: right; border-bottom: 1px solid #eee;">New Price</th>
                            <th style="padding: 12px; text-align: right; border-bottom: 1px solid #eee;">Change</th>
                        </tr>
                        <tr>
                            <td style="padding: 12px; text-align: left; border-bottom: 1px solid #eee;">{self.name}</td>
                            <td style="padding: 12px; text-align: right; border-bottom: 1px solid #eee;">₱{old_price}</td>
                            <td style="padding: 12px; text-align: right; border-bottom: 1px solid #eee;">₱{new_price}</td>
                            <td style="padding: 12px; text-align: right; border-bottom: 1px solid #eee; color: {price_change_color}; font-weight: bold;">
                                {price_change_icon} {abs(float(new_price) - float(old_price)):.2f}
                            </td>
                        </tr>
                    </table>

                    <p style="font-size: 16px; line-height: 1.5; color: #333;">
                        Please update any relevant records or inform customers as needed.
                    </p>
                </td>
            </tr>

            <!-- Footer -->
            <tr>
                <td style="background-color: {colors['background']}; padding: 20px; text-align: center; color: #666; font-size: 14px; border-top: 1px solid {colors['border']};">
                    <p style="margin: 0;">This is an automated notification from BMC MedForecast System.</p>
                    <p style="margin: 10px 0 0 0;">Please do not reply to this email.</p>
                </td>
            </tr>

            <!-- Bottom gradient bar -->
            <tr>
                <td style="background: linear-gradient(135deg, {colors['primary']} 0%, {colors['secondary']} 100%);
                           height: 8px; border-radius: 0 0 8px 8px;">&nbsp;</td>
            </tr>
        </table>

        <!--[if mso]>
        </td>
        </tr>
        </table>
        <![endif]-->
    </center>
</body>
</html>
                """

                send_mail(
                    subject=subject,
                    message='',
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=list(recipient_emails),
                    html_message=html_message,
                    fail_silently=False
                )

                # Create in-app notifications using the utility function
                create_price_change_notification(self, old_price, new_price)

                logger.info(f"Price change notification sent for {self.name} to {len(recipient_emails)} recipients")

        except EmailNotificationSetting.DoesNotExist:
            pass
        except Exception as e:
            logger.error(f"Failed to send price change email notification: {str(e)}")

    def send_out_of_stock_notification(self):
        """Send email notification when stock is completely out"""
        if self.quantity == 0:
            try:
                notification_setting = EmailNotificationSetting.objects.get(
                    notification_type=EmailNotificationSetting.OUT_OF_STOCK,
                    is_enabled=True
                )

                recipient_emails = notification_setting.recipients.filter(
                    is_active=True
                ).values_list('email', flat=True)

                if recipient_emails:
                    subject = f'Out of Stock Alert: {self.name}'

                                    # Define specific colors for out-of-stock alerts
                # Define specific colors for out-of-stock alerts
                    colors = {
                        'primary': '#dc3545',      # Red for danger
                        'secondary': '#ff6b6b',    # Lighter red
                        'background': '#f8d7da',   # Very light red
                        'border': '#dc3545',       # Red border
                    }

                    html_message = f"""

<!DOCTYPE html>
<html lang="en">
<!-- ... [head section remains the same] ... -->
<body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #f2f2f2; width: 100%;">
    <div style="display: none; max-height: 0px; overflow: hidden;">
        ⚠️ URGENT: {self.name} is out of stock - Immediate action required
    </div>

    <center style="width: 100%; background-color: #f2f2f2; padding: 20px 0;">
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
               style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">

            <!-- Top gradient bar - Distinct red gradient -->
            <tr>
                <td style="background: linear-gradient(135deg, {colors['primary']} 0%, {colors['secondary']} 100%);
                           height: 8px; border-radius: 8px 8px 0 0;">&nbsp;</td>
            </tr>

            <!-- Header - More prominent warning -->
            <tr>
                <td align="center" style="padding: 30px 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
                    <div style="color: {colors['primary']}; font-size: 32px; margin-bottom: 10px;">⚠️</div>
                    <h1 style="color: {colors['primary']}; font-size: 24px; margin: 0; font-weight: 600;">CRITICAL STOCK ALERT</h1>
                    <p style="color: {colors['primary']}; margin: 10px 0 0 0; font-size: 18px; font-weight: 500;">{self.name}</p>
                </td>
            </tr>

            <!-- Alert Box - More prominent -->
            <tr>
                <td style="padding: 20px;">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
                           style="background-color: {colors['background']}; border-radius: 4px; border: 2px solid {colors['border']};">
                        <tr>
                            <td style="padding: 20px; text-align: center;">
                                <h2 style="margin: 0; color: {colors['primary']}; font-size: 20px;">STOCK DEPLETED</h2>
                                <p style="margin: 15px 0 0 0; color: #333333; font-size: 16px;">
                                    This item requires immediate reordering to prevent supply chain disruption.
                                </p>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <!-- Stock Details -->
            <tr>
                <td style="padding: 0 20px;">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td width="100%" style="padding: 10px;">
                                <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
                                       style="background-color: #f8f9fa; border-radius: 4px; border: 1px solid #e0e0e0;">
                                    <tr>
                                        <td style="padding: 20px; text-align: center;">
                                            <div style="font-size: 48px; font-weight: bold; color: {colors['primary']};">{self.quantity}</div>
                                            <div style="color: #666666; font-size: 16px; margin-top: 5px;">Current Stock</div>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <!-- Reorder Information -->
            <tr>
                <td style="padding: 20px;">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
                           style="background-color: #f8f9fa; border-radius: 4px; border: 1px solid #e0e0e0;">
                        <tr>
                            <td style="padding: 20px;">
                                <h3 style="margin: 0 0 15px 0; color: #2c3e50; font-size: 18px;">Required Action</h3>
                                <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <td style="padding: 10px 0; border-bottom: 1px solid #dee2e6;">
                                            <span style="color: #666666;">Recommended Order Quantity:</span>
                                            <span style="float: right; color: {colors['primary']}; font-weight: bold;">{self.reorder_quantity}</span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td style="padding: 10px 0;">
                                            <span style="color: #666666;">Reorder Level:</span>
                                            <span style="float: right; color: #666666;">{self.reorder_level}</span>
                                        </td>
                                    </tr>
                                </table>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <!-- Footer -->
            <tr>
                <td style="padding: 20px; text-align: center; border-top: 1px solid #dee2e6;">
                    <p style="margin: 0; color: #666666; font-size: 14px;">BMC MedForecast</p>
                    <p style="margin: 8px 0 0 0; color: #666666; font-size: 12px;">
                        This is an automated critical stock alert - Immediate attention required
                    </p>
                </td>
            </tr>

            <!-- Bottom gradient bar -->
            <tr>
                <td style="background: linear-gradient(135deg, {colors['primary']} 0%, {colors['secondary']} 100%);
                           height: 8px; border-radius: 0 0 8px 8px;">&nbsp;</td>
            </tr>
        </table>
    </center>
</body>
</html>
                    """

                    send_mail(
                        subject=subject,
                        message='',
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=list(recipient_emails),
                        html_message=html_message,
                        fail_silently=False
                    )

                    # Create in-app notifications using the utility function
                    create_out_of_stock_notification(self)

                    logger.info(f"Out of stock notification sent for {self.name} to {len(recipient_emails)} recipients")

            except EmailNotificationSetting.DoesNotExist:
                pass
            except Exception as e:
                logger.error(f"Failed to send out of stock email notification: {str(e)}")

    def send_expiring_notification(self):
        """Send email notification when medicine is expiring soon or expired"""
        if not self.expiration_date:
            return

        days_until_expiry = self.days_until_expiration()

        if days_until_expiry is not None and days_until_expiry <= 30:
            try:
                notification_setting = EmailNotificationSetting.objects.get(
                    notification_type=EmailNotificationSetting.EXPIRING_SOON,
                    is_enabled=True
                )

                recipient_emails = notification_setting.recipients.filter(
                    is_active=True
                ).values_list('email', flat=True)

                if recipient_emails:
                    if days_until_expiry < 0:
                        subject = f'Expired Medicine Alert: {self.name}'
                        status_text = "has expired"
                        alert_type = "danger"
                    else:
                        subject = f'Expiring Soon Alert: {self.name}'
                        status_text = "will expire"
                        alert_type = "warning"


                    html_message = f"""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body, table, td {{
            font-family: Arial, Helvetica, sans-serif !important;
        }}
        body {{
            margin: 0;
            padding: 0;
            background-color: #f2f2f2;
            width: 100%;
        }}
        .email-content {{
            line-height: 1.5;
        }}
    </style>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, Helvetica, sans-serif; background-color: #f2f2f2; width: 100%;">
    <!-- Preheader text -->
    <div style="display: none; max-height: 0px; overflow: hidden;">
        Medicine {status_text}: {self.name} - Expiration Date: {self.expiration_date.strftime('%Y-%m-%d')}
    </div>

    <center style="width: 100%; background-color: #f2f2f2; padding: 20px 0;">
        <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%"
               style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">

            <!-- Top gradient bar -->
            <tr>
                <td style="background: linear-gradient(135deg, var(--primary-color) 0%, {("#dc3545" if days_until_expiry < 0 else "#f39c12")} 100%);
                           height: 8px; font-size: 0; line-height: 0;">&nbsp;</td>
            </tr>

            <!-- Header -->
            <tr>
                <td align="center" style="padding: 30px 20px; text-align: center; border-bottom: 1px solid #e0e0e0;">
                    <h1 style="color: {("#dc3545" if days_until_expiry < 0 else "#f39c12")}; font-size: 24px; margin: 0; font-weight: 600; line-height: 28px;">
                        {("EXPIRED MEDICINE" if days_until_expiry < 0 else "EXPIRING SOON")}
                    </h1>
                    <p style="color: #2c3e50; margin: 10px 0 0 0; font-size: 18px; font-weight: 500; line-height: 22px;">
                        {self.name}
                    </p>
                </td>
            </tr>

            <!-- Medicine Details -->
            <tr>
                <td style="padding: 20px;">
                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td width="50%" style="padding: 10px;">
                                <div style="background-color: #f8f9fa; border-radius: 4px; padding: 15px; text-align: center;">
                                    <div style="font-size: 16px; color: #666666;">Current Stock</div>
                                    <div style="font-size: 24px; font-weight: bold; color: #2c3e50; margin-top: 5px;">
                                        {self.quantity}
                                    </div>
                                </div>
                            </td>
                            <td width="50%" style="padding: 10px;">
                                <div style="background-color: #f8f9fa; border-radius: 4px; padding: 15px; text-align: center;">
                                    <div style="font-size: 16px; color: #666666;">Expiration Date</div>
                                    <div style="font-size: 24px; font-weight: bold; color: {("#dc3545" if days_until_expiry < 0 else "#f39c12")}; margin-top: 5px;">
                                        {self.expiration_date.strftime('%Y-%m-%d')}
                                    </div>
                                </div>
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>

            <!-- Status Message -->
            <tr>
                <td style="padding: 0 20px 20px;">
                    <div style="background-color: {("#fde8e8" if days_until_expiry < 0 else "#fff3cd")}; border-radius: 4px; padding: 15px;">
                        <p style="margin: 0; color: {("#dc3545" if days_until_expiry < 0 else "#f39c12")}; font-size: 16px; font-weight: bold;">
                            {("⚠️ Medicine has expired!" if days_until_expiry < 0 else f"⚠️ Expires in {days_until_expiry} days")}
                        </p>
                        <p style="margin: 10px 0 0 0; color: #666666;">
                            {("Please remove this medicine from inventory and dispose of it properly." if days_until_expiry < 0 else "Please plan for replacement stock before expiration.")}
                        </p>
                    </div>
                </td>
            </tr>

            <!-- Footer -->
            <tr>
                <td style="padding: 20px; text-align: center; border-top: 1px solid #e0e0e0;">
                    <p style="margin: 0; color: #666666; font-size: 14px;">
                        This is an automated notification from your Inventory Management System
                    </p>
                </td>
            </tr>

            <!-- Bottom gradient bar -->
            <tr>
                <td style="background: linear-gradient(135deg, var(--primary-color) 0%, {("#dc3545" if days_until_expiry < 0 else "#f39c12")} 100%);
                           height: 8px; font-size: 0; line-height: 0;">&nbsp;</td>
            </tr>
        </table>
    </center>
</body>
</html>
"""

    # Rest of the function remains the same
                    send_mail(
                        subject=subject,
                        message='',
                        from_email=settings.DEFAULT_FROM_EMAIL,
                        recipient_list=list(recipient_emails),
                        html_message=html_message,
                        fail_silently=False
                    )

                    # Create in-app notifications using the utility function
                    create_expiring_medicine_notification(self)

                    logger.info(f"Expiration notification sent for {self.name} to {len(recipient_emails)} recipients")

            except EmailNotificationSetting.DoesNotExist:
                pass
            except Exception as e:
                logger.error(f"Failed to send expiration email notification: {str(e)}")

    def is_expired(self):
        if self.expiration_date:
            return self.expiration_date <= timezone.now().date()
        return False

    def days_until_expiration(self):
        if self.expiration_date:
            delta = self.expiration_date - timezone.now().date()
            return delta.days
        return None

    def get_latest_forecast(self):
        return self.forecast_set.order_by('-forecast_date').first()

    def __str__(self):
        return f"{self.name} (₱{self.price})"

    def save(self, *args, **kwargs):
        is_new = self.pk is None
        quantity_changed = False
        expired_status_changed = False

        # Check if this is an update and quantity changed
        if not is_new:
            try:
                old_instance = Medicine.objects.get(pk=self.pk)
                quantity_changed = old_instance.quantity != self.quantity

                # Check if expiration status changed
                if old_instance.expiration_date != self.expiration_date:
                    expired_status_changed = True
            except Medicine.DoesNotExist:
                pass

        # Call the original save method
        super().save(*args, **kwargs)

        # Create audit trail if doesn't exist
        if not hasattr(self, 'audit_trail'):
            AuditTrail.objects.create(content_object=self)

        # Send notifications based on updated data
        if quantity_changed or is_new:
            # Check for out of stock
            if self.quantity == 0:
                create_out_of_stock_notification(self)
                self.send_out_of_stock_notification()
            # Check for low stock
            elif self.quantity <= self.reorder_level:
                create_low_stock_notification(self)
                self.send_low_stock_notification()  # This will send email and create notification

        # Check expiration status
        if expired_status_changed or is_new:
            create_expiring_medicine_notification(self)


class AuditTrail(models.Model):
    ACTION_CHOICES = [
        ('create', 'Create'),
        ('update', 'Update'),
        ('delete', 'Delete'),
        ('add_quantity', 'Add Quantity'),
        ('stock_movement', 'Stock Movement'),
    ]

    content_type = models.ForeignKey(ContentType, on_delete=models.CASCADE)
    object_id = models.PositiveIntegerField()
    content_object = GenericForeignKey('content_type', 'object_id')
    action = models.CharField(max_length=20, choices=ACTION_CHOICES)
    created_at = models.DateTimeField(auto_now_add=True)

    def get_action_display(self):
        return dict(self.ACTION_CHOICES).get(self.action, self.action)

class Transaction(models.Model):
    CUSTOMER_CHOICES = [
        ('in_patient', 'In Patient'),
        ('out_patient', 'Out Patient'),
    ]

    medicine = models.ForeignKey(Medicine, on_delete=models.CASCADE)
    transaction_date = models.DateTimeField()
    quantity = models.IntegerField()
    transaction_type = models.CharField(max_length=10, choices=[('sale', 'Sale'), ('purchase', 'Purchase')])
    customer_name = models.CharField(max_length=255, blank=True, null=True)
    patient_number = models.CharField(max_length=255, blank=True, null=True)
    customer_type = models.CharField(max_length=20, choices=CUSTOMER_CHOICES, default='out_patient')
    audit_trail = models.ForeignKey(AuditTrail, on_delete=models.CASCADE, related_name='transactions', blank=True, null=True)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    def save(self, *args, **kwargs):
        # Calculate total_amount before saving
        self.total_amount = self.quantity * self.medicine.price

        # Check if this is a new transaction
        is_new = self.pk is None

        super().save(*args, **kwargs)

        # Create audit trail if it doesn't exist
        if not self.audit_trail:
            self.audit_trail = AuditTrail.objects.create(
                content_object=self,
                action='create'
            )
            self.save(update_fields=['audit_trail'])

        # Automatically create a stock movement for this transaction
        if is_new:
            # Map transaction type to movement type
            movement_type = 'purchase' if self.transaction_type == 'purchase' else 'sale'

            # For sales, quantity should be negative in stock movements
            movement_quantity = self.quantity if self.transaction_type == 'purchase' else -self.quantity

            # Create the stock movement
            from django.contrib.auth import get_user_model
            User = get_user_model()

            try:
                # Get the first admin user for the performed_by field
                admin_user = User.objects.filter(is_staff=True).first()

                StockMovement.objects.create(
                    medicine=self.medicine,
                    movement_date=self.transaction_date,
                    quantity=movement_quantity,
                    movement_type=movement_type,
                    reference_number=f"Transaction #{self.id}",
                    notes=f"Automatically created from {self.transaction_type} transaction",
                    brand=self.medicine.brand,
                    supplier=self.medicine.supplier,
                    performed_by=admin_user,
                    unit_price=self.medicine.price,
                    total_value=abs(movement_quantity) * self.medicine.price
                )
            except Exception as e:
                logger.error(f"Failed to create stock movement for transaction {self.id}: {str(e)}")

class Forecast(models.Model):
    FORECAST_PERIODS = [
        ('weekly', 'Weekly'),
        ('monthly', 'Monthly'),
        ('yearly', 'Yearly')
    ]

    FORECAST_METHODS = [
        ('arima', 'ARIMA'),
        ('linear', 'Linear Regression'),
        ('polynomial', 'Polynomial Regression'),
        ('moving_avg', 'Moving Average'),
        ('holtwinters', 'Holt-Winters'),
        ('ensemble', 'Ensemble')
    ]

    medicine = models.ForeignKey(Medicine, on_delete=models.CASCADE, null=True, blank=True)
    generic_medicine = models.ForeignKey(GenericMedicine, on_delete=models.CASCADE, null=True, blank=True,
                                        related_name='forecasts',
                                        help_text="Generic medicine category for forecasting")
    forecast_date = models.DateTimeField()
    predicted_quantity = models.FloatField()  # Changed from IntegerField for more precise prediction
    forecast_period = models.CharField(
        max_length=10,
        choices=FORECAST_PERIODS,
        default='monthly'
    )

    # Forecast metrics
    accuracy = models.FloatField(null=True, blank=True)
    forecast_method = models.CharField(max_length=50, choices=FORECAST_METHODS, null=True, blank=True)
    mae = models.FloatField(null=True, blank=True)
    mape = models.FloatField(null=True, blank=True)
    rmse = models.FloatField(null=True, blank=True)

    # Confidence intervals
    confidence_lower = models.FloatField(null=True, blank=True)
    confidence_upper = models.FloatField(null=True, blank=True)

    # Inventory management
    recommended_order_quantity = models.IntegerField(null=True, blank=True)
    safety_stock = models.IntegerField(null=True, blank=True)

    # Seasonal analysis
    has_seasonality = models.BooleanField(default=False)
    seasonal_pattern = models.CharField(max_length=50, null=True, blank=True)
    peak_period = models.CharField(max_length=50, null=True, blank=True)

    # Trend analysis
    trend_direction = models.CharField(max_length=20, null=True, blank=True)
    trend_strength = models.FloatField(null=True, blank=True)

    # Explanation of forecast method selection
    notes = models.TextField(null=True, blank=True, help_text="Explanation of why this forecasting method was selected")

    # Last updated timestamp
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-forecast_date']
        indexes = [
            models.Index(fields=['medicine', 'forecast_date']),
            models.Index(fields=['forecast_period']),
            models.Index(fields=['forecast_method']),
            models.Index(fields=['accuracy'])
        ]

    def __str__(self):
        medicine_name = self.generic_medicine.name if self.generic_medicine else self.medicine.name if self.medicine else "Unknown"
        return f"{medicine_name} - {self.forecast_date.strftime('%Y-%m-%d')} ({self.get_forecast_method_display() or 'Unknown'})"

    def get_confidence_range(self):
        """Return the confidence range as a formatted string"""
        if self.confidence_lower is not None and self.confidence_upper is not None:
            return f"{self.confidence_lower:.1f} - {self.confidence_upper:.1f}"
        return "N/A"

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        if not hasattr(self, 'audit_trail'):
            AuditTrail.objects.create(
                content_object=self,
                action='create'
            )

    # This is a duplicate __str__ method that should be removed
    # The one above will be used instead

    def get_confidence_range(self):
        return {
            'lower': self.confidence_lower,
            'upper': self.confidence_upper
        }

    def get_forecast_metrics(self):
        return {
            'accuracy': self.accuracy,
            'mae': self.mae,
            'mape': self.mape,
            'rmse': self.rmse
        }


class StockMovement(models.Model):
    """
    Tracks the movement of medicines in the inventory system.
    This includes tracking brand and supplier information for each movement.
    """
    MOVEMENT_TYPES = [
        ('purchase', 'Purchase'),
        ('sale', 'Sale'),
        ('adjustment', 'Inventory adjustment'),
        ('return', 'Return'),
        ('expired', 'Expired medicine removal'),
        ('transfer', 'Transfer'),
    ]

    medicine = models.ForeignKey(Medicine, on_delete=models.CASCADE, related_name='stock_movements')
    movement_date = models.DateTimeField(default=timezone.now)
    quantity = models.IntegerField(help_text="Positive for additions, negative for reductions")
    movement_type = models.CharField(max_length=20, choices=MOVEMENT_TYPES)
    reference_number = models.CharField(max_length=50, blank=True, null=True, help_text="Invoice, PO, or reference number")
    notes = models.TextField(blank=True, help_text="Additional information about this movement")

    # Brand and supplier tracking
    brand = models.CharField(max_length=255, blank=True, help_text="Brand name of the medicine")
    supplier = models.CharField(max_length=255, blank=True, help_text="Supplier or manufacturer")

    # User who performed the movement
    performed_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)

    # Audit trail
    audit_trail = models.ForeignKey(AuditTrail, on_delete=models.CASCADE, related_name='stock_movements', blank=True, null=True)

    # Movement value
    unit_price = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)
    total_value = models.DecimalField(max_digits=10, decimal_places=2, default=0.00)

    class Meta:
        ordering = ['-movement_date']
        indexes = [
            models.Index(fields=['medicine', 'movement_date']),
            models.Index(fields=['movement_type']),
            models.Index(fields=['brand']),
            models.Index(fields=['supplier']),
        ]

    def __str__(self):
        return f"{self.medicine.name} - {self.get_movement_type_display()} - {self.movement_date.strftime('%Y-%m-%d %H:%M')}"

    def save(self, *args, **kwargs):
        # Copy brand and supplier from medicine if not provided
        if not self.brand and self.medicine.brand:
            self.brand = self.medicine.brand
        if not self.supplier and self.medicine.supplier:
            self.supplier = self.medicine.supplier

        # Calculate total value
        self.unit_price = self.medicine.price
        self.total_value = abs(self.quantity) * self.unit_price

        # Create audit trail if it doesn't exist
        is_new = self.pk is None
        super().save(*args, **kwargs)

        if is_new and not self.audit_trail:
            content_type = ContentType.objects.get_for_model(self)
            audit_trail = AuditTrail.objects.create(
                content_object=self,
                content_type=content_type,
                object_id=self.pk,
                action='stock_movement'
            )
            self.audit_trail = audit_trail
            self.save(update_fields=['audit_trail'])

        # Update movement analysis for this medicine
        if is_new:
            try:
                # Get or create movement analysis for this medicine
                movement_analysis, _ = MovementAnalysis.objects.get_or_create(
                    medicine=self.medicine
                )
                # Update the analysis
                movement_analysis.update_analysis()
                logger.info(f"Updated movement analysis for {self.medicine.name}")
            except Exception as e:
                logger.error(f"Failed to update movement analysis for {self.medicine.name}: {str(e)}")

    @property
    def days_since_movement(self):
        """Return the number of days since this movement occurred"""
        if self.movement_date:
            delta = timezone.now() - self.movement_date
            return delta.days
        return None


class MovementAnalysis(models.Model):
    """
    Stores analysis data about medicine movements for reporting purposes.
    """
    medicine = models.OneToOneField(Medicine, on_delete=models.CASCADE, related_name='movement_analysis')
    last_updated = models.DateTimeField(auto_now=True)

    # Movement rates
    monthly_movement_rate = models.FloatField(default=0, help_text="Average units moved per month")
    quarterly_movement_rate = models.FloatField(default=0, help_text="Average units moved per quarter")
    annual_movement_rate = models.FloatField(default=0, help_text="Average units moved per year")

    # Movement classification
    MOVEMENT_CLASSES = [
        ('A', 'Fast-moving (top 20%)'),
        ('B', 'Medium-moving (middle 30%)'),
        ('C', 'Slow-moving (bottom 50%)'),
        ('D', 'No movement (no transactions in 90 days)'),
    ]
    movement_class = models.CharField(max_length=1, choices=MOVEMENT_CLASSES, default='C')

    # Movement statistics
    last_movement_date = models.DateTimeField(null=True, blank=True)
    days_since_last_movement = models.IntegerField(null=True, blank=True)
    total_quantity_moved = models.IntegerField(default=0)
    movement_frequency = models.FloatField(default=0, help_text="Average number of movements per month")

    # Trend data
    movement_trend = models.CharField(max_length=20, blank=True, null=True,
                                     help_text="Increasing, Decreasing, Stable, or Volatile")
    trend_percentage = models.FloatField(null=True, blank=True,
                                        help_text="Percentage change in movement rate over the last 3 months")

    # Brand and supplier statistics
    top_brand = models.CharField(max_length=255, blank=True, null=True)
    top_supplier = models.CharField(max_length=255, blank=True, null=True)

    # Seasonal patterns
    has_seasonality = models.BooleanField(default=False)
    peak_season = models.CharField(max_length=20, blank=True, null=True)

    def __str__(self):
        return f"Movement Analysis - {self.medicine.name}"

    def update_analysis(self):
        """Update the movement analysis data based on current stock movements"""
        from django.db.models import Sum, Count, F, Case, When

        # Get all movements for this medicine
        movements = StockMovement.objects.filter(medicine=self.medicine)

        # Skip if no movements
        if not movements.exists():
            self.movement_class = 'D'
            self.days_since_last_movement = None
            self.save()
            return

        # Get the last movement date
        last_movement = movements.order_by('-movement_date').first()
        self.last_movement_date = last_movement.movement_date

        # Calculate days since last movement
        if self.last_movement_date:
            delta = timezone.now() - self.last_movement_date
            self.days_since_last_movement = delta.days

        # Calculate movement rates
        today = timezone.now()
        one_month_ago = today - timezone.timedelta(days=30)
        three_months_ago = today - timezone.timedelta(days=90)
        one_year_ago = today - timezone.timedelta(days=365)

        # Monthly movement (last 30 days)
        monthly_movements = movements.filter(movement_date__gte=one_month_ago)
        monthly_quantity = monthly_movements.aggregate(
            total=Sum(F('quantity') * Case(
                When(movement_type__in=['purchase', 'adjustment', 'transfer'], then=1),
                default=-1,
                output_field=models.IntegerField()
            ))
        )['total'] or 0
        self.monthly_movement_rate = abs(monthly_quantity)

        # Quarterly movement (last 90 days)
        quarterly_movements = movements.filter(movement_date__gte=three_months_ago)
        quarterly_quantity = quarterly_movements.aggregate(
            total=Sum(F('quantity') * Case(
                When(movement_type__in=['purchase', 'adjustment', 'transfer'], then=1),
                default=-1,
                output_field=models.IntegerField()
            ))
        )['total'] or 0
        self.quarterly_movement_rate = abs(quarterly_quantity) / 3  # Average per month

        # Annual movement (last 365 days)
        annual_movements = movements.filter(movement_date__gte=one_year_ago)
        annual_quantity = annual_movements.aggregate(
            total=Sum(F('quantity') * Case(
                When(movement_type__in=['purchase', 'adjustment', 'transfer'], then=1),
                default=-1,
                output_field=models.IntegerField()
            ))
        )['total'] or 0
        self.annual_movement_rate = abs(annual_quantity) / 12  # Average per month

        # Total quantity moved
        self.total_quantity_moved = abs(annual_quantity)

        # Movement frequency (movements per month)
        movement_count = annual_movements.count()
        self.movement_frequency = movement_count / 12 if movement_count > 0 else 0

        # Determine movement class
        if self.days_since_last_movement and self.days_since_last_movement > 90:
            self.movement_class = 'D'  # No movement
        elif self.monthly_movement_rate > 0:
            # We'll need to compare with other medicines to determine A, B, or C
            # This will be handled by a management command that processes all medicines
            self.movement_class = 'C'  # Default to slow-moving

        # Calculate trend
        if monthly_movements.exists() and quarterly_movements.exists():
            last_month_rate = self.monthly_movement_rate
            prev_two_months_rate = (self.quarterly_movement_rate * 3 - last_month_rate) / 2

            if prev_two_months_rate > 0:
                percentage_change = ((last_month_rate - prev_two_months_rate) / prev_two_months_rate) * 100
                self.trend_percentage = percentage_change

                if percentage_change > 10:
                    self.movement_trend = 'Increasing'
                elif percentage_change < -10:
                    self.movement_trend = 'Decreasing'
                else:
                    self.movement_trend = 'Stable'
            else:
                self.movement_trend = 'New'
                self.trend_percentage = 100

        # Top brand and supplier
        brand_counts = movements.exclude(brand='').values('brand').annotate(
            count=Count('id')).order_by('-count')
        if brand_counts.exists():
            self.top_brand = brand_counts.first()['brand']

        supplier_counts = movements.exclude(supplier='').values('supplier').annotate(
            count=Count('id')).order_by('-count')
        if supplier_counts.exists():
            self.top_supplier = supplier_counts.first()['supplier']

        # Save the updated analysis
        self.save()
