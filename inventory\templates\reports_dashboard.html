﻿{% extends 'base.html' %}
{% load static %}
{% load humanize %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/reports_dashboard.css' %}">
{% endblock %}

{% block content %}
    <!-- Page Header -->
    <div class="container-fluid mb-4">
        <div class="d-sm-flex align-items-center justify-content-between">
            <h1 class="h3 mb-0 text-gray-800">Reports Dashboard</h1>
            <p class="text-muted">Generate professional reports for your inventory management needs</p>
        </div>
        <hr class="separator">
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <!-- Monthly Sales Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2 stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Monthly Sales
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800 counter-value">
                                ₱{{ total_sales|floatformat:2|intcomma }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="icon-circle bg-primary">
                                <i class="fas fa-peso-sign fa-lg text-white"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Medicines Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2 stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Medicines
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800 counter-value">{{ total_medicines }}</div>
                        </div>
                        <div class="col-auto">
                            <div class="icon-circle bg-success">
                                <i class="fas fa-pills fa-lg text-white"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Low Stock Items Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2 stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Low Stock Items
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800 counter-value">{{ low_stock_count }}</div>
                        </div>
                        <div class="col-auto">
                            <div class="icon-circle bg-warning">
                                <i class="fas fa-exclamation-triangle fa-lg text-white"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Monthly Transactions Card -->
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2 stat-card">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Monthly Transactions
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800 counter-value">{{ total_transactions }}</div>
                        </div>
                        <div class="col-auto">
                            <div class="icon-circle bg-info">
                                <i class="fas fa-exchange-alt fa-lg text-white"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Generation Cards -->
    <div class="row">
        <!-- Sales Report Card -->
        <div class="col-lg-3 mb-4">
            <div class="card shadow h-100 report-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-gradient-primary text-white">
                    <h6 class="m-0 font-weight-bold">Sales Report</h6>
                    <div class="report-icon">
                        <i class="fas fa-chart-line"></i>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{% url 'reports_dashboard' %}" class="report-form" data-report-type="sales" enctype="multipart/form-data">
                        {% csrf_token %}
                        <input type="hidden" name="report_type" value="sales">

                        <div class="mb-3">
                            <label class="form-label fw-bold">Date Range</label>
                            <select name="date_range" class="form-select form-control-professional">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 3 months</option>
                                <option value="180">Last 6 months</option>
                                <option value="365">Last year</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">Format</label>
                            <div class="d-flex format-selector">
                                <div class="form-check form-check-inline flex-fill">
                                    <input class="form-check-input" type="radio" name="file_format" id="excel-sales" value="excel" checked>
                                    <label class="form-check-label" for="excel-sales">
                                        <i class="fas fa-file-excel text-success me-1"></i> Excel
                                    </label>
                                </div>
                                <div class="form-check form-check-inline flex-fill">
                                    <input class="form-check-input" type="radio" name="file_format" id="pdf-sales" value="pdf">
                                    <label class="form-check-label" for="pdf-sales">
                                        <i class="fas fa-file-pdf text-danger me-1"></i> PDF
                                    </label>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100 btn-generate">
                            <i class="fas fa-download me-2"></i>Generate Report
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Forecast Report Card -->
        <div class="col-lg-3 mb-4">
            <div class="card shadow h-100 report-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-gradient-success text-white">
                    <h6 class="m-0 font-weight-bold">Forecast Report</h6>
                    <div class="report-icon">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{% url 'reports_dashboard' %}" class="report-form" data-report-type="forecast" enctype="multipart/form-data">
                        {% csrf_token %}
                        <input type="hidden" name="report_type" value="forecast">

                        <div class="mb-3">
                            <label class="form-label fw-bold">Forecast Period</label>
                            <select name="date_range" class="form-select form-control-professional">
                                <option value="30">Next 30 days</option>
                                <option value="90">Next 3 months</option>
                                <option value="180">Next 6 months</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">Format</label>
                            <div class="d-flex format-selector">
                                <div class="form-check form-check-inline flex-fill">
                                    <input class="form-check-input" type="radio" name="file_format" id="excel-forecast" value="excel" checked>
                                    <label class="form-check-label" for="excel-forecast">
                                        <i class="fas fa-file-excel text-success me-1"></i> Excel
                                    </label>
                                </div>
                                <div class="form-check form-check-inline flex-fill">
                                    <input class="form-check-input" type="radio" name="file_format" id="pdf-forecast" value="pdf">
                                    <label class="form-check-label" for="pdf-forecast">
                                        <i class="fas fa-file-pdf text-danger me-1"></i> PDF
                                    </label>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-success w-100 btn-generate">
                            <i class="fas fa-download me-2"></i>Generate Report
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Inventory Report Card -->
        <div class="col-lg-3 mb-4">
            <div class="card shadow h-100 report-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-gradient-warning text-white">
                    <h6 class="m-0 font-weight-bold">Inventory Report</h6>
                    <div class="report-icon">
                        <i class="fas fa-warehouse"></i>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{% url 'reports_dashboard' %}" class="report-form" data-report-type="inventory" enctype="multipart/form-data">
                        {% csrf_token %}
                        <input type="hidden" name="report_type" value="inventory">

                        <div class="mb-3">
                            <label class="form-label fw-bold">Include</label>
                            <div class="form-check custom-checkbox">
                                <input class="form-check-input" type="checkbox" name="include_low_stock" id="include-low-stock" checked>
                                <label class="form-check-label" for="include-low-stock">
                                    <i class="fas fa-exclamation-triangle text-warning me-1"></i> Low Stock Items
                                </label>
                            </div>
                            <div class="form-check custom-checkbox">
                                <input class="form-check-input" type="checkbox" name="include_expired" id="include-expired" checked>
                                <label class="form-check-label" for="include-expired">
                                    <i class="fas fa-calendar-times text-danger me-1"></i> Expired Items
                                </label>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">Format</label>
                            <div class="d-flex format-selector">
                                <div class="form-check form-check-inline flex-fill">
                                    <input class="form-check-input" type="radio" name="file_format" id="excel-inventory" value="excel" checked>
                                    <label class="form-check-label" for="excel-inventory">
                                        <i class="fas fa-file-excel text-success me-1"></i> Excel
                                    </label>
                                </div>
                                <div class="form-check form-check-inline flex-fill">
                                    <input class="form-check-input" type="radio" name="file_format" id="pdf-inventory" value="pdf">
                                    <label class="form-check-label" for="pdf-inventory">
                                        <i class="fas fa-file-pdf text-danger me-1"></i> PDF
                                    </label>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-warning w-100 btn-generate text-white">
                            <i class="fas fa-download me-2"></i>Generate Report
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Transactions Report Card -->
        <div class="col-lg-3 mb-4">
            <div class="card shadow h-100 report-card">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between bg-gradient-info text-white">
                    <h6 class="m-0 font-weight-bold">Transactions Report</h6>
                    <div class="report-icon">
                        <i class="fas fa-exchange-alt"></i>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" action="{% url 'reports_dashboard' %}" class="report-form" data-report-type="transactions" enctype="multipart/form-data">
                        {% csrf_token %}
                        <input type="hidden" name="report_type" value="transactions">

                        <div class="mb-3">
                            <label class="form-label fw-bold">Date Range</label>
                            <select name="date_range" class="form-select form-control-professional">
                                <option value="7">Last 7 days</option>
                                <option value="30" selected>Last 30 days</option>
                                <option value="90">Last 3 months</option>
                                <option value="180">Last 6 months</option>
                            </select>
                        </div>

                        <div class="mb-3">
                            <label class="form-label fw-bold">Format</label>
                            <div class="d-flex format-selector">
                                <div class="form-check form-check-inline flex-fill">
                                    <input class="form-check-input" type="radio" name="file_format" id="excel-transactions" value="excel" checked>
                                    <label class="form-check-label" for="excel-transactions">
                                        <i class="fas fa-file-excel text-success me-1"></i> Excel
                                    </label>
                                </div>
                                <div class="form-check form-check-inline flex-fill">
                                    <input class="form-check-input" type="radio" name="file_format" id="pdf-transactions" value="pdf">
                                    <label class="form-check-label" for="pdf-transactions">
                                        <i class="fas fa-file-pdf text-danger me-1"></i> PDF
                                    </label>
                                </div>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-info w-100 btn-generate text-white">
                            <i class="fas fa-download me-2"></i>Generate Report
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Download Status -->
    <div class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 11">
        <div id="reportToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="toast-header bg-success text-white">
                <i class="fas fa-check-circle me-2"></i>
                <strong class="me-auto">Success</strong>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">
                Report generated successfully!
            </div>
        </div>
    </div>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add animation to counter values
    document.querySelectorAll('.counter-value').forEach(counter => {
        const finalValue = counter.textContent;
        counter.textContent = '0';

        setTimeout(() => {
            animateCounter(counter, finalValue);
        }, 300);
    });

    // Handle individual report form submissions
    document.querySelectorAll('.report-form').forEach(form => {
        form.addEventListener('submit', function(e) {
            e.preventDefault();
            const reportType = this.dataset.reportType;
            const btn = this.querySelector('button[type="submit"]');

            // Get the selected file format
            const fileFormatRadio = form.querySelector('input[name="file_format"]:checked');
            const fileFormat = fileFormatRadio ? fileFormatRadio.value : 'excel';

            // Disable button and show loading state
            btn.disabled = true;
            btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';

            // Add loading animation to card
            const card = this.closest('.card');
            card.classList.add('card-loading');

            // Use a direct form submission approach for both Excel and PDF
            console.log('Submitting report form to:', form.action, 'Format:', fileFormat);

            // Make sure the form has the correct action
            if (!form.action) {
                form.action = window.location.href;
            }

            // Set the form target to _blank to open in a new tab/window
            // This helps with file downloads
            form.target = '_blank';

            // Submit the form directly
            form.submit();

            // Reset the form target after submission
            setTimeout(() => {
                form.target = '';
            }, 100);

            // Show success message after a delay
            setTimeout(() => {
                // Show success message
                const toast = new bootstrap.Toast(document.getElementById('reportToast'));
                toast.show();

                // Reset UI
                card.classList.remove('card-loading');
                card.classList.add('card-success');
                setTimeout(() => {
                    card.classList.remove('card-success');
                }, 2000);

                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-download me-2"></i>Generate Report';
            }, 1500);

            return;

            // The code below is unreachable - we're now using iframe for both Excel and PDF
            // For PDF files, use AJAX
            const formData = new FormData();

            // Add all form fields to FormData
            const formElements = form.elements;
            for (let i = 0; i < formElements.length; i++) {
                const field = formElements[i];
                if (field.name) {
                    if (field.type === 'checkbox') {
                        if (field.checked) {
                            formData.append(field.name, field.value || 'on');
                        }
                    } else if (field.type !== 'radio' || field.checked) {
                        formData.append(field.name, field.value);
                    }
                }
            }

            // Submit form data
            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }

                // Check the content type to ensure it's an Excel or PDF file
                const contentType = response.headers.get('content-type');
                console.log('Response content type:', contentType);

                if (fileFormat === 'excel' && !contentType.includes('spreadsheetml')) {
                    console.warn('Expected Excel file but got:', contentType);
                }

                if (fileFormat === 'pdf' && !contentType.includes('pdf')) {
                    console.warn('Expected PDF file but got:', contentType);
                }

                return response.blob();
            })
            .then(blob => {
                // Create download link
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                // Get the file format from the radio button
                const fileFormat = fileFormatRadio ? fileFormatRadio.value : 'excel';

                // Determine the correct file extension
                const contentType = blob.type;
                console.log('Blob content type:', contentType);
                console.log('Blob size:', blob.size, 'bytes');

                // Set the correct file extension based on the selected format
                const fileExtension = fileFormat === 'excel' ? 'xlsx' : 'pdf';

                // Generate a timestamp for the filename
                const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
                const filename = `${reportType}_report_${timestamp}.${fileExtension}`;

                console.log('Downloading file as:', filename);
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);

                // Show success message with toast
                const toast = new bootstrap.Toast(document.getElementById('reportToast'));
                toast.show();

                // Add success animation to card
                card.classList.remove('card-loading');
                card.classList.add('card-success');
                setTimeout(() => {
                    card.classList.remove('card-success');
                }, 2000);
            })
            .catch(error => {
                console.error('Error:', error);

                // Show error toast
                const reportToast = document.getElementById('reportToast');
                reportToast.querySelector('.toast-header').classList.remove('bg-success');
                reportToast.querySelector('.toast-header').classList.add('bg-danger');
                reportToast.querySelector('.toast-header i').classList.remove('fa-check-circle');
                reportToast.querySelector('.toast-header i').classList.add('fa-exclamation-circle');
                reportToast.querySelector('.toast-header strong').textContent = 'Error';
                reportToast.querySelector('.toast-body').textContent = 'Error generating report. Please try again.';

                const toast = new bootstrap.Toast(reportToast);
                toast.show();

                // Add error animation to card
                card.classList.remove('card-loading');
                card.classList.add('card-error');
                setTimeout(() => {
                    card.classList.remove('card-error');
                }, 2000);
            })
            .finally(() => {
                // Reset button state
                btn.disabled = false;
                btn.innerHTML = '<i class="fas fa-download me-2"></i>Generate Report';
            });
        });
    });

    // Function to animate counter
    function animateCounter(counter, finalValue) {
        // Remove currency symbol if present
        let numericValue = finalValue;
        let prefix = '';

        if (finalValue.toString().includes('₱')) {
            prefix = '₱';
            numericValue = finalValue.toString().replace('₱', '').replace(/,/g, '');
        } else if (finalValue.toString().includes(',')) {
            numericValue = finalValue.toString().replace(/,/g, '');
        }

        const duration = 1500;
        const frameDuration = 1000/60;
        const totalFrames = Math.round(duration / frameDuration);
        const easeOutQuad = t => t * (2 - t);

        let frame = 0;
        const countTo = parseFloat(numericValue);

        const counter_animation = setInterval(() => {
            frame++;
            const progress = easeOutQuad(frame / totalFrames);
            const currentCount = Math.round(countTo * progress);

            if (countTo > 999) {
                counter.textContent = prefix + currentCount.toLocaleString();
            } else {
                counter.textContent = prefix + currentCount;
            }

            if (frame === totalFrames) {
                clearInterval(counter_animation);
                counter.textContent = finalValue; // Ensure we end with the exact final value
            }
        }, frameDuration);
    }
});
</script>
{% endblock %}