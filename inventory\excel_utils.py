from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
from django.utils import timezone
from django.http import HttpResponse
import logging

logger = logging.getLogger(__name__)

def apply_professional_styling(ws, title, metadata=None, columns=None):
    """
    Apply professional styling to an Excel worksheet

    Args:
        ws: The worksheet to style
        title: The report title
        metadata: Dictionary of metadata to include (e.g., {'Generated on': '2023-01-01'})
        columns: List of column names for the header row
    """
    # Title styling
    ws['A1'] = title
    ws.merge_cells(f'A1:{get_column_letter(len(columns) if columns else 7)}1')
    ws['A1'].font = Font(bold=True, size=16, color="2C3E50")
    ws['A1'].alignment = Alignment(horizontal="center", vertical="center")
    ws.row_dimensions[1].height = 30

    # Add metadata if provided
    row = 2
    if metadata:
        for key, value in metadata.items():
            ws.cell(row=row, column=1, value=key)
            ws.cell(row=row, column=2, value=value)
            ws.cell(row=row, column=1).font = Font(bold=True)
            row += 1

        # Add empty row after metadata
        row += 1
    else:
        # If no metadata, still add some spacing
        row = 3

    # Add header row if columns provided
    if columns:
        # Header styling
        header_row = row
        header_fill = PatternFill(start_color="2C3E50", end_color="2C3E50", fill_type="solid")
        header_font = Font(bold=True, color="FFFFFF")
        header_border = Border(
            bottom=Side(border_style="thin", color="FFFFFF")
        )

        # Apply headers
        for col_num, header in enumerate(columns, 1):
            cell = ws.cell(row=header_row, column=col_num, value=header)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = Alignment(horizontal="center", vertical="center")
            cell.border = header_border

        # Set header row height
        ws.row_dimensions[header_row].height = 20

    # Return the row number where data should start
    return row + (1 if columns else 0)

def auto_adjust_columns(ws):
    """
    Auto-adjust column widths based on content
    """
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter

        for cell in col:
            if cell.value:
                try:
                    max_length = max(max_length, len(str(cell.value)))
                except:
                    pass

        adjusted_width = min(max_length + 3, 50)  # Cap width at 50 to prevent too wide columns
        ws.column_dimensions[column].width = adjusted_width

def apply_row_styling(ws, row_num, data, row_fill=None, is_total_row=False):
    """
    Apply styling to a data row

    Args:
        ws: The worksheet
        row_num: The row number
        data: List of values for the row
        row_fill: Optional PatternFill for the row
        is_total_row: Whether this is a total/summary row
    """
    for col_num, value in enumerate(data, 1):
        cell = ws.cell(row=row_num, column=col_num, value=value)

        if is_total_row:
            cell.font = Font(bold=True)
            if col_num == 1:
                cell.alignment = Alignment(horizontal="right")
            bottom_border = Side(border_style="thin", color="2C3E50")
            top_border = Side(border_style="thin", color="2C3E50")
            cell.border = Border(top=top_border, bottom=bottom_border)

        if row_fill:
            cell.fill = row_fill

        # Align numeric values to the right
        if isinstance(value, (int, float)) or (isinstance(value, str) and value.startswith('₱')):
            cell.alignment = Alignment(horizontal="right")

    return row_num + 1

def add_total_row(ws, row_num, data, num_columns):
    """
    Add a total row with proper styling

    Args:
        ws: The worksheet
        row_num: The row number to add the total row
        data: List of values for the total row
        num_columns: Total number of columns in the table
    """
    # Add a separator row before totals
    row_num += 1

    # Add the total row
    for col_num, value in enumerate(data, 1):
        cell = ws.cell(row=row_num, column=col_num, value=value)
        cell.font = Font(bold=True)

        # Add borders
        bottom_border = Side(border_style="thin", color="2C3E50")
        top_border = Side(border_style="thin", color="2C3E50")
        cell.border = Border(top=top_border, bottom=bottom_border)

        # Add background color
        cell.fill = PatternFill(start_color="ECF0F1", end_color="ECF0F1", fill_type="solid")

        # Right-align numeric values
        if col_num > 1 or (isinstance(value, str) and value.startswith('Total')):
            if isinstance(value, (int, float)) or (isinstance(value, str) and (value.startswith('₱') or value.startswith('$'))):
                cell.alignment = Alignment(horizontal="right")

    return row_num + 1

def create_excel_workbook(title=None):
    """
    Create a new Excel workbook with basic setup

    Args:
        title: Optional title for the active worksheet

    Returns:
        A new openpyxl Workbook object
    """
    wb = Workbook()
    if title:
        wb.active.title = title
    return wb

def create_excel_response(wb, report_type):
    """
    Create an HTTP response with the Excel file

    Args:
        wb: The workbook
        report_type: The type of report (e.g., 'sales', 'inventory')

    Returns:
        HttpResponse object
    """
    try:
        # Create a response with the correct MIME type for Excel files
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )

        # Generate a timestamp for the filename
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')

        # Set the Content-Disposition header with the correct filename and extension
        filename = f"{report_type}_report_{timestamp}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{filename}"'

        # Save the workbook to the response
        wb.save(response)

        return response
    except Exception as e:
        logger.error(f"Error creating Excel response: {str(e)}")
        raise
