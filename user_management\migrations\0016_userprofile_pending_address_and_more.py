# Generated by Django 4.2.9 on 2025-04-14 05:17

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_management', '0015_customer'),
    ]

    operations = [
        migrations.AddField(
            model_name='userprofile',
            name='pending_address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pending_changes_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pending_email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='userprofile',
            name='pending_phone_number',
            field=models.CharField(blank=True, max_length=17, null=True, validators=[django.core.validators.RegexValidator(message='Please enter a valid Philippine phone number (e.g., +639123456789 or 09123456789).', regex='^(\\+639|09)\\d{9}$')]),
        ),
    ]
