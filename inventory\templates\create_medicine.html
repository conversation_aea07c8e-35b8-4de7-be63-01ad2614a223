﻿{% extends 'base.html' %}

{% block title %}Create Medicine - MedInventory{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-primary fw-bold">
                    <i class="fas fa-plus-circle me-2"></i>Create Medicine
                </h1>
                <a href="{% url 'medicine_list' %}" class="btn btn-outline-secondary rounded-pill">
                    <i class="fas fa-arrow-left me-1"></i>Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Form Card -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-0">
                    <form method="post" class="medicine-form">
                        {% csrf_token %}
                        <div class="row g-0">
                            <!-- Left Column: Basic Information -->
                            <div class="col-md-6 p-4 border-end">
                                <h5 class="text-primary mb-3">Basic Information</h5>
                                <div class="row g-3">
                                    <!-- Name Field -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label d-flex align-items-center">
                                                Medicine Name <span class="text-danger ms-1">*</span>
                                            </label>
                                            <input type="text" name="name" class="form-control" required>
                                        </div>
                                    </div>

                                    <!-- Category Field - Now as text field to match the model -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label d-flex align-items-center">
                                                Category <span class="text-danger ms-1">*</span>
                                            </label>
                                            <input type="text" name="category" class="form-control" required>
                                        </div>
                                    </div>

                                    <!-- Brand Field -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label d-flex align-items-center">
                                                Brand
                                            </label>
                                            <input type="text" name="brand" class="form-control" placeholder="e.g., Tylenol, Panadol">
                                            <small class="form-text text-muted">
                                                Brand name of the medicine
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Supplier Field -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label d-flex align-items-center">
                                                Supplier
                                            </label>
                                            <input type="text" name="supplier" class="form-control" placeholder="e.g., Johnson & Johnson">
                                            <small class="form-text text-muted">
                                                Manufacturer or supplier
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Price Field -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label d-flex align-items-center">
                                                Unit Price <span class="text-danger ms-1">*</span>
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text">₱</span>
                                                <input type="number" name="price" id="medicine-price" class="form-control" step="0.01" required>
                                                <button type="button" id="edit-price-btn" class="btn btn-outline-primary" style="display: none;">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                            </div>
                                            <small class="form-text text-muted">
                                                Price in Philippine Peso (₱)
                                            </small>
                                            <div id="price-update-status" class="mt-1"></div>
                                        </div>
                                    </div>

                                    <!-- Initial Stock Field -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label d-flex align-items-center">
                                                Initial Stock <span class="text-danger ms-1">*</span>
                                            </label>
                                            <input type="number" name="quantity" class="form-control" min="0" required>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column: Additional Details -->
                            <div class="col-md-6 p-4">
                                <h5 class="text-primary mb-3">Inventory Management</h5>
                                <div class="row g-3">
                                    <!-- Reorder Level Field -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label d-flex align-items-center">
                                                Reorder Level <span class="text-danger ms-1">*</span>
                                            </label>
                                            <input type="number" name="reorder_level" class="form-control" min="1" value="10" required>
                                            <small class="form-text text-muted">
                                                <i class="fas fa-info-circle me-1"></i>You'll be alerted when stock falls below this level
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Reorder Quantity Field (Added to match model) -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label d-flex align-items-center">
                                                Reorder Quantity <span class="text-danger ms-1">*</span>
                                            </label>
                                            <input type="number" name="reorder_quantity" class="form-control" min="1" value="20" required>
                                            <small class="form-text text-muted">
                                                <i class="fas fa-info-circle me-1"></i>Default amount to reorder when low
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Expiration Date Field -->
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="form-label">Expiration Date</label>
                                            <input type="date" name="expiration_date" class="form-control">
                                            <small class="form-text text-muted">
                                                Optional - for expiry notifications
                                            </small>
                                        </div>
                                    </div>

                                    <!-- Description Field (Textarea) -->
                                    <div class="col-12">
                                        <div class="form-group">
                                            <label class="form-label">Description</label>
                                            <textarea name="description" class="form-control" rows="3" placeholder="Brief description of the medicine"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Footer with Buttons -->
                        <div class="card-footer bg-light d-flex justify-content-end gap-2 p-3 border-top">
                            <a href="{% url 'medicine_list' %}" class="btn btn-light px-4">
                                <i class="fas fa-times me-1"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-primary px-4">
                                <i class="fas fa-save me-1"></i>Save Medicine
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* MEDUSA-like styling for form elements */
    :root {
        --primary-color: #2c3e50;
        --secondary-color: #3498db;
        --form-element-height: 38px;
    }

    .form-control, .form-select, .input-group-text {
        height: var(--form-element-height);
        border-radius: 4px;
        border-color: #e2e8f0;
        font-size: 0.95rem;
    }

    .card {
        border-radius: 8px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .form-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
    }

    .form-text {
        font-size: 0.8rem;
    }

    textarea.form-control {
        height: auto;
        min-height: 38px;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--secondary-color);
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    }

    .medicine-form .col-md-6 {
        transition: all 0.3s ease;
    }

    .card-footer {
        background-color: #f8fafc;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .border-end {
            border-right: none !important;
            border-bottom: 1px solid #e2e8f0;
        }
    }

    /* Add faint highlight to focused container */
    .shadow-sm {
        background-color: rgba(52, 152, 219, 0.03);
        border-radius: 4px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Form validation
        const form = document.querySelector('.medicine-form');
        const priceField = document.getElementById('medicine-price');
        const editPriceBtn = document.getElementById('edit-price-btn');
        const priceUpdateStatus = document.getElementById('price-update-status');
        const urlParams = new URLSearchParams(window.location.search);
        const medicineId = urlParams.get('id');

        // Show edit button only when editing an existing medicine
        if (medicineId) {
            editPriceBtn.style.display = 'block';

            // Handle price edit button click
            editPriceBtn.addEventListener('click', function() {
                const currentPrice = priceField.value;

                // Create modal for price editing
                const modalHtml = `
                    <div class="modal fade" id="editPriceModal" tabindex="-1" aria-labelledby="editPriceModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header bg-primary text-white">
                                    <h5 class="modal-title" id="editPriceModalLabel">Update Medicine Price</h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="new-price" class="form-label">New Price (₱)</label>
                                        <input type="number" class="form-control" id="new-price" value="${currentPrice}" min="0.01" step="0.01">
                                        <div class="form-text">Enter the new price for this medicine</div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary" id="save-price-btn">Save Changes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Add modal to the document
                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // Initialize and show the modal
                const priceModal = new bootstrap.Modal(document.getElementById('editPriceModal'));
                priceModal.show();

                // Handle save button click
                document.getElementById('save-price-btn').addEventListener('click', function() {
                    const newPrice = document.getElementById('new-price').value;

                    if (parseFloat(newPrice) <= 0) {
                        alert('Price must be greater than zero');
                        return;
                    }

                    // Update the price via AJAX
                    updateMedicinePrice(medicineId, newPrice, priceModal);
                });
            });
        }

        // Function to update medicine price
        function updateMedicinePrice(medicineId, newPrice, modal) {
            // Show loading indicator
            priceUpdateStatus.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div> Updating price...';

            fetch('{% url "update_medicine_price" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify({
                    medicine_id: medicineId,
                    price: newPrice
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the price field
                    priceField.value = data.new_price;

                    // Show success message
                    priceUpdateStatus.innerHTML = '<div class="alert alert-success py-1 px-2 mb-0"><i class="fas fa-check-circle me-1"></i>' + data.message + '</div>';

                    // Close the modal
                    modal.hide();

                    // Remove the modal from DOM after hiding
                    document.getElementById('editPriceModal').addEventListener('hidden.bs.modal', function() {
                        this.remove();
                    });

                    // Clear the success message after 5 seconds
                    setTimeout(() => {
                        priceUpdateStatus.innerHTML = '';
                    }, 5000);
                } else {
                    // Show error message
                    priceUpdateStatus.innerHTML = '<div class="alert alert-danger py-1 px-2 mb-0"><i class="fas fa-exclamation-circle me-1"></i>Error: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                priceUpdateStatus.innerHTML = '<div class="alert alert-danger py-1 px-2 mb-0"><i class="fas fa-exclamation-circle me-1"></i>Error updating price. Please try again.</div>';
            });
        }

        form.addEventListener('submit', function (e) {
            let isValid = true;

            // Validate required fields
            form.querySelectorAll('input[required]').forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            // Validate price is positive
            if (priceField && parseFloat(priceField.value) <= 0) {
                isValid = false;
                priceField.classList.add('is-invalid');

                // Create error message if it doesn't exist
                let errorMsg = priceField.parentElement.querySelector('.invalid-feedback');
                if (!errorMsg) {
                    errorMsg = document.createElement('div');
                    errorMsg.className = 'invalid-feedback';
                    priceField.parentElement.appendChild(errorMsg);
                }
                errorMsg.textContent = 'Price must be greater than zero';
            }

            // Validate reorder values
            const reorderLevel = form.querySelector('input[name="reorder_level"]');
            const reorderQuantity = form.querySelector('input[name="reorder_quantity"]');

            if (reorderLevel && parseInt(reorderLevel.value) <= 0) {
                isValid = false;
                reorderLevel.classList.add('is-invalid');
                let errorMsg = reorderLevel.parentElement.querySelector('.invalid-feedback');
                if (!errorMsg) {
                    errorMsg = document.createElement('div');
                    errorMsg.className = 'invalid-feedback';
                    reorderLevel.parentElement.appendChild(errorMsg);
                }
                errorMsg.textContent = 'Reorder level must be greater than zero';
            }

            if (reorderQuantity && parseInt(reorderQuantity.value) <= 0) {
                isValid = false;
                reorderQuantity.classList.add('is-invalid');
                let errorMsg = reorderQuantity.parentElement.querySelector('.invalid-feedback');
                if (!errorMsg) {
                    errorMsg = document.createElement('div');
                    errorMsg.className = 'invalid-feedback';
                    reorderQuantity.parentElement.appendChild(errorMsg);
                }
                errorMsg.textContent = 'Reorder quantity must be greater than zero';
            }

            if (!isValid) {
                e.preventDefault();
            }
        });

        // Highlight fields on focus
        form.querySelectorAll('.form-control, .form-select').forEach(field => {
            field.addEventListener('focus', function () {
                this.closest('.col-md-6')?.classList.add('shadow-sm');
                this.closest('.col-12')?.classList.add('shadow-sm');
            });

            field.addEventListener('blur', function () {
                this.closest('.col-md-6')?.classList.remove('shadow-sm');
                this.closest('.col-12')?.classList.remove('shadow-sm');
            });
        });

        // Add today's date as min date for expiration
        const expirationDateField = document.querySelector('input[name="expiration_date"]');
        if (expirationDateField) {
            const today = new Date();
            const yyyy = today.getFullYear();
            const mm = String(today.getMonth() + 1).padStart(2, '0');
            const dd = String(today.getDate()).padStart(2, '0');
            expirationDateField.min = `${yyyy}-${mm}-${dd}`;
        }
    });
</script>
{% endblock %}
