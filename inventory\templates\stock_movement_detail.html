{% extends 'base.html' %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary fw-bold">
                        <i class="fas fa-exchange-alt me-2"></i>Stock Movement Details
                    </h1>
                    <p class="text-muted mt-2">
                        Detailed information about this stock movement including medicine details, movement type,
                        quantity, brand, supplier, and related movements.
                    </p>
                </div>
                <div>
                    <a href="{% url 'medicine_movement_analysis' movement.medicine.id %}" class="btn btn-info rounded-pill">
                        <i class="fas fa-chart-line me-1"></i>Movement Analysis
                    </a>
                    <a href="{% url 'stock_movement_list' %}" class="btn btn-outline-secondary rounded-pill">
                        <i class="fas fa-arrow-left me-1"></i>Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Movement Information</h6>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">Medicine</p>
                            <h5>
                                <a href="{% url 'medicine_detail' movement.medicine.id %}">{{ movement.medicine.name }}</a>
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">Movement Type</p>
                            <h5>
                                {% if movement.movement_type == 'purchase' %}
                                <span class="badge bg-success">Purchase</span>
                                {% elif movement.movement_type == 'sale' %}
                                <span class="badge bg-danger">Sale</span>
                                {% elif movement.movement_type == 'adjustment' %}
                                <span class="badge bg-warning">Adjustment</span>
                                {% elif movement.movement_type == 'return' %}
                                <span class="badge bg-info">Return</span>
                                {% elif movement.movement_type == 'expired' %}
                                <span class="badge bg-dark">Expired</span>
                                {% elif movement.movement_type == 'transfer' %}
                                <span class="badge bg-primary">Transfer</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ movement.get_movement_type_display }}</span>
                                {% endif %}
                            </h5>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">Quantity</p>
                            <h5>
                                {% if movement.quantity > 0 %}
                                <span class="text-success">+{{ movement.quantity }}</span>
                                {% else %}
                                <span class="text-danger">{{ movement.quantity }}</span>
                                {% endif %}
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">Movement Date</p>
                            <h5>{{ movement.movement_date|date:"M d, Y H:i" }}</h5>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">Brand</p>
                            <h5>
                                {% if movement.brand %}
                                <span class="badge bg-info">{{ movement.brand }}</span>
                                {% else %}
                                <span class="text-muted">Not specified</span>
                                {% endif %}
                            </h5>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">Supplier</p>
                            <h5>
                                {% if movement.supplier %}
                                <span class="badge bg-secondary">{{ movement.supplier }}</span>
                                {% else %}
                                <span class="text-muted">Not specified</span>
                                {% endif %}
                            </h5>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">Reference Number</p>
                            <h5>{{ movement.reference_number|default:"Not specified" }}</h5>
                        </div>
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">Value</p>
                            <h5>₱{{ movement.total_value }}</h5>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-12">
                            <p class="mb-0 text-muted">Notes</p>
                            <p>{{ movement.notes|default:"No notes provided"|linebreaks }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Audit Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p class="mb-0 text-muted">Performed By</p>
                        <h5>{{ movement.performed_by.get_full_name|default:movement.performed_by.username|default:"System" }}</h5>
                    </div>
                    <div class="mb-3">
                        <p class="mb-0 text-muted">Created At</p>
                        <h5>{{ movement.audit_trail.created_at|date:"M d, Y H:i"|default:movement.movement_date|date:"M d, Y H:i" }}</h5>
                    </div>
                    <div class="mb-3">
                        <p class="mb-0 text-muted">Days Since Movement</p>
                        <h5>{{ movement.days_since_movement }} days</h5>
                    </div>
                </div>
            </div>
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Medicine Information</h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <p class="mb-0 text-muted">Current Quantity</p>
                        <h5>{{ movement.medicine.quantity }}</h5>
                    </div>
                    <div class="mb-3">
                        <p class="mb-0 text-muted">Category</p>
                        <h5>{{ movement.medicine.category|default:"Not categorized" }}</h5>
                    </div>
                    <div class="mb-3">
                        <p class="mb-0 text-muted">Unit Price</p>
                        <h5>₱{{ movement.medicine.price }}</h5>
                    </div>
                    <div class="mb-3">
                        <a href="{% url 'medicine_detail' movement.medicine.id %}" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye me-1"></i>View Medicine Details
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Related Movements -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Related Movements</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Quantity</th>
                            <th>Brand</th>
                            <th>Supplier</th>
                            <th>Reference</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for related in related_movements %}
                        <tr>
                            <td>{{ related.movement_date|date:"M d, Y H:i" }}</td>
                            <td>
                                {% if related.movement_type == 'purchase' %}
                                <span class="badge bg-success">Purchase</span>
                                {% elif related.movement_type == 'sale' %}
                                <span class="badge bg-danger">Sale</span>
                                {% elif related.movement_type == 'adjustment' %}
                                <span class="badge bg-warning">Adjustment</span>
                                {% elif related.movement_type == 'return' %}
                                <span class="badge bg-info">Return</span>
                                {% elif related.movement_type == 'expired' %}
                                <span class="badge bg-dark">Expired</span>
                                {% elif related.movement_type == 'transfer' %}
                                <span class="badge bg-primary">Transfer</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ related.get_movement_type_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if related.quantity > 0 %}
                                <span class="text-success">+{{ related.quantity }}</span>
                                {% else %}
                                <span class="text-danger">{{ related.quantity }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if related.brand %}
                                <span class="badge bg-info">{{ related.brand }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if related.supplier %}
                                <span class="badge bg-secondary">{{ related.supplier }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>{{ related.reference_number|default:"-" }}</td>
                            <td>
                                <a href="{% url 'stock_movement_detail' related.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">No related movements found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            <div class="mt-3">
                <a href="{% url 'medicine_movement_analysis' movement.medicine.id %}" class="btn btn-primary">
                    <i class="fas fa-chart-line me-1"></i>View All Movements
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
