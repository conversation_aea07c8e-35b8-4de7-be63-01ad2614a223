// Forecast Detail Page JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize charts
    initDemandChart();
    initQuarterlyChart();

    // Initialize feature importance chart if data is available
    if (window.featureImportanceData) {
        initFeatureImportanceChart();
    }
});

// Initialize demand chart
function initDemandChart() {
    const ctx = document.getElementById('demandChart');
    if (!ctx) return;

    // Get chart data from the page
    const labels = Array.from(document.querySelectorAll('.month-label')).map(el => el.textContent);
    const values = Array.from(document.querySelectorAll('.month-value')).map(el => parseFloat(el.textContent));

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Monthly Demand',
                data: values,
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2,
                tension: 0.4,
                pointRadius: 4,
                pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleFont: {
                        size: 14
                    },
                    bodyFont: {
                        size: 13
                    },
                    padding: 10,
                    displayColors: true
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false,
                        color: 'rgba(200, 200, 200, 0.2)'
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                }
            }
        }
    });
}

// Initialize quarterly chart
function initQuarterlyChart() {
    const ctx = document.getElementById('quarterlyChart');
    if (!ctx) return;

    // Get chart data from the page
    const labels = Array.from(document.querySelectorAll('.quarter-label')).map(el => el.textContent);
    const values = Array.from(document.querySelectorAll('.quarter-value')).map(el => parseFloat(el.textContent));

    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Quarterly Demand',
                data: values,
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'top'
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    titleFont: {
                        size: 14
                    },
                    bodyFont: {
                        size: 13
                    },
                    padding: 10,
                    displayColors: true
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        drawBorder: false,
                        color: 'rgba(200, 200, 200, 0.2)'
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            size: 12
                        }
                    }
                }
            }
        }
    });
}

// Initialize feature importance chart
function initFeatureImportanceChart() {
    try {
        const ctx = document.getElementById('featureImportanceChart');
        if (!ctx) return;

        // Check if data is valid
        if (!window.featureImportanceData ||
            !window.featureImportanceData.values ||
            !window.featureImportanceData.labels ||
            window.featureImportanceData.values.length === 0) {
            console.error('Invalid feature importance data');
            return;
        }

        // Sort data by importance (descending)
        const sortedIndices = window.featureImportanceData.values
            .map((value, index) => ({ value, index }))
            .sort((a, b) => b.value - a.value)
            .map(item => item.index);

        const sortedLabels = sortedIndices.map(i => window.featureImportanceData.labels[i]);
        const sortedValues = sortedIndices.map(i => window.featureImportanceData.values[i]);

        // Use only top 10 features
        const labels = sortedLabels.slice(0, 10);
        const values = sortedValues.slice(0, 10);

        // Generate colors with gradient
        const colors = values.map((value, index) => {
            const hue = 200; // Blue hue
            const saturation = 80;
            const lightness = 50 + (index * 3); // Gradually lighter
            return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
        });

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Feature Importance',
                    data: values,
                    backgroundColor: colors,
                    borderColor: colors,
                    borderWidth: 1
                }]
            },
            options: {
                indexAxis: 'y',
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Importance: ${context.raw.toFixed(4)}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Importance Score'
                        }
                    }
                }
            }
        });
    } catch (error) {
        console.error('Error initializing feature importance chart:', error);
    }
}
