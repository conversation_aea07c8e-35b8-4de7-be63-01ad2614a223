from functools import wraps
from django.shortcuts import redirect
from django.contrib import messages

def email_verification_required(function):
    @wraps(function)
    def wrap(request, *args, **kwargs):
        if request.user.is_authenticated:
            if hasattr(request.user, 'profile') and request.user.profile.email_verified:
                return function(request, *args, **kwargs)
            else:
                messages.warning(request, 'Please verify your email address to access this feature.')
                return redirect('verify_email_prompt')
        return redirect('login')
    return wrap