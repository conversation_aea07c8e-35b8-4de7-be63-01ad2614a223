{% extends "admin/base_site.html" %}
{% load i18n admin_urls %}

{% block extrastyle %}
{{ block.super }}
<style type="text/css">
    .user-info-card {
        margin-bottom: 2em;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        padding: 1em;
        border-bottom: 1px solid #eee;
        font-weight: bold;
    }

    .user-details {
        display: flex;
        flex-wrap: wrap;
        padding: 1em;
        background: #f9f9f9;
    }

    .user-detail-item {
        min-width: 200px;
        margin-right: 2em;
        margin-bottom: 0.5em;
    }

    .user-detail-label {
        font-weight: 600;
        margin-bottom: 0.25em;
    }

    .form-container {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 1em;
        margin-bottom: 1.5em;
    }

    .form-title {
        margin-top: 0;
        padding-bottom: 0.75em;
        border-bottom: 1px solid #eee;
        font-size: 1.2em;
        font-weight: 500;
    }

    .form-row {
        padding: 1em 0;
        border-bottom: 1px solid #f5f5f5;
    }

        .form-row:last-child {
            border-bottom: none;
        }

    .field-box label {
        display: block;
        font-weight: 600;
        margin-bottom: 0.5em;
    }

    .field-box input[type="text"],
    .field-box input[type="password"] {
        width: 100%;
        padding: 0.75em;
        border-radius: 4px;
        border: 1px solid #ddd;
        font-size: 14px;
    }

    .field-box input:focus {
        border-color: #aaa;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
    }

    .checkbox-container {
        display: flex;
        align-items: center;
        margin-top: 0.5em;
    }

        .checkbox-container input[type="checkbox"] {
            margin-right: 0.5em;
            transform: scale(1.2);
        }

        .checkbox-container label {
            display: inline;
            font-weight: normal;
        }

    .submit-row {
        display: flex;
        justify-content: flex-end;
        padding: 1.5em 0 0.5em;
    }

        .submit-row input[type="submit"] {
            font-weight: 600;
            padding: 0.75em 1.5em;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 1em;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.5px;
        }

    .cancel-link {
        padding: 0.75em 1.5em;
        border-radius: 4px;
        text-decoration: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.9em;
        letter-spacing: 0.5px;
    }

    .help {
        font-size: 0.85em;
        margin-top: 0.5em;
        font-style: italic;
    }

    .password-strength {
        margin-top: 0.5em;
    }

    .password-strength-meter {
        height: 5px;
        width: 100%;
        background-color: #f5f5f5;
        border-radius: 3px;
        margin-top: 0.5em;
    }

    .password-strength-meter-fill {
        height: 100%;
        border-radius: 3px;
        width: 0;
        transition: width 0.3s ease;
    }

    .password-strength-text {
        font-size: 0.85em;
        margin-top: 0.3em;
    }

    .password-toggle {
        position: absolute;
        right: 0.75em;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        cursor: pointer;
        font-size: 0.85em;
        opacity: 0.7;
    }

        .password-toggle:hover {
            opacity: 1;
        }

    .password-field-wrapper {
        position: relative;
    }

    .disabled-field {
        background-color: #f5f5f5;
        cursor: not-allowed;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:auth_user_changelist' %}">{% trans 'Users' %}</a>
    &rsaquo; <span>{% trans 'Reset Password' %}</span>
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="user-info-card">
        <div class="card-header">
            User Information
        </div>
        <div class="user-details">
            <div class="user-detail-item">
                <div class="user-detail-label">Username</div>
                <div>{{ user.username }}</div>
            </div>
            {% if user.first_name or user.last_name %}
            <div class="user-detail-item">
                <div class="user-detail-label">Name</div>
                <div>{{ user.first_name }} {{ user.last_name }}</div>
            </div>
            {% endif %}
            <div class="user-detail-item">
                <div class="user-detail-label">Email</div>
                <div>{{ user.email|default:"-" }}</div>
            </div>
            <div class="user-detail-item">
                <div class="user-detail-label">Last Login</div>
                <div>{{ user.last_login|date:"F j, Y, g:i a"|default:"-" }}</div>
            </div>
        </div>
    </div>

    <div class="form-container">
        <h2 class="form-title">Reset Password</h2>
        <form method="post">
            {% csrf_token %}

            <div class="form-row">
                <div class="field-box">
                    <label for="custom_password">New Password:</label>
                    <div class="password-field-wrapper">
                        <input type="password" id="custom_password" name="custom_password" autocomplete="new-password" placeholder="Enter a new password">
                        <button type="button" class="password-toggle" id="password-toggle">Show</button>
                    </div>
                    <p class="help">Leave empty to generate a random password</p>

                    <div class="password-strength">
                        <div class="password-strength-meter">
                            <div class="password-strength-meter-fill" id="password-strength-meter-fill"></div>
                        </div>
                        <div class="password-strength-text" id="password-strength-text"></div>
                    </div>
                </div>
            </div>

            <div class="form-row">
                <div class="field-box">
                    <div class="checkbox-container">
                        <input type="checkbox" id="generate_random" name="generate_random">
                        <label for="generate_random">Generate Random Password</label>
                    </div>
                    <p class="help">A secure 12-character password will be automatically generated</p>
                </div>
            </div>

            <div class="submit-row">
                <a href="{% url 'admin:auth_user_changelist' %}" class="button cancel-link">Cancel</a>
                <input type="submit" value="Reset Password" class="default" name="_reset">
            </div>
        </form>
    </div>
</div>

<script>
    // Toggle password visibility
    document.getElementById('password-toggle').addEventListener('click', function () {
        var passwordField = document.getElementById('custom_password');
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            this.textContent = 'Hide';
        } else {
            passwordField.type = 'password';
            this.textContent = 'Show';
        }
    });

    // Toggle the password field based on checkbox
    document.getElementById('generate_random').addEventListener('change', function () {
        var passwordField = document.getElementById('custom_password');
        var passwordToggle = document.getElementById('password-toggle');
        var strengthMeter = document.getElementById('password-strength-meter-fill');
        var strengthText = document.getElementById('password-strength-text');

        if (this.checked) {
            passwordField.disabled = true;
            passwordField.value = '';
            passwordField.classList.add('disabled-field');
            passwordToggle.disabled = true;
            passwordToggle.style.opacity = 0.5;
            strengthMeter.style.width = '0';
            strengthText.textContent = '';
        } else {
            passwordField.disabled = false;
            passwordField.classList.remove('disabled-field');
            passwordToggle.disabled = false;
            passwordToggle.style.opacity = 0.7;
        }
    });

    // Password strength meter
    document.getElementById('custom_password').addEventListener('input', function () {
        var password = this.value;
        var strength = 0;
        var strengthMeter = document.getElementById('password-strength-meter-fill');
        var strengthText = document.getElementById('password-strength-text');

        if (password.length === 0) {
            strengthMeter.style.width = '0';
            strengthMeter.style.backgroundColor = '#f5f5f5';
            strengthText.textContent = '';
            return;
        }

        // Check password length
        if (password.length > 7) strength += 25;

        // Check for mixed case
        if (password.match(/[a-z]/) && password.match(/[A-Z]/)) strength += 25;

        // Check for numbers
        if (password.match(/\d/)) strength += 25;

        // Check for special characters
        if (password.match(/[^a-zA-Z\d]/)) strength += 25;

        // Update the strength meter
        strengthMeter.style.width = strength + '%';

        // Set color and text based on strength
        if (strength < 25) {
            strengthMeter.style.backgroundColor = '#ff5252';
            strengthText.textContent = 'Very Weak';
        } else if (strength < 50) {
            strengthMeter.style.backgroundColor = '#ffab40';
            strengthText.textContent = 'Weak';
        } else if (strength < 75) {
            strengthMeter.style.backgroundColor = '#ffd740';
            strengthText.textContent = 'Moderate';
        } else if (strength < 100) {
            strengthMeter.style.backgroundColor = '#9ccc65';
            strengthText.textContent = 'Strong';
        } else {
            strengthMeter.style.backgroundColor = '#4caf50';
            strengthText.textContent = 'Very Strong';
        }
    });
</script>
{% endblock %}
