{% extends "base.html" %}

{% block title %}User Management{% endblock %}

{% block content %}
<div class="container my-5">
    <h1>User Management</h1>

    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">

            <form class="d-flex" method="get">
                <input class="form-control me-2" type="search" name="q" placeholder="Search users" aria-label="Search" value="{{ request.GET.q }}">
                <button class="btn btn-outline-primary" type="submit">Search</button>
            </form>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Username</th>
                            <th>Email</th>
                            <th>Full Name</th>
                            <th>Joined</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for user in users %}
                        <tr>
                            <td>
                                {{ user.username }}
                                {% if user.id in users_with_pending_changes %}
                                <span class="badge bg-warning ms-2" title="Has pending profile changes">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </span>
                                {% endif %}
                            </td>
                            <td>{{ user.email }}</td>
                            <td>{{ user.first_name }} {{ user.last_name }}</td>
                            <td>{{ user.date_joined|date:"M d, Y" }}</td>
                            <td>
                                <span class="badge {% if user.is_active %}bg-success{% else %}bg-danger{% endif %}">
                                    {% if user.is_active %}Active{% else %}Inactive{% endif %}
                                </span>
                            </td>
                            <td>
                                <a href="{% url 'user_detail' user.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>

                                {% if user.id in users_with_pending_changes %}
                                <a href="{% url 'user_detail' user.id %}#pending-changes" class="btn btn-sm btn-warning">
                                    <i class="fas fa-exclamation-triangle"></i> Review
                                </a>
                                {% endif %}

                                <button class="btn btn-sm {% if user.is_active %}btn-outline-warning{% else %}btn-outline-success{% endif %} toggle-status"
                                        data-user-id="{{ user.id }}"
                                        data-current-status="{% if user.is_active %}active{% else %}inactive{% endif %}">
                                    <i class="fas {% if user.is_active %}fa-ban{% else %}fa-check-circle{% endif %}"></i>
                                    {% if user.is_active %}Deactivate{% else %}Activate{% endif %}
                                </button>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">No users found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if users.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if users.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ users.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&laquo;</span>
                    </li>
                    {% endif %}

                    {% for i in users.paginator.page_range %}
                    {% if users.number == i %}
                    <li class="page-item active">
                        <span class="page-link">{{ i }}</span>
                    </li>
                    {% else %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ i }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}">{{ i }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if users.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ users.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link" aria-hidden="true">&raquo;</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const toggleButtons = document.querySelectorAll('.toggle-status');

    toggleButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.getAttribute('data-user-id');
            const currentStatus = this.getAttribute('data-current-status');
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

            fetch(`/users/${userId}/toggle-status/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken,
                    'Content-Type': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update button text and class
                    if (data.is_active) {
                        this.textContent = 'Deactivate';
                        this.classList.remove('btn-success');
                        this.classList.add('btn-warning');
                        this.setAttribute('data-current-status', 'active');
                        this.closest('tr').querySelector('.badge').textContent = 'Active';
                        this.closest('tr').querySelector('.badge').classList.remove('bg-danger');
                        this.closest('tr').querySelector('.badge').classList.add('bg-success');
                    } else {
                        this.textContent = 'Activate';
                        this.classList.remove('btn-warning');
                        this.classList.add('btn-success');
                        this.setAttribute('data-current-status', 'inactive');
                        this.closest('tr').querySelector('.badge').textContent = 'Inactive';
                        this.closest('tr').querySelector('.badge').classList.remove('bg-success');
                        this.closest('tr').querySelector('.badge').classList.add('bg-danger');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
    });
});
</script>
{% endblock %}

{% endblock %}