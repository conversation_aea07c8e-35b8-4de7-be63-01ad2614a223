# Generated by Django 4.2.9 on 2025-04-13 09:19

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_management', '0013_alter_userprofile_profile_picture '),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='userprofile',
            name='phone_number',
            field=models.CharField(blank=True, max_length=17, validators=[django.core.validators.RegexValidator(message='Please enter a valid Philippine phone number (e.g., +639123456789 or 09123456789).', regex='^(\\+639|09)\\d{9}$')]),
        ),
    ]
