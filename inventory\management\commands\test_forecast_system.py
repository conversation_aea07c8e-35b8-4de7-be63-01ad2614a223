"""
Advanced machine learning forecasting methods for inventory management.

This module implements Gradient Boosting and feature engineering for improved forecast accuracy.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Sum, Avg, Count, F, Q
import logging
import joblib
import os
from pathlib import Path
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import xgboost as xgb
from inventory.models import Medicine, Transaction, Forecast


from django.core.management.base import BaseCommand
import logging

_logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = "Test the forecasting system with sample data"
    
    def handle(self, *args, **options):
        self.stdout.write("Testing forecasting system...")
        # Your existing code goes here
        try:
            # Your test logic
            self.stdout.write(self.style.SUCCESS("Forecasting system test completed successfully"))
        except Exception as e:
            _logger.error(f"Error during forecast system test: {str(e)}")
            self.stdout.write(self.style.ERROR(f"Test failed: {str(e)}"))

# Create models directory if it doesn't exist
MODELS_DIR = Path('inventory/ml_models')
MODELS_DIR.mkdir(exist_ok=True)

def calculate_gb_accuracy(y_true, y_pred):
    """
    Calculate accuracy for gradient boosting predictions.
    
    Args:
        y_true: Actual values
        y_pred: Predicted values
        
    Returns:
        Accuracy as a percentage
    """
    # Avoid division by zero
    if np.sum(y_true) == 0:
        return 0
    
    # Calculate MAPE (Mean Absolute Percentage Error)
    mape = np.mean(np.abs((y_true - y_pred) / np.maximum(1, y_true))) * 100
    
    # Convert to accuracy (100 - MAPE, capped at 0)
    accuracy = max(0, 100 - mape)
    
    return accuracy

def engineer_features(medicine, days_history=180):
    """
    Engineer features for gradient boosting model.
    
    Args:
        medicine: Medicine object
        days_history: Number of days of history to use
        
    Returns:
        DataFrame with engineered features
    """
    # Get local reference to logger
    _logger = logging.getLogger(__name__)
    
    try:
        # Get transaction history
        end_date = timezone.now()
        start_date = end_date - timedelta(days=days_history)
        
        transactions = Transaction.objects.filter(
            medicine=medicine,
            transaction_date__gte=start_date,
            transaction_date__lte=end_date,
            transaction_type='OUT'
        ).order_by('transaction_date')
        
        if transactions.count() < 10:
            _logger.warning(f"Not enough transaction data for {medicine.name}")
            return None
        
        # Create daily aggregation
        daily_data = []
        current_date = start_date
        
        while current_date <= end_date:
            next_date = current_date + timedelta(days=1)
            
            # Get transactions for this day
            day_transactions = transactions.filter(
                transaction_date__gte=current_date,
                transaction_date__lt=next_date
            )
            
            # Calculate daily quantity
            daily_quantity = day_transactions.aggregate(
                total=Sum('quantity')
            )['total'] or 0
            
            # Add to dataset
            daily_data.append({
                'date': current_date.date(),
                'day_of_week': current_date.weekday(),
                'day_of_month': current_date.day,
                'month': current_date.month,
                'quantity': daily_quantity,
                'is_weekend': 1 if current_date.weekday() >= 5 else 0,
                'is_month_start': 1 if current_date.day <= 3 else 0,
                'is_month_end': 1 if current_date.day >= 28 else 0,
            })
            
            current_date = next_date
        
        # Convert to DataFrame
        df = pd.DataFrame(daily_data)
        
        if len(df) < 30:
            _logger.warning(f"Not enough daily data points for {medicine.name}")
            return None
        
        # Add lag features
        for lag in [1, 3, 7, 14, 30]:
            if len(df) > lag:
                df[f'lag_{lag}'] = df['quantity'].shift(lag)
        
        # Add rolling window features
        for window in [3, 7, 14, 30]:
            if len(df) > window:
                df[f'rolling_mean_{window}'] = df['quantity'].rolling(window=window).mean()
                df[f'rolling_std_{window}'] = df['quantity'].rolling(window=window).std()
                df[f'rolling_max_{window}'] = df['quantity'].rolling(window=window).max()
        
        # Add trend features
        df['trend'] = np.arange(len(df))
        df['trend_squared'] = df['trend'] ** 2
        
        # Add seasonality features
        df['sin_day'] = np.sin(2 * np.pi * df['day_of_month'] / 30)
        df['cos_day'] = np.cos(2 * np.pi * df['day_of_month'] / 30)
        df['sin_week'] = np.sin(2 * np.pi * df['day_of_week'] / 7)
        df['cos_week'] = np.cos(2 * np.pi * df['day_of_week'] / 7)
        df['sin_month'] = np.sin(2 * np.pi * df['month'] / 12)
        df['cos_month'] = np.cos(2 * np.pi * df['month'] / 12)
        
        # Drop rows with NaN values
        df = df.dropna()
        
        return df
        
    except Exception as e:
        _logger.error(f"Error engineering features for {medicine.name}: {str(e)}")
        return None

def train_gradient_boosting_model(medicine, days_history=180):
    """
    Train a gradient boosting model for forecasting.
    
    Args:
        medicine: Medicine object
        days_history: Number of days of history to use
        
    Returns:
        Trained model and feature list
    """
    # Get local reference to logger
    _logger = logging.getLogger(__name__)
    
    try:
        # Engineer features
        data = engineer_features(medicine, days_history)
        
        if data is None or len(data) < 30:
            _logger.warning(f"Insufficient data for {medicine.name} to train gradient boosting model")
            return None, None
        
        # Prepare features and target
        X = data.drop(['date', 'quantity'], axis=1)
        y = data['quantity']
        
        # Save feature names for later use
        feature_names = list(X.columns)
        
        # Split data
        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Scale features
        scaler = StandardScaler()
        X_train = scaler.fit_transform(X_train)
        X_val = scaler.transform(X_val)
        
        # Create and train model
        model = xgb.XGBRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=4,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42
        )
        
        # Simple fit without problematic parameters
        model.fit(X_train, y_train)
        
        # Save model
        model_path = MODELS_DIR / f"gb_model_{medicine.id}.joblib"
        joblib.dump(model, model_path)
        
        # Save feature names
        feature_path = MODELS_DIR / f"gb_features_{medicine.id}.joblib"
        joblib.dump(feature_names, feature_path)
        
        # Calculate accuracy on validation set
        y_pred = model.predict(X_val)
        accuracy = calculate_gb_accuracy(y_val, y_pred)
        
        _logger.info(f"Trained gradient boosting model for {medicine.name} with accuracy: {accuracy:.2f}%")
        
        return model, feature_names
    
    except Exception as e:
        _logger.error(f"Model training failed for {medicine.name}: {str(e)}")
        return None, None

def predict_with_gradient_boosting(medicine, forecast_horizon=30):
    """
    Generate forecasts using gradient boosting.
    
    Args:
        medicine: Medicine object
        forecast_horizon: Number of days to forecast
        
    Returns:
        Predicted quantities for the forecast horizon
    """
    # Get local reference to logger
    _logger = logging.getLogger(__name__)
    
    try:
        # Check if model exists
        model_path = MODELS_DIR / f"gb_model_{medicine.id}.joblib"
        feature_path = MODELS_DIR / f"gb_features_{medicine.id}.joblib"
        
        if not model_path.exists() or not feature_path.exists():
            # Train new model
            model, feature_names = train_gradient_boosting_model(medicine)
            if model is None:
                _logger.warning(f"Could not train gradient boosting model for {medicine.name}")
                return None
        else:
            # Load existing model
            model = joblib.load(model_path)
            feature_names = joblib.load(feature_path)
        
        # Get recent data for prediction
        data = engineer_features(medicine)
        if data is None:
            _logger.warning(f"Could not engineer features for {medicine.name}")
            return None
        
        # Get the most recent data point
        last_row = data.iloc[-1:].copy()
        
        # Make prediction for the next period (average daily demand)
        X = last_row.drop(['date', 'quantity'], axis=1)
        X = X[feature_names]  # Ensure columns match the model's expected features
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Predict
        prediction = model.predict(X_scaled)[0]
        
        # Return the prediction (average daily demand)
        return max(0, prediction)
    
    except Exception as e:
        _logger.error(f"Error predicting with gradient boosting for {medicine.name}: {str(e)}")
        return None

def get_feature_importance(medicine):
    """
    Get feature importance from the gradient boosting model.
    
    Args:
        medicine: Medicine object
        
    Returns:
        Dictionary of feature importances
    """
    # Get local reference to logger
    _logger = logging.getLogger(__name__)
    
    try:
        # Check if model exists
        model_path = MODELS_DIR / f"gb_model_{medicine.id}.joblib"
        feature_path = MODELS_DIR / f"gb_features_{medicine.id}.joblib"
        
        if not model_path.exists() or not feature_path.exists():
            _logger.warning(f"No gradient boosting model found for {medicine.name}")
            return None
        
        # Load model and features
        model = joblib.load(model_path)
        feature_names = joblib.load(feature_path)
        
        # Get feature importance
        importance = model.feature_importances_
        
        # Create dictionary of feature importances
        importance_dict = dict(zip(feature_names, importance))
        
        # Sort by importance
        importance_dict = {k: v for k, v in sorted(importance_dict.items(), key=lambda item: item[1], reverse=True)}
        
        return importance_dict
    
    except Exception as e:
        _logger.error(f"Error getting feature importance for {medicine.name}: {str(e)}")
        return None