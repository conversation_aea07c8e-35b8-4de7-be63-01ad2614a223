﻿{% extends 'base.html' %}

{% block title %}Transaction Detail{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0 rounded-lg">
                <div class="card-body d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0 text-gradient-primary fw-bold">Transaction Detail</h1>
                        <p class="text-muted mb-0">Reference #{{ transaction.pk }}</p>
                    </div>
                    <div class="d-flex align-items-center gap-3">
                        <div class="text-end">
                            <p class="text-muted mb-0 small">Current Time</p>
                            <p class="mb-0 fw-semibold text-primary" id="current-time"></p>
                        </div>
                        <a href="{% url 'transaction_list' %}" class="btn btn-outline-primary btn-sm rounded-pill">
                            <i class="bi bi-arrow-left me-2"></i>Back to List
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row g-4">
        <!-- Transaction Details Card -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-primary-subtle me-3">
                            <i class="bi bi-info-circle text-primary"></i>
                        </div>
                        <h5 class="mb-0 fw-bold text-primary">Transaction Information</h5>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <!-- Basic Information -->
                        <div class="col-md-6">
                            <div class="info-card bg-light rounded-4 p-3 h-100">
                                <h6 class="text-primary mb-3">Basic Details</h6>
                                <div class="mb-3">
                                    <label class="text-muted small d-block">Medicine Name</label>
                                    <span class="fw-semibold">{{ transaction.medicine.name }}</span>
                                </div>
                                <div class="mb-3">
                                    <label class="text-muted small d-block">Brand</label>
                                    <span class="fw-semibold">{{ transaction.medicine.brand|default:"Generic" }}</span>
                                </div>
                                <div class="mb-3">
                                    <label class="text-muted small d-block">Supplier</label>
                                    <span class="fw-semibold">{{ transaction.medicine.supplier|default:"Unknown" }}</span>
                                </div>
                                <div class="mb-3">
                                    <label class="text-muted small d-block">Quantity</label>
                                    <span class="fw-semibold">{{ transaction.quantity }} units</span>
                                </div>
                                <div class="mb-3">
                                    <label class="text-muted small d-block">Transaction Type</label>
                                    <span class="badge {% if transaction.transaction_type == 'IN' %}bg-success-subtle text-success{% else %}bg-danger-subtle text-danger{% endif %} rounded-pill">
                                        {% if transaction.transaction_type == 'IN' %}Stock In{% else %}Stock Out{% endif %}
                                    </span>
                                </div>

                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div class="col-md-6">
                            <div class="info-card bg-light rounded-4 p-3 h-100">
                                <h6 class="text-primary mb-3">Customer Information</h6>
                                <div class="mb-3">
                                    <label class="text-muted small d-block">Customer Type</label>
                                    <span class="badge bg-info-subtle text-info rounded-pill">
                                        {{ transaction.get_customer_type_display }}
                                    </span>
                                </div>
                                <div class="mb-3">
                                    <label class="text-muted small d-block">Customer Name</label>
                                    <span class="fw-semibold">{{ transaction.customer_name|default:"Not specified" }}</span>
                                </div>
                                <div>
                                    <label class="text-muted small d-block">Patient Number</label>
                                    <span class="fw-semibold">{{ transaction.patient_number|default:"Not specified" }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Timing Information -->
                        <div class="col-md-6">
                            <div class="info-card bg-light rounded-4 p-3 h-100">
                                <h6 class="text-primary mb-3">Timing Details</h6>
                                <div class="mb-3">
                                    <label class="text-muted small d-block">Transaction Date</label>
                                    <span class="fw-semibold">{{ transaction.transaction_date|date:"F d, Y" }}</span>
                                </div>
                                <div>
                                    <label class="text-muted small d-block">Transaction Time</label>
                                    <span class="fw-semibold">{{ transaction.transaction_date|date:"h:i A" }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Pricing Information -->
                        <div class="col-md-6">
                            <div class="info-card bg-light rounded-4 p-3 h-100">
                                <h6 class="text-primary mb-3">Pricing Details</h6>
                                <div class="mb-3">
                                    <label class="text-muted small d-block">Unit Price</label>
                                    <span class="fw-semibold">₱{{ transaction.medicine.price|floatformat:2 }}</span>
                                </div>
                                <div>
                                    <label class="text-muted small d-block">Total Amount</label>
                                    <span class="fw-bold text-primary fs-5">₱{{ transaction.total_amount|floatformat:2|default:"0.00" }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="col-12">
                            <div class="info-card bg-light rounded-4 p-3">
                                <h6 class="text-primary mb-3">Description</h6>
                                <p class="mb-0">{{ transaction.medicine.description|default:"No description available" }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock Impact & Actions Cards -->
        <div class="col-lg-4">
            <!-- Stock Impact Card -->
            <div class="card shadow-sm border-0 mb-4">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex align-items-center">
                        <div class="icon-circle bg-primary-subtle me-3">
                            <i class="bi bi-graph-up text-primary"></i>
                        </div>
                        <h5 class="mb-0 fw-bold text-primary">Stock Impact</h5>
                    </div>
                </div>
                <div class="card-body text-center">
                    <div class="impact-circle mx-auto mb-3 {% if transaction.transaction_type == 'IN' %}bg-success-subtle{% else %}bg-danger-subtle{% endif %}">
                        <span class="display-4 fw-bold {% if transaction.transaction_type == 'IN' %}text-success{% else %}text-danger{% endif %}">
                            {% if transaction.transaction_type == 'IN' %}+{% else %}-{% endif %}{{ transaction.quantity }}
                        </span>
                    </div>
                    <p class="text-muted mb-4">
                        Units {% if transaction.transaction_type == 'IN' %}added to{% else %}removed from{% endif %} inventory
                    </p>
                    <div class="stats-container bg-light rounded-4 p-3">
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Processed by:</span>
                            <span class="fw-semibold">{{ transaction.created_by|default:"System" }}</span>
                        </div>
                        <div class="d-flex justify-content-between mb-2">
                            <span class="text-muted">Processing Time:</span>
                            <span class="fw-semibold">{{ transaction.transaction_date|date:"h:i A" }}</span>
                        </div>

                    </div>
                </div>
            </div>




        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>

    .text-gradient-primary {
        background: linear-gradient(45deg, var(--primary-color), #6610f2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .card {
        border-radius: 1rem;
        transition: all 0.3s ease;
    }

        .card:hover {
            transform: translateY(-5px);
        }

    .icon-circle {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .info-card {
        transition: all 0.3s ease;
    }

        .info-card:hover {
            background-color: white !important;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
        }

    .impact-circle {
        width: 150px;
        height: 150px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 1rem auto;
    }

    .badge {
        padding: 0.5em 1em;
        font-weight: 500;
    }

    .btn {
        padding: 0.75rem 1.5rem;
        font-weight: 500;
        border-radius: 0.75rem;
    }

    .stats-container {
        transition: all 0.3s ease;
    }

        .stats-container:hover {
            background-color: white !important;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
        }

    .bg-primary-subtle {
        background-color: var(--primary-light) !important;
    }

    .bg-success-subtle {
        background-color: var(--success-light) !important;
    }

    .bg-danger-subtle {
        background-color: var(--danger-light) !important;
    }

    .bg-info-subtle {
        background-color: var(--info-light) !important;
    }

    .rounded-4 {
        border-radius: 0.75rem !important;
    }
</style>
{% endblock %}


