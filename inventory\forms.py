from django import forms
from .models import Medicine, Transaction, Forecast, GenericMedicine, StockMovement, MovementAnalysis
from django.utils import timezone

class MedicineForm(forms.ModelForm):
    class Meta:
        model = Medicine
        fields = ('name', 'description', 'quantity', 'reorder_level', 'reorder_quantity',
                 'category', 'medicine_type', 'price', 'expiration_date',
                 'generic_medicine', 'brand', 'supplier')  # Include new fields
        widgets = {
            'expiration_date': forms.DateInput(
                attrs={'type': 'date', 'class': 'form-control'}
            ),
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make generic_medicine field optional in the form
        self.fields['generic_medicine'].required = False

        # Add help text for brand and supplier fields
        self.fields['brand'].help_text = "Brand name (e.g., Tylenol, Panadol)"
        self.fields['supplier'].help_text = "Supplier or manufacturer (e.g., Johnson & Johnson, GlaxoSmithKline)"

        # Add Bootstrap classes to all fields
        for field in self.fields:
            self.fields[field].widget.attrs.update({'class': 'form-control'})

class TransactionForm(forms.ModelForm):
    current_quantity = forms.IntegerField(label='Current Quantity', required=False, disabled=True)

    class Meta:
        model = Transaction
        fields = ('medicine', 'transaction_date', 'quantity', 'transaction_type',
                 'customer_name', 'patient_number', 'customer_type')
        widgets = {
            'transaction_date': forms.DateTimeInput(
                attrs={
                    'type': 'datetime-local',
                    'class': 'form-control'
                }
            ),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Set initial datetime with both date and time
        current_datetime = timezone.localtime(timezone.now())
        self.fields['transaction_date'].initial = current_datetime
        self.fields['transaction_date'].widget.attrs['readonly'] = True
        self.fields['current_quantity'].widget.attrs['id'] = 'current-quantity'

        if 'medicine' in self.data:
            try:
                medicine_id = int(self.data.get('medicine'))
                medicine = Medicine.objects.get(pk=medicine_id)
                self.fields['current_quantity'].initial = medicine.quantity
                self.medicine = medicine
            except (ValueError, Medicine.DoesNotExist):
                pass
        elif self.instance.pk:
            self.fields['current_quantity'].initial = self.instance.medicine.quantity
            self.medicine = self.instance.medicine

    def clean_quantity(self):
        quantity = self.cleaned_data.get('quantity')
        if hasattr(self, 'medicine'):
            # Only block if quantity exceeds available stock
            if quantity > self.medicine.quantity:
                raise forms.ValidationError('The quantity is more than the available stock.')

            # Add warning message but don't block transaction
            if self.medicine.quantity - quantity < self.medicine.reorder_level:
                from django.contrib import messages
                messages.warning(
                    self.request if hasattr(self, 'request') else None,
                    f'Warning: Stock will be below reorder level after this transaction.'
                )
        return quantity

    def clean_transaction_date(self):
        """Ensure transaction_date includes time component"""
        date = self.cleaned_data.get('transaction_date')
        if not date:
            date = timezone.now()
        return date

    def save(self, commit=True):
        instance = super().save(commit=False)
        if not instance.transaction_date:
            instance.transaction_date = timezone.now()
        if commit:
            instance.save()
        return instance
class GenericMedicineForm(forms.ModelForm):
    class Meta:
        model = GenericMedicine
        fields = ('name', 'category', 'description')
        widgets = {
            'description': forms.Textarea(attrs={'rows': 3}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add Bootstrap classes to all fields
        for field in self.fields:
            self.fields[field].widget.attrs.update({'class': 'form-control'})

class ForecastForm(forms.ModelForm):
    class Meta:
        model = Forecast
        fields = ('medicine', 'generic_medicine', 'forecast_date', 'predicted_quantity')
        widgets = {
            'forecast_date': forms.DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Make both medicine and generic_medicine optional in the form
        # At least one should be provided, but we'll handle that in clean()
        self.fields['medicine'].required = False
        self.fields['generic_medicine'].required = False

    def clean(self):
        cleaned_data = super().clean()
        medicine = cleaned_data.get('medicine')
        generic_medicine = cleaned_data.get('generic_medicine')

        # Ensure at least one of medicine or generic_medicine is provided
        if not medicine and not generic_medicine:
            raise forms.ValidationError("Either a specific medicine or a generic medicine category must be selected.")

        return cleaned_data


class StockMovementForm(forms.ModelForm):
    """Form for creating and editing stock movements"""

    class Meta:
        model = StockMovement
        fields = ('medicine', 'quantity', 'movement_type', 'movement_date',
                 'reference_number', 'notes', 'brand', 'supplier')
        widgets = {
            'movement_date': forms.DateTimeInput(
                attrs={'type': 'datetime-local', 'class': 'form-control'}
            ),
            'notes': forms.Textarea(attrs={'rows': 3, 'class': 'form-control'}),
        }

    def __init__(self, *args, **kwargs):
        self.user = kwargs.pop('user', None)
        super().__init__(*args, **kwargs)

        # Set initial datetime
        self.fields['movement_date'].initial = timezone.localtime(timezone.now())

        # Add Bootstrap classes to all fields
        for field in self.fields:
            if 'class' not in self.fields[field].widget.attrs:
                self.fields[field].widget.attrs.update({'class': 'form-control'})

        # Add help text
        self.fields['quantity'].help_text = "Enter positive value for additions, negative for reductions"
        self.fields['brand'].required = False
        self.fields['supplier'].required = False

        # Update movement type choices help text
        self.fields['movement_type'].choices = [
            ('purchase', 'Purchase'),
            ('sale', 'Sale'),
            ('adjustment', 'Inventory adjustment'),
            ('return', 'Return'),
            ('expired', 'Expired medicine removal'),
            ('transfer', 'Transfer'),
        ]

    def clean_quantity(self):
        """Validate quantity based on movement type"""
        quantity = self.cleaned_data.get('quantity')
        movement_type = self.cleaned_data.get('movement_type')

        # For sale and return movements, quantity should be negative
        if movement_type in ['sale', 'return', 'expired'] and quantity > 0:
            quantity = -quantity

        # For purchase and adjustment movements, quantity should be positive
        elif movement_type in ['purchase', 'adjustment'] and quantity < 0:
            quantity = abs(quantity)

        return quantity

    def save(self, commit=True):
        instance = super().save(commit=False)

        # Set the performed_by field to the current user
        if self.user and not instance.performed_by:
            instance.performed_by = self.user

        # Copy brand and supplier from medicine if not provided
        if not instance.brand and instance.medicine.brand:
            instance.brand = instance.medicine.brand

        if not instance.supplier and instance.medicine.supplier:
            instance.supplier = instance.medicine.supplier

        if commit:
            instance.save()

            # Update medicine quantity based on movement type
            medicine = instance.medicine

            if instance.movement_type in ['purchase', 'adjustment'] and instance.quantity > 0:
                medicine.quantity += instance.quantity
            elif instance.movement_type in ['sale', 'return', 'expired'] and instance.quantity < 0:
                medicine.quantity = max(0, medicine.quantity + instance.quantity)  # Ensure quantity doesn't go below 0

            medicine.save()

            # Update movement analysis
            try:
                analysis, created = MovementAnalysis.objects.get_or_create(medicine=medicine)
                analysis.update_analysis()
            except Exception as e:
                # Log the error but don't prevent the movement from being saved
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Error updating movement analysis: {str(e)}")

        return instance