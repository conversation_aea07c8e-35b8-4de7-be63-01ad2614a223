{% extends 'base.html' %}
{% load static %}

{% block title %}Customer List - BMC MedForecast{% endblock %}

{% block page_title %}Customer Management{% endblock %}

{% block extra_css %}
<style>
    .customer-card {
        transition: all 0.2s ease;
    }
    .customer-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
    .customer-search-form .input-group {
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.08);
        border-radius: 0.5rem;
        overflow: hidden;
    }
    .customer-search-form .form-control {
        border-right: none;
        padding-left: 1rem;
    }
    .customer-search-form .input-group-text {
        background-color: white;
        border-left: none;
    }
    .customer-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }
    .customer-type-in {
        color: #2c7be5;
        background-color: rgba(44, 123, 229, 0.1);
    }
    .customer-type-out {
        color: #00864e;
        background-color: rgba(0, 134, 78, 0.1);
    }
    .pagination .page-item.active .page-link {
        background-color: #2c7be5;
        border-color: #2c7be5;
    }
    .pagination .page-link {
        color: #2c7be5;
    }
    .customer-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    .customer-actions .btn-icon {
        width: 28px;
        height: 28px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }
    .last-transaction {
        font-size: 0.75rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-3">
    {% if messages %}
    <div class="row animate__animated animate__fadeIn">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show shadow-sm border-0" role="alert">
                <i class="fas fa-info-circle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <h1 class="h3 mb-0 text-gray-800">
                <i class="fas fa-users me-2 text-primary"></i>Customer List
            </h1>
            <p class="text-muted">Manage your transaction customers</p>
        </div>
        <div class="col-md-6 text-md-end">
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                <i class="fas fa-user-plus me-2"></i>Add New Customer
            </button>
        </div>
    </div>

    <!-- Search and Filter Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <form method="get" class="customer-search-form">
                <div class="input-group">
                    <input type="text" class="form-control" name="q" placeholder="Search by name, patient number, or phone..." value="{{ request.GET.q|default:'' }}">
                    <span class="input-group-text bg-white">
                        <button type="submit" class="btn btn-link text-primary p-0 m-0">
                            <i class="fas fa-search"></i>
                        </button>
                    </span>
                </div>
            </form>
        </div>
        <div class="col-md-4">
            <div class="d-flex justify-content-md-end">
                <select class="form-select" name="customer_type" id="customerTypeFilter">
                    <option value="">All Customer Types</option>
                    <option value="in_patient" {% if request.GET.customer_type == 'in_patient' %}selected{% endif %}>In Patient</option>
                    <option value="out_patient" {% if request.GET.customer_type == 'out_patient' %}selected{% endif %}>Out Patient</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Customer List Section -->
    <div class="card shadow-sm border-0 rounded-3 mb-4">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0 text-primary">Customers</h5>
                </div>
                <div class="col text-end">
                    <span class="badge bg-primary rounded-pill">{{ page_obj.paginator.count }} Total</span>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th class="ps-4">Customer Name</th>
                            <th>Patient Number</th>
                            <th>Type</th>
                            <th>Contact</th>
                            <th>Last Transaction</th>
                            <th class="text-end pe-4">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for customer in page_obj %}
                        <tr>
                            <td class="ps-4">
                                <div class="d-flex align-items-center">
                                    <div class="avatar-circle bg-primary text-white me-3">
                                        {{ customer.customer_name|slice:":1" }}
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ customer.customer_name }}</h6>
                                        {% if customer.email %}
                                        <small class="text-muted">{{ customer.email }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if customer.patient_number %}
                                <span class="badge bg-light text-dark">{{ customer.patient_number }}</span>
                                {% else %}
                                <span class="text-muted">—</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge rounded-pill {% if customer.customer_type == 'in_patient' %}customer-type-in{% else %}customer-type-out{% endif %}">
                                    {{ customer.get_customer_type_display }}
                                </span>
                            </td>
                            <td>
                                {% if customer.phone_number %}
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-phone-alt text-muted me-2"></i>
                                    <span>{{ customer.phone_number }}</span>
                                </div>
                                {% else %}
                                <span class="text-muted">No phone number</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if customer.last_transaction_date %}
                                <div class="last-transaction">
                                    <i class="fas fa-calendar-alt text-muted me-1"></i>
                                    {{ customer.last_transaction_date|date:"M d, Y" }}
                                </div>
                                <small class="text-muted">{{ customer.last_transaction_date|time:"g:i A" }}</small>
                                {% else %}
                                <span class="text-muted">No transactions</span>
                                {% endif %}
                            </td>
                            <td class="text-end pe-4">
                                <div class="customer-actions">
                                    <button type="button" class="btn btn-sm btn-outline-primary me-1" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#editCustomerModal" 
                                            data-customer-id="{{ customer.id }}"
                                            data-customer-name="{{ customer.customer_name }}"
                                            data-patient-number="{{ customer.patient_number|default:'' }}"
                                            data-customer-type="{{ customer.customer_type }}"
                                            data-phone-number="{{ customer.phone_number|default:'' }}"
                                            data-email="{{ customer.email|default:'' }}"
                                            data-address="{{ customer.address|default:'' }}"
                                            data-notes="{{ customer.notes|default:'' }}">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <a href="{% url 'customer_detail' customer.id %}" class="btn btn-sm btn-outline-info me-1">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            data-bs-toggle="modal" 
                                            data-bs-target="#deleteCustomerModal"
                                            data-customer-id="{{ customer.id }}"
                                            data-customer-name="{{ customer.customer_name }}">
                                        <i class="fas fa-trash-alt"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center py-5">
                                <div class="py-4">
                                    <i class="fas fa-users text-muted" style="font-size: 3rem;"></i>
                                    <h5 class="mt-3">No customers found</h5>
                                    <p class="text-muted">Try adjusting your search or filter criteria</p>
                                    <button type="button" class="btn btn-primary mt-2" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                                        <i class="fas fa-user-plus me-2"></i>Add New Customer
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        <div class="card-footer bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <small class="text-muted">
                        Showing {{ page_obj.start_index }} to {{ page_obj.end_index }} of {{ page_obj.paginator.count }} customers
                    </small>
                </div>
                <div class="col">
                    {% if page_obj.has_other_pages %}
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-end mb-0">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.customer_type %}&customer_type={{ request.GET.customer_type }}{% endif %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.customer_type %}&customer_type={{ request.GET.customer_type }}{% endif %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <li class="page-item active">
                                    <span class="page-link">{{ num }}</span>
                                </li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ num }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.customer_type %}&customer_type={{ request.GET.customer_type }}{% endif %}">{{ num }}</a>
                                </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.customer_type %}&customer_type={{ request.GET.customer_type }}{% endif %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if request.GET.q %}&q={{ request.GET.q }}{% endif %}{% if request.GET.customer_type %}&customer_type={{ request.GET.customer_type }}{% endif %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Customer Modal -->
<div class="modal fade" id="addCustomerModal" tabindex="-1" aria-labelledby="addCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="addCustomerModalLabel">
                    <i class="fas fa-user-plus me-2"></i>Add New Customer
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'add_customer' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="customer_name" class="form-label">Customer Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="patient_number" class="form-label">Patient Number</label>
                            <input type="text" class="form-control" id="patient_number" name="patient_number">
                        </div>
                        <div class="col-md-6">
                            <label for="customer_type" class="form-label">Customer Type</label>
                            <select class="form-select" id="customer_type" name="customer_type">
                                <option value="out_patient">Out Patient</option>
                                <option value="in_patient">In Patient</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="phone_number" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone_number" name="phone_number" placeholder="e.g., 09123456789">
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        <div class="col-12">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2"></textarea>
                        </div>
                        <div class="col-12">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Save Customer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Customer Modal -->
<div class="modal fade" id="editCustomerModal" tabindex="-1" aria-labelledby="editCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editCustomerModalLabel">
                    <i class="fas fa-edit me-2"></i>Edit Customer
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'edit_customer' 0 %}" id="editCustomerForm">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="edit_customer_name" class="form-label">Customer Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="edit_customer_name" name="customer_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_patient_number" class="form-label">Patient Number</label>
                            <input type="text" class="form-control" id="edit_patient_number" name="patient_number">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_customer_type" class="form-label">Customer Type</label>
                            <select class="form-select" id="edit_customer_type" name="customer_type">
                                <option value="out_patient">Out Patient</option>
                                <option value="in_patient">In Patient</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="edit_phone_number" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="edit_phone_number" name="phone_number" placeholder="e.g., 09123456789">
                        </div>
                        <div class="col-md-6">
                            <label for="edit_email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="edit_email" name="email">
                        </div>
                        <div class="col-12">
                            <label for="edit_address" class="form-label">Address</label>
                            <textarea class="form-control" id="edit_address" name="address" rows="2"></textarea>
                        </div>
                        <div class="col-12">
                            <label for="edit_notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="edit_notes" name="notes" rows="2"></textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Customer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Customer Modal -->
<div class="modal fade" id="deleteCustomerModal" tabindex="-1" aria-labelledby="deleteCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteCustomerModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Delete Customer
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong id="deleteCustomerName"></strong>?</p>
                <p class="text-danger">This action cannot be undone. All transaction history for this customer will remain but will no longer be linked to a customer record.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="{% url 'delete_customer' 0 %}" id="deleteCustomerForm">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash-alt me-2"></i>Delete Customer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Customer type filter
        const customerTypeFilter = document.getElementById('customerTypeFilter');
        if (customerTypeFilter) {
            customerTypeFilter.addEventListener('change', function() {
                const currentUrl = new URL(window.location.href);
                if (this.value) {
                    currentUrl.searchParams.set('customer_type', this.value);
                } else {
                    currentUrl.searchParams.delete('customer_type');
                }
                window.location.href = currentUrl.toString();
            });
        }
        
        // Edit customer modal
        const editCustomerModal = document.getElementById('editCustomerModal');
        if (editCustomerModal) {
            editCustomerModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const customerId = button.getAttribute('data-customer-id');
                const customerName = button.getAttribute('data-customer-name');
                const patientNumber = button.getAttribute('data-patient-number');
                const customerType = button.getAttribute('data-customer-type');
                const phoneNumber = button.getAttribute('data-phone-number');
                const email = button.getAttribute('data-email');
                const address = button.getAttribute('data-address');
                const notes = button.getAttribute('data-notes');
                
                // Update form action URL
                const form = editCustomerModal.querySelector('#editCustomerForm');
                form.action = form.action.replace(/\/\d+\/$/, `/${customerId}/`);
                
                // Set form values
                document.getElementById('edit_customer_name').value = customerName;
                document.getElementById('edit_patient_number').value = patientNumber;
                document.getElementById('edit_customer_type').value = customerType;
                document.getElementById('edit_phone_number').value = phoneNumber;
                document.getElementById('edit_email').value = email;
                document.getElementById('edit_address').value = address;
                document.getElementById('edit_notes').value = notes;
            });
        }
        
        // Delete customer modal
        const deleteCustomerModal = document.getElementById('deleteCustomerModal');
        if (deleteCustomerModal) {
            deleteCustomerModal.addEventListener('show.bs.modal', function(event) {
                const button = event.relatedTarget;
                const customerId = button.getAttribute('data-customer-id');
                const customerName = button.getAttribute('data-customer-name');
                
                // Update form action URL
                const form = deleteCustomerModal.querySelector('#deleteCustomerForm');
                form.action = form.action.replace(/\/\d+\/$/, `/${customerId}/`);
                
                // Set customer name in confirmation message
                document.getElementById('deleteCustomerName').textContent = customerName;
            });
        }
        
        // Avatar circle styling
        const avatarCircles = document.querySelectorAll('.avatar-circle');
        avatarCircles.forEach(avatar => {
            avatar.style.width = '36px';
            avatar.style.height = '36px';
            avatar.style.borderRadius = '50%';
            avatar.style.display = 'flex';
            avatar.style.alignItems = 'center';
            avatar.style.justifyContent = 'center';
            avatar.style.fontWeight = 'bold';
        });
    });
</script>
{% endblock %}
