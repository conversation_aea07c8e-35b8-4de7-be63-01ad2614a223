from django.core.management.base import BaseCommand
from inventory.models import Medicine
import random

class Command(BaseCommand):
    help = 'Adds random brand and supplier information to medicines'

    def handle(self, *args, **options):
        # Define common pharmaceutical brands and suppliers
        brands = {
            'Paracetamol': ['Tylenol', 'Panadol', 'Calpol', 'Alvedon', 'Tempra'],
            'Ibuprofen': ['Advil', 'Motrin', 'Nurofen', 'Brufen', 'Actiprofen'],
            'Amoxicillin': ['Amoxil', 'Trimox', 'Wymox', 'Polymox', 'Augmentin'],
            'Fluoxetine': ['Prozac', 'Sarafem', 'Adofen', 'Fluctin', 'Fontex'],
            'Sertraline': ['Zoloft', 'Lu<PERSON>l', 'Asentra', 'Gladem', 'Serlain'],
            'Lisinopril': ['Prinivil', 'Zestril', 'Qbrelis', '<PERSON>ce', 'Tensopril'],
            'Losartan': ['<PERSON><PERSON><PERSON>', '<PERSON>yzaar', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'],
            'Metoprol<PERSON>': ['Lopressor', 'Toprol-XL', '<PERSON><PERSON>', '<PERSON><PERSON><PERSON>', 'Minax'],
            '<PERSON>ydrochlorothiazide': ['Microzide', 'Hydrodiuril', 'Esidrix', 'Oretic', 'Aquazide'],
            'Albuterol': ['Ventolin', 'ProAir', 'Proventil', 'Airomir', 'Salamol'],
            'Salbutamol': ['Ventolin', 'ProAir', 'Proventil', 'Airomir', 'Salamol'],
            'Simvastatin': ['Zocor', 'Lipex', 'Simlup', 'Simcard', 'Simvacor'],
            'Atorvastatin': ['Lipitor', 'Torvast', 'Sortis', 'Torvacard', 'Totalip'],
            'Omeprazole': ['Prilosec', 'Losec', 'Omepral', 'Gastrimut', 'Omez'],
            'Levothyroxine': ['Synthroid', 'Levoxyl', 'Euthyrox', 'Eltroxin', 'Levothroid'],
            'Metformin': ['Glucophage', 'Fortamet', 'Riomet', 'Glumetza', 'Obimet'],
            'Gabapentin': ['Neurontin', 'Gralise', 'Horizant', 'Gabarone', 'Fanatrex'],
            'Cetirizine': ['Zyrtec', 'Reactine', 'Zirtec', 'Virlix', 'Cetryn'],
            'Amlodipine': ['Norvasc', 'Istin', 'Amlodin', 'Amlor', 'Amlodis'],
            'Prednisone': ['Deltasone', 'Rayos', 'Sterapred', 'Prednicot', 'Orasone'],
        }
        
        # Default brands for medicines not in the specific list
        default_brands = ['Generic', 'MedBrand', 'PharmaCare', 'HealthPlus', 'MediCorp']
        
        # Suppliers (pharmaceutical companies)
        suppliers = [
            'Johnson & Johnson', 'Pfizer', 'Roche', 'Novartis', 'Merck', 
            'GlaxoSmithKline', 'Sanofi', 'AbbVie', 'Bayer', 'Eli Lilly',
            'Bristol-Myers Squibb', 'AstraZeneca', 'Boehringer Ingelheim', 
            'Amgen', 'Gilead Sciences', 'Teva Pharmaceutical', 'Novo Nordisk',
            'Takeda Pharmaceutical', 'Biogen', 'Baxter International'
        ]
        
        # Update medicines
        updated_count = 0
        for medicine in Medicine.objects.all():
            # Get medicine name (lowercase for case-insensitive matching)
            medicine_name = medicine.name.lower()
            
            # Find matching brand options
            brand_options = None
            for key in brands:
                if key.lower() in medicine_name:
                    brand_options = brands[key]
                    break
            
            # Use default brands if no specific match found
            if not brand_options:
                brand_options = default_brands
            
            # Assign random brand and supplier
            medicine.brand = random.choice(brand_options)
            medicine.supplier = random.choice(suppliers)
            medicine.save()
            updated_count += 1
            
            self.stdout.write(f"Updated {medicine.name} with brand '{medicine.brand}' from supplier '{medicine.supplier}'")
        
        self.stdout.write(self.style.SUCCESS(f"Successfully updated {updated_count} medicines with brand and supplier information"))
