{% extends 'base.html' %}
{% load static %}
{% load base_filters %}

{% block title %}Inventory Forecast{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/forecasting_dashboard.css' %}">
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card h-100 border-danger border-start border-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mb-1">Items Below Reorder Level</h6>
                            <h3 class="text-danger mb-0">{{ metrics.below_reorder_level }}</h3>
                        </div>
                        <div class="icon-bg bg-danger bg-opacity-10 rounded p-3">
                            <i class="fas fa-exclamation-triangle text-danger fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-3">
            <div class="card h-100 border-info border-start border-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted fw-normal mb-1">Forecast Accuracy</h6>
                            <h3 class="text-info mb-0">{{ metrics.forecast_accuracy }}%</h3>
                        </div>
                        <div class="icon-bg bg-info bg-opacity-10 rounded p-3">
                            <i class="fas fa-chart-line text-info fs-4"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Section -->
    <div class="row mb-4">
        <!-- Demand Trends Chart -->
        <div class="col-xl-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Demand Trends Analysis</h5>
                </div>
                <div class="card-body">
                    <canvas id="demandTrendsChart" height="300"></canvas>
                </div>
            </div>
        </div>





        <!-- Stock Levels Chart -->
        <div class="col-xl-6 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-white">
                    <h5 class="card-title mb-0">Projected Stock Levels</h5>
                </div>
                <div class="card-body">
                    <canvas id="stockLevelsChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Report Generation Section -->
    <div class="card mb-4">
        <div class="card-header bg-white d-flex justify-content-between align-items-center">
            <h5 class="mb-0">Generate Inventory Report</h5>
            <div class="btn-group">
                <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#reportModal">
                    <i class="fas fa-file-export me-2"></i>Generate Report
                </button>
            </div>
        </div>
    </div>

    <!-- Report Generation Modal -->
    <div class="modal fade" id="reportModal" tabindex="-1" aria-labelledby="reportModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="reportModalLabel">Configure Report</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="reportConfigForm">
                        {% csrf_token %}
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Time Period</label>
                                <select class="form-select" id="timePeriod" name="time_period">
                                    <option value="7">Last 7 Days</option>
                                    <option value="30" selected>Last 30 Days</option>
                                    <option value="90">Last Quarter</option>
                                    <option value="180">Last 6 Months</option>
                                    <option value="365">Last Year</option>
                                </select>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Report Format</label>
                                <select class="form-select" id="reportFormat" name="format">
                                    <option value="excel">Excel (.xlsx)</option>
                                    <option value="pdf">PDF Document</option>
                                </select>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-12">
                                <h6 class="mb-3">Report Components</h6>
                                <div class="d-flex flex-wrap gap-4">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeSummary" name="components[]" value="summary" checked>
                                        <label class="form-check-label">Executive Summary</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeTrends" name="components[]" value="trends" checked>
                                        <label class="form-check-label">Demand Trends</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeProjections" name="components[]" value="projections" checked>
                                        <label class="form-check-label">Stock Projections</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeMetrics" name="components[]" value="metrics" checked>
                                        <label class="form-check-label">Performance Metrics</label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info d-none" id="reportProgress" role="alert">
                            <div class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status"></div>
                                <span>Generating report...</span>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="generateReportBtn">
                        <i class="fas fa-file-download me-2"></i>Generate Report
                    </button>
                </div>
            </div>
        </div>
    </div>
   
    <!-- Main Content Card -->
    <div class="card shadow-sm">
        <div class="card-body p-0">
            <div class="tab-content" id="reorderTimeTabContent">
                <div class="tab-pane fade show active" id="weekly" role="tabpanel">
                    <div class="table-responsive">
                        {% include "forecast_table.html" with medicines=weekly_medicines %}
                    </div>
                </div>
                <div class="tab-pane fade" id="monthly" role="tabpanel">
                    <div class="table-responsive">
                        {% include "forecast_table.html" with medicines=monthly_medicines %}
                    </div>
                </div>
                <div class="tab-pane fade" id="yearly" role="tabpanel">
                    <div class="table-responsive">
                        {% include "forecast_table.html" with medicines=yearly_medicines %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
<script src="{% static 'js/forecasting_dashboard.js' %}"></script>
<script>
    const FORECAST_DATA = {{ forecast_data|safe }};
    const CSRF_TOKEN = '{{ csrf_token }}';

    // Initialize charts
    document.addEventListener('DOMContentLoaded', function() {
        // Demand Trends Chart
        const demandCtx = document.getElementById('demandTrendsChart').getContext('2d');
        new Chart(demandCtx, {
            type: 'line',
            data: {
                labels: FORECAST_DATA.demand_trends.labels,
                datasets: [{
                    label: 'Actual Demand',
                    data: FORECAST_DATA.demand_trends.actual,
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    fill: true,
                    tension: 0.4
                }, {
                    label: 'Predicted Demand',
                    data: FORECAST_DATA.demand_trends.predicted,
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderDash: [5, 5],
                    fill: false,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Quantity'
                        }
                    }
                }
            }
        });

        // Stock Levels Chart
        const stockCtx = document.getElementById('stockLevelsChart').getContext('2d');
        new Chart(stockCtx, {
            type: 'line',
            data: {
                labels: FORECAST_DATA.stock_levels.labels,
                datasets: [{
                    label: 'Current Stock',
                    data: FORECAST_DATA.stock_levels.current,
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.1)',
                    fill: true
                }, {
                    label: 'Reorder Level',
                    data: FORECAST_DATA.stock_levels.reorder_level,
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderDash: [5, 5],
                    fill: false
                }]
            },
            options: {
                responsive: true,
                plugins: {
                    title: {
                        display: false
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Stock Level'
                        }
                    }
                }
            }
        });
    });


 


</script>
{% endblock %}