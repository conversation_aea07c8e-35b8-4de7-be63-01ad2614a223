{% extends 'base.html' %}

{% block content %}
<div class="container mt-4">
    <div class="row mb-4">
        <div class="col-md-8">
            <h2 class="display-6"><i class="fas fa-chart-line text-primary me-2"></i>Forecast Results</h2>
            <p class="text-muted">Analysis and prediction of future trends based on historical data</p>
        </div>
        <div class="col-md-4 text-end d-flex justify-content-end align-items-center">
            <button class="btn btn-outline-primary me-2" id="downloadBtn" style="display: none;">
                <i class="fas fa-download me-1"></i> Export Data
            </button>
            <button class="btn btn-outline-secondary" id="printBtn" style="display: none;">
                <i class="fas fa-print me-1"></i> Print
            </button>
        </div>
    </div>

    {% if error %}
    <div class="alert alert-danger alert-dismissible fade show shadow-sm" role="alert">
        <div class="d-flex">
            <div class="me-3">
                <i class="fas fa-exclamation-triangle fa-2x"></i>
            </div>
            <div>
                <h5 class="alert-heading">Error Occurred</h5>
                <p class="mb-0">{{ error }}</p>
            </div>
        </div>
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    {% endif %}

    {% if forecast_results %}
    <div class="card shadow-sm border-0 rounded-3 mb-4">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">
                <i class="fas fa-chart-bar text-primary me-2"></i>Comparative Forecast Results
            </h5>
        </div>
        <div class="card-body">
            <!-- Tabs for different forecasting methods -->
            <ul class="nav nav-pills nav-fill mb-4" id="forecastTabs" role="tablist">
                {% for method, result in forecast_results.items %}
                <li class="nav-item" role="presentation">
                    <button class="nav-link {% if forloop.first %}active{% endif %}"
                            id="{{ method }}-tab"
                            data-bs-toggle="tab"
                            data-bs-target="#{{ method }}"
                            type="button"
                            role="tab">
                        {% if method == 'arima' %}
                        <i class="fas fa-chart-line me-2"></i>ARIMA
                        {% elif method == 'exponential_smoothing' %}
                        <i class="fas fa-chart-area me-2"></i>Exp. Smoothing
                        {% elif method == 'moving_average' %}
                        <i class="fas fa-chart-bar me-2"></i>Moving Avg
                        {% elif method == 'linear' %}
                        <i class="fas fa-arrow-trend-up me-2"></i>Linear
                        {% else %}
                        <i class="fas fa-chart-pie me-2"></i>{{ method|title }}
                        {% endif %}
                    </button>
                </li>
                {% endfor %}
            </ul>

            <!-- Tab content -->
            <div class="tab-content" id="forecastTabContent">
                {% for method, result in forecast_results.items %}
                <div class="tab-pane fade {% if forloop.first %}show active{% endif %}"
                     id="{{ method }}"
                     role="tabpanel">
                    {% if result.error %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-circle me-2"></i>{{ result.error }}
                    </div>
                    {% else %}
                    <div class="row mb-4">
                        {% if result.accuracy %}
                        <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                            <div class="card h-100 border-0 bg-light rounded-3 shadow-sm">
                                <div class="card-body text-center">
                                    <h6 class="card-subtitle mb-2 text-muted">Accuracy</h6>
                                    <h3 class="card-title mb-0 fw-bold text-primary">{{ result.accuracy.accuracy|default:"0.00"|floatformat:2 }}%</h3>
                                    <div class="progress mt-2" style="height: 5px;">
                                        <div class="progress-bar bg-primary" role="progressbar"
                                             style="width: {{ result.accuracy.accuracy|default:'0' }}%;"
                                             aria-valuenow="{{ result.accuracy.accuracy|default:'0' }}"
                                             aria-valuemin="0"
                                             aria-valuemax="100"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                            <div class="card h-100 border-0 bg-light rounded-3 shadow-sm">
                                <div class="card-body text-center">
                                    <h6 class="card-subtitle mb-2 text-muted">MSE</h6>
                                    <h3 class="card-title mb-0 fw-bold text-info">{{ result.accuracy.mse|default:"0.00"|floatformat:2 }}</h3>
                                    <p class="text-muted small mt-2">Mean Squared Error</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                            <div class="card h-100 border-0 bg-light rounded-3 shadow-sm">
                                <div class="card-body text-center">
                                    <h6 class="card-subtitle mb-2 text-muted">RMSE</h6>
                                    <h3 class="card-title mb-0 fw-bold text-warning">{{ result.accuracy.rmse|default:"0.00"|floatformat:2 }}</h3>
                                    <p class="text-muted small mt-2">Root Mean Squared Error</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3 col-sm-6 mb-3 mb-md-0">
                            <div class="card h-100 border-0 bg-light rounded-3 shadow-sm">
                                <div class="card-body text-center">
                                    <h6 class="card-subtitle mb-2 text-muted">MAPE</h6>
                                    <h3 class="card-title mb-0 fw-bold text-danger">{{ result.accuracy.mape|default:"0.00"|floatformat:2 }}%</h3>
                                    <p class="text-muted small mt-2">Mean Absolute Percentage Error</p>
                                </div>
                            </div>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Chart container with improved styling -->
                    <div class="chart-container shadow-sm rounded-3 p-3 bg-white border" style="height: 450px; position: relative;">
                        <canvas id="{{ method }}Chart"></canvas>
                        <div id="{{ method }}Loading" class="position-absolute top-50 start-50 translate-middle d-none">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>

                    {% if method == 'arima' and result.model_summary %}
                    <div class="mt-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-info-circle me-2 text-primary"></i>ARIMA Model Summary</h6>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary" onclick="copyModelSummary('{{ method }}Summary')">
                                        <i class="fas fa-copy me-1"></i> Copy
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary ms-2" onclick="toggleModelSummary('{{ method }}Summary')">
                                        <i class="fas fa-expand-alt me-1"></i> Expand
                                    </button>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <pre id="{{ method }}Summary" class="bg-light p-3 mb-0 rounded-bottom" style="max-height: 300px; overflow-y: auto;">{{ result.model_summary }}</pre>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Forecast data table -->
                    <div class="mt-4">
                        <div class="card border-0 shadow-sm">
                            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                                <h6 class="mb-0"><i class="fas fa-table me-2 text-primary"></i>Forecast Data</h6>
                                <button class="btn btn-sm btn-outline-primary" onclick="toggleDataTable('{{ method }}DataTable')">
                                    <i class="fas fa-table me-1"></i> Show/Hide Data
                                </button>
                            </div>
                            <div class="card-body p-0">
                                <div id="{{ method }}DataTable" class="table-responsive" style="display: none;">
                                    <table class="table table-striped table-hover mb-0">
                                        <thead class="table-light">
                                            <tr>
                                                <th>Period</th>
                                                <th>Forecast Value</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for value in result.forecasts %}
                                            <tr>
                                                <td>Period {{ forloop.counter }}</td>
                                                <td>{{ value|floatformat:2 }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Additional information card -->
    <div class="card shadow-sm border-0 rounded-3 mb-4">
        <div class="card-header bg-white py-3">
            <h5 class="card-title mb-0">
                <i class="fas fa-lightbulb text-warning me-2"></i>Forecast Insights
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold"><i class="fas fa-check-circle text-success me-2"></i>Recommended Model</h6>
                    <p>
                        {% with best_model=forecast_results.arima %}
                        {% if best_model.accuracy and best_model.accuracy.accuracy > 70 %}
                        <span class="badge bg-success me-2">ARIMA</span> is recommended with {{ best_model.accuracy.accuracy|floatformat:2 }}% accuracy
                        {% else %}
                        <span class="badge bg-warning me-2">Caution</span> All models show lower accuracy. Consider collecting more data.
                        {% endif %}
                        {% endwith %}
                    </p>
                </div>
                <div class="col-md-6">
                    <h6 class="fw-bold"><i class="fas fa-exclamation-triangle text-warning me-2"></i>Limitations</h6>
                    <p>Forecasts are based on historical patterns and may not account for unexpected market changes or external factors.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Chart.js initialization with improved options -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            {% for method, result in forecast_results.items %}
            {% if not result.error %}
            try {
                const loadingElement = document.getElementById('{{ method }}Loading');
                loadingElement.classList.remove('d-none');

                const ctx = document.getElementById('{{ method }}Chart');
                if (!ctx) {
                    console.error('Canvas element not found for {{ method }}');
                    return;
                }

                // Parse the data
                const historicalData = {{ historical_data|safe }};
                const forecastData = {{ result.forecasts|safe }};

                // Create better labels for the entire dataset
                const labels = [];
                for (let i = 0; i < historicalData.length + forecastData.length; i++) {
                    if (i < historicalData.length) {
                        labels.push(`Historical ${i + 1}`);
                    } else {
                        labels.push(`Forecast ${i - historicalData.length + 1}`);
                    }
                }

                // Calculate the average of historical data for reference line
                const avgHistorical = historicalData.reduce((a, b) => a + b, 0) / historicalData.length;

                // Create the datasets with improved styling
                const datasets = [
                    {
                        label: 'Historical Data',
                        data: [...historicalData, ...Array(forecastData.length).fill(null)],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderWidth: 2,
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        tension: 0.1
                    },
                    {
                        label: '{{ method|title }} Forecast',
                        data: [...Array(historicalData.length).fill(null), ...forecastData],
                        borderColor: '{{ result.color|default:"rgb(255, 99, 132)" }}',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        borderWidth: 2,
                        borderDash: [5, 5],
                        pointRadius: 4,
                        pointHoverRadius: 6,
                        pointStyle: 'triangle',
                        tension: 0.1
                    }
                ];

                // Create chart with enhanced options
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: datasets
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            intersect: false,
                            mode: 'index'
                        },
                        scales: {
                            y: {
                                beginAtZero: false,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                title: {
                                    display: true,
                                    text: 'Quantity',
                                    font: {
                                        weight: 'bold'
                                    }
                                },
                                ticks: {
                                    callback: function(value) {
                                        return value.toFixed(1);
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.05)'
                                },
                                title: {
                                    display: true,
                                    text: 'Time Period',
                                    font: {
                                        weight: 'bold'
                                    }
                                }
                            }
                        },
                        plugins: {
                            title: {
                                display: true,
                                text: '{{ method|title }} Forecast Analysis',
                                font: {
                                    size: 16,
                                    weight: 'bold'
                                },
                                padding: {
                                    top: 10,
                                    bottom: 20
                                }
                            },
                            subtitle: {
                                display: true,
                                text: 'Comparing historical data with forecasted values',
                                padding: {
                                    bottom: 10
                                }
                            },
                            legend: {
                                position: 'top',
                                labels: {
                                    usePointStyle: true,
                                    padding: 15
                                }
                            },
                            tooltip: {
                                enabled: true,
                                backgroundColor: 'rgba(0, 0, 0, 0.8)',
                                titleFont: {
                                    size: 14
                                },
                                bodyFont: {
                                    size: 13
                                },
                                padding: 10,
                                callbacks: {
                                    label: function(context) {
                                        return `${context.dataset.label}: ${context.parsed.y.toFixed(2)}`;
                                    }
                                }
                            },
                            annotation: {
                                annotations: {
                                    line1: {
                                        type: 'line',
                                        yMin: avgHistorical,
                                        yMax: avgHistorical,
                                        borderColor: 'rgba(100, 100, 100, 0.5)',
                                        borderWidth: 1,
                                        borderDash: [6, 6],
                                        label: {
                                            display: true,
                                            content: `Avg: ${avgHistorical.toFixed(2)}`,
                                            position: 'start'
                                        }
                                    },
                                    box1: {
                                        type: 'box',
                                        xMin: historicalData.length - 0.5,
                                        xMax: historicalData.length + forecastData.length - 0.5,
                                        backgroundColor: 'rgba(200, 200, 255, 0.1)',
                                        borderColor: 'rgba(200, 200, 255, 0.2)'
                                    }
                                }
                            }
                        }
                    }
                });

                loadingElement.classList.add('d-none');
                document.getElementById('downloadBtn').style.display = 'block';
                document.getElementById('printBtn').style.display = 'block';

            } catch (error) {
                console.error('Error creating chart for {{ method }}:', error);
                const errorDiv = document.createElement('div');
                errorDiv.className = 'alert alert-danger mt-3';
                errorDiv.innerHTML = `<i class="fas fa-exclamation-circle me-2"></i> Error creating chart: ${error.message}`;
                document.getElementById('{{ method }}').appendChild(errorDiv);
            }
            {% endif %}
            {% endfor %}

            // Setup export functionality
            document.getElementById('downloadBtn').addEventListener('click', function() {
                exportForecastData();
            });

            // Setup print functionality
            document.getElementById('printBtn').addEventListener('click', function() {
                window.print();
            });
        });

        function copyModelSummary(elementId) {
            const element = document.getElementById(elementId);
            navigator.clipboard.writeText(element.textContent)
                .then(() => {
                    const btn = event.target.closest('button');
                    const originalText = btn.innerHTML;
                    btn.innerHTML = '<i class="fas fa-check me-1"></i> Copied!';
                    setTimeout(() => btn.innerHTML = originalText, 2000);
                })
                .catch(err => console.error('Failed to copy text:', err));
        }

        function toggleModelSummary(elementId) {
            const element = document.getElementById(elementId);
            if (element.style.maxHeight === '300px') {
                element.style.maxHeight = 'none';
                event.target.closest('button').innerHTML = '<i class="fas fa-compress-alt me-1"></i> Collapse';
            } else {
                element.style.maxHeight = '300px';
                event.target.closest('button').innerHTML = '<i class="fas fa-expand-alt me-1"></i> Expand';
            }
        }

        function toggleDataTable(elementId) {
            const element = document.getElementById(elementId);
            if (element.style.display === 'none') {
                element.style.display = 'block';
            } else {
                element.style.display = 'none';
            }
        }

        function exportForecastData() {
            // Get active tab
            const activeTab = document.querySelector('.tab-pane.active');
            const activeMethod = activeTab.id;

            // Create CSV content
            let csvContent = "data:text/csv;charset=utf-8,";
            csvContent += "Period,Value\n";

            {% for method, result in forecast_results.items %}
            if (activeMethod === '{{ method }}' && !{{ result.error|yesno:"true,false" }}) {
                const forecastData = {{ result.forecasts|safe }};
                forecastData.forEach((value, index) => {
                    csvContent += `Forecast ${index + 1},${value}\n`;
                });
            }
            {% endfor %}

            // Create download link
            const encodedUri = encodeURI(csvContent);
            const link = document.createElement("a");
            link.setAttribute("href", encodedUri);
            link.setAttribute("download", `${activeMethod}_forecast_data.csv`);
            document.body.appendChild(link);

            // Trigger download
            link.click();
            document.body.removeChild(link);
        }
    </script>
    {% else %}
    <div class="card shadow-sm border-0 rounded-3">
        <div class="card-body text-center p-5">
            <img src="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.0/icons/graph-up-arrow.svg" alt="Forecast" width="80" height="80" class="mb-4 text-primary">
            <h4 class="card-title mb-3">Generate Forecast Analysis</h4>
            <p class="card-text text-muted mb-4">Create predictive models based on your historical data to anticipate future trends</p>
            <form method="post" class="d-inline-block">
                {% csrf_token %}
                <button type="submit" class="btn btn-primary btn-lg px-4 py-2">
                    <i class="fas fa-chart-line me-2"></i>Generate Forecast
                </button>
            </form>
        </div>
    </div>

    <!-- Information cards -->
    <div class="row mt-4">
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm rounded-3">
                <div class="card-body">
                    <div class="text-primary mb-3">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                    <h5 class="card-title">ARIMA Forecasting</h5>
                    <p class="card-text">Advanced time series forecasting that accounts for trends, seasonality, and autocorrelation in your data.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm rounded-3">
                <div class="card-body">
                    <div class="text-warning mb-3">
                        <i class="fas fa-chart-area fa-2x"></i>
                    </div>
                    <h5 class="card-title">Exponential Smoothing</h5>
                    <p class="card-text">Weighted averaging technique that gives more importance to recent observations for accurate short-term forecasts.</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 border-0 shadow-sm rounded-3">
                <div class="card-body">
                    <div class="text-success mb-3">
                        <i class="fas fa-chart-bar fa-2x"></i>
                    </div>
                    <h5 class="card-title">Moving Average</h5>
                    <p class="card-text">Simple but effective method that smooths out short-term fluctuations to highlight longer-term trends.</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
</div>

<!-- Add print styles -->
<style>
    @media print {
        .container {
            width: 100%;
            max-width: 100%;
        }

        .nav-pills, .btn, form, .card-header button {
            display: none !important;
        }

        .tab-pane {
            display: block !important;
            opacity: 1 !important;
            break-inside: avoid;
        }

        .chart-container {
            height: 350px !important;
            page-break-inside: avoid;
            margin-bottom: 20px;
        }

        pre {
            white-space: pre-wrap;
            max-height: none !important;
        }

        .card {
            border: 1px solid #ddd !important;
            box-shadow: none !important;
            margin-bottom: 20px;
        }
    }
</style>
{% endblock %}