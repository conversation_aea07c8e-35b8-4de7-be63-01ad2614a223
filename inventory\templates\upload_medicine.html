{% extends 'base.html' %}

{% block title %}Upload Medicines - MedInventory{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-primary fw-bold">
                    <i class="fas fa-file-upload me-2"></i>Upload Medicines
                </h1>
                <a href="{% url 'medicine_list' %}" class="btn btn-outline-secondary rounded-pill">
                    <i class="fas fa-arrow-left me-1"></i>Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Upload Card -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">Upload Medicine Data</h5>
                            <p class="text-muted">
                                Upload a CSV file containing medicine data to add or update multiple medicines at once.
                                Make sure your CSV file follows the required format.
                            </p>

                            <div class="alert alert-info">
                                <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>CSV Format Requirements</h6>
                                <p class="mb-0">Your CSV file should include the following columns:</p>
                                <ul class="mb-0">
                                    <li><strong>name</strong> - Medicine name (required)</li>
                                    <li><strong>category</strong> - Medicine category (required)</li>
                                    <li><strong>price</strong> - Unit price (required)</li>
                                    <li><strong>quantity</strong> - Initial stock (required)</li>
                                    <li><strong>reorder_level</strong> - Reorder level (optional, default: 10)</li>
                                    <li><strong>reorder_quantity</strong> - Reorder quantity (optional, default: 20)</li>
                                    <li><strong>expiration_date</strong> - Expiration date in YYYY-MM-DD format (optional)</li>
                                    <li><strong>description</strong> - Medicine description (optional)</li>
                                </ul>
                            </div>

                            <div class="alert alert-warning mt-3">
                                <h6 class="alert-heading"><i class="fas fa-bell me-2"></i>Automatic Notifications</h6>
                                <p class="mb-0">
                                    When you upload medicines, the system will automatically:
                                </p>
                                <ul class="mb-0 mt-2">
                                    <li>Create notifications for new medicines</li>
                                    <li>Send price change alerts for updated medicines</li>
                                    <li>Generate low stock warnings if applicable</li>
                                    <li>Send email notifications to all configured recipients</li>
                                </ul>
                            </div>

                            <div class="mt-3">
                                <a href="{% url 'download_medicine_template' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-download me-2"></i>Download Template
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="upload-container p-4 border rounded bg-light">
                                <form method="post" enctype="multipart/form-data" id="upload-form" action="{% url 'upload_medicine' %}">
                                    {% csrf_token %}

                                    <div class="mb-4 text-center">
                                        <div class="upload-icon mb-3">
                                            <i class="fas fa-file-csv fa-3x text-primary"></i>
                                        </div>
                                        <h5 class="text-secondary">Select CSV File</h5>
                                        <p class="text-muted small">Maximum file size: 5MB</p>
                                    </div>

                                    <div class="mb-3">
                                        <div class="custom-file-upload">
                                            <input type="file" name="csv_file" id="csv_file" class="form-control" accept=".csv" required>
                                        </div>
                                        <div class="form-text">Only CSV files are accepted</div>
                                    </div>

                                    <div class="progress mb-3 d-none" id="upload-progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                    </div>

                                    <div id="upload-result" class="mb-3"></div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary" id="upload-button">
                                            <i class="fas fa-upload me-2"></i>Upload Medicines
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Impact Section -->
    <div class="row mt-4">
        <div class="col-lg-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-boxes me-2"></i>Impact on Inventory
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold text-primary mb-3">Automatic Inventory Updates</h6>
                            <p>
                                When you upload medicines, the system automatically updates the inventory database.
                                For existing medicines, any quantity changes will be recorded as transactions in the system.
                            </p>
                            <div class="alert alert-info mt-3">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <i class="fas fa-info-circle fa-2x"></i>
                                    </div>
                                    <div>
                                        <h6 class="alert-heading">Transaction Records</h6>
                                        <p class="mb-0">
                                            If you update the quantity of an existing medicine, the system will automatically create
                                            an "in" or "out" transaction to reflect the change. This ensures accurate tracking of all
                                            inventory movements.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="fas fa-check-circle me-2"></i>Best Practices</h6>
                                    <ul class="mb-0">
                                        <li class="mb-2">Verify medicine names to avoid duplicates</li>
                                        <li class="mb-2">Use consistent categories for better organization</li>
                                        <li class="mb-2">Set appropriate reorder levels based on demand</li>
                                        <li class="mb-2">Include accurate expiration dates for perishable medicines</li>
                                        <li>Add detailed descriptions for better identification</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card border-0 shadow-sm mt-3">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="fas fa-calendar-check me-2"></i>Flexible Date Formats</h6>
                                    <p class="mb-2">The system accepts various date formats:</p>
                                    <ul class="mb-0 small">
                                        <li>YYYY-MM-DD (2025-12-31)</li>
                                        <li>DD/MM/YYYY (31/12/2025)</li>
                                        <li>MM/DD/YYYY (12/31/2025)</li>
                                        <li>DD-MM-YYYY (31-12-2025)</li>
                                        <li>And many more!</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="card border-0 shadow-sm mt-3">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="fas fa-shield-alt me-2"></i>Duplicate Handling</h6>
                                    <p class="mb-0">
                                        Duplicate medicines in the same file will be skipped automatically.
                                        Existing medicines will be updated rather than creating duplicates.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('upload-form');
        const fileInput = document.getElementById('csv_file');
        const uploadButton = document.getElementById('upload-button');
        const progressBar = document.getElementById('upload-progress');
        const progressBarInner = progressBar.querySelector('.progress-bar');
        const resultContainer = document.getElementById('upload-result');

        // File input change handler
        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                // Check file type
                if (!file.name.endsWith('.csv')) {
                    showError('Please select a CSV file.');
                    this.value = '';
                    return;
                }

                // Check file size (5MB max)
                if (file.size > 5 * 1024 * 1024) {
                    showError('File size exceeds 5MB limit.');
                    this.value = '';
                    return;
                }

                // Show file name
                const fileName = file.name;
                resultContainer.innerHTML = `<div class="alert alert-info">
                    <i class="fas fa-file-csv me-2"></i>Selected file: <strong>${fileName}</strong>
                </div>`;
            }
        });

        // Form submit handler
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!fileInput.files[0]) {
                showError('Please select a file to upload.');
                return;
            }

            // Show progress bar
            progressBar.classList.remove('d-none');
            progressBarInner.style.width = '0%';

            // Disable button during upload
            uploadButton.disabled = true;
            uploadButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';

            // Create FormData object
            const formData = new FormData(this);

            // Simulate progress (since we can't get real progress for small files)
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += 5;
                if (progress > 90) {
                    clearInterval(progressInterval);
                }
                progressBarInner.style.width = `${progress}%`;
            }, 100);

            // Send AJAX request
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Clear progress interval
                clearInterval(progressInterval);

                // Complete progress bar
                progressBarInner.style.width = '100%';

                // Show results
                if (data.error) {
                    showError(data.error);
                } else {
                    showSuccess(data);
                }

                // Re-enable button
                uploadButton.disabled = false;
                uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>Upload Medicines';

                // Redirect to medicine list after 3 seconds if successful
                if (!data.error && (data.created > 0 || data.updated > 0)) {
                    setTimeout(() => {
                        window.location.href = "{% url 'medicine_list' %}";
                    }, 3000);
                }
            })
            .catch(error => {
                // Clear progress interval
                clearInterval(progressInterval);

                // Show error
                showError('An error occurred during upload. Please try again.');
                console.error('Upload error:', error);

                // Re-enable button
                uploadButton.disabled = false;
                uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>Upload Medicines';
            });
        });

        // Function to show error message
        function showError(message) {
            resultContainer.innerHTML = `<div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>${message}
            </div>`;
        }

        // Function to show success message
        function showSuccess(data) {
            let html = `<div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>Upload successful!
                <ul class="mb-0 mt-2">
                    <li>Created: ${data.created} medicines</li>
                    <li>Updated: ${data.updated} medicines</li>
                </ul>
            </div>`;

            // Add errors if any
            if (data.errors && data.errors.length > 0) {
                html += `<div class="alert alert-warning mt-2">
                    <i class="fas fa-exclamation-triangle me-2"></i>Warnings:
                    <ul class="mb-0 mt-2">
                        ${data.errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>`;
            }

            resultContainer.innerHTML = html;
        }
    });
</script>
{% endblock %}
