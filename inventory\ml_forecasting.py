
"""
Advanced machine learning forecasting methods for inventory management.

This module implements Gradient Boosting and feature engineering for improved forecast accuracy.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from django.utils import timezone
from django.db.models import Sum, Avg, Count, F, Q
import logging
import joblib
import os
from pathlib import Path
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import xgboost as xgb
from .models import Medicine, Transaction, Forecast

# Configure logger
logger = logging.getLogger(__name__)

# Create models directory if it doesn't exist
MODELS_DIR = Path('inventory/ml_models')
MODELS_DIR.mkdir(exist_ok=True)

def engineer_features(medicine, days_history=180):
    """
    Create advanced features for machine learning forecasting.
    
    Args:
        medicine: Medicine object
        days_history: Number of days of history to use
        
    Returns:
        DataFrame with engineered features
    """
    try:
        # Get historical transaction data
        current_time = timezone.now()
        start_date = current_time - timedelta(days=days_history)
        
        # Get all transactions for this medicine
        transactions = medicine.transaction_set.filter(
            transaction_date__gte=start_date
        ).order_by('transaction_date')
        
        # Convert to DataFrame
        df = pd.DataFrame(list(transactions.values('transaction_date', 'quantity', 'transaction_type', 'customer_type')))
        
        if df.empty:
            logger.warning(f"No transaction data available for {medicine.name}")
            return None
        
        # Convert transaction_date to datetime
        df['transaction_date'] = pd.to_datetime(df['transaction_date'])
        df.set_index('transaction_date', inplace=True)
        
        # Create daily aggregated data
        daily_data = df.resample('D').agg({
            'quantity': 'sum',
            'transaction_type': lambda x: 'sale' if 'sale' in x.values else 'purchase'
        }).fillna(0)
        
        # Reset index to use date as a feature
        daily_data.reset_index(inplace=True)
        
        # Extract date features
        daily_data['day_of_week'] = daily_data['transaction_date'].dt.dayofweek
        daily_data['day_of_month'] = daily_data['transaction_date'].dt.day
        daily_data['month'] = daily_data['transaction_date'].dt.month
        daily_data['quarter'] = daily_data['transaction_date'].dt.quarter
        daily_data['year'] = daily_data['transaction_date'].dt.year
        daily_data['is_weekend'] = daily_data['day_of_week'].apply(lambda x: 1 if x >= 5 else 0)
        daily_data['is_month_start'] = daily_data['transaction_date'].dt.is_month_start.astype(int)
        daily_data['is_month_end'] = daily_data['transaction_date'].dt.is_month_end.astype(int)
        daily_data['is_quarter_start'] = daily_data['transaction_date'].dt.is_quarter_start.astype(int)
        daily_data['is_quarter_end'] = daily_data['transaction_date'].dt.is_quarter_end.astype(int)
        
        # Create lag features (previous days' demand)
        for lag in [1, 3, 7, 14, 30]:
            daily_data[f'lag_{lag}'] = daily_data['quantity'].shift(lag)
        
        # Create rolling window features
        for window in [3, 7, 14, 30]:
            daily_data[f'rolling_mean_{window}'] = daily_data['quantity'].rolling(window=window).mean()
            daily_data[f'rolling_std_{window}'] = daily_data['quantity'].rolling(window=window).std()
            daily_data[f'rolling_max_{window}'] = daily_data['quantity'].rolling(window=window).max()
            daily_data[f'rolling_min_{window}'] = daily_data['quantity'].rolling(window=window).min()
        
        # Create transaction type features (one-hot encoding)
        daily_data['is_sale'] = (daily_data['transaction_type'] == 'sale').astype(int)
        daily_data['is_purchase'] = (daily_data['transaction_type'] == 'purchase').astype(int)
        
        # Add medicine-specific features
        daily_data['category'] = medicine.category
        daily_data['price'] = float(medicine.price)
        daily_data['reorder_level'] = medicine.reorder_level
        
        # Add trend feature
        daily_data['trend'] = np.arange(len(daily_data))
        
        # Drop unnecessary columns
        daily_data.drop('transaction_type', axis=1, inplace=True)
        
        # Fill NaN values
        daily_data.fillna(0, inplace=True)
        
        return daily_data
    
    except Exception as e:
        logger.error(f"Feature engineering failed for {medicine.name}: {str(e)}")
        return None

def train_gradient_boosting_model(medicine, days_history=180):
    """
    Train a gradient boosting model for forecasting.
    
    Args:
        medicine: Medicine object
        days_history: Number of days of history to use
        
    Returns:
        Trained model and feature list
    """
    try:
        # Engineer features
        data = engineer_features(medicine, days_history)
        
        if data is None or len(data) < 30:
            logger.warning(f"Insufficient data for {medicine.name} to train gradient boosting model")
            return None, None
        
        # Prepare features and target
        y = data['quantity'].values
        
        # Drop target and date column
        X = data.drop(['quantity', 'transaction_date'], axis=1)
        
        # Convert categorical features to numeric
        X = pd.get_dummies(X, drop_first=True)
        
        # Store feature names
        feature_names = X.columns.tolist()
        
        # Split data for training and validation
        X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, shuffle=False)
        
        # Train gradient boosting model
        model = xgb.XGBRegressor(
            n_estimators=100,
            learning_rate=0.1,
            max_depth=3,
            subsample=0.8,
            colsample_bytree=0.8,
            objective='reg:squarederror',
            random_state=42
        )
        
        # Simple fit without problematic parameters
        model.fit(X_train, y_train)
        
        # Save model
        model_path = MODELS_DIR / f"gb_model_{medicine.id}.joblib"
        joblib.dump(model, model_path)
        
        # Save feature names
        feature_path = MODELS_DIR / f"gb_features_{medicine.id}.joblib"
        joblib.dump(feature_names, feature_path)
        
        # Calculate accuracy on validation set
        y_pred = model.predict(X_val)
        accuracy = calculate_gb_accuracy(y_val, y_pred)
        
        logger.info(f"Trained gradient boosting model for {medicine.name} with accuracy: {accuracy:.2f}%")
        
        return model, feature_names
    
    except Exception as e:
        logger.error(f"Model training failed for {medicine.name}: {str(e)}")
        return None, None

def predict_with_gradient_boosting(medicine, forecast_horizon=30):
    """
    Generate forecasts using gradient boosting.
    
    Args:
        medicine: Medicine object
        forecast_horizon: Number of days to forecast
        
    Returns:
        Predicted quantities for the forecast horizon
    """
    try:
        # Check if model exists
        model_path = MODELS_DIR / f"gb_model_{medicine.id}.joblib"
        feature_path = MODELS_DIR / f"gb_features_{medicine.id}.joblib"
        
        if not model_path.exists() or not feature_path.exists():
            # Train new model
            model, feature_names = train_gradient_boosting_model(medicine)
            if model is None:
                logger.warning(f"Could not train gradient boosting model for {medicine.name}")
                return None
        else:
            # Load existing model
            model = joblib.load(model_path)
            feature_names = joblib.load(feature_path)
        
        # Engineer features for the most recent data
        data = engineer_features(medicine)
        if data is None or len(data) < 30:
            logger.warning(f"Insufficient recent data for {medicine.name} to make gradient boosting predictions")
            return None
        
        # Get the most recent data point
        last_data = data.iloc[-1:].copy()
        
        # Generate predictions for the forecast horizon
        predictions = []
        current_date = last_data['transaction_date'].iloc[0]
        
        for i in range(forecast_horizon):
            # Increment date
            next_date = current_date + timedelta(days=1)
            
            # Create a new row for prediction
            next_row = last_data.copy()
            next_row['transaction_date'] = next_date
            
            # Update date features
            next_row['day_of_week'] = next_date.dayofweek
            next_row['day_of_month'] = next_date.day
            next_row['month'] = next_date.month
            next_row['quarter'] = next_date.quarter
            next_row['year'] = next_date.year
            next_row['is_weekend'] = 1 if next_date.dayofweek >= 5 else 0
            next_row['is_month_start'] = 1 if next_date.day == 1 else 0
            next_row['is_month_end'] = 1 if (next_date + timedelta(days=1)).day == 1 else 0
            next_row['is_quarter_start'] = 1 if next_date.month in [1, 4, 7, 10] and next_date.day == 1 else 0
            next_row['is_quarter_end'] = 1 if next_date.month in [3, 6, 9, 12] and (next_date + timedelta(days=1)).day == 1 else 0
            
            # Update trend
            next_row['trend'] = next_row['trend'] + 1
            
            # Prepare features
            X_next = next_row.drop(['quantity', 'transaction_date'], axis=1)
            X_next = pd.get_dummies(X_next, drop_first=True)
            
            # Ensure all features are present
            for feature in feature_names:
                if feature not in X_next.columns:
                    X_next[feature] = 0
            
            # Keep only the features used during training
            X_next = X_next[feature_names]
            
            # Make prediction
            pred = model.predict(X_next)[0]
            predictions.append(max(0, pred))  # Ensure non-negative predictions
            
            # Update lag features for next prediction
            last_data = next_row.copy()
            last_data['quantity'] = pred
            
            # Update current date
            current_date = next_date
        
        # Return the average prediction for the forecast horizon
        return float(np.mean(predictions))
    
    except Exception as e:
        logger.error(f"Gradient boosting prediction failed for {medicine.name}: {str(e)}")
        return None

def calculate_gb_accuracy(y_true, y_pred):
    """
    Calculate accuracy for gradient boosting predictions.
    
    Args:
        y_true: Actual values
        y_pred: Predicted values
        
    Returns:
        Accuracy percentage
    """
    try:
        # Ensure arrays are numpy arrays
        y_true = np.array(y_true)
        y_pred = np.array(y_pred)
        
        # Calculate MAPE (Mean Absolute Percentage Error)
        # Handle division by zero
        mask = y_true != 0
        if mask.any():
            mape = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
        else:
            mape = 0
        
        # Convert MAPE to accuracy
        accuracy = max(0, 100 - mape)
        return accuracy
    
    except Exception as e:
        logger.error(f"Error calculating gradient boosting accuracy: {str(e)}")
        return 0

def get_feature_importance(medicine):
    """
    Get feature importance from the gradient boosting model.
    
    Args:
        medicine: Medicine object
        
    Returns:
        Dictionary of feature importances
    """
    try:
        # Check if model exists
        model_path = MODELS_DIR / f"gb_model_{medicine.id}.joblib"
        feature_path = MODELS_DIR / f"gb_features_{medicine.id}.joblib"
        
        if not model_path.exists() or not feature_path.exists():
            logger.warning(f"No gradient boosting model found for {medicine.name}")
            return None
        
        # Load model and features
        model = joblib.load(model_path)
        feature_names = joblib.load(feature_path)
        
        # Get feature importance
        importance = model.feature_importances_
        
        # Create dictionary of feature importances
        feature_importance = dict(zip(feature_names, importance))
        
        # Sort by importance
        feature_importance = {k: v for k, v in sorted(feature_importance.items(), key=lambda item: item[1], reverse=True)}
        
        return feature_importance
    
    except Exception as e:
        logger.error(f"Error getting feature importance for {medicine.name}: {str(e)}")
        return None
