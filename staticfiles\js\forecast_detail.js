// Forecast Detail JavaScript
// Enhances the medicine forecast detail page with interactive charts and data visualizations

document.addEventListener('DOMContentLoaded', function () {
    // Initialize primary charts
    initializeDemandChart();
    initializeQuarterlyChart();
    initializeStockLevelChart();
    // Removed forecast accuracy gauge
    initializeTooltips();
});

/**
 * Initializes the primary demand and forecast comparison chart
 */
function initializeDemandChart() {
    const ctx = document.getElementById('demandChart');

    // If the canvas doesn't exist in the DOM, return early
    if (!ctx) return;

    // Extract data from the page
    const medicineName = document.querySelector('.card-body strong')?.textContent || 'Medicine';
    const predictedQuantity = parseFloat(document.querySelectorAll('.card-body table tr')[1]?.querySelectorAll('td strong')[0]?.textContent || 0);
    const actualDemand = parseFloat(document.querySelectorAll('.p-3.bg-light h4')[1]?.textContent || 0);

    // Monthly demand data (extract from table if available)
    let monthlyData = [];
    const monthlyTable = document.querySelector('.table.table-sm.table-bordered');
    if (monthlyTable) {
        const rows = monthlyTable.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 2) {
                const month = cells[0].textContent;
                const quantity = parseFloat(cells[1].textContent);
                if (!isNaN(quantity)) {
                    monthlyData.push({ month, quantity });
                }
            }
        });
    }

    // Default data if monthly data couldn't be extracted
    if (monthlyData.length === 0) {
        const currentDate = new Date();
        for (let i = 5; i >= 0; i--) {
            const date = new Date(currentDate);
            date.setMonth(currentDate.getMonth() - i);
            const month = date.toLocaleString('default', { month: 'short' });
            monthlyData.push({
                month: month,
                quantity: Math.floor(Math.random() * 100) + 50 // Random placeholder data
            });
        }
    }

    // Create the chart
    const months = monthlyData.map(item => item.month);
    const quantities = monthlyData.map(item => item.quantity);

    // Add forecast point at the end
    months.push('Forecast');
    quantities.push(predictedQuantity);

    new Chart(ctx, {
        type: 'line',
        data: {
            labels: months,
            datasets: [{
                label: 'Historical Demand',
                data: quantities.slice(0, -1),
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 2,
                tension: 0.4,
                fill: true
            }, {
                label: 'Forecast',
                data: [null, ...Array(months.length - 2).fill(null), quantities[quantities.length - 1]],
                borderColor: '#e74c3c',
                backgroundColor: 'rgba(231, 76, 60, 0.1)',
                borderWidth: 2,
                borderDash: [5, 5],
                pointBackgroundColor: '#e74c3c',
                pointRadius: 6,
                pointHoverRadius: 8
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: `Demand Trend & Forecast for ${medicineName}`,
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function (context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y.toFixed(1);
                            }
                            return label;
                        }
                    }
                },
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Month'
                    }
                }
            }
        }
    });
}

/**
 * Initializes the quarterly analysis chart
 */
function initializeQuarterlyChart() {
    const ctx = document.getElementById('quarterlyChart');

    // If the canvas doesn't exist in the DOM, return early
    if (!ctx) return;

    // Try to get data from the hidden script tag
    let quarterlyData;
    try {
        const dataScript = document.getElementById('quarterly-data');
        if (dataScript) {
            quarterlyData = JSON.parse(dataScript.textContent);
        }
    } catch (e) {
        console.error('Error parsing quarterly data:', e);
    }

    // If we couldn't get the data, extract from the table or use placeholder data
    if (!quarterlyData) {
        quarterlyData = {
            quarters: ['Q1', 'Q2', 'Q3', 'Q4'],
            averages: [65, 59, 80, 81],
            stdDevs: [5, 4, 7, 6]
        };

        // Try to extract from table
        const quarterlyTable = document.querySelector('.table-bordered:not(.table-sm)');
        if (quarterlyTable) {
            const rows = quarterlyTable.querySelectorAll('tbody tr');
            const quarters = [];
            const averages = [];
            const stdDevs = [];

            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 3) {
                    quarters.push(cells[0].textContent);
                    averages.push(parseFloat(cells[1].textContent));
                    stdDevs.push(parseFloat(cells[2].textContent));
                }
            });

            if (quarters.length > 0) {
                quarterlyData = {
                    quarters,
                    averages,
                    stdDevs
                };
            }
        }
    }

    // Create the chart
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: quarterlyData.quarters,
            datasets: [{
                label: 'Average Demand',
                data: quarterlyData.averages,
                backgroundColor: 'rgba(52, 152, 219, 0.7)',
                borderColor: '#3498db',
                borderWidth: 1,
                borderRadius: 5,
                yAxisID: 'y'
            }, {
                label: 'Standard Deviation',
                data: quarterlyData.stdDevs,
                type: 'line',
                backgroundColor: 'transparent',
                borderColor: '#e74c3c',
                borderWidth: 2,
                pointBackgroundColor: '#e74c3c',
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Quarterly Demand Patterns',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Average Demand'
                    }
                },
                y1: {
                    beginAtZero: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                    title: {
                        display: true,
                        text: 'Standard Deviation'
                    }
                }
            }
        }
    });
}

/**
 * Initializes the stock level chart that shows current stock vs. reorder level
 */
function initializeStockLevelChart() {
    // Check if we have a place to put this chart
    let chartContainer = document.querySelector('.latest-forecast .card-body');
    if (!chartContainer) {
        // If not found, try to add it to the page
        const latestForecastCard = document.querySelector('.col-xl-6 .card.h-100.shadow');
        if (latestForecastCard) {
            const chartDiv = document.createElement('div');
            chartDiv.className = 'chart-container';
            chartDiv.style.height = '180px';
            chartDiv.style.marginTop = '15px';

            const canvas = document.createElement('canvas');
            canvas.id = 'stockLevelChart';
            chartDiv.appendChild(canvas);

            const cardBody = latestForecastCard.querySelector('.card-body');
            if (cardBody) {
                cardBody.appendChild(chartDiv);
                chartContainer = true;
            }
        }
    }

    // If we still couldn't find or create a container, return
    if (!chartContainer) return;

    // Get the canvas
    const ctx = document.getElementById('stockLevelChart');
    if (!ctx) return;

    // Extract data from the page
    const currentStock = parseFloat(document.querySelectorAll('.p-3.bg-light h4')[2]?.textContent || 0);
    const reorderLevel = parseFloat(document.querySelectorAll('.p-3.bg-light h4')[3]?.textContent || 0);

    // Create the chart
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Current Stock', 'Reorder Level'],
            datasets: [{
                label: 'Quantity',
                data: [currentStock, reorderLevel],
                backgroundColor: [
                    currentStock > reorderLevel ? 'rgba(39, 174, 96, 0.7)' : 'rgba(231, 76, 60, 0.7)',
                    'rgba(243, 156, 18, 0.7)'
                ],
                borderColor: [
                    currentStock > reorderLevel ? '#27ae60' : '#e74c3c',
                    '#f39c12'
                ],
                borderWidth: 1,
                borderRadius: 5
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Stock Level vs. Reorder Point',
                    font: {
                        size: 14,
                        weight: 'bold'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            return `Quantity: ${context.raw}`;
                        }
                    }
                },
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity'
                    }
                }
            }
        }
    });
}

/**
 * Forecast accuracy gauge function has been removed
 */

/**
 * Initializes Bootstrap tooltips throughout the page
 */
function initializeTooltips() {
    // Add tooltips to various UI elements for improved UX
    const tooltipElements = [
        { selector: '.badge.bg-warning', title: 'Quantity to order based on forecast and current stock levels' },
        { selector: '.badge.bg-success', title: 'No additional ordering needed at this time' },
        { selector: '.text-success .fas.fa-arrow-up', title: 'Peak demand quarter' },
        { selector: '.text-danger .fas.fa-arrow-down', title: 'Lowest demand quarter' }
    ];

    tooltipElements.forEach(item => {
        const elements = document.querySelectorAll(item.selector);
        elements.forEach(el => {
            // Check if Bootstrap 5 tooltip is available
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                new bootstrap.Tooltip(el, {
                    title: item.title,
                    placement: 'top'
                });
            } else {
                // Fallback to adding a title attribute
                el.setAttribute('title', item.title);
            }
        });
    });
}

/**
 * Adds a button to toggle between chart views
 */
document.addEventListener('DOMContentLoaded', function () {
    // First, check if the main demand chart exists
    if (!document.getElementById('demandChart')) {
        // If it doesn't exist yet, create it
        const firstCard = document.querySelector('.col-12 .card');
        if (firstCard) {
            const chartContainer = document.createElement('div');
            chartContainer.className = 'chart-container mb-4';
            chartContainer.style.height = '350px';

            const canvas = document.createElement('canvas');
            canvas.id = 'demandChart';
            chartContainer.appendChild(canvas);

            // Insert before the first card
            firstCard.parentNode.insertBefore(chartContainer, firstCard);
        }
    }

    // Add export buttons
    addExportOptions();
});

/**
 * Adds export options for the charts and data
 */




