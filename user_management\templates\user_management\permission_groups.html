{% extends "base.html" %}

{% block title %}Permission Groups{% endblock %}

{% block content %}
<div class="container my-5">
    {% if messages %}
    <div class="messages mb-4">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}
    
    <div class="row">
        <div class="col-md-12 mb-4">
            <div class="d-flex justify-content-between align-items-center">
                <h1>Permission Groups</h1>
                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createGroupModal">
                    <i class="fas fa-plus"></i> Create Group
                </button>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Group Name</th>
                                    <th>Description</th>
                                    <th>Members</th>
                                    <th>Created</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for group in groups %}
                                <tr>
                                    <td>{{ group.name }}</td>
                                    <td>{{ group.description }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ group.members.count }} users</span>
                                    </td>
                                    <td>{{ group.created_at|date:"M d, Y" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewGroupModal-{{ group.id }}">
                                                View
                                            </button>
                                            <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#editGroupModal-{{ group.id }}">
                                                Edit
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteGroupModal-{{ group.id }}">
                                                Delete
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <p class="text-muted mb-0">No permission groups found.</p>
                                        <p class="mb-0">Click "Create Group" to add a new permission group.</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Group Modal -->
<div class="modal fade" id="createGroupModal" tabindex="-1" aria-labelledby="createGroupModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
           
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title" id="createGroupModalLabel">Create Permission Group</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="group-name" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="group-name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="group-description" class="form-label">Description</label>
                        <textarea class="form-control" id="group-description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="permission-list">
                            {% for permission in available_permissions %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="permissions[]" 
                                       value="{{ permission.codename }}" id="perm-{{ permission.codename }}">
                                <label class="form-check-label" for="perm-{{ permission.codename }}">
                                    {{ permission.name }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Create Group</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Group Modal Template -->
{% for group in groups %}
<div class="modal fade" id="viewGroupModal-{{ group.id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ group.name }} - Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Group Information</h6>
                        <p><strong>Description:</strong> {{ group.description }}</p>
                        <p><strong>Created:</strong> {{ group.created_at|date:"M d, Y H:i" }}</p>
                        <p><strong>Last Modified:</strong> {{ group.modified_at|date:"M d, Y H:i" }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6>Members ({{ group.members.count }})</h6>
                        <ul class="list-unstyled">
                            {% for member in group.members.all %}
                            <li>{{ member.get_full_name|default:member.username }}</li>
                            {% empty %}
                            <li class="text-muted">No members</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-12">
                        <h6>Permissions</h6>
                        <ul class="list-unstyled row">
                            {% for permission in group.permissions.all %}
                            <li class="col-md-6">
                                <i class="fas fa-check-circle text-success"></i> {{ permission.name }}
                            </li>
                            {% empty %}
                            <li class="text-muted">No permissions assigned</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Group Modal Template -->
<div class="modal fade" id="editGroupModal-{{ group.id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{% url 'user_management:edit_group' group.id %}" method="post">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title">Edit {{ group.name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit-name-{{ group.id }}" class="form-label">Group Name</label>
                        <input type="text" class="form-control" id="edit-name-{{ group.id }}" 
                               name="name" value="{{ group.name }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit-description-{{ group.id }}" class="form-label">Description</label>
                        <textarea class="form-control" id="edit-description-{{ group.id }}" 
                                  name="description" rows="3">{{ group.description }}</textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="permission-list">
                            {% for permission in available_permissions %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="permissions[]" 
                                       value="{{ permission.codename }}" id="edit-perm-{{ group.id }}-{{ permission.codename }}"
                                       {% if permission in group.permissions.all %}checked{% endif %}>
                                <label class="form-check-label" for="edit-perm-{{ group.id }}-{{ permission.codename }}">
                                    {{ permission.name }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save Changes</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Group Modal Template -->
<div class="modal fade" id="deleteGroupModal-{{ group.id }}" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="{% url 'user_management:delete_group' group.id %}" method="post">
                {% csrf_token %}
                <div class="modal-header">
                    <h5 class="modal-title">Delete {{ group.name }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this permission group? This action cannot be undone.</p>
                    <p class="text-danger">
                        <i class="fas fa-exclamation-triangle"></i>
                        Warning: This will remove all associated permissions from {{ group.members.count }} member(s).
                    </p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">Delete Group</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endfor %}

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Add form validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        });
    });
});
</script>
{% endblock %}