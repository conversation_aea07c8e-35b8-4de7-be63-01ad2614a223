﻿document.addEventListener('DOMContentLoaded', function () {
    // Initialize charts and variables
    let forecastChart = null;
    let modalForecast<PERSON>hart = null;

    // DOM Elements
    const medicineSearch = document.getElementById('medicine-search');
    const viewForecastBtns = document.querySelectorAll('.view-forecast-btn');
    const exportReportBtn = document.getElementById('export-report');

    // Initialize tooltips and popovers
    initializeTooltipsAndPopovers();

    // Event Listeners
    setupEventListeners();

    function initializeTooltipsAndPopovers() {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        const popovers = document.querySelectorAll('[data-bs-toggle="popover"]');
        popovers.forEach(popover => {
            new bootstrap.Popover(popover);
        });
    }

    function setupEventListeners() {
        // Search functionality
        medicineSearch.addEventListener('input', handleSearch);

        // View forecast buttons
        viewForecastBtns.forEach(btn => {
            btn.addEventListener('click', handleViewForecast);
        });

        // Export report
        exportReportBtn.addEventListener('click', handleExport);

        // Tab changes
        const tabElements = document.querySelectorAll('button[data-bs-toggle="tab"]');
        tabElements.forEach(tab => {
            tab.addEventListener('shown.bs.tab', handleTabChange);
        });
    }

    function handleSearch(e) {
        const searchTerm = e.target.value.toLowerCase();
        const searchUrl = e.target.dataset.searchUrl;

        clearTimeout(window.searchTimeout);
        window.searchTimeout = setTimeout(() => {
            if (searchTerm.length >= 2) {
                fetch(`${searchUrl}?query=${encodeURIComponent(searchTerm)}`, {
                    headers: {
                        'X-CSRFToken': CSRF_TOKEN
                    }
                })
                    .then(response => response.json())
                    .then(updateSearchResults)
                    .catch(error => console.error('Search error:', error));
            }
        }, 300);
    }

    function updateSearchResults(results) {
        const rows = document.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const medicineId = row.dataset.medicineId;
            const found = results.some(result => result.id.toString() === medicineId);
            row.style.display = found ? '' : 'none';
        });
    }

    function handleViewForecast() {
        const medicineId = this.dataset.medicineId;

        if (FORECAST_DATA && FORECAST_DATA[medicineId]) {
            const medicineData = FORECAST_DATA[medicineId];

            updateModalContent(medicineData);
            setupModalChart(medicineData);
        } else {
            console.error('Forecast data not found for medicine ID:', medicineId);
        }
    }

    function updateModalContent(data) {
        document.getElementById('forecast-medicine-name').textContent = data.name;
        document.getElementById('forecast-current-stock').textContent = data.current_stock;
        document.getElementById('forecast-reorder-level').textContent = data.reorder_level;

        const latestForecast = data.data.find(d => d.week === 'Week +1');
        const predictedDemand = latestForecast ? latestForecast.forecast : 0;
        document.getElementById('forecast-predicted-demand').textContent = predictedDemand;

        const recommendedOrder = Math.max(0, predictedDemand - data.current_stock);
        document.getElementById('forecast-recommended-order').textContent = recommendedOrder;
    }

    function setupModalChart(data) {
        if (modalForecastChart) {
            modalForecastChart.destroy();
        }

        const ctx = document.getElementById('forecast-chart').getContext('2d');
        const chartData = prepareChartData(data);

        modalForecastChart = new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: getChartOptions(data.name)
        });
    }

    function prepareChartData(data) {
        const labels = data.data.map(item => item.week);
        const actualData = data.data.map(item => item.actual || null);
        const forecastData = data.data.map(item => item.forecast || null);
        const reorderLevelData = Array(labels.length).fill(data.reorder_level);

        return {
            labels: labels,
            datasets: [
                {
                    label: 'Actual Demand',
                    data: actualData,
                    borderColor: 'rgba(75, 192, 192, 1)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1,
                    fill: true
                },
                {
                    label: 'Forecast',
                    data: forecastData,
                    borderColor: 'rgba(54, 162, 235, 1)',
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderDash: [5, 5],
                    tension: 0.1,
                    fill: true
                },
                {
                    label: 'Reorder Level',
                    data: reorderLevelData,
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderDash: [2, 2],
                    fill: false
                }
            ]
        };
    }

    function getChartOptions(medicineName) {
        return {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: `Demand Forecast for ${medicineName}`,
                    font: { size: 16 }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function (context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += formatPeso(context.parsed.y);
                            }
                            return label;
                        }
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Time Period'
                    }
                },
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity'
                    },
                    ticks: {
                        callback: function (value) {
                            return formatPeso(value);
                        }
                    }
                }
            }
        };
    }

    function handleExport() {
        window.location.href = '/export-forecast-report/';
    }

    function handleTabChange(event) {
        const targetId = event.target.getAttribute('data-bs-target').replace('#', '');
        updateTableVisibility(targetId);
    }

    function updateTableVisibility(period) {
        const tables = document.querySelectorAll('.tab-pane');
        tables.forEach(table => {
            if (table.id === period) {
                table.classList.add('show', 'active');
            } else {
                table.classList.remove('show', 'active');
            }
        });
    }

    // Update metrics periodically
    setInterval(updateMetrics, 300000); // Every 5 minutes

    function updateMetrics() {
        fetch('/get-dashboard-metrics/', {
            headers: {
                'X-CSRFToken': CSRF_TOKEN
            }
        })
            .then(response => response.json())
            .then(data => {
                updateMetricDisplays(data);
            })
            .catch(error => console.error('Error updating metrics:', error));
    }

    function updateMetricDisplays(data) {
        document.getElementById('below-reorder-count').textContent = data.below_reorder_level;
        document.getElementById('forecast-accuracy').textContent = data.forecast_accuracy + '%';
        document.getElementById('pending-reorders').textContent = data.pending_reorders;
        document.getElementById('estimated-reorder-cost').textContent = formatPeso(data.estimated_reorder_cost);
    }

    function formatPeso(amount) {
        return '₱' + parseFloat(amount).toLocaleString('en-PH', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    }
});








// Add this to your existing event listeners setup
function setupEventListeners() {
    // ... existing code ...

    // Report generation buttons
    document.querySelectorAll('.generate-report').forEach(btn => {
        btn.addEventListener('click', function (e) {
            e.preventDefault();
            e.stopPropagation();
            const period = this.dataset.period;
            generateReport(period);
        });
    });
}

function generateReport(period) {
    // Show loading indicator
    const loadingToast = showToast('Generating report...', 'info');

    fetch(`/generate-forecast-report/${period}/`, {
        method: 'POST',
        headers: {
            'X-CSRFToken': CSRF_TOKEN,
            'Content-Type': 'application/json',
        }
    })
        .then(response => {
            if (!response.ok) throw new Error('Network response was not ok');
            return response.blob();
        })
        .then(blob => {
            // Hide loading indicator
            loadingToast.hide();

            // Create download link
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `forecast_report_${period}_${new Date().toISOString().split('T')[0]}.pdf`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            a.remove();

            showToast('Report generated successfully!', 'success');
        })
        .catch(error => {
            console.error('Error:', error);
            loadingToast.hide();
            showToast('Error generating report. Please try again.', 'error');
        });
}

function showToast(message, type) {
    const toast = new bootstrap.Toast(document.createElement('div'));
    toast._element.className = `toast align-items-center text-white bg-${type} border-0`;
    toast._element.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">${message}</div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;
    document.body.appendChild(toast._element);
    toast.show();
    return toast;
}





// Initialize charts
document.addEventListener('DOMContentLoaded', function () {
    // Demand Trends Chart
    const demandCtx = document.getElementById('demandTrendsChart').getContext('2d');
    new Chart(demandCtx, {
        type: 'line',
        data: {
            labels: FORECAST_DATA.demand_trends.labels,
            datasets: [{
                label: 'Actual Demand',
                data: FORECAST_DATA.demand_trends.actual,
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                fill: true,
                tension: 0.4
            }, {
                label: 'Predicted Demand',
                data: FORECAST_DATA.demand_trends.predicted,
                borderColor: 'rgba(54, 162, 235, 1)',
                borderDash: [5, 5],
                fill: false,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity'
                    }
                }
            }
        }
    });

    // Stock Levels Chart
    const stockCtx = document.getElementById('stockLevelsChart').getContext('2d');
    new Chart(stockCtx, {
        type: 'line',
        data: {
            labels: FORECAST_DATA.stock_levels.labels,
            datasets: [{
                label: 'Current Stock',
                data: FORECAST_DATA.stock_levels.current,
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                fill: true
            }, {
                label: 'Reorder Level',
                data: FORECAST_DATA.stock_levels.reorder_level,
                borderColor: 'rgba(255, 99, 132, 1)',
                borderDash: [5, 5],
                fill: false
            }]
        },
        options: {
            responsive: true,
            plugins: {
                title: {
                    display: false
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Stock Level'
                    }
                }
            }
        }
    });
});

// Report Generation Handler
class ReportGenerator {
    constructor() {
        this.form = document.getElementById('reportConfigForm');
        this.generateBtn = document.getElementById('generateReportBtn');
        this.progressAlert = document.getElementById('reportProgress');
        this.modal = document.getElementById('reportModal');
        this.modalInstance = null;

        this.init();
    }

    init() {
        // Initialize Bootstrap modal
        this.modalInstance = new bootstrap.Modal(this.modal);

        // Add event listeners
        this.generateBtn.addEventListener('click', () => this.generateReport());

        // Component selection validation
        const checkboxes = this.form.querySelectorAll('input[type="checkbox"]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', () => this.validateComponents());
        });
    }

    validateComponents() {
        const checkedComponents = this.form.querySelectorAll('input[name="components[]"]:checked');
        this.generateBtn.disabled = checkedComponents.length === 0;
    }

    showProgress() {
        this.progressAlert.classList.remove('d-none');
        this.generateBtn.disabled = true;
    }

    hideProgress() {
        this.progressAlert.classList.add('d-none');
        this.generateBtn.disabled = false;
    }

    async generateReport() {
        try {
            this.showProgress();

            const formData = {
                time_period: document.getElementById('timePeriod').value,
                format: document.getElementById('reportFormat').value,
                components: Array.from(this.form.querySelectorAll('input[name="components[]"]:checked'))
                    .map(cb => cb.value)
            };

            const response = await fetch('/inventory/generate-report/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: JSON.stringify(formData)
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const blob = await response.blob();
            const filename = this.getFilename(formData.format);
            this.downloadFile(blob, filename);

            // Close modal after successful generation
            this.modalInstance.hide();

            // Show success toast
            this.showToast('Success', 'Report generated successfully!', 'success');

        } catch (error) {
            console.error('Report generation failed:', error);
            this.showToast('Error', 'Failed to generate report. Please try again.', 'error');
        } finally {
            this.hideProgress();
        }
    }

    getFilename(format) {
        const date = new Date().toISOString().split('T')[0];
        const extension = format === 'excel' ? 'xlsx' : 'pdf';
        return `inventory_report_${date}.${extension}`;
    }

    downloadFile(blob, filename) {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    showToast(title, message, type) {
        // Assuming you have a toast notification system
        if (window.toastr) {
            toastr[type](message, title);
        } else {
            alert(`${title}: ${message}`);
        }
    }
}

// Initialize report generator when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ReportGenerator();
});







// Initialize charts and date pickers
document.addEventListener('DOMContentLoaded', function () {
    // Initialize date range picker
    if ($.fn.daterangepicker) {
        $('input[name="daterange"]').daterangepicker({
            startDate: moment().startOf('month'),
            endDate: moment().endOf('month'),
            locale: {
                format: 'YYYY-MM-DD'
            }
        });
    }

    // Initialize charts if Chart.js is available
    if (typeof Chart !== 'undefined' && document.getElementById('forecastChart')) {
        const ctx = document.getElementById('forecastChart').getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: FORECAST_DATA.labels,
                datasets: [{
                    label: 'Predicted Demand',
                    data: FORECAST_DATA.values,
                    borderColor: '#3498db',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
});