from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.contrib.auth.models import User
from django.utils.html import format_html
from django.urls import path, reverse
from django.shortcuts import redirect, get_object_or_404, render
from django.contrib import messages
from django.utils.crypto import get_random_string
from django.http import HttpResponseRedirect
from django.template.response import TemplateResponse
from .models import UserProfile, UserActivity, UserPermissionGroup, UserNotification, ArchivedUser

class UserProfileInline(admin.StackedInline):
    model = UserProfile
    can_delete = False
    verbose_name_plural = 'Profile'
    fieldsets = (
        ('Personal Information', {
            'fields': ('first_name', 'middle_name', 'last_name', 'birthday', 'sex')
        }),
        ('Contact Details', {
            'fields': ('phone_number', 'address')
        }),
        ('Profile Settings', {
            'fields': ('profile_picture', 'is_active')
        }),
        ('2FA Settings', {
            'fields': ('totp_secret', 'totp_verified'),
            'classes': ('collapse',)
        }),
    )

class UserAdmin(BaseUserAdmin):
    class Media:
        css = {
            'all': ('admin/css/custom_admin.css',)
        }

    inlines = (UserProfileInline,)
    list_display = ('username', 'email', 'get_phone_number', 'first_name', 'last_name', 
                   'is_active', 'is_staff', 'reset_password_button', 'edit_contact_button',
                    'archive_user_button', 'reset_totp_button')

    list_filter = ('is_staff', 'is_superuser', 'is_active', 'groups')
    search_fields = ('username', 'first_name', 'last_name', 'email', 'profile__phone_number')
    ordering = ('username',)
    actions = ['archive_selected_users', 'make_active', 'make_inactive']
    actions_on_top = True
    actions_on_bottom = True
    actions_selection_counter = True

    def reset_totp_button(self, obj):
        return format_html(
            '<a class="button" href="{}" style="background-color: #28a745; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 2px 0;">Reset 2FA</a>',
            reverse('admin:auth_user_reset_totp', args=[obj.pk])
        )
    reset_totp_button.short_description = 'Reset 2FA'

    def get_phone_number(self, obj):
        return obj.profile.phone_number if hasattr(obj, 'profile') else '-'
    get_phone_number.short_description = 'Phone Number'

    def reset_password_button(self, obj):
        return format_html(
            '<a class="button" href="{}" style="background-color: #417690; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 2px 0;">Reset Password</a>',
            reverse('admin:reset_user_password', args=[obj.pk])
        )
    reset_password_button.short_description = 'Reset Password'

    def edit_contact_button(self, obj):
        return format_html(
            '<a class="button" href="{}" style="background-color: #79aec8; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 2px 0;">Edit Contact</a>',
            reverse('admin:edit_user_contact', args=[obj.pk])
        )
    edit_contact_button.short_description = 'Edit Contact'

    def archive_user_button(self, obj):
        return format_html(
            '<a class="button" href="{}" style="background-color: #ba2121; color: white; padding: 5px 10px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 2px 0;">Archive User</a>',
            reverse('admin:archive_user', args=[obj.pk])
        )
    archive_user_button.short_description = 'Archive'

    def make_active(self, request, queryset):
        queryset.update(is_active=True)
        messages.success(request, f'{queryset.count()} users have been marked as active')
    make_active.short_description = "Mark selected users as active"

    def make_inactive(self, request, queryset):
        queryset.update(is_active=False)
        messages.success(request, f'{queryset.count()} users have been marked as inactive')
    make_inactive.short_description = "Mark selected users as inactive"

    def get_urls(self):
        urls = super().get_urls()
        custom_urls = [
            path(
                '<id>/reset-password/',
                self.admin_site.admin_view(self.reset_user_password),
                name='reset_user_password',
            ),
            path(
                '<id>/edit-contact/',
                self.admin_site.admin_view(self.edit_user_contact),
                name='edit_user_contact',
            ),
            path(
                '<id>/archive/',
                self.admin_site.admin_view(self.archive_user),
                name='archive_user',
            ),
            # Add URL pattern for TOTP reset
            path(
                '<id>/reset-totp/',
                self.admin_site.admin_view(self.reset_user_totp),
                name='auth_user_reset_totp',
            ),
        ]
        return custom_urls + urls

    def reset_user_totp(self, request, id):
        user = get_object_or_404(User, pk=id)
        
        if request.method == 'POST':
            try:
                # Reset TOTP settings
                if hasattr(user, 'profile'):
                    user.profile.totp_secret = ''
                    user.profile.totp_verified = False
                    user.profile.save()
                    
                    # Record the action in the activity log
                    UserActivity.objects.create(
                        user=user,
                        action='totp_reset',
                        description=f'2FA was reset by admin {request.user.username}',
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
                    
                    messages.success(request, f'2FA has been reset for user {user.username}')
                else:
                    messages.error(request, f'User profile not found for {user.username}')
                    
            except Exception as e:
                messages.error(request, f'Failed to reset 2FA: {str(e)}')
                
            return redirect('admin:auth_user_changelist')
        
        # For GET requests, show a confirmation page
        context = {
            'title': f'Reset 2FA for {user.username}',
            'user': user,
            'opts': self.model._meta,
            'original': user,
            'is_popup': False,
            'to_field': None,
            'has_delete_permission': False,
            'has_add_permission': False,
            'has_change_permission': True,
            'show_save': True
        }
        return TemplateResponse(
            request,
            'user_management/reset_totp_confirmation.html',
            context,
        )

    def reset_user_password(self, request, id):
        user = get_object_or_404(User, pk=id)
        if request.method == 'POST':
            # Check if custom password is provided or if we should generate one
            custom_password = request.POST.get('custom_password')
            generate_random = request.POST.get('generate_random') == 'on'
            
            if custom_password and not generate_random:
                # Use the custom password provided by admin
                new_password = custom_password
            else:
                # Generate a random password
                new_password = get_random_string(12)
                
            # Set the new password
            user.set_password(new_password)
            user.save()
            
            # Record the action in the activity log
            try:
                UserActivity.objects.create(
                    user=user,
                    action='password_reset',
                    description=f'Password was reset by admin {request.user.username}',
                    ip_address=request.META.get('REMOTE_ADDR')
                )
            except:
                pass  # If activity log fails, continue anyway
            
            # Display success message with the new password
            messages.success(request, f'Password reset successful for user {user.username}. '
                                     f'New password: {new_password}')
            
            return redirect('admin:auth_user_changelist')

        # For GET requests, show a form to enter custom password or generate random one
        context = {
            'title': f'Reset password for {user.username}',
            'user': user,
            'opts': self.model._meta,
            'original': user,
            'is_popup': False,
            'to_field': None,
            'has_delete_permission': False,
            'has_add_permission': False,
            'has_change_permission': True,
            'show_save': True
        }
        return TemplateResponse(
            request,
            'user_management/reset_password_form.html',
            context,
        )

    def edit_user_contact(self, request, id):
        user = get_object_or_404(User, pk=id)
        
        if request.method == 'POST':
            new_email = request.POST.get('email')
            new_phone = request.POST.get('phone_number')

            if new_email:
                user.email = new_email
                user.save()

            if new_phone and hasattr(user, 'profile'):
                user.profile.phone_number = new_phone
                user.profile.save()
                
            # Record the action in the activity log
            try:
                UserActivity.objects.create(
                    user=user,
                    action='contact_update',
                    description=f'Contact information updated by admin {request.user.username}',
                    ip_address=request.META.get('REMOTE_ADDR')
                )
            except:
                pass  # If activity log fails, continue anyway

            messages.success(request, f'Contact information updated for user {user.username}')
            return redirect('admin:auth_user_changelist')
        
        # For GET requests, show a form with the current values
        context = {
            'title': f'Edit contact information for {user.username}',
            'user': user,
            'current_email': user.email,
            'current_phone': user.profile.phone_number if hasattr(user, 'profile') else '',
            'opts': self.model._meta,
            'original': user,
            'is_popup': False,
            'to_field': None,
            'has_delete_permission': False,
            'has_add_permission': False,
            'has_change_permission': True,
            'show_save': True
        }
        return TemplateResponse(
            request,
            'user_management/edit_contact_form.html',
            context,
        )

    def archive_user(self, request, id):
        user = get_object_or_404(User, pk=id)
        
        if request.method == 'POST':
            try:
                # Gather all necessary data before deletion
                profile_data = self._get_profile_data(user)
                username = user.username
                email = user.email
                first_name = user.first_name
                last_name = user.last_name
                
                # Create archived user record
                ArchivedUser.objects.create(
                    username=username,
                    email=email,
                    first_name=first_name,
                    last_name=last_name,
                    profile_data=profile_data,
                    archived_by=request.user
                )
                
                # Record the action in the activity log before deletion
                try:
                    UserActivity.objects.create(
                        user=request.user,  # Log this under the admin user since target user will be deleted
                        action='user_archived',
                        description=f'User {username} was archived',
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
                except:
                    pass  # If activity log fails, continue anyway
                
                # Delete the user
                user.delete()
                messages.success(request, f'User {username} has been archived successfully')
            except Exception as e:
                messages.error(request, f'Failed to archive user: {str(e)}')

            return redirect('admin:auth_user_changelist')
        
        # For GET requests, show a confirmation page
        context = {
            'title': f'Archive user {user.username}',
            'user': user,
            'opts': self.model._meta,
            'original': user,
            'is_popup': False,
            'to_field': None,
            'has_delete_permission': True,
            'has_add_permission': False,
            'has_change_permission': True,
            'show_save': True
        }
        return TemplateResponse(
            request,
            'user_management/archive_user_confirmation.html',
            context,
        )

    def archive_selected_users(self, request, queryset):
        successful_archives = 0
        failed_archives = 0
        
        for user in queryset:
            try:
                # Gather profile data before deletion
                profile_data = self._get_profile_data(user)
                
                # Create archived user record
                ArchivedUser.objects.create(
                    username=user.username,
                    email=user.email,
                    first_name=user.first_name,
                    last_name=user.last_name,
                    profile_data=profile_data,
                    archived_by=request.user
                )
                
                # Record the action in the activity log before deletion
                try:
                    UserActivity.objects.create(
                        user=request.user,  # Log this under the admin user since target user will be deleted
                        action='user_archived',
                        description=f'User {user.username} was archived via bulk action',
                        ip_address=request.META.get('REMOTE_ADDR')
                    )
                except:
                    pass  # If activity log fails, continue anyway
                
                # Delete the user
                user.delete()
                successful_archives += 1
            except Exception as e:
                messages.error(request, f'Failed to archive user {user.username}: {str(e)}')
                failed_archives += 1
        
        if successful_archives > 0:
            messages.success(request, f'{successful_archives} users have been archived successfully')
        if failed_archives > 0:
            messages.warning(request, f'{failed_archives} users could not be archived')
            
    archive_selected_users.short_description = "Archive selected users"

    def _get_profile_data(self, user):
        """Helper method to get profile data for archiving"""
        if hasattr(user, 'profile'):
            return {
                'phone_number': user.profile.phone_number,
                'address': user.profile.address,
                'birthday': str(user.profile.birthday) if user.profile.birthday else None,
                'sex': user.profile.sex
            }
        return {}

    def save_model(self, request, obj, form, change):
        super().save_model(request, obj, form, change)
        UserProfile.objects.get_or_create(user=obj)

    fieldsets = (
        (None, {'fields': ('username', 'password')}),
        ('Personal info', {'fields': ('first_name', 'last_name', 'email')}),
        ('Permissions', {
            'fields': ('is_active', 'is_staff', 'is_superuser', 'groups', 'user_permissions'),
        }),
        ('Important dates', {'fields': ('last_login', 'date_joined')}),
    )

class ArchivedUserAdmin(admin.ModelAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'archived_at', 'archived_by')
    search_fields = ('username', 'email', 'first_name', 'last_name')
    readonly_fields = ('username', 'email', 'first_name', 'last_name', 'profile_data', 
                      'archived_at', 'archived_by')
    ordering = ('-archived_at',)
    list_filter = ('archived_at',)

class UserActivityAdmin(admin.ModelAdmin):
    list_display = ('user', 'action', 'ip_address', 'timestamp')
    list_filter = ('action', 'timestamp')
    search_fields = ('user__username', 'description', 'ip_address')
    readonly_fields = ('timestamp', 'ip_address')

class UserPermissionGroupAdmin(admin.ModelAdmin):
    list_display = ('name', 'created_at', 'modified_at')
    filter_horizontal = ('users',)
    search_fields = ('name', 'description')
    readonly_fields = ('created_at', 'modified_at')

class UserNotificationAdmin(admin.ModelAdmin):
    list_display = ('user', 'title', 'notification_type', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('user__username', 'title', 'message')
    readonly_fields = ('created_at', 'read_at')

# Unregister the default User admin and register our custom one
admin.site.unregister(User)
admin.site.register(User, UserAdmin)

# Register other models
admin.site.register(ArchivedUser, ArchivedUserAdmin)
admin.site.register(UserActivity, UserActivityAdmin)
admin.site.register(UserPermissionGroup, UserPermissionGroupAdmin)
admin.site.register(UserNotification, UserNotificationAdmin)