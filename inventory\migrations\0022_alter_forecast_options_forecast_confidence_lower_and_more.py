# Generated by Django 4.2.9 on 2025-03-12 02:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0021_remove_forecast_monthly_forecast_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='forecast',
            options={'ordering': ['-forecast_date']},
        ),
        migrations.AddField(
            model_name='forecast',
            name='confidence_lower',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='forecast',
            name='confidence_upper',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='forecast',
            name='forecast_period',
            field=models.CharField(choices=[('weekly', 'Weekly'), ('monthly', 'Monthly'), ('yearly', 'Yearly')], default='monthly', max_length=10),
        ),
        migrations.AddField(
            model_name='forecast',
            name='safety_stock',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='forecast',
            name='forecast_method',
            field=models.CharField(blank=True, choices=[('arima', 'ARIMA'), ('linear', 'Linear Regression'), ('polynomial', 'Polynomial Regression'), ('moving_avg', 'Moving Average')], max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='forecast',
            name='predicted_quantity',
            field=models.FloatField(),
        ),
        migrations.AddIndex(
            model_name='forecast',
            index=models.Index(fields=['medicine', 'forecast_date'], name='inventory_f_medicin_c7a372_idx'),
        ),
        migrations.AddIndex(
            model_name='forecast',
            index=models.Index(fields=['forecast_period'], name='inventory_f_forecas_642b31_idx'),
        ),
    ]
