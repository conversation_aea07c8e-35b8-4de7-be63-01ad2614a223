﻿{% extends 'base.html' %}

{% block content %}
<style>
    .table .table-header-gradient {
        background: linear-gradient(135deg, #2c3e50 0%, #3498db 100%) !important;
    }

        .table .table-header-gradient th {
            color: white !important;
            background: transparent !important;
        }

            .table .table-header-gradient th a {
                color: white !important;
            }

                .table .table-header-gradient th a:hover {
                    color: rgba(255, 255, 255, 0.8) !important;
                    transition: color 0.3s ease;
                }
</style>

<div class="container-fluid px-4 py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-primary fw-bold">
                    <i class="fas fa-pills me-2"></i>Medicine List
                </h1>
                <div class="d-flex gap-2">
                    <a href="{% url 'upload_medicine_form' %}" class="btn btn-primary btn-sm rounded-pill shadow-sm">
                        <i class="fas fa-file-upload me-2"></i>Upload Medicines
                    </a>
                    <a href="{% url 'download_medicines_excel' %}" class="btn btn-success btn-sm rounded-pill shadow-sm">
                        <i class="fas fa-file-excel me-2"></i>Download Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-2">
                    <form method="get" action="">
                        <div class="input-group">
                            <span class="input-group-text bg-white border-end-0">
                                <i class="fas fa-search text-primary"></i>
                            </span>
                            <input type="text" name="search" value="{{ search_query }}" class="form-control border-start-0" placeholder="Search for a medicine...">
                            {% if sort_by %}
                            <input type="hidden" name="sort_by" value="{{ sort_by }}">
                            {% endif %}
                            <button type="submit" class="btn btn-primary px-4">
                                Search
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="mb-0">Medicines List</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover mb-0">
                            <thead class="table-header-gradient">
                                <tr>
                                    <th class="px-3 py-3">
                                        <a href="?sort_by=name{% if search_query %}&search={{ search_query }}{% endif %}" class="text-decoration-none">
                                            Name
                                            <i class="fas fa-sort ms-1 small"></i>
                                        </a>
                                    </th>
                                    <th class="px-3 py-3">
                                        <a href="?sort_by=brand{% if search_query %}&search={{ search_query }}{% endif %}" class="text-decoration-none">
                                            Brand
                                            <i class="fas fa-sort ms-1 small"></i>
                                        </a>
                                    </th>
                                    <th class="px-3 py-3">
                                        <a href="?sort_by=quantity{% if search_query %}&search={{ search_query }}{% endif %}" class="text-decoration-none">
                                            Quantity
                                            <i class="fas fa-sort ms-1 small"></i>
                                        </a>
                                    </th>
                                    <th class="px-3 py-3">
                                        <a href="?sort_by=price{% if search_query %}&search={{ search_query }}{% endif %}" class="text-decoration-none">
                                            Price
                                            <i class="fas fa-sort ms-1 small"></i>
                                        </a>
                                    </th>
                                    <th class="px-3 py-3">
                                        <a href="?sort_by=category{% if search_query %}&search={{ search_query }}{% endif %}" class="text-decoration-none">
                                            Category
                                            <i class="fas fa-sort ms-1 small"></i>
                                        </a>
                                    </th>
                                    <th class="px-3 py-3">
                                        <a href="?sort_by=expiration_date{% if search_query %}&search={{ search_query }}{% endif %}" class="text-decoration-none">
                                            Expiration
                                            <i class="fas fa-sort ms-1 small"></i>
                                        </a>
                                    </th>
                                    <th class="px-3 py-3 text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medicine in medicines %}
                                <tr>
                                    <td class="px-3 py-3 align-middle text-primary fw-medium">{{ medicine.name }}</td>
                                    <td class="px-3 py-3 align-middle">
                                        <div class="d-flex flex-column">
                                            <span class="fw-medium">{{ medicine.brand }}</span>
                                            <small class="text-muted">{{ medicine.supplier }}</small>
                                        </div>
                                    </td>
                                    <td class="px-3 py-3 align-middle">
                                        <span class="{% if medicine.quantity <= medicine.reorder_level %}text-danger fw-bold{% endif %}">
                                            {{ medicine.quantity }}
                                        </span>
                                        {% if medicine.quantity <= medicine.reorder_level %}
                                        <span class="badge bg-danger ms-2">Low</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-3 py-3 align-middle">
                                        <div class="d-flex align-items-center">
                                            <span id="price-display-{{ medicine.pk }}">₱{{ medicine.price }}</span>
                                            <button type="button" class="btn btn-sm btn-outline-primary ms-2 edit-price-btn" data-medicine-id="{{ medicine.pk }}" data-current-price="{{ medicine.price }}">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                        </div>
                                    </td>
                                    <td class="px-3 py-3 align-middle">
                                        <span class="badge bg-primary bg-opacity-10 text-primary px-2 py-1 rounded-pill">
                                            {{ medicine.category }}
                                        </span>
                                    </td>
                                    <td class="px-3 py-3 align-middle">
                                        {% if medicine.expiration_date %}
                                        <span class="
                                              {% if medicine.is_expired %}
                                              badge bg-danger
                                              {% elif medicine.days_until_expiration <= 30 %}
                                            badge bg-warning text-dark
                                            {% else %}
                                            badge bg-success
                                            {% endif %} px-2 py-1">
                                            {{ medicine.expiration_date|date:"F d, Y" }}
                                            {% if medicine.is_expired %}
                                            (Expired)
                                            {% elif medicine.days_until_expiration <= 30 %}
                                            ({{ medicine.days_until_expiration }} days left)
                                            {% endif %}
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary px-2 py-1">Not Set</span>
                                        {% endif %}
                                    </td>
                                    <td class="px-3 py-3 align-middle text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'medicine_detail' medicine.pk %}" class="btn btn-primary btn-sm me-1" title="View Details">
                                                <i class="fas fa-info-circle"></i>
                                            </a>
                                            <button type="button" class="btn btn-success btn-sm me-1" data-bs-toggle="modal" data-bs-target="#addQuantityModal-{{ medicine.pk }}" title="Add Quantity">
                                                <i class="fas fa-plus"></i>
                                            </button>
                                            <a href="{% url 'medicine_forecast_detail' medicine_id=medicine.pk %}" class="btn btn-primary btn-sm me-1" title="View Forecast">
                                                <i class="fas fa-chart-line"></i>
                                            </a>
                                            <a href="{% url 'medicine_movement_analysis' medicine.pk %}" class="btn btn-info btn-sm" title="Movement Analysis">
                                                <i class="fas fa-exchange-alt"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        <div class="d-flex flex-column align-items-center">
                                            <i class="fas fa-box-open text-primary mb-2" style="font-size: 2rem;"></i>
                                            <p class="text-primary mb-0">No medicines found.</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="row mt-4">
        <div class="col-md-12">
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if medicines.has_previous %}
                    <li class="page-item">
                        <a class="page-link text-primary" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort_by={{ sort_by }}{% endif %}">&laquo; First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link text-primary" href="?page={{ medicines.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort_by={{ sort_by }}{% endif %}">Previous</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#">&laquo; First</a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#">Previous</a>
                    </li>
                    {% endif %}

                    {% with ''|center:medicines.paginator.num_pages as range %}
                    {% for _ in range %}
                    {% with forloop.counter as i %}
                    {% if medicines.number == i %}
                    <li class="page-item active bg-primary">
                        <span class="page-link border-primary">{{ i }}</span>
                    </li>
                    {% elif i > medicines.number|add:'-3' and i < medicines.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link text-primary" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort_by={{ sort_by }}{% endif %}">{{ i }}</a>
                    </li>
                    {% endif %}
                    {% endwith %}
                    {% endfor %}
                    {% endwith %}

                    <li class="page-item active bg-primary">
                        <span class="page-link border-primary">
                            Page {{ medicines.number }} of {{ medicines.paginator.num_pages }}
                        </span>
                    </li>

                    {% if medicines.has_next %}
                    <li class="page-item">
                        <a class="page-link text-primary" href="?page={{ medicines.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort_by={{ sort_by }}{% endif %}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link text-primary" href="?page={{ medicines.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if sort_by %}&sort_by={{ sort_by }}{% endif %}">Last &raquo;</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#">Next</a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#">Last &raquo;</a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
    </div>

    <!-- Modals -->
    {% for medicine in medicines %}
    <div class="modal fade" id="addQuantityModal-{{ medicine.pk }}" tabindex="-1" aria-labelledby="addQuantityModalLabel-{{ medicine.pk }}" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addQuantityModalLabel-{{ medicine.pk }}">Add Quantity for {{ medicine.name }}</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" action="{% url 'add_quantity' medicine.pk %}">
                    {% csrf_token %}
                    <input type="hidden" name="next" value="{{ request.get_full_path }}">
                    <div class="modal-body">
                        <div class="mb-3">
                            <label for="quantity-{{ medicine.pk }}" class="form-label text-primary">Quantity</label>
                            <input type="number" class="form-control" id="quantity-{{ medicine.pk }}" name="quantity" required min="1">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal">
                            <i class="fas fa-times"></i> Close
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Add Quantity
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize all modals
        var modals = document.querySelectorAll('.modal');
        modals.forEach(function (modal) {
            new bootstrap.Modal(modal);
        });

        // Add form submission handling
        var forms = document.querySelectorAll('.modal form');
        forms.forEach(function (form) {
            form.addEventListener('submit', function (e) {
                var quantityInput = form.querySelector('input[name="quantity"]');
                if (parseInt(quantityInput.value) <= 0) {
                    e.preventDefault();
                    alert('Please enter a valid quantity (greater than 0)');
                }
            });
        });

        // Handle price edit buttons
        document.querySelectorAll('.edit-price-btn').forEach(button => {
            button.addEventListener('click', function() {
                const medicineId = this.getAttribute('data-medicine-id');
                const currentPrice = this.getAttribute('data-current-price');

                // Create modal for price editing
                const modalHtml = `
                    <div class="modal fade" id="editPriceModal-${medicineId}" tabindex="-1" aria-labelledby="editPriceModalLabel-${medicineId}" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header bg-primary text-white">
                                    <h5 class="modal-title" id="editPriceModalLabel-${medicineId}">Update Medicine Price</h5>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body">
                                    <div class="mb-3">
                                        <label for="new-price-${medicineId}" class="form-label">New Price (₱)</label>
                                        <input type="number" class="form-control" id="new-price-${medicineId}" value="${currentPrice}" min="0.01" step="0.01">
                                        <div class="form-text">Enter the new price for this medicine</div>
                                    </div>
                                    <div id="price-update-status-${medicineId}"></div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-primary save-price-btn" data-medicine-id="${medicineId}">Save Changes</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Add modal to the document
                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // Initialize and show the modal
                const priceModal = new bootstrap.Modal(document.getElementById(`editPriceModal-${medicineId}`));
                priceModal.show();

                // Handle save button click
                document.querySelector(`#editPriceModal-${medicineId} .save-price-btn`).addEventListener('click', function() {
                    const newPrice = document.getElementById(`new-price-${medicineId}`).value;

                    if (parseFloat(newPrice) <= 0) {
                        alert('Price must be greater than zero');
                        return;
                    }

                    // Update the price via AJAX
                    updateMedicinePrice(medicineId, newPrice, priceModal);
                });
            });
        });

        // Function to update medicine price
        function updateMedicinePrice(medicineId, newPrice, modal) {
            const statusElement = document.getElementById(`price-update-status-${medicineId}`);

            // Show loading indicator
            statusElement.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div> Updating price...';

            fetch('{% url "update_medicine_price" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({
                    medicine_id: medicineId,
                    price: newPrice
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the price display
                    document.getElementById(`price-display-${medicineId}`).textContent = '₱' + data.new_price;

                    // Update the data attribute for future edits
                    document.querySelector(`.edit-price-btn[data-medicine-id="${medicineId}"]`).setAttribute('data-current-price', data.new_price);

                    // Show success message
                    statusElement.innerHTML = '<div class="alert alert-success py-1 px-2 mb-0"><i class="fas fa-check-circle me-1"></i>' + data.message + '</div>';

                    // Close the modal after a short delay
                    setTimeout(() => {
                        modal.hide();

                        // Remove the modal from DOM after hiding
                        document.getElementById(`editPriceModal-${medicineId}`).addEventListener('hidden.bs.modal', function() {
                            this.remove();
                        });
                    }, 1500);
                } else {
                    // Show error message
                    statusElement.innerHTML = '<div class="alert alert-danger py-1 px-2 mb-0"><i class="fas fa-exclamation-circle me-1"></i>Error: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                statusElement.innerHTML = '<div class="alert alert-danger py-1 px-2 mb-0"><i class="fas fa-exclamation-circle me-1"></i>Error updating price. Please try again.</div>';
            });
        }
    });
</script>
{% endblock %}

{% endblock %}
