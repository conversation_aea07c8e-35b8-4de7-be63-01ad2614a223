from django.db import migrations

def populate_generic_medicines(apps, schema_editor):
    """
    Populate the GenericMedicine model from existing medicines.
    Group medicines by name (ignoring case) and create a GenericMedicine for each unique name.
    Then link each Medicine to its corresponding GenericMedicine.
    """
    Medicine = apps.get_model('inventory', 'Medicine')
    GenericMedicine = apps.get_model('inventory', 'GenericMedicine')
    
    # Get all medicines
    medicines = Medicine.objects.all()
    
    # Create a dictionary to track generic medicines by normalized name
    generic_medicines = {}
    
    # First pass: Create generic medicines
    for medicine in medicines:
        # Normalize the name (lowercase, strip whitespace)
        normalized_name = medicine.name.lower().strip()
        
        # Skip if we already have this generic medicine
        if normalized_name in generic_medicines:
            continue
        
        # Create a new generic medicine
        generic = GenericMedicine.objects.create(
            name=medicine.name,  # Use the original case for display
            category=medicine.category,
            description=f"Generic {medicine.name}"
        )
        
        # Store in our dictionary
        generic_medicines[normalized_name] = generic
    
    # Second pass: Link medicines to their generic counterparts
    for medicine in medicines:
        normalized_name = medicine.name.lower().strip()
        if normalized_name in generic_medicines:
            medicine.generic_medicine = generic_medicines[normalized_name]
            medicine.save()

def reverse_populate(apps, schema_editor):
    """
    Reverse the migration by unlinking medicines from generic medicines
    and deleting all generic medicines.
    """
    Medicine = apps.get_model('inventory', 'Medicine')
    GenericMedicine = apps.get_model('inventory', 'GenericMedicine')
    
    # Unlink all medicines
    Medicine.objects.all().update(generic_medicine=None)
    
    # Delete all generic medicines
    GenericMedicine.objects.all().delete()

class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0029_forecast_generic_medicine_alter_forecast_medicine'),
    ]

    operations = [
        migrations.RunPython(populate_generic_medicines, reverse_populate),
    ]
