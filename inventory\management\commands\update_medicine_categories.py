from django.core.management.base import BaseCommand
from inventory.models import Medicine

class Command(BaseCommand):
    help = 'Updates medicine categories to more appropriate ones'

    def handle(self, *args, **options):
        # Define category mappings
        category_mappings = {
            'Analgesics': ['Paracetamol', 'Ibuprofen'],
            'Antibiotics': ['Amoxicillin'],
            'Antidepressants': ['Fluoxetine', 'Sertraline'],
            'Antihypertensive': ['Lisinopril', 'Losartan', 'Metoprolol', 'Hydrochlorothiazide'],
            'Respiratory': ['Albuterol', 'Salbutamol'],
            'Cardiovascular': ['Simvastatin', 'Atorvastatin'],
            'Gastrointestinal': ['Omeprazole'],
            'Hormones': ['Levothyroxine'],
            'Antidiabetic': ['Metformin'],
            'Neurological': ['Gabapentin'],
        }
        
        # Track changes
        changes = []
        
        # Update categories
        for category, medicine_names in category_mappings.items():
            for name in medicine_names:
                medicines = Medicine.objects.filter(name__icontains=name)
                for medicine in medicines:
                    if medicine.category != category:
                        old_category = medicine.category
                        medicine.category = category
                        medicine.save()
                        changes.append(f"Updated {medicine.name} from '{old_category}' to '{category}'")
        
        # Print summary
        if changes:
            self.stdout.write(self.style.SUCCESS(f"Updated {len(changes)} medicine categories:"))
            for change in changes:
                self.stdout.write(f"  - {change}")
        else:
            self.stdout.write(self.style.SUCCESS("No category updates needed."))
