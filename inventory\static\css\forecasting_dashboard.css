:root {
    --primary-color: #2c3e50; /* Changed to match base.css */
    --secondary-color: #3498db; /* Changed to match base.css */
    --accent-color: #e74c3c; /* Changed to match base.css */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --text-dark: #343a40;
    --text-muted: #6c757d;
    --border-radius: 0.375rem; /* Added from base.css */
    --light-bg: #f8f9fa; /* Added from base.css */
    --text-color: #333; /* Added from base.css */
}

/* Dashboard Layout */
.dashboard-header {
    background-color: white;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

/* Cards */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
    transition: transform 0.2s, box-shadow 0.2s;
}

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }

/* Status Badges */
.status-badge {
    padding: 0.4rem 0.8rem;
    border-radius: var(--border-radius);
    font-weight: 500;
}

.status-critical {
    background-color: var(--accent-color);
    color: white;
}

.status-warning {
    background-color: var(--warning-color);
    color: var(--text-dark);
}

/* Modal Styles */
.modal-content {
    border-radius: var(--border-radius);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    background-color: var(--primary-color);
    color: white;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
}

/* Navigation Pills */
.time-period-nav {
    background-color: white;
    padding: 0.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

    .time-period-nav .nav-link {
        color: var(--text-muted);
        padding: 0.75rem 1.25rem;
        border-radius: var(--border-radius);
        transition: all 0.3s ease;
    }

        .time-period-nav .nav-link.active {
            background-color: var(--secondary-color);
            color: white;
        }

/* Table Styles */
.table thead th {
    background-color: var(--primary-color);
    color: white;
    font-weight: 600;
    border-bottom: none;
    padding: 1rem 0.75rem;
}

.table td {
    vertical-align: middle;
    padding: 1rem;
}

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    margin-bottom: 1.5rem;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1rem;
    }

    .time-period-nav {
        overflow-x: auto;
        white-space: nowrap;
    }

    .card-body {
        padding: 1rem;
    }
}
