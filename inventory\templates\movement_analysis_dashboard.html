{% extends 'base.html' %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-primary fw-bold">
                    <i class="fas fa-chart-line me-2"></i>Movement Analysis Dashboard
                </h1>
                <div>
                    <a href="{% url 'stock_movement_list' %}" class="btn btn-info rounded-pill">
                        <i class="fas fa-list me-1"></i>Stock Movements
                    </a>
                    <form method="post" action="{% url 'update_movement_analyses' %}" class="d-inline">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-primary rounded-pill" onclick="return confirm('Update all movement analyses? This may take a moment.')">
                            <i class="fas fa-sync me-1"></i>Update Analyses
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card shadow mb-4 border-left-primary">
        <div class="card-header py-3 bg-gradient-light d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">
                <i class="fas fa-filter me-2"></i>Filters
            </h6>
            {% if search or movement_class != 'all' or category_filter or brand_filter or supplier_filter %}
            <a href="{% url 'movement_analysis_dashboard' %}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-times me-1"></i>Clear Filters
            </a>
            {% endif %}
        </div>
        <div class="card-body bg-light">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="search" class="form-label fw-medium">Search</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" value="{{ search }}" placeholder="Medicine, brand, supplier...">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#searchModal">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2">
                    <label for="movement_class" class="form-label fw-medium">Movement Class</label>
                    <select class="form-select" id="movement_class" name="movement_class">
                        <option value="all" {% if movement_class == 'all' %}selected{% endif %}>All Classes</option>
                        {% for class_code, class_name in movement_classes %}
                        <option value="{{ class_code }}" {% if movement_class == class_code %}selected{% endif %}>
                            {% if class_code == 'A' %}🚀 Class A
                            {% elif class_code == 'B' %}🚗 Class B
                            {% elif class_code == 'C' %}🚶 Class C
                            {% elif class_code == 'D' %}⛔ Class D
                            {% else %}{{ class_name }}
                            {% endif %}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="category" class="form-label fw-medium">Category</label>
                    <select class="form-select" id="category" name="category">
                        <option value="">All Categories</option>
                        {% for category_name in categories %}
                        <option value="{{ category_name }}" {% if category_filter == category_name %}selected{% endif %}>{{ category_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="brand" class="form-label fw-medium">Brand</label>
                    <select class="form-select" id="brand" name="brand">
                        <option value="">All Brands</option>
                        {% for brand_name in brands %}
                        <option value="{{ brand_name }}" {% if brand_filter == brand_name %}selected{% endif %}>{{ brand_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="supplier" class="form-label fw-medium">Supplier</label>
                    <select class="form-select" id="supplier" name="supplier">
                        <option value="">All Suppliers</option>
                        {% for supplier_name in suppliers %}
                        <option value="{{ supplier_name }}" {% if supplier_filter == supplier_name %}selected{% endif %}>{{ supplier_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-1 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100 rounded-pill">
                        <i class="fas fa-filter me-1"></i>Apply
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Movement Class Distribution -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow h-100">
                <div class="card-header py-3 bg-gradient-light">
                    <h6 class="m-0 font-weight-bold text-primary">Movement Class Distribution</h6>
                </div>
                <div class="card-body">
                    <div class="chart-bar" style="height: 300px;">
                        <canvas id="movementClassChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow h-100">
                <div class="card-header py-3 bg-gradient-light">
                    <h6 class="m-0 font-weight-bold text-primary">Movement Class Legend</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-lg-12">
                            <div class="mb-3">
                                <div class="card bg-success text-white shadow border-0">
                                    <div class="card-body py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="fas fa-rocket fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold">Class A - Fast-moving</h6>
                                                <div class="text-white-50 small">Top 20% of medicines by movement rate</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="card bg-info text-white shadow border-0">
                                    <div class="card-body py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="fas fa-car fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold">Class B - Medium-moving</h6>
                                                <div class="text-white-50 small">Middle 30% of medicines by movement rate</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="card bg-warning text-white shadow border-0">
                                    <div class="card-body py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="fas fa-walking fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold">Class C - Slow-moving</h6>
                                                <div class="text-white-50 small">Bottom 50% of medicines by movement rate</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-0">
                                <div class="card bg-danger text-white shadow border-0">
                                    <div class="card-body py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <i class="fas fa-stop-circle fa-2x"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-bold">Class D - No movement</h6>
                                                <div class="text-white-50 small">No transactions in the last 90 days</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Movers and No Movement -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow h-100">
                <div class="card-header py-3 bg-gradient-light d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Top Moving Medicines</h6>
                    <span class="badge bg-success">Class A</span>
                </div>
                <div class="card-body d-flex flex-column">
                    <div class="table-responsive flex-grow-1">
                        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead class="table-light">
                                <tr>
                                    <th>Medicine</th>
                                    <th>Monthly Rate</th>
                                    <th>Brand</th>
                                    <th>Supplier</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for analysis in top_movers %}
                                <tr>
                                    <td>
                                        <a href="{% url 'medicine_detail' analysis.medicine.id %}" class="text-decoration-none fw-medium">{{ analysis.medicine.name }}</a>
                                    </td>
                                    <td class="text-end">
                                        <span class="text-success fw-bold">{{ analysis.monthly_movement_rate|floatformat:1 }} units</span>
                                    </td>
                                    <td>
                                        {% if analysis.medicine.brand %}
                                        <span class="badge bg-info">{{ analysis.medicine.brand }}</span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if analysis.medicine.supplier %}
                                        <span class="badge bg-secondary">{{ analysis.medicine.supplier }}</span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <a href="{% url 'medicine_movement_analysis' analysis.medicine.id %}" class="btn btn-sm btn-primary rounded-pill">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="5" class="text-center py-5">
                                        <i class="fas fa-box-open text-muted fa-3x mb-3"></i>
                                        <p class="text-muted mb-0">No data available</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow h-100">
                <div class="card-header py-3 bg-gradient-light d-flex justify-content-between align-items-center">
                    <h6 class="m-0 font-weight-bold text-primary">Medicines with No Movement</h6>
                    <span class="badge bg-danger">{{ total_no_movement }} items</span>
                </div>
                <div class="card-body d-flex flex-column">
                    <div class="table-responsive flex-grow-1">
                        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead class="table-light">
                                <tr>
                                    <th>Medicine</th>
                                    <th>Days Since Last Movement</th>
                                    <th>Current Quantity</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for analysis in no_movement %}
                                <tr>
                                    <td>
                                        <a href="{% url 'medicine_detail' analysis.medicine.id %}" class="text-decoration-none fw-medium">{{ analysis.medicine.name }}</a>
                                    </td>
                                    <td class="text-end">
                                        {% if analysis.days_since_last_movement %}
                                        <span class="text-danger fw-bold">{{ analysis.days_since_last_movement }} days</span>
                                        {% else %}
                                        <span class="text-danger fw-bold">No movement</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-end">{{ analysis.medicine.quantity }}</td>
                                    <td class="text-center">
                                        <a href="{% url 'medicine_movement_analysis' analysis.medicine.id %}" class="btn btn-sm btn-primary rounded-pill">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="4" class="text-center py-5">
                                        <i class="fas fa-check-circle text-success fa-3x mb-3"></i>
                                        <p class="text-muted mb-0">No medicines with stagnant inventory</p>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if total_no_movement > 10 %}
                    <div class="text-center mt-3">
                        <p class="text-muted">Showing 10 of {{ total_no_movement }} medicines with no movement</p>
                        <a href="?movement_class=D" class="btn btn-danger rounded-pill">
                            <i class="fas fa-list me-1"></i>View All No Movement Items
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Category, Brand, and Supplier Analysis -->
    <div class="row mb-4">
        <div class="col-lg-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 bg-gradient-light">
                    <h6 class="m-0 font-weight-bold text-primary">Categories with Highest Movement</h6>
                </div>
                <div class="card-body d-flex flex-column">
                    {% if category_movement %}
                    <div class="table-responsive flex-grow-1">
                        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead class="table-light">
                                <tr>
                                    <th>Category</th>
                                    <th>Average Monthly Rate</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in category_movement %}
                                <tr>
                                    <td>{{ category.medicine__category }}</td>
                                    <td class="text-end">{{ category.avg_rate|floatformat:1 }} units</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5 my-4 flex-grow-1 d-flex align-items-center justify-content-center">
                        <div>
                            <i class="fas fa-chart-bar text-muted fa-3x mb-3"></i>
                            <p class="text-muted mb-0">No category data available</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 bg-gradient-light">
                    <h6 class="m-0 font-weight-bold text-primary">Brands with Highest Movement</h6>
                </div>
                <div class="card-body d-flex flex-column">
                    {% if brand_movement %}
                    <div class="table-responsive flex-grow-1">
                        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead class="table-light">
                                <tr>
                                    <th>Brand</th>
                                    <th>Medicine Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for brand in brand_movement %}
                                <tr>
                                    <td>
                                        <span class="badge bg-info">{{ brand.top_brand }}</span>
                                    </td>
                                    <td class="text-end">{{ brand.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5 my-4 flex-grow-1 d-flex align-items-center justify-content-center">
                        <div>
                            <i class="fas fa-tags text-muted fa-3x mb-3"></i>
                            <p class="text-muted mb-0">No brand data available</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow h-100">
                <div class="card-header py-3 bg-gradient-light">
                    <h6 class="m-0 font-weight-bold text-primary">Suppliers with Highest Movement</h6>
                </div>
                <div class="card-body d-flex flex-column">
                    {% if supplier_movement %}
                    <div class="table-responsive flex-grow-1">
                        <table class="table table-bordered table-hover" width="100%" cellspacing="0">
                            <thead class="table-light">
                                <tr>
                                    <th>Supplier</th>
                                    <th>Medicine Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for supplier in supplier_movement %}
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">{{ supplier.top_supplier }}</span>
                                    </td>
                                    <td class="text-end">{{ supplier.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5 my-4 flex-grow-1 d-flex align-items-center justify-content-center">
                        <div>
                            <i class="fas fa-truck text-muted fa-3x mb-3"></i>
                            <p class="text-muted mb-0">No supplier data available</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- All Medicines Movement Analysis -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 bg-gradient-light d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">All Medicines Movement Analysis</h6>
            <span class="badge bg-primary">{{ analyses|length }} medicines</span>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered table-hover" id="dataTable" width="100%" cellspacing="0">
                    <thead class="table-light">
                        <tr>
                            <th>Medicine</th>
                            <th>Class</th>
                            <th>Monthly Rate</th>
                            <th>Trend</th>
                            <th>Last Movement</th>
                            <th>Brand</th>
                            <th>Supplier</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for analysis in analyses %}
                        <tr>
                            <td>
                                <a href="{% url 'medicine_detail' analysis.medicine.id %}" class="text-decoration-none fw-medium">{{ analysis.medicine.name }}</a>
                            </td>
                            <td class="text-center">
                                {% if analysis.movement_class == 'A' %}
                                <span class="badge bg-success rounded-pill px-2">A</span>
                                {% elif analysis.movement_class == 'B' %}
                                <span class="badge bg-info rounded-pill px-2">B</span>
                                {% elif analysis.movement_class == 'C' %}
                                <span class="badge bg-warning rounded-pill px-2">C</span>
                                {% elif analysis.movement_class == 'D' %}
                                <span class="badge bg-danger rounded-pill px-2">D</span>
                                {% endif %}
                            </td>
                            <td class="text-end">
                                {% if analysis.monthly_movement_rate > 0 %}
                                <span class="fw-medium">{{ analysis.monthly_movement_rate|floatformat:1 }} units</span>
                                {% else %}
                                <span class="text-muted">0 units</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if analysis.movement_trend == 'Increasing' %}
                                <span class="text-success fw-medium"><i class="fas fa-arrow-up me-1"></i>{{ analysis.trend_percentage|floatformat:1 }}%</span>
                                {% elif analysis.movement_trend == 'Decreasing' %}
                                <span class="text-danger fw-medium"><i class="fas fa-arrow-down me-1"></i>{{ analysis.trend_percentage|floatformat:1 }}%</span>
                                {% elif analysis.movement_trend == 'Stable' %}
                                <span class="text-info fw-medium"><i class="fas fa-equals me-1"></i>Stable</span>
                                {% elif analysis.movement_trend == 'Volatile' %}
                                <span class="text-warning fw-medium"><i class="fas fa-random me-1"></i>Volatile</span>
                                {% elif analysis.movement_trend == 'New' %}
                                <span class="text-primary fw-medium"><i class="fas fa-star me-1"></i>New</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                {% if analysis.last_movement_date %}
                                {{ analysis.last_movement_date|date:"M d, Y" }}
                                {% else %}
                                <span class="text-danger">Never</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if analysis.top_brand %}
                                <span class="badge bg-info">{{ analysis.top_brand }}</span>
                                {% elif analysis.medicine.brand %}
                                <span class="badge bg-info">{{ analysis.medicine.brand }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if analysis.top_supplier %}
                                <span class="badge bg-secondary">{{ analysis.top_supplier }}</span>
                                {% elif analysis.medicine.supplier %}
                                <span class="badge bg-secondary">{{ analysis.medicine.supplier }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{% url 'medicine_movement_analysis' analysis.medicine.id %}" class="btn btn-sm btn-primary rounded-pill">
                                    <i class="fas fa-chart-line"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center py-5">
                                <i class="fas fa-chart-line text-muted fa-3x mb-3"></i>
                                <p class="text-muted mb-0">No movement analysis data available</p>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="searchModalLabel">Advanced Search</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="advancedSearchForm" method="get" action="{% url 'movement_analysis_dashboard' %}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="modal-medicine-name" class="form-label">Medicine Name</label>
                            <select class="form-select" id="modal-medicine-name" name="medicine_name">
                                <option value="">All Medicines</option>
                                {% for medicine in all_medicines %}
                                <option value="{{ medicine.name }}">{{ medicine.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="modal-category" class="form-label">Category</label>
                            <select class="form-select" id="modal-category" name="category">
                                <option value="">All Categories</option>
                                {% for category_name in categories %}
                                <option value="{{ category_name }}">{{ category_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="modal-brand" class="form-label">Brand</label>
                            <select class="form-select" id="modal-brand" name="brand">
                                <option value="">All Brands</option>
                                {% for brand_name in brands %}
                                <option value="{{ brand_name }}">{{ brand_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="modal-supplier" class="form-label">Supplier</label>
                            <select class="form-select" id="modal-supplier" name="supplier">
                                <option value="">All Suppliers</option>
                                {% for supplier_name in suppliers %}
                                <option value="{{ supplier_name }}">{{ supplier_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="modal-movement-class" class="form-label">Movement Class</label>
                            <select class="form-select" id="modal-movement-class" name="movement_class">
                                <option value="all">All Classes</option>
                                {% for class_code, class_name in movement_classes %}
                                <option value="{{ class_code }}">{{ class_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="applyAdvancedSearch">Apply Search</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Movement Class Chart
        var ctx = document.getElementById('movementClassChart').getContext('2d');
        var classData = {{ class_data|safe }};

        var backgroundColors = [
            'rgba(40, 167, 69, 0.7)',  // Success - Class A
            'rgba(23, 162, 184, 0.7)',  // Info - Class B
            'rgba(255, 193, 7, 0.7)',  // Warning - Class C
            'rgba(220, 53, 69, 0.7)'   // Danger - Class D
        ];

        var borderColors = [
            'rgba(40, 167, 69, 1)',  // Success - Class A
            'rgba(23, 162, 184, 1)',  // Info - Class B
            'rgba(255, 193, 7, 1)',  // Warning - Class C
            'rgba(220, 53, 69, 1)'   // Danger - Class D
        ];

        new Chart(ctx, {
            type: 'bar',
            data: {
                labels: classData.labels,
                datasets: [{
                    label: 'Number of Medicines',
                    data: classData.data,
                    backgroundColor: backgroundColors,
                    borderColor: borderColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
        // Function to get medicine details
        function getMedicineDetails(medicineId) {
            // Find the medicine in the list
            const medicineSelect = document.getElementById('modal-medicine-name');
            const selectedMedicine = medicineSelect.options[medicineSelect.selectedIndex].text;

            // Find the medicine's details from the data attributes
            {% for medicine in all_medicines %}
            if ("{{ medicine.name }}" === selectedMedicine) {
                // Set the brand, supplier, and category
                const brandSelect = document.getElementById('modal-brand');
                const supplierSelect = document.getElementById('modal-supplier');
                const categorySelect = document.getElementById('modal-category');

                // Set brand if available
                if ("{{ medicine.brand }}") {
                    for (let i = 0; i < brandSelect.options.length; i++) {
                        if (brandSelect.options[i].value === "{{ medicine.brand }}") {
                            brandSelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                // Set supplier if available
                if ("{{ medicine.supplier }}") {
                    for (let i = 0; i < supplierSelect.options.length; i++) {
                        if (supplierSelect.options[i].value === "{{ medicine.supplier }}") {
                            supplierSelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                // Set category if available
                if ("{{ medicine.category }}") {
                    for (let i = 0; i < categorySelect.options.length; i++) {
                        if (categorySelect.options[i].value === "{{ medicine.category }}") {
                            categorySelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                return;
            }
            {% endfor %}
        }

        // Add event listener to medicine select
        document.getElementById('modal-medicine-name').addEventListener('change', function() {
            getMedicineDetails(this.value);
        });

        // Advanced Search Modal
        document.getElementById('applyAdvancedSearch').addEventListener('click', function() {
            const form = document.getElementById('advancedSearchForm');
            const formData = new FormData(form);

            // Build search query
            let searchQuery = '';

            const medicineName = formData.get('medicine_name');
            if (medicineName) {
                searchQuery += medicineName + ' ';
            }

            const category = formData.get('category');
            if (category) {
                searchQuery += category + ' ';
            }

            const brand = formData.get('brand');
            if (brand) {
                searchQuery += brand + ' ';
            }

            const supplier = formData.get('supplier');
            if (supplier) {
                searchQuery += supplier + ' ';
            }

            // Set the search query to the main search input
            document.getElementById('search').value = searchQuery.trim();

            // Set the movement class if selected
            const movementClass = formData.get('movement_class');
            if (movementClass && movementClass !== 'all') {
                document.getElementById('movement_class').value = movementClass;
            }

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('searchModal'));
            modal.hide();

            // Submit the main form
            document.querySelector('form.row.g-3').submit();
        });
    });
</script>
{% endblock %}
