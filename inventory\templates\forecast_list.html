﻿{% extends 'base.html' %}
{% load static %}
{% load base_filters %}

{% block title %}Medicine Inventory Forecast{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/forecast_list.css' %}">
<style>
    .code-block {
        font-family: 'Courier New', monospace;
        font-size: 0.85rem;
        line-height: 1.5;
        overflow-x: auto;
    }

    .code-block pre {
        margin-bottom: 0;
    }

    .code-block code {
        color: #333;
    }

    /* Custom colors for forecast methods */
    .bg-purple {
        background-color: #6f42c1 !important;
    }

    .text-purple {
        color: #6f42c1 !important;
    }

    /* Professional colors for Moving Average and Ensemble - no transparency */
    .bg-navy {
        background-color: #0a4275 !important;
    }

    .text-navy {
        color: white !important;
    }

    .bg-slate {
        background-color: #475569 !important;
    }

    .text-slate {
        color: white !important;
    }

    /* Ensure gradient boosting uses warning color instead of silver */
    .badge[class*="gradient_boosting"] {
        background-color: rgba(255, 193, 7, 0.1) !important;
        color: #fd7e14 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-3">
    {% if messages %}
    <div class="row animate__animated animate__fadeIn">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show shadow-sm border-0" role="alert">
                <i class="fas fa-info-circle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Search Bar -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body p-3">
                <form method="get" action="" class="search-form">
                    <div class="input-group">
                        <span class="input-group-text bg-white border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text"
                               name="search"
                               value="{{ search_query }}"
                               class="form-control border-start-0"
                               placeholder="Search medicines..."
                               id="searchInput">
                        <button type="submit" class="btn btn-primary px-4">Search</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>


<!-- Filter Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <form method="get" action="{% url 'forecast_list' %}" class="row g-3 align-items-end">
                    <div class="col-md-4">
                        <label for="search" class="form-label">Search</label>
                        <div class="input-group">
                            <span class="input-group-text"><i class="fas fa-search"></i></span>
                            <input type="text" class="form-control" id="search" name="search" value="{{ search_query }}" placeholder="Medicine name or category">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="method" class="form-label">Forecast Method</label>
                        <select class="form-select" id="method" name="method">
                            <option value="all" {% if method_filter == 'all' or not method_filter %}selected{% endif %}>All Methods</option>
                            <option value="arima" {% if method_filter == 'arima' %}selected{% endif %}>ARIMA</option>
                            <option value="linear" {% if method_filter == 'linear' %}selected{% endif %}>Linear Regression</option>
                            <option value="polynomial" {% if method_filter == 'polynomial' %}selected{% endif %}>Polynomial</option>
                            <option value="moving_avg" {% if method_filter == 'moving_avg' %}selected{% endif %}>Moving Average</option>
                            <option value="holtwinters" {% if method_filter == 'holtwinters' %}selected{% endif %}>Holt-Winters</option>
                            <option value="ensemble" {% if method_filter == 'ensemble' %}selected{% endif %}>Ensemble</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="accuracy" class="form-label">Min. Accuracy</label>
                        <select class="form-select" id="accuracy" name="accuracy">
                            <option value="" {% if not accuracy_filter %}selected{% endif %}>Any Accuracy</option>
                            <option value="90" {% if accuracy_filter == '90' %}selected{% endif %}>90% or higher</option>
                            <option value="80" {% if accuracy_filter == '80' %}selected{% endif %}>80% or higher</option>
                            <option value="70" {% if accuracy_filter == '70' %}selected{% endif %}>70% or higher</option>
                            <option value="60" {% if accuracy_filter == '60' %}selected{% endif %}>60% or higher</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-2"></i>Apply Filters
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Forecast Summary Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="rounded-circle bg-primary bg-opacity-10 p-3 me-3">
                        <i class="fas fa-chart-line text-primary"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-0">Average Accuracy</h6>
                        <h4 class="mb-0">{{ avg_accuracy|default:"85"|floatformat:1 }}%</h4>
                    </div>
                </div>
                <div class="progress" style="height: 6px;">
                    <div class="progress-bar bg-primary" role="progressbar" style="width: {{ avg_accuracy|default:85 }}%"
                         aria-valuenow="{{ avg_accuracy|default:85 }}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="rounded-circle bg-warning bg-opacity-10 p-3 me-3">
                        <i class="fas fa-shopping-cart text-warning"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-0">Need Ordering</h6>
                        <h4 class="mb-0">{{ needs_ordering|default:"5" }}</h4>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <span class="badge bg-warning bg-opacity-10 text-warning me-2">
                        <i class="fas fa-exclamation-triangle me-1"></i> Attention needed
                    </span>
                    <span class="small text-muted">{{ needs_ordering_percent|default:"15" }}% of inventory</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="rounded-circle bg-success bg-opacity-10 p-3 me-3">
                        <i class="fas fa-check-circle text-success"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-0">Sufficient Stock</h6>
                        <h4 class="mb-0">{{ sufficient_stock|default:"28" }}</h4>
                    </div>
                </div>
                <div class="d-flex align-items-center">
                    <span class="badge bg-success bg-opacity-10 text-success me-2">
                        <i class="fas fa-thumbs-up me-1"></i> Good status
                    </span>
                    <span class="small text-muted">{{ sufficient_stock_percent|default:"85" }}% of inventory</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex align-items-center mb-3">
                    <div class="rounded-circle bg-info bg-opacity-10 p-3 me-3">
                        <i class="fas fa-calendar-alt text-info"></i>
                    </div>
                    <div>
                        <h6 class="text-muted mb-0">Last Updated</h6>
                        <h4 class="mb-0">{{ last_updated|default:"Today" }}</h4>
                    </div>
                </div>
                <div class="d-flex flex-column">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-info bg-opacity-10 text-info px-3 py-2">
                            <i class="fas fa-clock me-1"></i> Auto-updates daily at 7:00 PM
                        </span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="small">
                            <span class="text-muted">Next forecast in: </span>
                            <span class="next-forecast-time fw-bold text-primary">--h --m</span>
                            <div class="mt-1 small text-success">
                                <i class="fas fa-robot me-1"></i> Using advanced ML algorithms
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Hidden: Forecast Methods Distribution and Comparison -->

<!-- Main Content Card with Quick Filters -->
<div class="card border-0 shadow-sm rounded-lg mb-4 overflow-hidden">
    <div class="card-header bg-white py-3">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h5 class="mb-0 text-primary">
                    <i class="fas fa-chart-line me-2"></i>Inventory Forecast List
                </h5>
            </div>
            <div class="col-md-6">
                <!-- Forecast simulation button and method filter hidden as requested -->
            </div>
        </div>
    </div>

    <!-- Quick Filter Pills -->
    <div class="mt-3 filter-pills">
        <div class="d-flex flex-wrap gap-2">
            <button class="btn btn-sm btn-outline-secondary rounded-pill active" data-filter="all">
                All Medicines
            </button>
            <button class="btn btn-sm btn-outline-warning rounded-pill" data-filter="needs-order">
                <i class="fas fa-shopping-cart me-1"></i> Needs Order
            </button>
            <button class="btn btn-sm btn-outline-success rounded-pill" data-filter="sufficient">
                <i class="fas fa-check-circle me-1"></i> Sufficient Stock
            </button>
            <button class="btn btn-sm btn-outline-danger rounded-pill" data-filter="low-accuracy">
                <i class="fas fa-exclamation-triangle me-1"></i> Low Accuracy
            </button>
        </div>
    </div>

    <div class="card-body p-0">
        <div class="table-responsive">
            <table id="forecasts-table" class="table table-hover align-middle mb-0">
                <thead class="bg-light">
                    <tr>
                        <th class="border-0 ps-4">Medicine</th>
                        <th class="border-0 text-center">Current Stock</th>
                        <th class="border-0 text-center">Forecast</th>
                        <th class="border-0 text-center">Accuracy</th>
                        <th class="border-0 text-center">Method</th>
                        <th class="border-0 text-center pe-4">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for forecast in page_obj %}
                    <tr class="align-middle border-top forecast-row {% if forecast.recommended_order_quantity > 0 %}needs-order{% else %}sufficient{% endif %} {% if forecast.accuracy < 60 %}low-accuracy{% endif %}" data-forecast-id="{{ forecast.pk }}">
                        <!-- Medicine column -->
                        <td class="ps-4">
                            {% if forecast.medicine %}
                            <div class="d-flex align-items-center">
                                <div class="medicine-icon rounded-circle bg-primary bg-opacity-10 text-primary p-2 me-3">
                                    <i class="fas fa-capsules"></i>
                                </div>
                                <div>
                                    <a href="{% url 'medicine_detail' forecast.medicine.id %}"
                                       class="fw-bold text-dark text-decoration-none d-block hover-text-primary"
                                       data-bs-toggle="tooltip"
                                       title="View detailed information for {{ forecast.medicine.name }} | Current Stock: {{ forecast.medicine.quantity }} | Forecast: {{ forecast.predicted_quantity|floatformat:1 }} | Accuracy: {{ forecast.accuracy|floatformat:1 }}%">
                                        {{ forecast.medicine.name }}
                                    </a>
                                    <div class="d-flex align-items-center mt-1">
                                        <span class="badge bg-secondary bg-opacity-10 text-secondary me-2"
                                              data-bs-toggle="tooltip"
                                              title="Category: {{ forecast.medicine.category }} | Medicines in this category have an average forecast accuracy of {{ forecast.accuracy|floatformat:1 }}%">
                                            <i class="fas fa-tag me-1 opacity-75"></i>{{ forecast.medicine.category }}
                                        </span>
                                        {% if forecast.recommended_order_quantity > 0 %}
                                        <span class="badge bg-warning bg-opacity-10 text-warning"
                                              data-bs-toggle="tooltip"
                                              title="Recommended order quantity based on forecast and current stock | Current Stock: {{ forecast.medicine.quantity }} | Reorder Level: {{ forecast.medicine.reorder_level }}">
                                            <i class="fas fa-shopping-cart me-1"></i>Order: {{ forecast.recommended_order_quantity|floatformat:0 }}
                                        </span>
                                        {% endif %}
                                    </div>
                                    <div class="d-flex align-items-center mt-1">
                                        {% if forecast.medicine.brand %}
                                        <span class="badge bg-info bg-opacity-10 text-info me-2"
                                              data-bs-toggle="tooltip"
                                              title="Brand: {{ forecast.medicine.brand }} | Medicines from this brand have an average forecast accuracy of {{ forecast.accuracy|floatformat:1 }}%">
                                            <i class="fas fa-tag me-1 opacity-75"></i>{{ forecast.medicine.brand }}
                                        </span>
                                        {% endif %}
                                        {% if forecast.medicine.supplier %}
                                        <span class="badge bg-primary bg-opacity-10 text-primary me-2"
                                              data-bs-toggle="tooltip"
                                              title="Supplier: {{ forecast.medicine.supplier }} | Lead time: {{ forecast.medicine.lead_time|default:'7' }} days | Last order: {{ forecast.medicine.last_order_date|default:'N/A' }}">
                                            <i class="fas fa-building me-1 opacity-75"></i>{{ forecast.medicine.supplier }}
                                        </span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </td>

                        <!-- Current Stock column with visual indicator -->
                        <td class="text-center">
                            <div class="d-flex flex-column align-items-center">
                                <div class="stock-indicator position-relative mb-1">
                                    {% with stock_percent=forecast.medicine.quantity|percentage:forecast.medicine.reorder_level %}
                                    <div class="progress" style="height: 8px; width: 60px;">
                                        <div class="progress-bar {% if stock_percent < 50 %}bg-danger{% elif stock_percent < 80 %}bg-warning{% else %}bg-success{% endif %}"
                                             role="progressbar"
                                             style="width: {{ stock_percent }}%;"
                                             aria-valuenow="{{ stock_percent }}"
                                             aria-valuemin="0"
                                             aria-valuemax="100"></div>
                                    </div>
                                    {% endwith %}
                                </div>
                                <div class="d-flex align-items-center">
                                    <span class="fw-bold">{{ forecast.medicine.quantity }}</span>
                                    <span class="text-muted small ms-1">/{{ forecast.medicine.reorder_level }}</span>
                                </div>
                            </div>
                        </td>

                        <!-- Forecast column with prediction and confidence interval -->
                        <td class="text-center">
                            <div class="d-flex flex-column align-items-center">
                                <span class="fw-bold"
                                      data-bs-toggle="tooltip"
                                      title="Short-term forecast (30 days) | This is the immediate forecast used for inventory management | For weekly, monthly, and yearly forecasts, click 'Forecast' button | Method: {{ forecast.forecast_method|default:'auto'|title }} | Last updated: {{ forecast.forecast_date|date:'Y-m-d' }}">
                                    {{ forecast.predicted_quantity|floatformat:1 }}
                                </span>
                                {% if forecast.confidence_lower and forecast.confidence_upper %}
                                <span class="text-muted small"
                                      data-bs-toggle="tooltip"
                                      title="95% Confidence Interval for 30-day forecast | This range represents the short-term forecast uncertainty | For detailed weekly, monthly, and yearly forecasts with their own confidence intervals, click the 'Forecast' button | Lower bound: {{ forecast.confidence_lower|floatformat:1 }} | Upper bound: {{ forecast.confidence_upper|floatformat:1 }}">
                                    [{{ forecast.confidence_lower|floatformat:1 }} - {{ forecast.confidence_upper|floatformat:1 }}]
                                </span>
                                {% endif %}
                            </div>
                        </td>

                        <!-- Accuracy column with enhanced visualization -->
                        <td class="text-center">
                            <div class="accuracy-wrapper">
                                {% with accuracy=forecast.accuracy %}
                                <div class="accuracy-chart position-relative" data-percentage="{{ accuracy }}"
                                     data-bs-toggle="tooltip"
                                     title="Forecast accuracy: {{ accuracy|floatformat:1 }}% | Method: {{ forecast.forecast_method|default:'auto'|title }}
                                     | MAPE: {{ 100|subtract:accuracy|floatformat:1 }}%
                                     {% if forecast.rmse %} | RMSE: {{ forecast.rmse|floatformat:2 }}{% endif %}
                                     | Calculation: 100% - Mean Absolute Percentage Error
                                     | Historical accuracy: {{ accuracy|floatformat:1 }}% over the past 90 days
                                     | Click for detailed accuracy metrics">
                                    <svg width="50" height="50" viewBox="0 0 50 50">
                                        <circle class="accuracy-ring" cx="25" cy="25" r="20" fill="transparent" stroke="#e9ecef" stroke-width="5"></circle>
                                        <circle class="accuracy-value {% if accuracy >= 80 %}text-success{% elif accuracy >= 60 %}text-warning{% else %}text-danger{% endif %}"
                                                cx="25" cy="25" r="20" fill="transparent"
                                                stroke="{% if accuracy >= 80 %}#28a745{% elif accuracy >= 60 %}#ffc107{% else %}#dc3545{% endif %}"
                                                stroke-width="5"
                                                stroke-dasharray="{{ accuracy }} {{ 100|subtract:accuracy }}"
                                                stroke-dashoffset="25"></circle>
                                    </svg>
                                    <div class="accuracy-text position-absolute top-50 start-50 translate-middle small fw-bold">
                                        {{ accuracy|floatformat:0 }}%
                                    </div>
                                </div>
                                {% endwith %}
                            </div>
                        </td>

                        <!-- Forecast Method column -->
                        <td class="text-center">
                            <span class="badge
                                {% if forecast.forecast_method == 'arima' %}bg-primary bg-opacity-10 text-primary
                                {% elif forecast.forecast_method == 'linear' %}bg-info bg-opacity-10 text-info
                                {% elif forecast.forecast_method == 'polynomial' %}bg-success bg-opacity-10 text-success
                                {% elif forecast.forecast_method == 'gradient_boosting' %}bg-warning bg-opacity-10 text-warning
                                {% elif forecast.forecast_method == 'ensemble' %}bg-slate text-white
                                {% elif forecast.forecast_method == 'holtwinters' %}bg-purple bg-opacity-10 text-purple
                                {% elif forecast.forecast_method == 'moving_avg' %}bg-navy text-white
                                {% else %}bg-dark bg-opacity-10 text-dark
                                {% endif %} px-2 py-1"
                                data-bs-toggle="tooltip"
                                title="{% if forecast.forecast_method == 'arima' %}Time series forecasting using Autoregressive Integrated Moving Average
                                {% elif forecast.forecast_method == 'linear' %}Predicts future values based on linear relationship between variables
                                {% elif forecast.forecast_method == 'polynomial' %}Uses polynomial equation to model non-linear relationships
                                {% elif forecast.forecast_method == 'gradient_boosting' %}Advanced machine learning technique using decision trees
                                {% elif forecast.forecast_method == 'ensemble' %}Combines multiple forecasting methods for improved accuracy
                                {% elif forecast.forecast_method == 'holtwinters' %}Triple exponential smoothing for data with trend and seasonality
                                {% elif forecast.forecast_method == 'moving_avg' %}Averages data points over a specific period to identify trends
                                {% else %}Automatic selection of best forecasting method
                                {% endif %} | Accuracy: {{ forecast.accuracy|floatformat:1 }}% | MAPE: {{ 100|subtract:forecast.accuracy|floatformat:1 }}%">
                                <i class="fas
                                {% if forecast.forecast_method == 'arima' %}fa-chart-line
                                {% elif forecast.forecast_method == 'linear' %}fa-arrow-trend-up
                                {% elif forecast.forecast_method == 'polynomial' %}fa-chart-area
                                {% elif forecast.forecast_method == 'gradient_boosting' %}fa-tree
                                {% elif forecast.forecast_method == 'ensemble' %}fa-layer-group
                                {% elif forecast.forecast_method == 'holtwinters' %}fa-wave-square
                                {% elif forecast.forecast_method == 'moving_avg' %}fa-chart-bar
                                {% else %}fa-calculator
                                {% endif %} me-1"></i>
                                {{ forecast.forecast_method|default:"auto"|title }}
                            </span>
                        </td>

                        <!-- Actions column -->
                        <td class="text-center pe-4">
                            <div class="btn-group" role="group">
                                {% if forecast.pk %}
                                <a href="{% url 'forecast_detail' forecast.pk %}" class="btn btn-sm btn-primary"
                                   data-bs-toggle="tooltip" title="View detailed forecast information">
                                    <i class="fas fa-chart-line me-1"></i> Details
                                </a>
                                {% endif %}
                                {% if forecast.medicine %}
                                <a href="{% url 'medicine_forecast_detail' medicine_id=forecast.medicine.id %}" class="btn btn-sm btn-outline-primary"
                                   data-bs-toggle="tooltip" title="View medicine-specific forecast trends">
                                    <i class="fas fa-chart-bar me-1"></i> Forecast
                                </a>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="text-center py-5">
                            <div class="empty-state">
                                <i class="fas fa-chart-area fa-3x text-muted mb-3"></i>
                                <h5>No Forecasts Available</h5>
                                <p class="text-muted mb-3">No forecast data is currently available for display.</p>
                                <div class="alert alert-info mb-0">
                                    <i class="fas fa-info-circle me-2"></i> Forecasts are automatically generated daily at 7:00 PM.
                                </div>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="card-footer bg-white border-0 py-3">
        <div class="row align-items-center">
            <div class="col-md-6">
                <p class="text-muted mb-md-0">
                    Showing <span id="visibleCount">{{ page_obj.end_index|add:1|sub:page_obj.start_index }}</span> of {{ page_obj.paginator.count }} forecasts
                    <span class="ms-2 badge bg-light text-dark">Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}</span>
                </p>
            </div>
            <div class="col-md-6">
                <nav aria-label="Forecast pagination">
                    <ul class="pagination justify-content-md-end mb-0">
                        {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page=1" aria-label="First">
                                <span aria-hidden="true">&laquo;&laquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                <span aria-hidden="true">&laquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link" aria-hidden="true">&laquo;&laquo;</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-hidden="true">&laquo;</span>
                        </li>
                        {% endif %}

                        {% for i in page_obj.paginator.page_range %}
                        {% if i == page_obj.number %}
                        <li class="page-item active">
                            <span class="page-link">{{ i }}</span>
                        </li>
                        {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ i }}">{{ i }}</a>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                <span aria-hidden="true">&raquo;</span>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                <span aria-hidden="true">&raquo;&raquo;</span>
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link" aria-hidden="true">&raquo;</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-hidden="true">&raquo;&raquo;</span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Forecast Detail Modal -->
<div class="modal fade" id="forecastDetailModal" tabindex="-1" aria-labelledby="forecastDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-light">
                <h5 class="modal-title" id="forecastDetailModalLabel">
                    <i class="fas fa-chart-line me-2"></i>Forecast Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-3 text-muted">Loading forecast details...</p>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- Success Modal -->
<div class="modal fade" id="forecastSuccessModal" tabindex="-1" aria-labelledby="forecastSuccessModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0">
            <div class="modal-header border-0" style="background: var(--primary-gradient)">
                <h5 class="modal-title text-white" id="forecastSuccessModalLabel">
                    <i class="fas fa-check-circle me-2"></i>Forecast Generation Complete
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <div class="text-center mb-4">
                    <div class="display-1 mb-4" style="color: var(--primary-color)">
                        <i class="fas fa-chart-line"></i>
                    </div>
                    <h4 class="mb-3">Forecasts Generated Successfully!</h4>
                    <p class="text-muted mb-0">The system has generated new forecasts for all medicines.</p>
                    <p class="mt-2">Page will refresh in <span id="countdown">5</span> seconds...</p>
                </div>
                <div class="forecast-stats mt-4">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="p-3 border rounded-3 text-center">
                                <div class="mb-2" style="color: var(--secondary-color)">
                                    <i class="fas fa-pills"></i>
                                </div>
                                <div class="small text-muted">Medicines Processed</div>
                                <div class="fw-bold" id="medicinesProcessed">0</div>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="p-3 border rounded-3 text-center">
                                <div class="mb-2" style="color: var(--accent-color)">
                                    <i class="fas fa-shopping-cart"></i>
                                </div>
                                <div class="small text-muted">Need Ordering</div>
                                <div class="fw-bold" id="needsOrdering">0</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer border-0">
                <button type="button" class="btn px-4" style="background: var(--primary-gradient); color: white" data-bs-dismiss="modal" id="doneButton">
                    <i class="fas fa-check me-2"></i>Done
                </button>
                <button type="button" class="btn btn-primary px-4" id="viewForecastsButton">
                    <i class="fas fa-chart-line me-2"></i>View Forecasts
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Initializing Modal -->
<div class="modal fade" id="forecastInitializingModal" tabindex="-1" aria-labelledby="forecastInitializingModalLabel" data-bs-backdrop="static" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content border-0">
            <div class="modal-header border-0" style="background: var(--primary-gradient)">
                <h5 class="modal-title text-white" id="forecastInitializingModalLabel">
                    <i class="fas fa-cog fa-spin me-2"></i>Initializing Forecasts
                </h5>
            </div>
            <div class="modal-body p-4">
                <div class="text-center">
                    <div class="progress mb-4" style="height: 10px;">
                        <div id="forecastProgress" class="progress-bar progress-bar-striped progress-bar-animated"
                             role="progressbar" style="width: 0%; background: var(--primary-gradient)"
                             aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <p class="mb-0" id="initializingStatus">Preparing forecast models...</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const generateForecastBtn = document.getElementById('generateForecast');
        const initializingModal = new bootstrap.Modal(document.getElementById('forecastInitializingModal'));
        const successModal = new bootstrap.Modal(document.getElementById('forecastSuccessModal'));
        const searchInput = document.getElementById('searchInput');
        let progressBar = document.getElementById('forecastProgress');
        let statusText = document.getElementById('initializingStatus');

        // Function to get CSRF cookie
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Function to show notifications
        function showNotification(title, message, type) {
            const alertHtml = `
                <div class="alert alert-${type} alert-dismissible fade show shadow-sm border-0" role="alert">
                    <i class="fas fa-info-circle me-2"></i> ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            `;
            const alertsContainer = document.querySelector('.container-fluid');
            const alertsRow = alertsContainer.querySelector('.row') || document.createElement('div');
            alertsRow.className = 'row animate__animated animate__fadeIn';
            alertsRow.innerHTML = alertHtml;

            if (!alertsContainer.querySelector('.row')) {
                alertsContainer.prepend(alertsRow);
            }
        }

        // Function to update URL with search parameters
        function updateURL(params) {
            const url = new URL(window.location);
            Object.entries(params).forEach(([key, value]) => {
                if (value) {
                    url.searchParams.set(key, value);
                } else {
                    url.searchParams.delete(key);
                }
            });
            window.location = url;
        }

        // Debounce function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // View Forecasts Button Handler
        const viewForecastsButton = document.getElementById('viewForecastsButton');
        if (viewForecastsButton) {
            viewForecastsButton.addEventListener('click', function() {
                window.location.reload();
            });
        }

        // Done Button Handler
        const doneButton = document.getElementById('doneButton');
        if (doneButton) {
            doneButton.addEventListener('click', function() {
                // Wait a short time to allow the modal to close
                setTimeout(() => {
                    window.location.reload();
                }, 300);
            });
        }

        // Add a countdown to next forecast generation
        function updateNextForecastTime() {
            const now = new Date();
            const nextForecast = new Date(now);

            // Set to 7:00 PM today
            nextForecast.setHours(19, 0, 0, 0);

            // If it's already past 7:00 PM, set to tomorrow
            if (now > nextForecast) {
                nextForecast.setDate(nextForecast.getDate() + 1);
            }

            // Calculate time difference
            const diff = nextForecast - now;
            const hours = Math.floor(diff / (1000 * 60 * 60));
            const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

            // Update any elements showing the next forecast time
            const timeElements = document.querySelectorAll('.next-forecast-time');
            timeElements.forEach(el => {
                el.textContent = `${hours}h ${minutes}m`;
            });
        }

        // Update the countdown every minute
        updateNextForecastTime();
        setInterval(updateNextForecastTime, 60000);

        // Enhanced filter functionality
        const filterButtons = document.querySelectorAll('.filter-pills button');
        const forecastRows = document.querySelectorAll('.forecast-row');
        const methodDropdownItems = document.querySelectorAll('.dropdown-item[data-method]');
        let activeFilter = 'all';
        let activeMethod = 'all';

        // Function to apply both filters
        function applyFilters() {
            forecastRows.forEach(row => {
                const matchesFilter = activeFilter === 'all' || row.classList.contains(activeFilter);
                const methodMatch = activeMethod === 'all' ||
                                   row.querySelector(`[class*="${activeMethod}"]`) !== null;

                row.style.display = (matchesFilter && methodMatch) ? '' : 'none';
            });

            // Update counts
            const visibleRows = document.querySelectorAll('.forecast-row[style=""], .forecast-row[style="display: "]').length;
            document.getElementById('visibleCount').textContent = visibleRows;
        }

        // Status filter buttons
        filterButtons.forEach(button => {
            button.addEventListener('click', function () {
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                activeFilter = this.getAttribute('data-filter');
                applyFilters();
            });
        });

        // Method dropdown filter
        methodDropdownItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                methodDropdownItems.forEach(i => i.classList.remove('active'));
                this.classList.add('active');
                activeMethod = this.getAttribute('data-method');
                document.getElementById('forecastMethodDropdown').textContent =
                    activeMethod === 'all' ? 'Forecast Method' : `Method: ${this.textContent.trim()}`;
                applyFilters();
            });
        });

        // Handle search input with debounce
        if (searchInput) {
            searchInput.addEventListener('input', debounce((e) => {
                updateURL({
                    search: e.target.value,
                    page: 1
                });
            }, 500));

            // Set initial search value from URL params
            const urlParams = new URLSearchParams(window.location.search);
            searchInput.value = urlParams.get('search') || '';
        }

        // Method Distribution Chart removed

        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl, {
                html: true,
                placement: 'auto',
                boundary: 'window',
                trigger: 'hover focus',
                delay: {
                    show: 100,
                    hide: 100
                }
            });
        });

        // Add click handler for accuracy charts to show detailed information
        document.querySelectorAll('.accuracy-chart').forEach(chart => {
            chart.addEventListener('click', function() {
                const forecastRow = this.closest('.forecast-row');
                const forecastId = forecastRow.getAttribute('data-forecast-id');
                const medicineName = forecastRow.querySelector('td:nth-child(1) .fw-bold').textContent.trim();
                const accuracy = this.getAttribute('data-percentage');
                const method = forecastRow.querySelector('td:nth-child(5) .badge').textContent.trim();

                // Navigate to the forecast detail page
                if (forecastId) {
                    // Use the URL pattern from the template
                    const detailUrl = document.querySelector(`a[href*="/forecast/${forecastId}/"]`);
                    if (detailUrl) {
                        window.location.href = detailUrl.getAttribute('href');
                    } else {
                        // Fallback to the medicine forecast detail page
                        const medicineId = forecastRow.querySelector('a[href*="/medicine/"]').getAttribute('href').match(/\/medicine\/(\d+)\//)[1];
                        if (medicineId) {
                            window.location.href = `/inventory/medicine/${medicineId}/forecast/`;
                        }
                    }
                }
            });
        });
    });
</script>
{% endblock %}

{% endblock %}