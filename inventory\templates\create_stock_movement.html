{% extends 'base.html' %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary fw-bold">
                        <i class="fas fa-exchange-alt me-2"></i>Create Stock Movement
                    </h1>
                    <p class="text-muted mt-2">
                        Record a new stock movement to track changes in medicine inventory.
                        Select the movement type, specify the quantity, and provide additional details as needed.
                    </p>
                </div>
                <a href="{% url 'stock_movement_list' %}" class="btn btn-outline-secondary rounded-pill">
                    <i class="fas fa-arrow-left me-1"></i>Back to List
                </a>
            </div>
        </div>
    </div>

    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Stock Movement Information</h6>
        </div>
        <div class="card-body">
            <form method="post" id="stockMovementForm">
                {% csrf_token %}

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="id_medicine" class="form-label">Medicine <span class="text-danger">*</span></label>
                        {{ form.medicine.errors }}
                        <select class="form-select" id="id_medicine" name="medicine" required>
                            <option value="">Select Medicine</option>
                            {% for medicine in form.fields.medicine.queryset %}
                            <option value="{{ medicine.id }}" {% if form.medicine.value == medicine.id %}selected{% endif %}>
                                {{ medicine.name }}
                                {% if medicine.brand %}({{ medicine.brand }}){% endif %}
                                {% if medicine.supplier %}- {{ medicine.supplier }}{% endif %}
                            </option>
                            {% endfor %}
                        </select>
                        <div id="medicineInfo" class="mt-2 small"></div>
                    </div>

                    <div class="col-md-6">
                        <label for="id_movement_type" class="form-label">Movement Type <span class="text-danger">*</span></label>
                        {{ form.movement_type.errors }}
                        <select class="form-select" id="id_movement_type" name="movement_type" required>
                            <option value="">Select Movement Type</option>
                            {% for value, text in form.fields.movement_type.choices %}
                            <option value="{{ value }}" {% if form.movement_type.value == value %}selected{% endif %}>{{ text }}</option>
                            {% endfor %}
                        </select>
                        <div class="form-text" id="movementTypeHelp"></div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="id_quantity" class="form-label">Quantity <span class="text-danger">*</span></label>
                        {{ form.quantity.errors }}
                        <input type="number" class="form-control" id="id_quantity" name="quantity" value="{{ form.quantity.value|default:'' }}" required>
                        <div class="form-text">{{ form.quantity.help_text }}</div>
                    </div>

                    <div class="col-md-6">
                        <label for="id_movement_date" class="form-label">Movement Date</label>
                        {{ form.movement_date.errors }}
                        <input type="datetime-local" class="form-control" id="id_movement_date" name="movement_date" value="{{ form.movement_date.value|date:'Y-m-d\TH:i' }}">
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="id_brand" class="form-label">Brand</label>
                        {{ form.brand.errors }}
                        <input type="text" class="form-control" id="id_brand" name="brand" value="{{ form.brand.value|default:'' }}">
                        <div class="form-text">Leave blank to use medicine's brand</div>
                    </div>

                    <div class="col-md-6">
                        <label for="id_supplier" class="form-label">Supplier</label>
                        {{ form.supplier.errors }}
                        <input type="text" class="form-control" id="id_supplier" name="supplier" value="{{ form.supplier.value|default:'' }}">
                        <div class="form-text">Leave blank to use medicine's supplier</div>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="id_reference_number" class="form-label">Reference Number</label>
                        {{ form.reference_number.errors }}
                        <input type="text" class="form-control" id="id_reference_number" name="reference_number" value="{{ form.reference_number.value|default:'' }}">
                        <div class="form-text">Invoice, PO, or reference number</div>
                    </div>

                    <div class="col-md-6">
                        <label for="id_notes" class="form-label">Notes</label>
                        {{ form.notes.errors }}
                        <textarea class="form-control" id="id_notes" name="notes" rows="3">{{ form.notes.value|default:'' }}</textarea>
                    </div>
                </div>

                <div class="row">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Save Stock Movement
                        </button>
                        <a href="{% url 'stock_movement_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-1"></i>Cancel
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const medicineSelect = document.getElementById('id_medicine');
        const brandInput = document.getElementById('id_brand');
        const supplierInput = document.getElementById('id_supplier');
        const quantityInput = document.getElementById('id_quantity');
        const movementTypeSelect = document.getElementById('id_movement_type');
        const medicineInfo = document.getElementById('medicineInfo');
        const movementTypeHelp = document.getElementById('movementTypeHelp');

        // Function to update movement type help text
        function updateMovementTypeHelp() {
            const movementType = movementTypeSelect.value;

            if (movementType === 'purchase') {
                movementTypeHelp.textContent = 'Adds to inventory. Enter positive quantity.';
                if (quantityInput.value < 0) {
                    quantityInput.value = Math.abs(quantityInput.value);
                }
            } else if (movementType === 'sale') {
                movementTypeHelp.textContent = 'Reduces inventory. Enter negative quantity.';
                if (quantityInput.value > 0) {
                    quantityInput.value = -Math.abs(quantityInput.value);
                }
            } else if (movementType === 'adjustment') {
                movementTypeHelp.textContent = 'Adjusts inventory. Enter positive to add, negative to reduce.';
            } else if (movementType === 'return') {
                movementTypeHelp.textContent = 'Return to supplier. Enter negative quantity.';
                if (quantityInput.value > 0) {
                    quantityInput.value = -Math.abs(quantityInput.value);
                }
            } else if (movementType === 'expired') {
                movementTypeHelp.textContent = 'Remove expired medicine. Enter negative quantity.';
                if (quantityInput.value > 0) {
                    quantityInput.value = -Math.abs(quantityInput.value);
                }
            } else if (movementType === 'transfer') {
                movementTypeHelp.textContent = 'Transfer between locations. Enter positive quantity.';
                if (quantityInput.value < 0) {
                    quantityInput.value = Math.abs(quantityInput.value);
                }
            }
        }

        // Function to fetch medicine details
        function fetchMedicineDetails() {
            const medicineId = medicineSelect.value;
            if (!medicineId) {
                medicineInfo.innerHTML = '';
                return;
            }

            fetch(`/api/medicine-details/${medicineId}/`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Update medicine info
                    medicineInfo.innerHTML = `
                        <div class="alert alert-info">
                            <strong>Current Quantity:</strong> ${data.quantity} units<br>
                            <strong>Price:</strong> ₱${data.price.toFixed(2)}
                        </div>
                    `;

                    // Auto-fill brand and supplier if empty
                    if (!brandInput.value && data.brand) {
                        brandInput.value = data.brand;
                    }

                    if (!supplierInput.value && data.supplier) {
                        supplierInput.value = data.supplier;
                    }
                })
                .catch(error => {
                    console.error('Error fetching medicine details:', error);
                    medicineInfo.innerHTML = `<div class="alert alert-danger">Error loading medicine details</div>`;
                });
        }

        // Set up event listeners
        medicineSelect.addEventListener('change', fetchMedicineDetails);
        movementTypeSelect.addEventListener('change', updateMovementTypeHelp);

        // Initialize on page load
        if (medicineSelect.value) {
            fetchMedicineDetails();
        }

        if (movementTypeSelect.value) {
            updateMovementTypeHelp();
        }

        // Form validation
        document.getElementById('stockMovementForm').addEventListener('submit', function(event) {
            const medicine = medicineSelect.value;
            const quantity = quantityInput.value;
            const movementType = movementTypeSelect.value;

            if (!medicine) {
                alert('Please select a medicine');
                event.preventDefault();
                return;
            }

            if (!quantity) {
                alert('Please enter a quantity');
                event.preventDefault();
                return;
            }

            if (!movementType) {
                alert('Please select a movement type');
                event.preventDefault();
                return;
            }

            // Validate quantity based on movement type
            if ((movementType === 'sale' || movementType === 'return' || movementType === 'expired') && quantity > 0) {
                if (!confirm('This movement type typically uses negative quantities. Do you want to continue with a positive quantity?')) {
                    event.preventDefault();
                    return;
                }
            }

            if ((movementType === 'purchase' || movementType === 'transfer') && quantity < 0) {
                if (!confirm('This movement type typically uses positive quantities. Do you want to continue with a negative quantity?')) {
                    event.preventDefault();
                    return;
                }
            }
        });
    });
</script>
{% endblock %}
