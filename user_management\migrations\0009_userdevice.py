# Generated by Django 4.2.9 on 2025-04-05 07:35

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('user_management', '0008_userprofile_email_verification_token_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UserDevice',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('device_type', models.CharField(choices=[('biometric', 'Biometric'), ('screenlock', 'Screen Lock')], max_length=20)),
                ('credential_id', models.CharField(max_length=255, unique=True)),
                ('public_key', models.TextField(blank=True, null=True)),
                ('pin_hash', models.CharField(blank=True, max_length=255, null=True)),
                ('device_name', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_used', models.DateTimeField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'indexes': [models.Index(fields=['user', 'device_type', 'is_active'], name='user_manage_user_id_194f59_idx'), models.Index(fields=['credential_id'], name='user_manage_credent_e167c0_idx')],
                'unique_together': {('user', 'credential_id')},
            },
        ),
    ]
