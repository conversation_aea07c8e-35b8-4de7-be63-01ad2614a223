﻿{% extends 'base.html' %}
{% block content %}
<div class="container py-4">
    <!-- Enhanced Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <h2 class="fw-bold text-primary mb-0">
                    <i class="bi bi-cart-plus me-2"></i>Create Transaction
                </h2>
                <div class="badge bg-light text-primary border px-3 py-2">
                    <i class="bi bi-clock me-1"></i><span id="current-datetime"></span>
                </div>
            </div>
            <hr class="text-primary opacity-25">
        </div>
    </div>

    <div class="row">
        <!-- Transaction Form Column -->
        <div class="col-lg-8">
            <div class="card shadow-sm border-0 rounded-3 mb-4">
                <div class="card-header bg-light py-3">
                    <h4 class="mb-0 text-secondary">
                        <i class="bi bi-pencil-square me-2"></i>Transaction Details
                    </h4>
                </div>
                <div class="card-body p-4">
                    <form method="post" id="transaction-form" class="transaction-form">
                        {% csrf_token %}

                        <!-- Medicine Selection with Search and Stock Info -->
                        <div class="row g-3 mb-4">
                            <div class="col-12">
                                <label class="form-label fw-medium">
                                    <i class="bi bi-capsule me-1"></i>{{ form.medicine.label }}
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <div class="position-relative flex-grow-1">
                                        {{ form.medicine|safe }}
                                        <div id="medicine-search-results" class="position-absolute w-100 mt-1 shadow-sm d-none"
                                             style="z-index: 1000; max-height: 200px; overflow-y: auto; background: white; border: 1px solid #dee2e6; border-radius: 0.25rem;">
                                        </div>
                                    </div>
                                    <span class="input-group-text bg-light" id="medicine-stock">
                                        <i class="bi bi-box-seam text-muted"></i>
                                    </span>
                                    <span class="input-group-text bg-light" id="medicine-price">
                                        <i class="bi bi-currency-dollar text-muted"></i>
                                    </span>
                                    <span class="input-group-text bg-light" id="medicine-type">
                                        <i class="bi bi-capsule text-muted"></i>
                                    </span>
                                    <span class="input-group-text bg-light" id="medicine-brand">
                                        <i class="bi bi-tag text-muted"></i>
                                    </span>
                                </div>
                                <div class="form-text" id="medicine-search-help">Type to search for medicines by name or category</div>

                                <!-- Alternative Medicines Section -->
                                <div id="alternative-medicines-container" class="mt-3 d-none">
                                    <div class="card border-0 shadow-sm">
                                        <div class="card-header bg-light">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <h6 class="mb-0 text-primary">
                                                    <i class="bi bi-shuffle me-2"></i>Alternative Medicines
                                                </h6>
                                                <button type="button" class="btn-close" id="close-alternatives"></button>
                                            </div>
                                        </div>
                                        <div class="card-body p-0">
                                            <div class="table-responsive">
                                                <table class="table table-hover table-sm mb-0">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th class="px-3">Name</th>
                                                            <th>Brand</th>
                                                            <th>Supplier</th>
                                                            <th>Stock</th>
                                                            <th>Price</th>
                                                            <th class="text-center">Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody id="alternative-medicines-list">
                                                        <!-- Alternative medicines will be populated here -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <!-- Transaction Details -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label class="form-label fw-medium">
                                    <i class="bi bi-tag me-1"></i>{{ form.transaction_type.label }}
                                </label>
                                {{ form.transaction_type|safe }}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium">
                                    <i class="bi bi-123 me-1"></i>{{ form.quantity.label }}
                                </label>
                                <div class="input-group">
                                    {{ form.quantity|safe }}
                                    <span class="input-group-text">units</span>
                                </div>
                            </div>
                        </div>

                        <!-- Customer Information with Enhanced Search -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label class="form-label fw-medium">
                                    <i class="bi bi-person me-1"></i>{{ form.customer_name.label }}
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-search"></i>
                                    </span>
                                    <div class="position-relative flex-grow-1">
                                        {{ form.customer_name|safe }}
                                        <div id="customer-search-results" class="position-absolute w-100 mt-1 shadow-sm d-none"
                                             style="z-index: 1000; max-height: 200px; overflow-y: auto; background: white; border: 1px solid #dee2e6; border-radius: 0.25rem;">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-text">Enter customer name or search existing customers</div>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium">
                                    <i class="bi bi-hash me-1"></i>{{ form.patient_number.label }}
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light">
                                        <i class="bi bi-upc-scan"></i>
                                    </span>
                                    {{ form.patient_number|safe }}
                                </div>
                                <div class="form-text">Enter patient number if available</div>
                            </div>
                        </div>

                        <!-- Additional Details -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label class="form-label fw-medium">
                                    <i class="bi bi-people me-1"></i>{{ form.customer_type.label }}
                                </label>
                                {{ form.customer_type|safe }}
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-medium">
                                    <i class="bi bi-calendar me-1"></i>{{ form.transaction_date.label }}
                                </label>
                                <div class="input-group">
                                    {{ form.transaction_date|safe }}
                                    <span class="input-group-text"><i class="bi bi-calendar"></i></span>
                                </div>
                            </div>
                        </div>

                        <!-- Transaction Time -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-6">
                                <label class="form-label fw-medium">
                                    <i class="bi bi-clock me-1"></i>Transaction Time
                                </label>
                                <div class="input-group">
                                    <input type="time" name="transaction_time" id="id_transaction_time" class="form-control" required>
                                    <span class="input-group-text"><i class="bi bi-clock"></i></span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <!-- Empty column for alignment -->
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary py-2 fw-medium">
                                <i class="bi bi-cart-plus me-2"></i>Add to Cart
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Enhanced Cart Summary Column -->
        <div class="col-lg-4">
            <div class="card shadow-sm border-0 rounded-3 mb-4 sticky-top" style="top: 1rem;">
                <div class="card-header bg-primary text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="bi bi-cart-check me-2"></i>Transaction Cart
                        </h4>
                        <span class="badge bg-white text-primary rounded-pill px-3 py-2 fw-bold">{{ cart|length }} items</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if cart %}
                    <div class="cart-items-container" style="max-height: 350px; overflow-y: auto;">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th class="ps-3">Medicine</th>
                                        <th class="text-center">Qty</th>
                                        <th class="text-end pe-3">Amount</th>
                                        <th class="text-center">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for item in cart %}
                                    <tr>
                                        <td class="ps-3">
                                            <div class="d-flex flex-column">
                                                <span class="fw-medium">{{ item.medicine }}</span>
                                                <small class="text-muted">
                                                    {% if item.brand %}{{ item.brand }}{% endif %}
                                                    {% if item.brand and item.supplier %} | {% endif %}
                                                    {% if item.supplier %}{{ item.supplier }}{% endif %}
                                                </small>
                                                <small class="text-muted">{{ item.description|truncatechars:30 }}</small>
                                            </div>
                                        </td>
                                        <td class="text-center align-middle">
                                            <span class="badge bg-light text-dark px-2 py-1 rounded-pill">{{ item.quantity }}</span>
                                        </td>
                                        <td class="text-end align-middle pe-3">
                                            <span class="fw-medium text-primary">₱{{ item.amount }}</span>
                                        </td>
                                        <td class="text-center align-middle">
                                            <div class="btn-group">
                                                <a href="{% url 'remove_from_cart' forloop.counter0 %}"
                                                   class="btn btn-sm btn-outline-danger"
                                                   title="Remove item">
                                                    <i class="bi bi-trash"></i>
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-primary edit-quantity"
                                                        data-index="{{ forloop.counter0 }}"
                                                        data-medicine="{{ item.medicine }}"
                                                        data-quantity="{{ item.quantity }}"
                                                        data-max="{{ item.max_quantity|default:100 }}"
                                                        title="Edit quantity">
                                                    <i class="bi bi-pencil"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <div class="p-3 bg-light border-top">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="text-muted">Total Units:</span>
                            <span class="fw-medium">{{ total_quantity }}</span>
                        </div>
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <span class="text-muted">Total Amount:</span>
                            <span class="fs-5 fw-bold text-primary">₱{{ total_price }}</span>
                        </div>

                        <div class="d-grid gap-2">
                            <a href="{% url 'save_transaction' %}" class="btn btn-success py-2 fw-medium">
                                <i class="bi bi-check-circle me-2"></i>Complete Transaction
                            </a>
                            <a href="{% url 'cancel_transaction' %}" class="btn btn-outline-danger">
                                <i class="bi bi-x-circle me-2"></i>Cancel
                            </a>
                        </div>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <div class="empty-cart-icon mb-3">
                            <i class="bi bi-cart text-secondary" style="font-size: 3.5rem;"></i>
                            <div class="position-absolute top-0 end-0">
                                <i class="bi bi-slash-circle text-danger" style="font-size: 1.5rem;"></i>
                            </div>
                        </div>
                        <h5 class="text-secondary mb-1">Your cart is empty</h5>
                        <p class="text-muted small">Add medicines to create a transaction</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Edit Quantity Modal -->
<div class="modal fade" id="editQuantityModal" tabindex="-1" aria-labelledby="editQuantityModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editQuantityModalLabel">
                    <i class="bi bi-pencil-square me-2"></i>Edit Quantity
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editQuantityForm" method="post" action="{% url 'update_cart_quantity' %}">
                    {% csrf_token %}
                    <input type="hidden" name="item_index" id="edit_item_index" value="">
                    <div class="mb-3">
                        <label for="edit_medicine_name" class="form-label">Medicine</label>
                        <input type="text" class="form-control" id="edit_medicine_name" readonly>
                    </div>
                    <div class="mb-3">
                        <label for="edit_quantity" class="form-label">Quantity</label>
                        <div class="input-group">
                            <button type="button" class="btn btn-outline-secondary" id="decrease_quantity">
                                <i class="bi bi-dash"></i>
                            </button>
                            <input type="number" class="form-control text-center" id="edit_quantity" name="quantity" min="1" value="1">
                            <button type="button" class="btn btn-outline-secondary" id="increase_quantity">
                                <i class="bi bi-plus"></i>
                            </button>
                        </div>
                        <div class="form-text" id="quantity_feedback">Available stock: <span id="max_quantity">0</span></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" form="editQuantityForm" class="btn btn-primary">
                    <i class="bi bi-check-circle me-2"></i>Update
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Price Modal -->
<div class="modal fade" id="editPriceModal" tabindex="-1" aria-labelledby="editPriceModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editPriceModalLabel">
                    <i class="bi bi-currency-dollar me-2"></i>Edit Price
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="edit_medicine_name_price" class="form-label">Medicine</label>
                    <input type="text" class="form-control" id="edit_medicine_name_price" readonly>
                </div>
                <div class="mb-3">
                    <label for="edit_price" class="form-label">Price (₱)</label>
                    <div class="input-group">
                        <span class="input-group-text">₱</span>
                        <input type="number" class="form-control" id="edit_price" min="0.01" step="0.01" value="0">
                    </div>
                    <div class="form-text">Enter the new price for this medicine</div>
                </div>
                <div id="price-update-status"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" id="save-price-btn" class="btn btn-primary">
                    <i class="bi bi-check-circle me-2"></i>Update Price
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Customer Search Modal -->
<div class="modal fade" id="customerSearchModal" tabindex="-1" aria-labelledby="customerSearchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="customerSearchModalLabel">
                    <i class="bi bi-search me-2"></i>Customer Search Results
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="customerSearchResults" class="p-2">
                    <!-- Results will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="selectCustomerBtn" disabled>
                    <i class="bi bi-check-circle me-2"></i>Select Customer
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Initialize date and time display
        function updateDateTime() {
            const now = new Date();
            const options = {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            };
            const formattedDate = now.toLocaleDateString('en-US', options);
            document.getElementById('current-datetime').textContent = formattedDate;

            // Also update the transaction date and time inputs
            const dateInput = document.getElementById('id_transaction_date');
            const timeInput = document.getElementById('id_transaction_time');

            if (dateInput) {
                const yyyy = now.getFullYear();
                const mm = String(now.getMonth() + 1).padStart(2, '0');
                const dd = String(now.getDate()).padStart(2, '0');
                dateInput.value = `${yyyy}-${mm}-${dd}`;
            }

            if (timeInput) {
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                timeInput.value = `${hours}:${minutes}`;
            }
        }

        // Function to restore customer data from localStorage if needed
        function restoreCustomerData() {
            try {
                const customerNameField = document.getElementById('id_customer_name');
                const patientNumberField = document.getElementById('id_patient_number');
                const customerTypeField = document.getElementById('id_customer_type');

                // Only restore if the fields are empty
                if (customerNameField && !customerNameField.value) {
                    const savedName = localStorage.getItem('lastCustomerName');
                    if (savedName) {
                        customerNameField.value = savedName;
                        console.log('Restored customer name from localStorage:', savedName);
                    }
                }

                if (patientNumberField && !patientNumberField.value) {
                    const savedNumber = localStorage.getItem('lastPatientNumber');
                    if (savedNumber) {
                        patientNumberField.value = savedNumber;
                        console.log('Restored patient number from localStorage:', savedNumber);
                    }
                }

                if (customerTypeField && !customerTypeField.value) {
                    const savedType = localStorage.getItem('lastCustomerType');
                    if (savedType) {
                        customerTypeField.value = savedType;
                        console.log('Restored customer type from localStorage:', savedType);
                    }
                }
            } catch (e) {
                console.warn('Error restoring customer data:', e);
            }
        }

        // Update time immediately and then every second
        updateDateTime();
        setInterval(updateDateTime, 1000);

        // Try to restore customer data from localStorage
        restoreCustomerData();

        // Initialize Bootstrap form controls
        const formControls = document.querySelectorAll('input, select');
        formControls.forEach(control => {
            control.classList.add('form-control');
        });

        // Enhanced date picker
        const dateInput = document.getElementById('id_transaction_date');
        if (dateInput) {
            dateInput.type = 'date';
            dateInput.classList.add('cursor-pointer');
            dateInput.setAttribute('readonly', 'readonly');
        }

        // Make time field read-only
        const timeInput = document.getElementById('id_transaction_time');
        if (timeInput) {
            timeInput.setAttribute('readonly', 'readonly');
        }

        // Debounce utility function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Customer fields logic with enhanced search
        const customerNameField = document.getElementById('id_customer_name');
        const patientNumberField = document.getElementById('id_patient_number');
        const customerSearchModal = new bootstrap.Modal(document.getElementById('customerSearchModal'));
        const customerSearchResults = document.getElementById('customerSearchResults');
        const selectCustomerBtn = document.getElementById('selectCustomerBtn');
        let selectedCustomerId = null;

        // Set up the select customer button event listener once
        if (selectCustomerBtn) {
            selectCustomerBtn.addEventListener('click', function() {
                if (!selectedCustomerId) return;

                // Find the selected row to get the most up-to-date data
                const selectedRow = document.querySelector(`.customer-row[data-id="${selectedCustomerId}"]`);

                // Use data from the row if available, otherwise fall back to the button's dataset
                let customerName, patientNumber, customerType;

                if (selectedRow) {
                    customerName = selectedRow.dataset.name;
                    patientNumber = selectedRow.dataset.number || '';
                    customerType = selectedRow.dataset.type || 'out_patient';
                } else {
                    customerName = this.dataset.name;
                    patientNumber = this.dataset.number || '';
                    customerType = this.dataset.type || 'out_patient';
                }

                if (customerName) {
                    // Set the form field values - use direct property assignment for reliability
                    customerNameField.value = customerName;
                    patientNumberField.value = patientNumber;
                    document.getElementById('id_customer_type').value = customerType;

                    // Close modal
                    customerSearchModal.hide();

                    // Ensure fields are properly enabled/disabled
                    patientNumberField.disabled = false;
                    customerNameField.disabled = false;
                    patientNumberField.closest('.col-md-6').style.opacity = '1';

                    // Store the values in localStorage as a backup
                    try {
                        localStorage.setItem('lastCustomerName', customerName);
                        localStorage.setItem('lastPatientNumber', patientNumber);
                        localStorage.setItem('lastCustomerType', customerType);
                    } catch (e) {
                        console.warn('Could not save customer data to localStorage:', e);
                    }

                    // Log for debugging
                    console.log('Customer selected:', customerName, patientNumber, customerType);

                    // Add a small delay before allowing blur events
                    setTimeout(() => {
                        // Force a focus on another field to prevent immediate blur events
                        document.getElementById('id_customer_type').focus();

                        // Double-check that the values are still set correctly
                        if (!customerNameField.value) {
                            console.warn('Customer name field was cleared - restoring');
                            customerNameField.value = customerName;
                        }
                        if (!patientNumberField.value) {
                            console.warn('Patient number field was cleared - restoring');
                            patientNumberField.value = patientNumber;
                        }
                    }, 200);
                }
            });
        }

        if (customerNameField && patientNumberField) {
            // Function to toggle fields
            function toggleFields(activeField, inactiveField) {
                const activeValue = activeField.value.trim();
                const inactiveFieldWrapper = inactiveField.closest('.col-md-6');

                if (activeValue) {
                    // Don't disable the field, just make it optional
                    inactiveField.required = false;
                    inactiveFieldWrapper.style.opacity = '0.8';
                } else {
                    inactiveField.disabled = false;
                    inactiveField.required = true;
                    inactiveFieldWrapper.style.opacity = '1';
                }
            }

            // Function to search customer by name with enhanced duplicate detection
            const searchByName = debounce((name) => {
                if (name.length < 2) return;

                // Show real-time search results in dropdown
                fetch(`/api/search_customer/?name=${encodeURIComponent(name)}`)
                    .then(response => response.json())
                    .then(data => {
                        const searchResults = document.getElementById('customer-search-results');

                        if (data.found) {
                            patientNumberField.value = data.patient_number;
                            if (data.customer_type) {
                                document.getElementById('id_customer_type').value = data.customer_type;
                            }

                            // Check for duplicate names
                            if (data.similar_customers && data.similar_customers.length > 0) {
                                // Create dropdown results
                                let html = '';
                                data.similar_customers.forEach(customer => {
                                    html += `
                                        <div class="p-2 border-bottom customer-result" data-id="${customer.id}"
                                             data-name="${customer.customer_name}" data-number="${customer.patient_number || ''}"
                                             data-type="${customer.customer_type}">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div>
                                                    <div class="fw-medium">${customer.customer_name}</div>
                                                    <small class="text-muted">${customer.patient_number || 'No patient number'}</small>
                                                </div>
                                                <span class="badge ${customer.customer_type === 'in_patient' ? 'bg-primary' : 'bg-success'} rounded-pill">
                                                    ${customer.customer_type_display}
                                                </span>
                                            </div>
                                        </div>
                                    `;
                                });

                                searchResults.innerHTML = html;
                                searchResults.classList.remove('d-none');

                                // Add click event to results
                                document.querySelectorAll('.customer-result').forEach(item => {
                                    item.addEventListener('click', function() {
                                        customerNameField.value = this.dataset.name;
                                        patientNumberField.value = this.dataset.number;
                                        document.getElementById('id_customer_type').value = this.dataset.type;
                                        searchResults.classList.add('d-none');
                                    });
                                });
                            }
                        } else if (name.length > 2) {
                            // Show 'no results' message
                            searchResults.innerHTML = '<div class="p-2 text-muted">No matching customers found</div>';
                            searchResults.classList.remove('d-none');
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        document.getElementById('customer-search-results').classList.add('d-none');
                    });
            }, 300);

            // Function to search customer by patient number
            const searchByPatientNumber = debounce((number) => {
                if (!number) return;

                fetch(`/api/search_customer/?patient_number=${encodeURIComponent(number)}`)
                    .then(response => response.json())
                    .then(data => {
                        if (data.found) {
                            customerNameField.value = data.customer_name;
                            if (data.customer_type) {
                                document.getElementById('id_customer_type').value = data.customer_type;
                            }
                            // Show success message
                            const patientNumberInput = document.getElementById('id_patient_number');
                            if (patientNumberInput) {
                                patientNumberInput.classList.add('is-valid');
                                setTimeout(() => {
                                    patientNumberInput.classList.remove('is-valid');
                                }, 2000);
                            }
                        } else {
                            // Show not found message
                            const patientNumberInput = document.getElementById('id_patient_number');
                            if (patientNumberInput && number.length > 3) {
                                patientNumberInput.classList.add('is-invalid');
                                setTimeout(() => {
                                    patientNumberInput.classList.remove('is-invalid');
                                }, 2000);
                            }
                        }
                    })
                    .catch(error => console.error('Error:', error));
            }, 300);

            // Function to display customer search results
            function showCustomerSearchResults(customers) {
                if (!customers || customers.length === 0) {
                    customerSearchResults.innerHTML = '<div class="alert alert-info">No matching customers found</div>';
                    selectCustomerBtn.disabled = true;
                    return;
                }

                // Create table with customer results
                let html = `
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col">Select</th>
                                    <th scope="col">Customer Name</th>
                                    <th scope="col">Patient Number</th>
                                    <th scope="col">Customer Type</th>
                                    <th scope="col">Last Transaction</th>
                                </tr>
                            </thead>
                            <tbody>
                `;

                customers.forEach(customer => {
                    html += `
                        <tr class="customer-row" data-id="${customer.id}" data-name="${customer.customer_name}"
                            data-number="${customer.patient_number || ''}" data-type="${customer.customer_type || 'out_patient'}">
                            <td>
                                <div class="form-check">
                                    <input class="form-check-input customer-select" type="radio" name="customerSelect" value="${customer.id}">
                                </div>
                            </td>
                            <td>${customer.customer_name}</td>
                            <td>${customer.patient_number || '<em>Not available</em>'}</td>
                            <td>${customer.customer_type_display || 'Out Patient'}</td>
                            <td>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span>${customer.last_transaction_date || 'N/A'}</span>
                                    ${customer.id ? `
                                    <a href="/customers/${customer.id}/" class="btn btn-sm btn-outline-primary ms-2"
                                       title="View transaction history" target="_blank">
                                        <i class="bi bi-clock-history"></i>
                                    </a>` : ''}
                                </div>
                            </td>
                        </tr>
                    `;
                });

                html += `
                            </tbody>
                        </table>
                    </div>
                `;

                customerSearchResults.innerHTML = html;

                // Add event listeners to rows
                document.querySelectorAll('.customer-row').forEach(row => {
                    row.addEventListener('click', function() {
                        // Find the radio button in this row and select it
                        const radio = this.querySelector('.customer-select');
                        radio.checked = true;

                        // Update selected customer ID
                        selectedCustomerId = this.dataset.id;
                        selectCustomerBtn.disabled = false;

                        // Store customer data in the button's dataset for easy access
                        selectCustomerBtn.dataset.name = this.dataset.name;
                        selectCustomerBtn.dataset.number = this.dataset.number || '';
                        selectCustomerBtn.dataset.type = this.dataset.type || 'out_patient';

                        // Highlight the selected row
                        document.querySelectorAll('.customer-row').forEach(r => r.classList.remove('table-primary'));
                        this.classList.add('table-primary');

                        // Double-click to select immediately
                        if (this._lastClickTime && (new Date() - this._lastClickTime < 300)) {
                            selectCustomerBtn.click();
                        }
                        this._lastClickTime = new Date();
                    });
                });

                // We'll set up the select button event listener outside this function to avoid duplicates
            }

            // Add event listeners for customer search
            customerNameField.addEventListener('input', (e) => {
                toggleFields(customerNameField, patientNumberField);
                if (e.target.value) {
                    searchByName(e.target.value);
                }
            });

            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                const searchResults = document.getElementById('customer-search-results');
                if (searchResults && !customerNameField.contains(e.target) && !searchResults.contains(e.target)) {
                    searchResults.classList.add('d-none');
                }
            });

            // Store a flag to track if we're selecting from the modal
            let isSelectingFromModal = false;

            // Update the modal show and hide events
            document.getElementById('customerSearchModal').addEventListener('show.bs.modal', function() {
                isSelectingFromModal = true;
            });

            document.getElementById('customerSearchModal').addEventListener('hidden.bs.modal', function() {
                // Give a short delay before allowing blur checks again
                setTimeout(() => {
                    isSelectingFromModal = false;
                }, 300);
            });

            customerNameField.addEventListener('blur', function() {
                // Skip duplicate check if we're selecting from the modal
                if (isSelectingFromModal) return;

                // Check for duplicate names after user finishes typing
                const name = this.value.trim();
                if (name.length > 2) {
                    setTimeout(() => {
                        fetch(`/api/check_duplicate_customers/?name=${encodeURIComponent(name)}`)
                            .then(response => response.json())
                            .then(data => {
                                if (data.duplicates && data.duplicates.length > 1) {
                                    showCustomerSearchResults(data.duplicates);
                                    customerSearchModal.show();
                                }
                            })
                            .catch(error => console.error('Error checking duplicates:', error));
                    }, 300);
                }
            });

            patientNumberField.addEventListener('input', (e) => {
                toggleFields(patientNumberField, customerNameField);
                if (e.target.value) {
                    searchByPatientNumber(e.target.value);
                }
            });
        }

        // Enhanced medicine search and details fetch
        const medicineSelect = document.getElementById('id_medicine');
        const stockDisplay = document.getElementById('medicine-stock');
        const typeDisplay = document.getElementById('medicine-type');
        const brandDisplay = document.getElementById('medicine-brand');
        const searchResults = document.getElementById('medicine-search-results');

        if (medicineSelect && stockDisplay && typeDisplay) {
            // Add search functionality
            medicineSelect.addEventListener('input', debounce(function() {
                const searchTerm = this.value.trim();
                if (searchTerm.length < 2) {
                    searchResults.classList.add('d-none');
                    return;
                }

                // Fetch matching medicines
                fetch(`/api/search_medicines/?term=${encodeURIComponent(searchTerm)}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Check for both results and medicines arrays for compatibility
                        const medicineList = data.medicines || data.results || [];

                        if (medicineList.length > 0) {
                            // Display search results
                            let html = '';
                            medicineList.forEach(medicine => {
                                // Get medicine type icon, default to 'other' if not available
                                const typeIcon = getMedicineTypeIcon(medicine.medicine_type || 'other');

                                // Determine stock level class
                                const stockClass = medicine.quantity <= 0 ? 'text-danger' :
                                                  (medicine.quantity <= (medicine.reorder_level || 0) ? 'text-warning' : 'text-success');

                                html += `
                                    <div class="p-2 border-bottom medicine-result" data-id="${medicine.id}">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <div class="fw-medium">${medicine.name}</div>
                                                <small class="text-muted">
                                                    ${medicine.brand ? medicine.brand : ''}
                                                    ${medicine.brand && medicine.supplier ? ' | ' : ''}
                                                    ${medicine.supplier ? medicine.supplier : ''}
                                                </small>
                                                <small class="text-muted">${medicine.category}</small>
                                            </div>
                                            <div class="d-flex align-items-center">
                                                <span class="me-2 ${stockClass}">${medicine.quantity}</span>
                                                <span class="text-primary">${typeIcon}</span>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });
                            searchResults.innerHTML = html;
                            searchResults.classList.remove('d-none');

                            // Add click event to results
                            document.querySelectorAll('.medicine-result').forEach(item => {
                                item.addEventListener('click', function() {
                                    const medicineId = this.dataset.id;
                                    medicineSelect.value = medicineId;
                                    searchResults.classList.add('d-none');

                                    // Trigger change event to load medicine details
                                    const event = new Event('change');
                                    medicineSelect.dispatchEvent(event);
                                });
                            });
                        } else {
                            searchResults.innerHTML = '<div class="p-2 text-muted">No medicines found</div>';
                            searchResults.classList.remove('d-none');
                        }
                    })
                    .catch(error => {
                        console.error('Error searching medicines:', error);
                        searchResults.classList.add('d-none');
                    });
            }, 300));

            // Hide search results when clicking outside
            document.addEventListener('click', function(e) {
                if (!medicineSelect.contains(e.target) && !searchResults.contains(e.target)) {
                    searchResults.classList.add('d-none');
                }
            });

            // Initialize price modal
            const priceModal = new bootstrap.Modal(document.getElementById('editPriceModal'));
            const priceDisplay = document.getElementById('medicine-price');
            const savePriceBtn = document.getElementById('save-price-btn');
            const priceUpdateStatus = document.getElementById('price-update-status');
            let currentMedicineId = null;
            let currentMedicineName = null;

            // Add click event to price display
            if (priceDisplay) {
                priceDisplay.style.cursor = 'pointer';
                priceDisplay.title = 'Click to edit price';

                priceDisplay.addEventListener('click', function() {
                    if (currentMedicineId) {
                        // Set modal values
                        document.getElementById('edit_medicine_name_price').value = currentMedicineName;
                        document.getElementById('edit_price').value = currentMedicinePrice;

                        // Show modal
                        priceModal.show();
                    }
                });
            }

            // Add click event to save price button
            if (savePriceBtn) {
                savePriceBtn.addEventListener('click', function() {
                    const newPrice = document.getElementById('edit_price').value;

                    if (parseFloat(newPrice) <= 0) {
                        alert('Price must be greater than zero');
                        return;
                    }

                    // Show loading indicator
                    priceUpdateStatus.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div> Updating price...';

                    // Update the price via AJAX
                    fetch('{% url "update_medicine_price" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        },
                        body: JSON.stringify({
                            medicine_id: currentMedicineId,
                            price: newPrice
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            // Update the price display
                            currentMedicinePrice = data.new_price;
                            priceDisplay.innerHTML = `<span class="text-success">₱${data.new_price}</span>`;

                            // Show success message
                            priceUpdateStatus.innerHTML = '<div class="alert alert-success py-1 px-2 mb-0"><i class="fas fa-check-circle me-1"></i>' + data.message + '</div>';

                            // Close the modal after a short delay
                            setTimeout(() => {
                                priceModal.hide();

                                // Clear the success message
                                setTimeout(() => {
                                    priceUpdateStatus.innerHTML = '';
                                }, 1000);
                            }, 1500);
                        } else {
                            // Show error message
                            priceUpdateStatus.innerHTML = '<div class="alert alert-danger py-1 px-2 mb-0"><i class="fas fa-exclamation-circle me-1"></i>Error: ' + data.error + '</div>';
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        priceUpdateStatus.innerHTML = '<div class="alert alert-danger py-1 px-2 mb-0"><i class="fas fa-exclamation-circle me-1"></i>Error updating price. Please try again.</div>';
                    });
                });
            }

            // Medicine details fetch on selection
            let currentMedicinePrice = 0;

            medicineSelect.addEventListener('change', function () {
                const medicineId = this.value;
                currentMedicineId = medicineId;

                if (medicineId) {
                    // Show loading indicator
                    stockDisplay.innerHTML = '<span class="text-muted"><i class="bi bi-hourglass-split me-1"></i>Loading...</span>';
                    priceDisplay.innerHTML = '<span class="text-muted"><i class="bi bi-hourglass-split me-1"></i></span>';
                    typeDisplay.innerHTML = '<span class="text-muted"><i class="bi bi-hourglass-split me-1"></i></span>';
                    brandDisplay.innerHTML = '<span class="text-muted"><i class="bi bi-hourglass-split me-1"></i></span>';

                    fetch(`/api/medicine_details/${medicineId}/`)
                        .then(response => {
                            if (!response.ok) {
                                throw new Error(`HTTP error! Status: ${response.status}`);
                            }
                            return response.json();
                        })
                        .then(data => {
                            // Check if there's an error in the response
                            if (data.error) {
                                throw new Error(data.error);
                            }

                            // Get quantity from the appropriate field
                            const quantity = data.quantity || data.current_stock || 0;

                            // Store medicine name and price
                            currentMedicineName = data.name;
                            currentMedicinePrice = data.price;

                            // Update stock display
                            stockDisplay.innerHTML = `<span class="text-success">Stock: ${quantity}</span>`;
                            document.getElementById('id_quantity').setAttribute('max', quantity);

                            // Update price display
                            priceDisplay.innerHTML = `<span class="text-success">₱${data.price}</span>`;

                            // Update medicine type display with fallback
                            const typeIcon = getMedicineTypeIcon(data.medicine_type || 'other');
                            const typeDisplayText = data.medicine_type_display || data.medicine_type || 'Other';
                            typeDisplay.innerHTML = `<span class="text-primary">${typeIcon} ${typeDisplayText}</span>`;

                            // Update brand and supplier display
                            const brandText = data.brand || 'Generic';
                            const supplierText = data.supplier || 'Unknown';
                            brandDisplay.innerHTML = `<span class="text-primary">${brandText} | ${supplierText}</span>`;

                            // Fetch and display alternative medicines
                            fetchAlternativeMedicines(medicineId, data.category, data.brand, data.supplier, data.generic_medicine);

                            // Disable quantity input if out of stock
                            const quantityInput = document.getElementById('id_quantity');
                            if (quantity <= 0) {
                                quantityInput.disabled = true;
                                quantityInput.value = 0;
                                stockDisplay.innerHTML = `<span class="text-danger">Out of Stock</span>`;
                            } else {
                                quantityInput.disabled = false;
                                quantityInput.value = 1; // Set default quantity to 1
                            }
                        })
                        .catch(error => {
                            console.error('Error fetching medicine details:', error);
                            stockDisplay.innerHTML = '<span class="text-danger">Error loading details</span>';
                            priceDisplay.innerHTML = '<span class="text-danger">Error</span>';
                            typeDisplay.innerHTML = '<i class="bi bi-capsule text-muted"></i>';

                            // Reset current medicine data
                            currentMedicineId = null;
                            currentMedicineName = null;
                            currentMedicinePrice = 0;

                            // Disable the quantity input on error
                            const quantityInput = document.getElementById('id_quantity');
                            if (quantityInput) {
                                quantityInput.disabled = true;
                                quantityInput.value = 0;
                            }
                        });
                } else {
                    stockDisplay.innerHTML = '<i class="bi bi-box-seam text-muted"></i>';
                    priceDisplay.innerHTML = '<i class="bi bi-currency-dollar text-muted"></i>';
                    typeDisplay.innerHTML = '<i class="bi bi-capsule text-muted"></i>';
                    brandDisplay.innerHTML = '<i class="bi bi-tag text-muted"></i>';

                    // Reset current medicine data
                    currentMedicineId = null;
                    currentMedicineName = null;
                    currentMedicinePrice = 0;

                    // Reset and disable quantity input when no medicine is selected
                    const quantityInput = document.getElementById('id_quantity');
                    if (quantityInput) {
                        quantityInput.disabled = true;
                        quantityInput.value = 0;
                    }
                }
            });
        }

        // Function to fetch and display alternative medicines
        function fetchAlternativeMedicines(medicineId, category, brand, supplier, genericMedicine) {
            const alternativesContainer = document.getElementById('alternative-medicines-container');
            const alternativesList = document.getElementById('alternative-medicines-list');
            const closeBtn = document.getElementById('close-alternatives');

            if (!alternativesContainer || !alternativesList) return;

            // Clear previous alternatives
            alternativesList.innerHTML = '<tr><td colspan="6" class="text-center"><div class="spinner-border spinner-border-sm text-primary" role="status"></div> Loading alternatives...</td></tr>';

            // Show the container
            alternativesContainer.classList.remove('d-none');

            // Fetch alternatives based on category, brand, supplier, and generic medicine
            fetch(`/api/search_medicines/?term=${encodeURIComponent(category || brand || supplier || '')}`)
                .then(response => response.json())
                .then(data => {
                    const medicines = data.medicines || data.results || [];

                    // Filter out the current medicine and limit to 5 alternatives
                    const alternatives = medicines
                        .filter(med => med.id != medicineId)
                        .filter(med => {
                            // Match by category, brand, supplier, or generic medicine
                            return (category && med.category === category) ||
                                   (brand && med.brand === brand) ||
                                   (supplier && med.supplier === supplier) ||
                                   (genericMedicine && med.generic_medicine === genericMedicine);
                        })
                        .slice(0, 5);

                    if (alternatives.length > 0) {
                        let html = '';
                        alternatives.forEach(med => {
                            const stockClass = med.quantity <= 0 ? 'text-danger' :
                                             (med.quantity <= (med.reorder_level || 0) ? 'text-warning' : 'text-success');

                            html += `
                                <tr>
                                    <td class="px-3">${med.name}</td>
                                    <td>${med.brand || 'Generic'}</td>
                                    <td>${med.supplier || 'Unknown'}</td>
                                    <td class="${stockClass}">${med.quantity}</td>
                                    <td>₱${med.price}</td>
                                    <td class="text-center">
                                        <button type="button" class="btn btn-sm btn-primary select-alternative"
                                                data-id="${med.id}" data-name="${med.name}">
                                            <i class="bi bi-check me-1"></i>Select
                                        </button>
                                    </td>
                                </tr>
                            `;
                        });

                        alternativesList.innerHTML = html;

                        // Add click event to select buttons
                        document.querySelectorAll('.select-alternative').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const altId = this.dataset.id;
                                const altName = this.dataset.name;

                                // Update the medicine select field
                                medicineSelect.value = altId;

                                // Trigger change event to load medicine details
                                const event = new Event('change');
                                medicineSelect.dispatchEvent(event);

                                // Hide the alternatives container
                                alternativesContainer.classList.add('d-none');
                            });
                        });
                    } else {
                        alternativesList.innerHTML = '<tr><td colspan="6" class="text-center">No alternative medicines found</td></tr>';
                    }
                })
                .catch(error => {
                    console.error('Error fetching alternatives:', error);
                    alternativesList.innerHTML = '<tr><td colspan="6" class="text-center text-danger">Error loading alternatives</td></tr>';
                });

            // Add close button event
            if (closeBtn) {
                closeBtn.addEventListener('click', function() {
                    alternativesContainer.classList.add('d-none');
                });
            }
        }

        // Helper function to get appropriate icon for medicine type
        function getMedicineTypeIcon(type) {
            const icons = {
                'tablet': '<i class="bi bi-circle-square"></i>',
                'capsule': '<i class="bi bi-capsule"></i>',
                'syrup': '<i class="bi bi-cup-straw"></i>',
                'injection': '<i class="bi bi-droplet"></i>',
                'cream': '<i class="bi bi-tube"></i>',
                'drops': '<i class="bi bi-droplet-half"></i>',
                'inhaler': '<i class="bi bi-wind"></i>',
                'powder': '<i class="bi bi-snow"></i>',
                'suppository': '<i class="bi bi-egg"></i>',
                'patch': '<i class="bi bi-bandaid"></i>',
                'other': '<i class="bi bi-prescription2"></i>'
            };
            return icons[type] || icons['other'];
        }

        // Edit quantity functionality
        const editQuantityModal = new bootstrap.Modal(document.getElementById('editQuantityModal'));
        const editButtons = document.querySelectorAll('.edit-quantity');
        const decreaseBtn = document.getElementById('decrease_quantity');
        const increaseBtn = document.getElementById('increase_quantity');
        const quantityInput = document.getElementById('edit_quantity');
        const maxQuantitySpan = document.getElementById('max_quantity');

        if (editButtons.length > 0) {
            editButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const index = this.dataset.index;
                    const medicine = this.dataset.medicine;
                    const quantity = parseInt(this.dataset.quantity);
                    const maxQuantity = parseInt(this.dataset.max);

                    // Set modal values
                    document.getElementById('edit_item_index').value = index;
                    document.getElementById('edit_medicine_name').value = medicine;
                    quantityInput.value = quantity;
                    quantityInput.max = maxQuantity;
                    maxQuantitySpan.textContent = maxQuantity;

                    // Show modal
                    editQuantityModal.show();
                });
            });
        }

        // Quantity increment/decrement buttons
        if (decreaseBtn && increaseBtn && quantityInput) {
            decreaseBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value);
                if (currentValue > 1) {
                    quantityInput.value = currentValue - 1;
                }
            });

            increaseBtn.addEventListener('click', function() {
                const currentValue = parseInt(quantityInput.value);
                const maxValue = parseInt(quantityInput.max);
                if (currentValue < maxValue) {
                    quantityInput.value = currentValue + 1;
                }
            });

            quantityInput.addEventListener('change', function() {
                const currentValue = parseInt(this.value);
                const maxValue = parseInt(this.max);

                if (isNaN(currentValue) || currentValue < 1) {
                    this.value = 1;
                } else if (currentValue > maxValue) {
                    this.value = maxValue;
                }
            });
        }
    });
</script>
{% endblock %}