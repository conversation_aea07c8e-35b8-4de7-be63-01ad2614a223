{% extends "admin/base_site.html" %}
{% load i18n admin_urls %}

{% block extrastyle %}
{{ block.super }}
<style type="text/css">
    .user-info-card {
        margin-bottom: 2em;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .card-header {
        padding: 1em;
        border-bottom: 1px solid #eee;
        font-weight: bold;
    }

    .user-details {
        display: flex;
        flex-wrap: wrap;
        padding: 1em;
        background: #f9f9f9;
    }

    .user-detail-item {
        min-width: 200px;
        margin-right: 2em;
        margin-bottom: 0.5em;
    }

    .user-detail-label {
        font-weight: 600;
        margin-bottom: 0.25em;
    }

    .form-container {
        background: #fff;
        border-radius: 4px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        padding: 1em;
        margin-bottom: 1.5em;
    }

    .form-title {
        margin-top: 0;
        padding-bottom: 0.75em;
        border-bottom: 1px solid #eee;
        font-size: 1.2em;
        font-weight: 500;
    }

    .form-row {
        padding: 1em 0;
        border-bottom: 1px solid #f5f5f5;
    }

        .form-row:last-child {
            border-bottom: none;
        }

    .field-box label {
        display: block;
        font-weight: 600;
        margin-bottom: 0.5em;
    }

    .field-box input[type="email"],
    .field-box input[type="text"] {
        width: 100%;
        padding: 0.75em;
        border-radius: 4px;
        border: 1px solid #ddd;
        font-size: 14px;
    }

    .field-box input:focus {
        border-color: #aaa;
        outline: none;
        box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.05);
    }

    .submit-row {
        display: flex;
        justify-content: flex-end;
        padding: 1.5em 0 0.5em;
    }

        .submit-row input[type="submit"] {
            font-weight: 600;
            padding: 0.75em 1.5em;
            border-radius: 4px;
            cursor: pointer;
            margin-left: 1em;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 0.5px;
        }

    .cancel-link {
        padding: 0.75em 1.5em;
        border-radius: 4px;
        text-decoration: none;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.9em;
        letter-spacing: 0.5px;
    }

    .help {
        font-size: 0.85em;
        margin-top: 0.5em;
        font-style: italic;
    }

    .phone-format-examples {
        margin-top: 0.5em;
        font-size: 0.85em;
        display: flex;
        flex-wrap: wrap;
    }

    .phone-example {
        background: #f9f9f9;
        border: 1px solid #eee;
        border-radius: 3px;
        padding: 0.25em 0.5em;
        margin-right: 0.5em;
        margin-bottom: 0.5em;
        font-family: monospace;
    }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
    &rsaquo; <a href="{% url 'admin:auth_user_changelist' %}">{% trans 'Users' %}</a>
    &rsaquo; <span>{% trans 'Edit Contact Information' %}</span>
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <div class="user-info-card">
        <div class="card-header">
            User Information
        </div>
        <div class="user-details">
            <div class="user-detail-item">
                <div class="user-detail-label">Username</div>
                <div>{{ user.username }}</div>
            </div>
            {% if user.first_name or user.last_name %}
            <div class="user-detail-item">
                <div class="user-detail-label">Name</div>
                <div>{{ user.first_name }} {{ user.last_name }}</div>
            </div>
            {% endif %}
            <div class="user-detail-item">
                <div class="user-detail-label">Active</div>
                <div>{{ user.is_active|yesno:"Yes,No" }}</div>
            </div>
            <div class="user-detail-item">
                <div class="user-detail-label">Last Login</div>
                <div>{{ user.last_login|date:"F j, Y, g:i a"|default:"-" }}</div>
            </div>
        </div>
    </div>

    <div class="form-container">
        <h2 class="form-title">Edit Contact Information</h2>
        <form method="post">
            {% csrf_token %}

            <div class="form-row">
                <div class="field-box">
                    <label for="email">Email Address:</label>
                    <input type="email" id="email" name="email" value="{{ current_email }}" required>
                </div>
            </div>

            <div class="form-row">
                <div class="field-box">
                    <label for="phone_number">Phone Number:</label>
                    <input type="text" id="phone_number" name="phone_number" value="{{ current_phone }}" placeholder="+63 (Philippines)">
                    <p class="help">Enter phone number with country code and no spaces</p>
                    <div class="phone-format-examples">
                 
                        <span class="phone-example">+63 (Philippines)</span>
                    </div>
                </div>
            </div>

            <div class="submit-row">
                <a href="{% url 'admin:auth_user_changelist' %}" class="button cancel-link">Cancel</a>
                <input type="submit" value="Save Changes" class="default" name="_save">
            </div>
        </form>
    </div>
</div>
{% endblock %}
