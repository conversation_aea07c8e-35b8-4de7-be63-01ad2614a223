{% extends 'base.html' %}
{% load base_filters %}

{% block content %}
<div class="container-fluid mt-4 px-md-4">
    {% if error_message %}
    <div class="alert alert-danger shadow-sm">
        <i class="fas fa-exclamation-circle me-2"></i>{{ error_message }}
    </div>
    {% endif %}

    <!-- Summary Cards with improved responsiveness -->
    <div class="row mb-4 justify-content-center g-3">
        <div class="col-12 col-sm-6 col-md-4 col-lg-4">
            <div class="card shadow-sm border-0 rounded-lg bg-gradient-light h-100">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title m-0 fw-bold text-primary fs-6 fs-md-5">Total Medicines</h5>
                        <i class="fas fa-capsules fa-2x text-primary-light opacity-50"></i>
                    </div>
                    <p class="card-text display-6 fw-bold mt-2 text-center">{{ total_medicines }}</p>
                </div>
            </div>
        </div>
        <div class="col-12 col-sm-6 col-md-4 col-lg-4">
            <div class="card shadow-sm border-0 rounded-lg bg-gradient-light h-100">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title m-0 fw-bold text-warning fs-6 fs-md-5">Low Stock Items</h5>
                        <i class="fas fa-exclamation-triangle fa-2x text-warning-light opacity-50"></i>
                    </div>
                    <p class="card-text display-6 fw-bold mt-2 text-center">{{ low_stock_count }}</p>
                </div>
            </div>
        </div>
        <!-- Sales card hidden -->
        {% comment %}
        <div class="col-12 col-sm-6 col-md-3 col-lg-3">
            <div class="card shadow-sm border-0 rounded-lg bg-gradient-light h-100">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title m-0 fw-bold text-success fs-6 fs-md-5">Sales (30 days)</h5>
                        <i class="fas fa-chart-line fa-2x text-success-light opacity-50"></i>
                    </div>
                    <p class="card-text display-6 fw-bold mt-2 text-center">₱{{ total_sales|floatformat:2 }}</p>
                </div>
            </div>
        </div>
        {% endcomment %}
        <div class="col-12 col-sm-6 col-md-4 col-lg-4">
            <div class="card shadow-sm border-0 rounded-lg bg-gradient-light h-100">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title m-0 fw-bold text-warning fs-6 fs-md-5">Forecast Horizon</h5>
                        <i class="fas fa-calendar-alt fa-2x text-warning-light opacity-50"></i>
                    </div>
                    <p class="card-text display-6 fw-bold mt-2 text-center">{{ forecast_horizon|default:"30" }}</p>
                    <div class="text-center mt-1">
                        <a href="{% url 'forecast_list' %}" class="small text-primary"><i class="fas fa-chart-line me-1"></i>View forecasts</a>
                    </div>
                    <div class="text-center mt-2">
                        <span class="badge bg-info bg-opacity-10 text-info">
                            <i class="fas fa-clock me-1"></i> Auto-updates daily at 7:00 PM
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Forecast Metrics Cards -->
    <div class="row mb-4 g-3">
        <!-- Gradient Boosting Highlight Card -->
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card shadow-sm border-0 rounded-lg h-100" style="background: linear-gradient(135deg, #4dabf7, #3b5998); color: white;">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title m-0 fw-bold fs-6">Gradient Boosting</h5>
                        <i class="fas fa-robot fa-2x opacity-50"></i>
                    </div>
                    <p class="small mb-2">Advanced ML forecasting with automatic daily updates!</p>
                    <div class="d-flex align-items-center mt-3">
                        <div class="position-relative me-3" style="width: 60px; height: 60px;">
                            <svg width="60" height="60" viewBox="0 0 60 60">
                                <circle cx="30" cy="30" r="25" fill="transparent" stroke="rgba(255,255,255,0.3)" stroke-width="5"></circle>
                                <circle cx="30" cy="30" r="25" fill="transparent"
                                        stroke="#ffffff" stroke-width="5"
                                        stroke-dasharray="{{ gb_adoption_rate|default:97 }} {{ 100|subtract:gb_adoption_rate|default:3 }}"
                                        stroke-dashoffset="25"></circle>
                            </svg>
                            <div class="position-absolute top-50 start-50 translate-middle fw-bold">{{ gb_adoption_rate|default:"97" }}%</div>
                        </div>
                        <div>
                            <div class="small opacity-75">Adoption Rate</div>
                            <div class="small" style="color: #a3e635;"><i class="fas fa-arrow-up me-1"></i>{{ forecast_accuracy_change|default:"+5.3" }}% Accuracy</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Regular Forecast Accuracy Card -->
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card shadow-sm border-0 rounded-lg h-100 bg-gradient-light">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title m-0 fw-bold text-primary fs-6">Forecast Accuracy</h5>
                        <i class="fas fa-bullseye fa-2x text-primary-light opacity-50"></i>
                    </div>
                    <div class="d-flex align-items-center mt-3">
                        <div class="position-relative me-3" style="width: 60px; height: 60px;">
                            <svg width="60" height="60" viewBox="0 0 60 60">
                                <circle cx="30" cy="30" r="25" fill="transparent" stroke="#e9ecef" stroke-width="5"></circle>
                                <circle cx="30" cy="30" r="25" fill="transparent"
                                        stroke="#4dabf7" stroke-width="5"
                                        stroke-dasharray="{{ forecast_accuracy|default:92 }} {{ 100|subtract:forecast_accuracy|default:8 }}"
                                        stroke-dashoffset="25"></circle>
                            </svg>
                            <div class="position-absolute top-50 start-50 translate-middle fw-bold">{{ forecast_accuracy|default:"92" }}%</div>
                        </div>
                        <div>
                            <div class="small text-muted">Last 30 days</div>
                            <div class="small text-success"><i class="fas fa-arrow-up me-1"></i>{{ forecast_accuracy_change|default:"+2.5" }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card shadow-sm border-0 rounded-lg h-100 bg-gradient-light">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title m-0 fw-bold text-success fs-6">MAPE</h5>
                        <i class="fas fa-percent fa-2x text-success-light opacity-50"></i>
                    </div>
                    <div class="d-flex align-items-center mt-3">
                        <div class="me-3">
                            <span class="display-6 fw-bold">{{ forecast_mape|default:"8.3" }}%</span>
                        </div>
                        <div>
                            <div class="small text-muted">Mean Absolute % Error</div>
                            <div class="small text-success"><i class="fas fa-arrow-down me-1"></i>{{ forecast_mape_change|default:"-1.2" }}%</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-md-6 col-lg-3">
            <div class="card shadow-sm border-0 rounded-lg h-100 bg-gradient-light">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="card-title m-0 fw-bold text-info fs-6">MAE</h5>
                        <i class="fas fa-calculator fa-2x text-info-light opacity-50"></i>
                    </div>
                    <div class="d-flex align-items-center mt-3">
                        <div class="me-3">
                            <span class="display-6 fw-bold">{{ forecast_mae|default:"5.2" }}</span>
                        </div>
                        <div>
                            <div class="small text-muted">Mean Absolute Error</div>
                            <div class="small text-success"><i class="fas fa-arrow-down me-1"></i>{{ forecast_mae_change|default:"-0.8" }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row with improved responsiveness -->
    <div class="row mb-4 g-3 justify-content-center">
        <div class="col-12 col-lg-8 col-xl-6">
            <div class="card shadow-sm border-0 rounded-lg h-100">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="card-title m-0 fw-bold">
                        <i class="fas fa-chart-pie me-2 text-primary"></i>Medicine Categories
                    </h5>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:250px; width:100%">
                        <canvas id="categoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        <!-- Sales Forecast chart hidden -->
        {% comment %}
        <div class="col-12 col-lg-6">
            <div class="card shadow-sm border-0 rounded-lg h-100">
                <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                    <h5 class="card-title m-0 fw-bold">
                        <i class="fas fa-chart-bar me-2 text-success"></i>Sales Forecast
                    </h5>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-secondary active" data-forecast-period="30">30 Days</button>
                        <button type="button" class="btn btn-outline-secondary" data-forecast-period="90">90 Days</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-container" style="position: relative; height:250px; width:100%">
                        <canvas id="forecastChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
        {% endcomment %}
    </div>


    <!-- Recent Transactions and Low Stock with improved responsiveness -->
    <div class="row g-3">
        <div class="col-12 col-lg-6">
            <div class="card shadow-sm border-0 rounded-lg h-100">
                <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                    <h5 class="card-title m-0 fw-bold">
                        <i class="fas fa-receipt me-2 text-info"></i>Recent Transactions
                    </h5>
                    <span class="badge bg-info text-white rounded-pill">Last 7 days</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-hover mb-0">
                            <thead class="table-light sticky-top">
                                <tr>
                                    <th class="px-4" style="background-color: #f8f9fa;">Date</th>
                                    <th style="background-color: #f8f9fa;">Medicine</th>
                                    <th style="background-color: #f8f9fa;">Brand/Supplier</th>
                                    <th class="text-end pe-4" style="background-color: #f8f9fa;">Amount</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for transaction in recent_transactions|slice:":10" %}
                                <tr>
                                    <td class="px-4">{{ transaction.transaction_date|date:"M d, Y" }}</td>
                                    <td>{{ transaction.medicine.name }}</td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            {% if transaction.medicine.brand %}
                                            <span class="badge bg-info bg-opacity-10 text-info mb-1">{{ transaction.medicine.brand }}</span>
                                            {% endif %}
                                            {% if transaction.medicine.supplier %}
                                            <span class="badge bg-primary bg-opacity-10 text-primary">{{ transaction.medicine.supplier }}</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="text-end fw-bold pe-4">₱{{ transaction.total_amount|floatformat:2 }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 col-lg-6">
            <div class="card shadow-sm border-0 rounded-lg h-100">
                <div class="card-header bg-white border-0 py-3 d-flex justify-content-between align-items-center">
                    <h5 class="card-title m-0 fw-bold">
                        <i class="fas fa-exclamation-circle me-2 text-danger"></i>Low Stock Alert
                    </h5>
                    <span class="badge bg-danger text-white rounded-pill">Attention needed</span>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive" style="max-height: 400px; overflow-y: auto;">
                        <table class="table table-hover mb-0">
                            <thead class="table-light sticky-top">
                                <tr>
                                    <th class="px-4" style="background-color: #f8f9fa;">Medicine</th>
                                    <th class="text-center" style="background-color: #f8f9fa;">Brand/Supplier</th>
                                    <th class="text-center" style="background-color: #f8f9fa;">Current Stock</th>
                                    <th class="text-center" style="background-color: #f8f9fa;">Reorder Level</th>
                                    <th class="text-end pe-4" style="background-color: #f8f9fa;">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for medicine in low_stock_medicines|slice:":10" %}
                                <tr>
                                    <td class="px-4">{{ medicine.name }}</td>
                                    <td class="text-center">
                                        <div class="d-flex flex-column">
                                            {% if medicine.brand %}
                                            <span class="badge bg-info bg-opacity-10 text-info mb-1">{{ medicine.brand }}</span>
                                            {% endif %}
                                            {% if medicine.supplier %}
                                            <span class="badge bg-primary bg-opacity-10 text-primary">{{ medicine.supplier }}</span>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="text-center">{{ medicine.quantity }}</td>
                                    <td class="text-center">{{ medicine.reorder_level }}</td>
                                    <td class="text-end pe-4">
                                        {% if medicine.quantity < medicine.reorder_level %}
                                        <span class="badge bg-danger">Critical</span>
                                        {% else %}
                                        <span class="badge bg-warning text-dark">Low</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add FontAwesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Add custom CSS for gradients, animations and responsive improvements -->
    <style>
        .bg-gradient-light {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
        }

        .card {
            transition: transform 0.3s, box-shadow 0.3s;
            margin-bottom: 1rem;
        }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
            }

        .text-primary-light {
            color: #4dabf7;
        }

        .text-warning-light {
            color: #ffd43b;
        }

        .text-success-light {
            color: #69db7c;
        }

        .text-info-light {
            color: #66d9e8;
        }

        .table-hover tbody tr:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }

        /* Responsive typography */
        @media (max-width: 576px) {
            .display-6 {
                font-size: 1.25rem;
            }

            .card-title {
                font-size: 0.9rem;
            }

            .fa-2x {
                font-size: 1.5em;
            }

            .table {
                font-size: 0.85rem;
            }
        }

        @media (min-width: 577px) and (max-width: 768px) {
            .display-6 {
                font-size: 1.5rem;
            }

            .card-title {
                font-size: 1rem;
            }
        }

        /* Ensure charts are responsive */
        .chart-container {
            position: relative;
            margin: auto;
            width: 100%;
        }

        /* Improve spacing on small screens */
        @media (max-width: 992px) {
            .container-fluid {
                padding-left: 10px;
                padding-right: 10px;
            }

            .g-3 {
                --bs-gutter-x: 0.75rem;
            }
        }
    </style>

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
    // Function to handle responsive charts
    function handleResize() {
        if (window.categoryChart) window.categoryChart.resize();
        // if (window.forecastChart) window.forecastChart.resize(); // Hidden
        if (window.forecastMethodsChart) window.forecastMethodsChart.resize();
    }

    // Add event listener for window resize
    window.addEventListener('resize', handleResize);

    // Handle forecast period buttons
    const forecastPeriodButtons = document.querySelectorAll('[data-forecast-period]');
    forecastPeriodButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Update active button
            forecastPeriodButtons.forEach(btn => btn.classList.remove('active'));
            this.classList.add('active');

            // Get the selected period
            const period = parseInt(this.getAttribute('data-forecast-period'));

            // Update chart title
            if (window.forecastChart && window.forecastChart.options && window.forecastChart.options.plugins && window.forecastChart.options.plugins.title) {
                window.forecastChart.options.plugins.title.text = `Sales Forecast (${period} Days Horizon)`;
            }

            // In a real implementation, you would fetch new data for the selected period
            // For now, we'll just update the existing chart with simulated data
            if (period === 90) {
                // Extend the forecast data for 90 days (simulated)
                const extendedLabels = [...forecastData.labels];
                const extendedActual = [...forecastData.actual];
                const extendedForecast = [...forecastData.forecast];
                const extendedLower = forecastData.lower_bound ? [...forecastData.lower_bound] : forecastData.forecast.map(val => val * 0.85);
                const extendedUpper = forecastData.upper_bound ? [...forecastData.upper_bound] : forecastData.forecast.map(val => val * 1.15);

                // Add more data points to simulate 90 days
                const lastLabel = new Date(forecastData.labels[forecastData.labels.length - 1]);
                const lastForecast = forecastData.forecast[forecastData.forecast.length - 1];

                for (let i = 1; i <= 6; i++) {  // Add 6 more data points (assuming monthly data)
                    const newDate = new Date(lastLabel);
                    newDate.setMonth(lastLabel.getMonth() + i);
                    extendedLabels.push(newDate.toLocaleDateString('en-US', { month: 'short', year: 'numeric' }));

                    // Generate some random variations for the extended forecast
                    const randomFactor = 0.9 + Math.random() * 0.3;  // Random between 0.9 and 1.2
                    const newForecast = lastForecast * randomFactor * (1 + i * 0.05);  // Slight upward trend

                    extendedActual.push(null);  // No actual data for future dates
                    extendedForecast.push(newForecast);
                    extendedLower.push(newForecast * 0.85);
                    extendedUpper.push(newForecast * 1.15);
                }

                // Update the chart data
                window.forecastChart.data.labels = extendedLabels;
                window.forecastChart.data.datasets[0].data = extendedActual;
                window.forecastChart.data.datasets[1].data = extendedForecast;
                window.forecastChart.data.datasets[2].data = extendedLower;
                window.forecastChart.data.datasets[3].data = extendedUpper;
            } else {
                // Reset to original 30-day data
                window.forecastChart.data.labels = forecastData.labels;
                window.forecastChart.data.datasets[0].data = forecastData.actual;
                window.forecastChart.data.datasets[1].data = forecastData.forecast;
                window.forecastChart.data.datasets[2].data = forecastData.lower_bound || forecastData.forecast.map(val => val * 0.85);
                window.forecastChart.data.datasets[3].data = forecastData.upper_bound || forecastData.forecast.map(val => val * 1.15);
            }

            // Update the chart
            window.forecastChart.update();
        });
    });

    // Forecasting Methods Distribution Chart
    window.forecastMethodsChart = new Chart(document.getElementById('forecastMethodsChart'), {
        type: 'doughnut',
        data: {
            labels: ['Gradient Boosting', 'ARIMA', 'Holt-Winters', 'Moving Average', 'Linear Regression', 'Ensemble'],
            datasets: [{
                data: [{{ gb_adoption_rate|default:65 }},
                       {{ 100|subtract:gb_adoption_rate|default:35|divide:5|multiply:1|floatformat:0 }},
                       {{ 100|subtract:gb_adoption_rate|default:35|divide:5|multiply:1|floatformat:0 }},
                       {{ 100|subtract:gb_adoption_rate|default:35|divide:5|multiply:1|floatformat:0 }},
                       {{ 100|subtract:gb_adoption_rate|default:35|divide:5|multiply:1|floatformat:0 }},
                       {{ 100|subtract:gb_adoption_rate|default:35|divide:5|multiply:1|floatformat:0 }}],
                backgroundColor: ['#4dabf7', '#69db7c', '#ffd43b', '#ff6b6b', '#9775fa', '#66d9e8'],
                borderWidth: 1,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    position: function() {
                        return window.innerWidth < 768 ? 'bottom' : 'right';
                    },
                    labels: {
                        boxWidth: 12,
                        padding: 15,
                        font: {
                            size: function() {
                                return window.innerWidth < 768 ? 10 : 11;
                            }
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    padding: 12,
                    usePointStyle: true,
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.raw + '%';
                        }
                    }
                }
            }
        }
    });

    // Category Chart with improved styling
    const categoryData = {{ category_data|safe }};
    window.categoryChart = new Chart(document.getElementById('categoryChart'), {
        type: 'doughnut',
        data: {
            labels: categoryData.labels,
            datasets: [{
                data: categoryData.data,
                backgroundColor: [
                    '#4dabf7', '#69db7c', '#ffd43b', '#ff8787', '#9775fa',
                    '#66d9e8', '#ffa94d', '#c0eb75', '#ff6b78', '#74c0fc'
                ],
                borderWidth: 1,
                borderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    position: function() {
                        return window.innerWidth < 768 ? 'bottom' : 'right';
                    },
                    labels: {
                        boxWidth: 12,
                        padding: 15,
                        font: {
                            size: function() {
                                return window.innerWidth < 768 ? 10 : 11;
                            }
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    padding: 12,
                    usePointStyle: true,
                    callbacks: {
                        label: function(context) {
                            return context.label + ': ' + context.raw + ' items';
                        }
                    }
                }
            },
            animation: {
                animateScale: true,
                animateRotate: true
            }
        }
    });

    // Enhanced Forecast Chart with confidence intervals and improved styling - Hidden
    /*
    const forecastData = {{ forecast_data|safe }};
    window.forecastChart = new Chart(document.getElementById('forecastChart'), {
        type: 'line',
        data: {
            labels: forecastData.labels,
            datasets: [
                {
                    label: 'Actual Sales',
                    data: forecastData.actual,
                    borderColor: '#4dabf7',
                    backgroundColor: 'rgba(77, 171, 247, 0.1)',
                    borderWidth: 3,
                    tension: 0.2,
                    fill: true,
                    pointBackgroundColor: '#ffffff',
                    pointBorderColor: '#4dabf7',
                    pointBorderWidth: 2,
                    pointRadius: function() {
                        return window.innerWidth < 768 ? 2 : 4;
                    },
                    pointHoverRadius: function() {
                        return window.innerWidth < 768 ? 3 : 6;
                    },
                    order: 1
                },
                {
                    label: 'Forecast',
                    data: forecastData.forecast,
                    borderColor: '#ff6b78',
                    backgroundColor: 'rgba(255, 107, 120, 0.05)',
                    borderWidth: 2,
                    borderDash: [5, 5],
                    tension: 0.2,
                    fill: false,
                    pointBackgroundColor: '#ffffff',
                    pointBorderColor: '#ff6b78',
                    pointBorderWidth: 2,
                    pointRadius: function() {
                        return window.innerWidth < 768 ? 1.5 : 3;
                    },
                    pointHoverRadius: function() {
                        return window.innerWidth < 768 ? 2.5 : 5;
                    },
                    order: 2
                },
                {
                    label: 'Lower Bound',
                    data: forecastData.lower_bound || forecastData.forecast.map(val => val * 0.85),
                    borderColor: 'rgba(255, 107, 120, 0.3)',
                    backgroundColor: 'transparent',
                    borderWidth: 1,
                    borderDash: [3, 3],
                    tension: 0.2,
                    fill: false,
                    pointRadius: 0,
                    order: 3,
                    hidden: window.innerWidth < 768
                },
                {
                    label: 'Upper Bound',
                    data: forecastData.upper_bound || forecastData.forecast.map(val => val * 1.15),
                    borderColor: 'rgba(255, 107, 120, 0.3)',
                    backgroundColor: 'rgba(255, 107, 120, 0.05)',
                    borderWidth: 1,
                    borderDash: [3, 3],
                    tension: 0.2,
                    fill: '-1', // Fill between this dataset and the previous one
                    pointRadius: 0,
                    order: 4,
                    hidden: window.innerWidth < 768
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            interaction: {
                mode: 'index',
                intersect: false
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Sales Amount (₱)',
                        font: {
                            weight: 'bold'
                        }
                    },
                    ticks: {
                        callback: function(value) {
                            return '₱' + value.toLocaleString();
                        },
                        padding: 10,
                        font: {
                            size: function() {
                                return window.innerWidth < 768 ? 9 : 11;
                            }
                        }
                    },
                    grid: {
                        drawBorder: false
                    }
                },
                x: {
                    grid: {
                        display: false
                    },
                    title: {
                        display: true,
                        text: 'Time Period',
                        font: {
                            weight: 'bold'
                        }
                    },
                    ticks: {
                        font: {
                            size: function() {
                                return window.innerWidth < 768 ? 9 : 11;
                            }
                        },
                        maxRotation: 45,
                        minRotation: 45
                    }
                }
            },
            plugins: {
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    padding: 12,
                    usePointStyle: true,
                    callbacks: {
                        label: function(context) {
                            const label = context.dataset.label || '';
                            if (label) {
                                return label + ': ₱' + context.parsed.y.toLocaleString();
                            }
                            return '₱' + context.parsed.y.toLocaleString();
                        },
                        footer: function(tooltipItems) {
                            const datasetIndex = tooltipItems[0].datasetIndex;
                            // Only show confidence interval for forecast points
                            if (datasetIndex === 1) { // Forecast dataset
                                const index = tooltipItems[0].dataIndex;
                                const lowerValue = forecastData.lower_bound ? forecastData.lower_bound[index] : forecastData.forecast[index] * 0.85;
                                const upperValue = forecastData.upper_bound ? forecastData.upper_bound[index] : forecastData.forecast[index] * 1.15;
                                return ['95% Confidence Interval:',
                                        '₱' + lowerValue.toLocaleString() + ' - ₱' + upperValue.toLocaleString()];
                            }
                            return '';
                        }
                    }
                },
                legend: {
                    labels: {
                        boxWidth: 12,
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            size: function() {
                                return window.innerWidth < 768 ? 10 : 12;
                            }
                        },
                        filter: function(item) {
                            // Only show Actual and Forecast in the legend
                            return ['Actual Sales', 'Forecast'].includes(item.text);
                        }
                    }
                },
                title: {
                    display: true,
                    text: 'Sales Forecast with Confidence Intervals',
                    font: {
                        size: 16,
                        weight: 'bold'
                    },
                    padding: {
                        top: 10,
                        bottom: 20
                    }
                }
            }
        }
    });
    */
    </script>
    {% endblock %}
