/* Enhanced Monthly Demand and Latest Forecast Styling */

:root {
    --light-gray: #e9ecef;
    --transition: all 0.2s ease-in-out;
}

/* Monthly Demand Section Enhancements */
.monthly-demand .chart-container {
    background-color: rgba(255, 255, 255, 0.7);
    border-radius: var(--border-radius);
    padding: 0.5rem;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.03);
}

.monthly-demand .btn-group {
    width: 100%;
    justify-content: center;
}

    .monthly-demand .btn-group .btn {
        box-shadow: none;
        border-color: var(--secondary-color);
        color: var(--secondary-color);
        transition: var(--transition);
    }

        .monthly-demand .btn-group .btn.active {
            background-color: var(--secondary-color);
            color: white;
        }

.monthly-demand .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

    .monthly-demand .card-header .badge {
        display: flex;
        align-items: center;
    }

/* Latest Forecast Section Enhancements */
.latest-forecast .chart-container {
    margin: 0 auto;
    max-width: 200px;
}

.latest-forecast .forecast-breakdown {
    background-color: var(--light-bg);
    border-radius: var(--border-radius);
    padding: 1rem;
}

.latest-forecast .progress {
    border-radius: 10px;
    background-color: rgba(0, 0, 0, 0.05);
}

.latest-forecast .form-check-input {
    cursor: pointer;
}

    .latest-forecast .form-check-input:checked {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
    }

/* Shared Enhancements */
.forecast-breakdown .fw-bold,
.text-dark.fw-bold {
    font-weight: 600 !important;
}

/* Visual feedback for interactive elements */
.btn-outline-primary:hover {
    transform: translateY(-1px);
}

.form-check-input:hover {
    border-color: var(--secondary-color);
}

#recalculateBtn:active {
    transform: scale(0.97);
}

/* Tooltip enhancements */
.tooltip {
    opacity: 1;
}

    .tooltip .tooltip-inner {
        background-color: var(--text-color);
        border-radius: var(--border-radius);
        padding: 0.5rem 0.75rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .tooltip .tooltip-arrow::before {
        border-top-color: var(--text-color);
    }

/* Table row hover effect */
.monthly-demand .table-hover tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
    cursor: pointer;
    transition: var(--transition);
}

/* Card header icon enhancements */
.card-header i {
    margin-right: 0.5rem;
    opacity: 0.8;
}

/* Animation for gauge charts */
@keyframes gaugeAppear {
    from {
        opacity: 0;
        transform: scale(0.8);
    }

    to {
        opacity: 1;
        transform: scale(1);
    }
}

.chart-container {
    animation: gaugeAppear 0.5s ease-out;
}

/* Mobile responsive adjustments */
@media (max-width: 768px) {
    .monthly-demand .btn-group {
        flex-wrap: wrap;
    }

        .monthly-demand .btn-group .btn {
            flex: 1;
            padding: 0.25rem 0.5rem;
            font-size: 0.75rem;
        }

    .latest-forecast .forecast-breakdown {
        padding: 0.75rem;
    }

    .chart-container {
        height: 200px !important;
    }
}

/* Print adjustments */
@media print {
    .btn-group,
    #recalculateBtn,
    .form-check {
        display: none !important;
    }

    .chart-container {
        border: 1px solid var(--light-gray);
        padding: 0.25rem;
    }

    .forecast-breakdown {
        border: 1px solid var(--light-gray);
        background-color: white !important;
    }
}


/* Back Navigation Styles */
.back-navigation {
    padding: 1rem 0;
    margin-bottom: 1.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    background-color: white;
}

    .back-navigation .breadcrumb {
        margin-bottom: 0;
        padding: 0;
    }

.back-btn {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    font-weight: 500;
    color: var(--primary-color);
    background-color: transparent;
    border: 1px solid var(--primary-color);
    border-radius: var(--border-radius);
    transition: all 0.3s ease;
}

    .back-btn:hover {
        color: white;
        background-color: var(--primary-color);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transform: translateY(-1px);
    }

    .back-btn i {
        margin-right: 0.5rem;
        font-size: 0.9em;
    }

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.page-title {
    margin: 0;
    color: var(--primary-color);
    font-size: 1.5rem;
    font-weight: 600;
}