﻿from django.contrib import admin
from .models import Medicine, Transaction, Forecast, AuditTrail, GenericMedicine

class MedicineAdmin(admin.ModelAdmin):
    list_display = ('name', 'generic_medicine', 'brand', 'supplier', 'quantity', 'category')
    list_filter = ('category', 'generic_medicine', 'brand', 'supplier')
    search_fields = ('name', 'brand', 'supplier')

class GenericMedicineAdmin(admin.ModelAdmin):
    list_display = ('name', 'category')
    list_filter = ('category',)
    search_fields = ('name', 'category')

class ForecastAdmin(admin.ModelAdmin):
    list_display = ('get_medicine_name', 'forecast_date', 'predicted_quantity', 'forecast_method')
    list_filter = ('forecast_method', 'forecast_date')

    def get_medicine_name(self, obj):
        if obj.generic_medicine:
            return f"Generic: {obj.generic_medicine.name}"
        elif obj.medicine:
            return obj.medicine.name
        return "Unknown"
    get_medicine_name.short_description = 'Medicine'

admin.site.register(Medicine, MedicineAdmin)
admin.site.register(GenericMedicine, GenericMedicineAdmin)
admin.site.register(Transaction)
admin.site.register(Forecast, ForecastAdmin)
admin.site.register(AuditTrail)