import logging
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db.models import F
from inventory.models import Medicine
from inventory.forecasting import get_forecasting_results

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Test the forecast Excel report calculation'

    def add_arguments(self, parser):
        parser.add_argument('--verbose', action='store_true', help='Show detailed output')

    def handle(self, *args, **options):
        verbose = options['verbose']
        
        # Get all medicines
        medicines = Medicine.objects.all().order_by('name')
        total_medicines = medicines.count()
        
        self.stdout.write(f"Testing forecast calculations for {total_medicines} medicines")
        
        # Table header
        self.stdout.write("\n{:<20} {:<10} {:<15} {:<15} {:<15} {:<15}".format(
            "Medicine", "Stock", "Reorder Level", "Predicted", "Recommended", "Model"
        ))
        self.stdout.write("-" * 90)
        
        total_predicted = 0
        total_recommended = 0
        
        for medicine in medicines:
            try:
                # Get forecast data
                forecast_data = get_forecasting_results(medicine)
                predicted_demand = forecast_data.get('predicted_demand', 0)
                recommended_order = forecast_data.get('recommended_order_quantity', 0)
                forecast_method = forecast_data.get('model_used', 'N/A')
                
                # Calculate recommended order manually as a fallback
                if recommended_order == 0:
                    # If medicine is below reorder level and has predicted demand, calculate recommended order
                    if medicine.quantity < medicine.reorder_level and predicted_demand > 0:
                        # Calculate how much we need to order to reach reorder level plus predicted demand
                        fallback_recommended = max(0, medicine.reorder_level - medicine.quantity + predicted_demand)
                        if fallback_recommended > 0:
                            recommended_order = fallback_recommended
                            if verbose:
                                self.stdout.write(f"Used fallback calculation for {medicine.name}: {recommended_order}")
                
                # Add to totals
                total_predicted += predicted_demand
                total_recommended += recommended_order
                
                # Print row
                self.stdout.write("{:<20} {:<10} {:<15} {:<15.2f} {:<15.2f} {:<15}".format(
                    medicine.name[:20], 
                    medicine.quantity,
                    medicine.reorder_level,
                    predicted_demand,
                    recommended_order,
                    forecast_method
                ))
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error forecasting for {medicine.name}: {str(e)}"))
        
        # Print totals
        self.stdout.write("-" * 90)
        self.stdout.write("{:<20} {:<10} {:<15} {:<15.2f} {:<15.2f} {:<15}".format(
            "TOTAL", "", "", total_predicted, total_recommended, ""
        ))
        
        # Check for medicines below reorder level
        below_reorder = Medicine.objects.filter(quantity__lt=F('reorder_level')).count()
        self.stdout.write(f"\nMedicines below reorder level: {below_reorder}/{total_medicines}")
        
        if below_reorder > 0:
            self.stdout.write("\nMedicines below reorder level:")
            for medicine in Medicine.objects.filter(quantity__lt=F('reorder_level')):
                self.stdout.write(f"  {medicine.name}: Stock={medicine.quantity}, Reorder Level={medicine.reorder_level}")
