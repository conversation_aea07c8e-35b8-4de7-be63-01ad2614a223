{% extends 'base.html' %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-primary fw-bold">
                    <i class="fas fa-chart-line me-2"></i>Movement Analysis: {{ medicine.name }}
                </h1>
                <div>
                    <a href="{% url 'medicine_detail' medicine.id %}" class="btn btn-info rounded-pill">
                        <i class="fas fa-eye me-1"></i>Medicine Details
                    </a>
                    <a href="{% url 'stock_movement_list' %}" class="btn btn-outline-secondary rounded-pill">
                        <i class="fas fa-list me-1"></i>All Movements
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Medicine Info -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Medicine Information</h6>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <p class="mb-0 text-muted">Name</p>
                    <h5>{{ medicine.name }}</h5>
                </div>
                <div class="col-md-3 mb-3">
                    <p class="mb-0 text-muted">Category</p>
                    <h5>{{ medicine.category|default:"Not categorized" }}</h5>
                </div>
                <div class="col-md-3 mb-3">
                    <p class="mb-0 text-muted">Brand</p>
                    <h5>
                        {% if medicine.brand %}
                        <span class="badge bg-info">{{ medicine.brand }}</span>
                        {% else %}
                        <span class="text-muted">Not specified</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="col-md-3 mb-3">
                    <p class="mb-0 text-muted">Supplier</p>
                    <h5>
                        {% if medicine.supplier %}
                        <span class="badge bg-secondary">{{ medicine.supplier }}</span>
                        {% else %}
                        <span class="text-muted">Not specified</span>
                        {% endif %}
                    </h5>
                </div>
            </div>
        </div>
    </div>

    <!-- Movement Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Movement Class</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if analysis.movement_class == 'A' %}
                                <span class="badge bg-success">Fast-moving</span>
                                {% elif analysis.movement_class == 'B' %}
                                <span class="badge bg-info">Medium-moving</span>
                                {% elif analysis.movement_class == 'C' %}
                                <span class="badge bg-warning">Slow-moving</span>
                                {% elif analysis.movement_class == 'D' %}
                                <span class="badge bg-danger">No movement</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-tachometer-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Monthly Movement Rate</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ analysis.monthly_movement_rate|floatformat:1 }} units</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-bar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                Movement Trend</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if analysis.movement_trend == 'Increasing' %}
                                <span class="text-success"><i class="fas fa-arrow-up me-1"></i>Increasing</span>
                                {% elif analysis.movement_trend == 'Decreasing' %}
                                <span class="text-danger"><i class="fas fa-arrow-down me-1"></i>Decreasing</span>
                                {% elif analysis.movement_trend == 'Stable' %}
                                <span class="text-info"><i class="fas fa-equals me-1"></i>Stable</span>
                                {% elif analysis.movement_trend == 'Volatile' %}
                                <span class="text-warning"><i class="fas fa-random me-1"></i>Volatile</span>
                                {% elif analysis.movement_trend == 'New' %}
                                <span class="text-primary"><i class="fas fa-star me-1"></i>New</span>
                                {% else %}
                                <span class="text-muted">Unknown</span>
                                {% endif %}

                                {% if analysis.trend_percentage %}
                                ({{ analysis.trend_percentage|floatformat:1 }}%)
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-chart-line fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                Days Since Last Movement</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {% if analysis.days_since_last_movement %}
                                {{ analysis.days_since_last_movement }} days
                                {% else %}
                                No movements
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Movement Charts -->
    <div class="row mb-4">
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Monthly Movement History</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="monthlyMovementChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Movement Types</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="movementTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Brand and Supplier Analysis -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Brand Analysis</h6>
                </div>
                <div class="card-body">
                    {% if brand_data %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Brand</th>
                                    <th>Movement Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for brand in brand_data %}
                                <tr>
                                    <td>
                                        <span class="badge bg-info">{{ brand.brand }}</span>
                                    </td>
                                    <td>{{ brand.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">No brand data available</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Supplier Analysis</h6>
                </div>
                <div class="card-body">
                    {% if supplier_data %}
                    <div class="table-responsive">
                        <table class="table table-bordered" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>Supplier</th>
                                    <th>Movement Count</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for supplier in supplier_data %}
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">{{ supplier.supplier }}</span>
                                    </td>
                                    <td>{{ supplier.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">No supplier data available</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Movements -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Recent Movements</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Type</th>
                            <th>Quantity</th>
                            <th>Brand</th>
                            <th>Supplier</th>
                            <th>Reference</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in movements %}
                        <tr>
                            <td>{{ movement.movement_date|date:"M d, Y H:i" }}</td>
                            <td>
                                {% if movement.movement_type == 'purchase' %}
                                <span class="badge bg-success">Purchase</span>
                                {% elif movement.movement_type == 'sale' %}
                                <span class="badge bg-danger">Sale</span>
                                {% elif movement.movement_type == 'adjustment' %}
                                <span class="badge bg-warning">Adjustment</span>
                                {% elif movement.movement_type == 'return' %}
                                <span class="badge bg-info">Return</span>
                                {% elif movement.movement_type == 'expired' %}
                                <span class="badge bg-dark">Expired</span>
                                {% elif movement.movement_type == 'transfer' %}
                                <span class="badge bg-primary">Transfer</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ movement.get_movement_type_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.quantity > 0 %}
                                <span class="text-success">+{{ movement.quantity }}</span>
                                {% else %}
                                <span class="text-danger">{{ movement.quantity }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.brand %}
                                <span class="badge bg-info">{{ movement.brand }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.supplier %}
                                <span class="badge bg-secondary">{{ movement.supplier }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>{{ movement.reference_number|default:"-" }}</td>
                            <td>
                                <a href="{% url 'stock_movement_detail' movement.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="7" class="text-center">No movements found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% if total_movements > 10 %}
            <div class="text-center mt-3">
                <p>Showing 10 of {{ total_movements }} movements</p>
                <a href="{% url 'stock_movement_list' %}?search={{ medicine.name }}{% if medicine.brand %} {{ medicine.brand }}{% endif %}{% if medicine.supplier %} {{ medicine.supplier }}{% endif %}{% if medicine.category %} {{ medicine.category }}{% endif %}" class="btn btn-primary">
                    <i class="fas fa-list me-1"></i>View All Movements
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Monthly Movement Chart
        var monthlyCtx = document.getElementById('monthlyMovementChart').getContext('2d');
        var monthlyData = {{ monthly_data|safe }};

        var labels = monthlyData.map(function(item) {
            return item.month;
        });

        var quantities = monthlyData.map(function(item) {
            return item.quantity;
        });

        new Chart(monthlyCtx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Monthly Movement',
                    data: quantities,
                    backgroundColor: 'rgba(78, 115, 223, 0.05)',
                    borderColor: 'rgba(78, 115, 223, 1)',
                    pointRadius: 3,
                    pointBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointBorderColor: 'rgba(78, 115, 223, 1)',
                    pointHoverRadius: 5,
                    pointHoverBackgroundColor: 'rgba(78, 115, 223, 1)',
                    pointHoverBorderColor: 'rgba(78, 115, 223, 1)',
                    pointHitRadius: 10,
                    pointBorderWidth: 2,
                    tension: 0.3,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + context.parsed.y + ' units';
                            }
                        }
                    }
                }
            }
        });

        // Movement Type Chart
        var typeCtx = document.getElementById('movementTypeChart').getContext('2d');
        var typeData = {{ movement_type_data|safe }};

        var backgroundColors = [
            'rgba(78, 115, 223, 0.7)',
            'rgba(28, 200, 138, 0.7)',
            'rgba(246, 194, 62, 0.7)',
            'rgba(231, 74, 59, 0.7)',
            'rgba(54, 185, 204, 0.7)',
            'rgba(133, 135, 150, 0.7)'
        ];

        var borderColors = [
            'rgba(78, 115, 223, 1)',
            'rgba(28, 200, 138, 1)',
            'rgba(246, 194, 62, 1)',
            'rgba(231, 74, 59, 1)',
            'rgba(54, 185, 204, 1)',
            'rgba(133, 135, 150, 1)'
        ];

        new Chart(typeCtx, {
            type: 'doughnut',
            data: {
                labels: typeData.labels,
                datasets: [{
                    data: typeData.data,
                    backgroundColor: backgroundColors,
                    borderColor: borderColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    }
                }
            }
        });
    });
</script>
{% endblock %}
