{% extends 'base.html' %}
{% load static %}

{% block content %}
<div class="login-container">
    <div class="login-box">
        <h2>Login to Inventory System</h2>
        <div id="client-error"></div>

        <form action="{% url 'custom_login' %}" method="post" id="login-form" novalidate>
            {% csrf_token %}
            <div class="form-group">
                <label for="id_username">Username</label>
                <input type="text" name="username" id="id_username" class="form-control">
                <span class="error-message" id="username-error"></span>
            </div>
            <div class="form-group">
                <label for="id_password">Password</label>
                <input type="password" name="password" id="id_password" class="form-control">
                <span class="error-message" id="password-error"></span>
                <input type="hidden" name="next" value="{{ next }}">
            </div>
            <div class="form-group">
                <button type="submit" id="submit-btn" class="btn btn-primary">Log in</button>
            </div>
        </form>
    </div>
</div>

<style>
    .login-container {
        display: flex;
        justify-content: center;
        align-items: center;
        min-height: 100vh;
        background-color: #f5f5f5;
    }

    .login-box {
        background: white;
        padding: 2rem;
        border-radius: 8px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        width: 100%;
        max-width: 400px;
    }

    .error-message {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
        display: none;
    }

        .error-message.visible {
            display: block;
        }

    #client-error {
        background: #fff0f0;
        border: 1px solid #dc3545;
        padding: 0.75rem;
        margin-bottom: 1rem;
        border-radius: 4px;
        display: none;
        color: #dc3545;
    }

    .form-group {
        margin-bottom: 1rem;
    }

    .form-control.error {
        border-color: #dc3545;
        background-color: #fff8f8;
    }
</style>

<script>
(function() {
    const form = document.getElementById('login-form');
    const username = document.getElementById('id_username');
    const password = document.getElementById('id_password');
    const usernameError = document.getElementById('username-error');
    const passwordError = document.getElementById('password-error');
    const clientError = document.getElementById('client-error');
    const submitBtn = document.getElementById('submit-btn');

    function showFieldError(field, errorElement, message) {
        field.classList.add('error');
        errorElement.textContent = message;
        errorElement.classList.add('visible');
    }

    function clearFieldError(field, errorElement) {
        field.classList.remove('error');
        errorElement.classList.remove('visible');
        errorElement.textContent = '';
    }

    function showGlobalError(message) {
        clientError.textContent = message;
        clientError.style.display = 'block';
    }

    function clearGlobalError() {
        clientError.style.display = 'none';
        clientError.textContent = '';
    }

    function validateField(field, errorElement) {
        if (!field.value.trim()) {
            showFieldError(field, errorElement, `${field.name.charAt(0).toUpperCase() + field.name.slice(1)} is required`);
            return false;
        }
        clearFieldError(field, errorElement);
        return true;
    }

    // Real-time validation
    username.addEventListener('input', () => {
        validateField(username, usernameError);
        if (username.value.trim() && password.value.trim()) {
            clearGlobalError();
        }
    });

    password.addEventListener('input', () => {
        validateField(password, passwordError);
        if (username.value.trim() && password.value.trim()) {
            clearGlobalError();
        }
    });

    form.addEventListener('submit', async function(e) {
        e.preventDefault();

        clearGlobalError();
        const isUsernameValid = validateField(username, usernameError);
        const isPasswordValid = validateField(password, passwordError);

        if (!isUsernameValid || !isPasswordValid) {
            showGlobalError('Please fill in all required fields');
            return;
        }

        submitBtn.disabled = true;
        submitBtn.textContent = 'Logging in...';

        try {
            const formData = new FormData(form);
            const response = await fetch(form.action, {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                },
                body: formData,
                credentials: 'same-origin'
            });

            const data = await response.json();

            if (data.status === 'success') {
                window.location.href = data.redirect_url;
            } else {
                showGlobalError(data.message || 'Invalid username or password');
            }
        } catch (error) {
            console.error('Login error:', error);
            showGlobalError('An error occurred. Please try again');
        } finally {
            submitBtn.disabled = false;
            submitBtn.textContent = 'Log in';
        }
    });
})();
</script>
{% endblock %}