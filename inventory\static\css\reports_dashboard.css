/* Reports Dashboard - Professional Styling */

/* General Styles */
.separator {
    height: 1px;
    background-image: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.1), transparent);
    margin: 1rem 0;
}

/* Statistics Cards */
.stat-card {
    transition: all 0.3s ease;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.icon-circle {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.stat-card:hover .icon-circle {
    transform: scale(1.1);
}

.counter-value {
    transition: all 0.2s ease;
}

/* Report Cards */
.report-card {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    overflow: hidden;
}

/* Card Header Gradients */
.bg-gradient-primary {
    background: linear-gradient(45deg, #2c3e50, #3498db) !important;
}

.bg-gradient-success {
    background: linear-gradient(45deg, #27ae60, #2ecc71) !important;
}

.bg-gradient-warning {
    background: linear-gradient(45deg, #f39c12, #f1c40f) !important;
}

.bg-gradient-info {
    background: linear-gradient(45deg, #3498db, #5dade2) !important;
}

.report-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

.report-card .card-header {
    border-bottom: none;
    position: relative;
    z-index: 5;
    padding: 1rem 1.25rem;
}

.report-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Form Controls */
.form-control-professional {
    border-radius: 0.375rem;
    border: 1px solid #ced4da;
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
}

.form-control-professional:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.format-selector {
    border: 1px solid #ced4da;
    border-radius: 0.375rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
}

.form-check-input:checked {
    background-color: #2c3e50;
    border-color: #2c3e50;
}

.custom-checkbox .form-check-input:checked ~ .form-check-label {
    font-weight: 600;
}

/* Generate Button */
.btn-generate {
    padding: 0.6rem 1rem;
    font-weight: 600;
    border-radius: 0.375rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.btn-generate:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Card States */
.card-loading {
    position: relative;
}

.card-loading::after {
    content: '';
    position: absolute;
    top: 60px; /* Leave space for the header */
    left: 0;
    width: 100%;
    height: calc(100% - 60px); /* Adjust height to exclude header */
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 4; /* Lower than header z-index */
    border-radius: 0 0 0.5rem 0.5rem; /* Round only bottom corners */
}

.card-loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
    z-index: 5;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

.card-success {
    animation: successPulse 1.5s ease;
}

.card-error {
    animation: errorPulse 1.5s ease;
}

@keyframes successPulse {
    0% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0.7); }
    70% { box-shadow: 0 0 0 15px rgba(46, 204, 113, 0); }
    100% { box-shadow: 0 0 0 0 rgba(46, 204, 113, 0); }
}

@keyframes errorPulse {
    0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7); }
    70% { box-shadow: 0 0 0 15px rgba(231, 76, 60, 0); }
    100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
}

/* Toast Styling */
.toast {
    border: none;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border-radius: 0.5rem;
    overflow: hidden;
}

.toast-header {
    border-bottom: none;
    padding: 0.75rem 1rem;
}

.toast-body {
    padding: 1rem;
    font-weight: 500;
}

/* Responsive Adjustments */
@media (max-width: 992px) {
    .report-card {
        margin-bottom: 1.5rem;
    }
}

@media (max-width: 768px) {
    .icon-circle {
        width: 40px;
        height: 40px;
    }

    .report-icon {
        width: 28px;
        height: 28px;
    }
}
