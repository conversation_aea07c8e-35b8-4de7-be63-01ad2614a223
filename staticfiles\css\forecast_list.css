/* Enhanced Medical Inventory Forecast Animations */

/* Card Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }

    100% {
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        background-position: -1000px 0;
    }

    100% {
        background-position: 1000px 0;
    }
}

@keyframes rotateIcon {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

@keyframes countUp {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Apply animations to elements */
.card {
    animation: fadeInUp 0.6s ease backwards;
}

    .card:nth-child(1) {
        animation-delay: 0.1s;
    }

    .card:nth-child(2) {
        animation-delay: 0.2s;
    }

    .card:nth-child(3) {
        animation-delay: 0.3s;
    }

    .card:nth-child(4) {
        animation-delay: 0.4s;
    }

/* Icon Animations */
.icon-bg i {
    transition: transform 0.3s ease;
}

.card:hover .icon-bg i {
    animation: rotateIcon 2s infinite linear;
}

/* Table Row Animation */
.forecast-row {
    animation: fadeInUp 0.5s ease backwards;
    transition: all 0.3s ease;
}

    .forecast-row:nth-child(1) {
        animation-delay: 0.1s;
    }

    .forecast-row:nth-child(2) {
        animation-delay: 0.15s;
    }

    .forecast-row:nth-child(3) {
        animation-delay: 0.2s;
    }

    .forecast-row:nth-child(4) {
        animation-delay: 0.25s;
    }

    .forecast-row:nth-child(5) {
        animation-delay: 0.3s;
    }

    .forecast-row:nth-child(6) {
        animation-delay: 0.35s;
    }

    .forecast-row:nth-child(7) {
        animation-delay: 0.4s;
    }

    .forecast-row:nth-child(8) {
        animation-delay: 0.45s;
    }

/* Button Animations */
.btn-primary:hover {
    animation: pulse 1s infinite;
}

#refreshData:hover i {
    animation: rotateIcon 1s linear;
}

/* Filter Pill Animations */
.filter-pills .btn {
    transition: all 0.3s ease;
}

    .filter-pills .btn:hover {
        transform: translateY(-2px);
    }

    .filter-pills .btn.active {
        animation: pulse 2s infinite;
    }

/* Accuracy Chart Animation */
.accuracy-value {
    animation: fadeInUp 1s ease forwards;
    stroke-dasharray: 0 100;
    animation-delay: 0.5s;
}

@keyframes fillChart {
    from {
        stroke-dasharray: 0 100;
    }

    to {
        stroke-dasharray: var(--accuracy) calc(100 - var(--accuracy));
    }
}

.accuracy-chart {
    --accuracy: attr(data-percentage number);
}

    .accuracy-chart[data-percentage] .accuracy-value {
        animation: fillChart 1.5s ease forwards 0.5s;
    }

/* Placeholder Animations */
.placeholder {
    overflow: hidden;
    position: relative;
}

    .placeholder::after {
        content: "";
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
        animation: shimmer 2s infinite;
        transform: translateX(-100%);
    }

/* Count Animation */
#requiredOrdersCount:not(.placeholder-glow),
#averageAccuracy:not(.placeholder-glow),
#lastUpdated:not(.placeholder-glow) {
    animation: countUp 0.5s ease forwards;
}

/* Alert Animations */
.alert {
    animation: fadeInUp 0.5s ease;
}

/* Stats Overview Section Animations */
.stats-overview {
    animation: fadeInUp 0.8s ease;
}

.stat-item {
    animation: fadeInUp 0.6s ease backwards;
}

    .stat-item:nth-child(1) {
        animation-delay: 0.2s;
    }

    .stat-item:nth-child(2) {
        animation-delay: 0.3s;
    }

    .stat-item:nth-child(3) {
        animation-delay: 0.4s;
    }

    .stat-item:nth-child(4) {
        animation-delay: 0.5s;
    }

    .stat-item:hover {
        animation: pulse 2s infinite;
    }

/* Modal Animations */
.modal.fade .modal-dialog {
    transform: scale(0.9);
    opacity: 0;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1);
    opacity: 1;
}
