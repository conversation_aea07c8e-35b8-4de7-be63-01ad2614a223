from django.http import HttpResponse
from django.utils import timezone
from django.db.models import Sum, Count, F, Q
from datetime import timedelta
import logging
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import PatternFill, Font
from .excel_utils import apply_professional_styling, auto_adjust_columns, apply_row_styling, create_excel_response
from reportlab.platypus import Paragraph, Spacer, Table, TableStyle
from reportlab.lib import colors
from reportlab.lib.units import inch
from io import BytesIO

# Import PDF utilities function when needed
def get_pdf_utils():
    from .pdf_utils import create_pdf_styles, create_pdf_table, create_pdf_response
    return create_pdf_styles, create_pdf_table, create_pdf_response

# Import models when needed
def get_models():
    from .models import Medicine, Transaction, AuditTrail
    return Medicine, Transaction, AuditTrail

# Define add_total_row function if it's not available in excel_utils
def add_total_row(ws, row_num, data, num_columns):
    """Add a total row with proper styling"""
    # Add a separator row before totals
    row_num += 1

    # Add the total row
    for col_num, value in enumerate(data, 1):
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        cell = ws.cell(row=row_num, column=col_num, value=value)
        cell.font = Font(bold=True)

        # Add borders
        bottom_border = Side(border_style="thin", color="2C3E50")
        top_border = Side(border_style="thin", color="2C3E50")
        cell.border = Border(top=top_border, bottom=bottom_border)

        # Add background color
        cell.fill = PatternFill(start_color="ECF0F1", end_color="ECF0F1", fill_type="solid")

        # Right-align numeric values
        if col_num > 1 or (isinstance(value, str) and value.startswith('Total')):
            if isinstance(value, (int, float)) or (isinstance(value, str) and (value.startswith('₱') or value.startswith('$'))):
                cell.alignment = Alignment(horizontal="right")

    return row_num + 1


logger = logging.getLogger(__name__)

def generate_audit_report(request, start_date, end_date, file_format, action_type='all'):
    """Generate an audit trail report in Excel or PDF format"""
    try:
        # Get models
        Medicine, Transaction, AuditTrail = get_models()

        # Get audit data
        audit_entries = AuditTrail.objects.filter(
            timestamp__range=[start_date, end_date]
        ).select_related('user', 'content_type').order_by('-timestamp')

        # Filter by action type if specified
        if action_type != 'all':
            audit_entries = audit_entries.filter(action=action_type)

        # Generate report title
        report_title = f"Audit Trail Report ({start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')})"

        if file_format == 'excel':
            # Create Excel workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Audit Trail"

            # Define headers
            headers = ['Timestamp', 'User', 'Action', 'Model', 'Object ID', 'Details']

            # Add metadata
            metadata = {
                'Period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                'Generated on': timezone.now().strftime('%Y-%m-%d %H:%M'),
                'Generated by': request.user.get_full_name() or request.user.username,
                'Total Entries': str(audit_entries.count())
            }

            # Apply styling and get starting row
            data_row = apply_professional_styling(ws, report_title, metadata, headers)

            # Add data rows
            row_num = data_row
            for i, entry in enumerate(audit_entries):
                # Get user name
                user_name = entry.user.get_full_name() if entry.user else 'System'
                if not user_name.strip():
                    user_name = entry.user.username if entry.user else 'System'

                row_data = [
                    entry.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    user_name,
                    entry.action,
                    entry.content_type.model.capitalize() if entry.content_type else 'Unknown',
                    str(entry.object_id) if entry.object_id else 'N/A',
                    entry.details or 'No details provided'
                ]

                # Apply alternating row styling
                row_type = 'even' if i % 2 == 1 else None
                row_num = apply_row_styling(ws, row_num, row_data, row_type)

            # Auto-adjust column widths
            auto_adjust_columns(ws)

            # Return Excel response
            return create_excel_response(wb, 'audit_trail')

        elif file_format == 'pdf':
            # Get PDF utilities
            create_pdf_styles, create_pdf_table, create_pdf_response = get_pdf_utils()

            # Create PDF
            styles = create_pdf_styles()
            elements = []

            # Add title
            elements.append(Paragraph(report_title, styles['title']))
            elements.append(Spacer(1, 0.25*inch))

            # Add metadata
            metadata_text = f"Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}<br/>"
            metadata_text += f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}<br/>"
            metadata_text += f"Generated by: {request.user.get_full_name() or request.user.username}<br/>"
            metadata_text += f"Total Entries: {audit_entries.count()}"

            elements.append(Paragraph(metadata_text, styles['metadata']))
            elements.append(Spacer(1, 0.25*inch))

            # Prepare table data
            table_data = [['Timestamp', 'User', 'Action', 'Model', 'Object ID', 'Details']]

            for entry in audit_entries:
                # Get user name
                user_name = entry.user.get_full_name() if entry.user else 'System'
                if not user_name.strip():
                    user_name = entry.user.username if entry.user else 'System'

                table_data.append([
                    entry.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                    user_name,
                    entry.action,
                    entry.content_type.model.capitalize() if entry.content_type else 'Unknown',
                    str(entry.object_id) if entry.object_id else 'N/A',
                    entry.details or 'No details provided'
                ])

            # Create and add table
            table = create_pdf_table(table_data)
            elements.append(table)

            # Return PDF response
            return create_pdf_response(
                elements,
                report_title,
                'audit_trail_report',
                landscape_mode=True
            )

    except Exception as e:
        logger.error(f"Error generating audit report: {str(e)}")
        raise

def generate_sales_report(request, start_date, end_date, file_format):
    """Generate a sales report in Excel or PDF format"""
    try:
        # Get models
        Medicine, Transaction = get_models()

        # Get sales data
        sales_data = Transaction.objects.filter(
            transaction_type='sale',
            transaction_date__range=[start_date, end_date]
        ).select_related('medicine')

        # Calculate totals
        total_sales = sales_data.aggregate(total=Sum('total_amount'))['total'] or 0
        total_quantity = sales_data.aggregate(total=Sum('quantity'))['total'] or 0

        # Generate report title
        report_title = f"Sales Report ({start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')})"

        if file_format == 'excel':
            # Create Excel workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Sales Report"

            # Define headers
            headers = ['Date', 'Medicine', 'Quantity', 'Unit Price', 'Total Amount', 'Customer Type', 'Payment Method']

            # Add metadata
            metadata = {
                'Report Period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                'Generated on': timezone.now().strftime('%Y-%m-%d %H:%M'),
                'Generated by': request.user.get_full_name() or request.user.username,
                'Total Sales': f"₱{total_sales:,.2f}",
                'Total Items Sold': str(total_quantity)
            }

            # Apply styling and get starting row
            data_row = apply_professional_styling(ws, report_title, metadata, headers)

            # Add data rows
            row_num = data_row
            for i, sale in enumerate(sales_data.order_by('-transaction_date')):
                row_data = [
                    sale.transaction_date.strftime('%Y-%m-%d %H:%M'),
                    sale.medicine.name,
                    sale.quantity,
                    f"₱{sale.medicine.price:,.2f}",
                    f"₱{sale.total_amount:,.2f}",
                    sale.customer_type,
                    sale.payment_method or 'N/A'
                ]

                # Apply alternating row styling
                row_type = 'even' if i % 2 == 1 else None
                row_num = apply_row_styling(ws, row_num, row_data, row_type)

            # Add total row
            total_row = ['Total', '', total_quantity, '', f"₱{total_sales:,.2f}", '', '']
            add_total_row(ws, row_num, total_row, len(headers))

            # Auto-adjust column widths
            auto_adjust_columns(ws)

            # Return Excel response
            return create_excel_response(wb, 'sales')

        elif file_format == 'pdf':
            # Get PDF utilities
            create_pdf_styles, create_pdf_table, create_pdf_response = get_pdf_utils()

            # Create PDF
            styles = create_pdf_styles()
            elements = []

            # Add title
            elements.append(Paragraph(report_title, styles['title']))
            elements.append(Spacer(1, 0.25*inch))

            # Add metadata
            metadata_text = f"Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}<br/>"
            metadata_text += f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}<br/>"
            metadata_text += f"Generated by: {request.user.get_full_name() or request.user.username}<br/>"
            metadata_text += f"Total Sales: ₱{total_sales:,.2f}<br/>"
            metadata_text += f"Total Items Sold: {total_quantity}"

            elements.append(Paragraph(metadata_text, styles['metadata']))
            elements.append(Spacer(1, 0.25*inch))

            # Prepare table data
            table_data = [['Date', 'Medicine', 'Quantity', 'Unit Price', 'Total Amount', 'Customer Type']]

            for sale in sales_data.order_by('-transaction_date'):
                table_data.append([
                    sale.transaction_date.strftime('%Y-%m-%d %H:%M'),
                    sale.medicine.name,
                    str(sale.quantity),
                    f"₱{sale.medicine.price:,.2f}",
                    f"₱{sale.total_amount:,.2f}",
                    sale.customer_type
                ])

            # Add total row
            table_data.append(['Total', '', str(total_quantity), '', f"₱{total_sales:,.2f}", ''])

            # Create table with custom styling for the total row
            table_style = [
                ('BACKGROUND', (0, -1), (-1, -1), colors.Color(0.93, 0.94, 0.95)),
                ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
                ('LINEABOVE', (0, -1), (-1, -1), 1, colors.Color(0.17, 0.24, 0.31)),
            ]

            # Create and add table
            table = create_pdf_table(table_data, style=table_style)
            elements.append(table)

            # Return PDF response
            return create_pdf_response(
                elements,
                report_title,
                'sales_report',
                landscape_mode=True
            )

    except Exception as e:
        logger.error(f"Error generating sales report: {str(e)}")
        raise

def generate_inventory_report(request, file_format):
    """Generate an inventory report in Excel or PDF format"""
    try:
        # Get models
        Medicine, Transaction = get_models()

        # Get inventory data
        medicines = Medicine.objects.all().order_by('name')

        # Calculate totals
        total_value = sum(medicine.quantity * medicine.price for medicine in medicines)
        total_items = medicines.count()
        low_stock_count = medicines.filter(quantity__lte=F('reorder_level')).count()

        # Generate report title
        report_title = "Inventory Status Report"

        if file_format == 'excel':
            # Create Excel workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Inventory Report"

            # Define headers
            headers = ['Medicine', 'Category', 'Current Stock', 'Reorder Level',
                      'Unit Price', 'Total Value', 'Status']

            # Add metadata
            metadata = {
                'Generated on': timezone.now().strftime('%Y-%m-%d %H:%M'),
                'Generated by': request.user.get_full_name() or request.user.username,
                'Total Items': str(total_items),
                'Total Inventory Value': f"₱{total_value:,.2f}",
                'Low Stock Items': str(low_stock_count)
            }

            # Apply styling and get starting row
            data_row = apply_professional_styling(ws, report_title, metadata, headers)

            # Add data rows
            row_num = data_row
            for i, medicine in enumerate(medicines):
                # Calculate item value
                item_value = medicine.quantity * medicine.price

                # Determine status based on stock level and expiry date
                today = timezone.now().date()

                # Check if medicine has an expiry date and if it's expired
                if hasattr(medicine, 'expiry_date') and medicine.expiry_date and medicine.expiry_date < today:
                    status = "EXPIRED"
                    row_type = 'critical'
                elif medicine.quantity <= 0:
                    status = "OUT OF STOCK"
                    row_type = 'critical'
                elif medicine.quantity <= medicine.reorder_level:
                    status = "LOW STOCK"
                    row_type = 'warning'
                else:
                    status = "IN STOCK"
                    row_type = 'even' if i % 2 == 1 else None

                row_data = [
                    medicine.name,
                    medicine.category or 'Uncategorized',
                    medicine.quantity,
                    medicine.reorder_level,
                    f"₱{medicine.price:,.2f}",
                    f"₱{item_value:,.2f}",
                    status
                ]

                row_num = apply_row_styling(ws, row_num, row_data, row_type)

            # Add total row
            total_row = ['Total', f"{total_items} items", sum(m.quantity for m in medicines), '', '',
                        f"₱{total_value:,.2f}", '']
            add_total_row(ws, row_num, total_row, len(headers))

            # Auto-adjust column widths
            auto_adjust_columns(ws)

            # Return Excel response
            return create_excel_response(wb, 'inventory')

        elif file_format == 'pdf':
            # Get PDF utilities
            create_pdf_styles, create_pdf_table, create_pdf_response = get_pdf_utils()

            # Create PDF
            styles = create_pdf_styles()
            elements = []

            # Add title
            elements.append(Paragraph(report_title, styles['title']))
            elements.append(Spacer(1, 0.25*inch))

            # Add metadata
            metadata_text = f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}<br/>"
            metadata_text += f"Generated by: {request.user.get_full_name() or request.user.username}<br/>"
            metadata_text += f"Total Items: {total_items}<br/>"
            metadata_text += f"Total Inventory Value: ₱{total_value:,.2f}<br/>"
            metadata_text += f"Low Stock Items: {low_stock_count}"

            elements.append(Paragraph(metadata_text, styles['metadata']))
            elements.append(Spacer(1, 0.25*inch))

            # Prepare table data
            table_data = [['Medicine', 'Category', 'Current Stock', 'Reorder Level',
                          'Unit Price', 'Total Value', 'Status']]

            # Track row indices for styling
            critical_rows = []
            warning_rows = []

            for i, medicine in enumerate(medicines):
                # Calculate item value
                item_value = medicine.quantity * medicine.price

                # Determine status based on stock level and expiry date
                today = timezone.now().date()

                # Check if medicine has an expiry date and if it's expired
                if hasattr(medicine, 'expiry_date') and medicine.expiry_date and medicine.expiry_date < today:
                    status = "EXPIRED"
                    critical_rows.append(i + 1)  # +1 because header is row 0
                elif medicine.quantity <= 0:
                    status = "OUT OF STOCK"
                    critical_rows.append(i + 1)
                elif medicine.quantity <= medicine.reorder_level:
                    status = "LOW STOCK"
                    warning_rows.append(i + 1)
                else:
                    status = "IN STOCK"

                table_data.append([
                    medicine.name,
                    medicine.category or 'Uncategorized',
                    str(medicine.quantity),
                    str(medicine.reorder_level),
                    f"₱{medicine.price:,.2f}",
                    f"₱{item_value:,.2f}",
                    status
                ])

            # Add total row
            table_data.append(['Total', f"{total_items} items", str(sum(m.quantity for m in medicines)), '', '',
                              f"₱{total_value:,.2f}", ''])

            # Create table style with conditional formatting
            table_style = [
                # Total row styling
                ('BACKGROUND', (0, -1), (-1, -1), colors.Color(0.93, 0.94, 0.95)),
                ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
                ('LINEABOVE', (0, -1), (-1, -1), 1, colors.Color(0.17, 0.24, 0.31)),
            ]

            # Add styling for critical rows
            for row in critical_rows:
                table_style.append(('BACKGROUND', (0, row), (-1, row), colors.Color(0.90, 0.30, 0.24, 0.3)))
                table_style.append(('TEXTCOLOR', (6, row), (6, row), colors.Color(0.90, 0.30, 0.24)))
                table_style.append(('FONTNAME', (6, row), (6, row), 'Helvetica-Bold'))

            # Add styling for warning rows
            for row in warning_rows:
                table_style.append(('BACKGROUND', (0, row), (-1, row), colors.Color(0.95, 0.61, 0.07, 0.2)))
                table_style.append(('TEXTCOLOR', (6, row), (6, row), colors.Color(0.95, 0.61, 0.07)))
                table_style.append(('FONTNAME', (6, row), (6, row), 'Helvetica-Bold'))

            # Create and add table
            table = create_pdf_table(table_data, style=table_style)
            elements.append(table)

            # Return PDF response
            return create_pdf_response(
                elements,
                report_title,
                'inventory_report',
                landscape_mode=True
            )

    except Exception as e:
        logger.error(f"Error generating inventory report: {str(e)}")
        raise

def generate_expiry_report(request, expiry_range, file_format):
    """Generate a report of medicines that are expiring soon or have already expired"""
    try:
        # Get models
        Medicine, Transaction, AuditTrail = get_models()

        # Get current date
        today = timezone.now().date()

        # Define expiry ranges
        if expiry_range == 'expired':
            # Only expired medicines
            medicines = Medicine.objects.filter(expiry_date__lt=today).order_by('expiry_date')
            report_title = "Expired Medicines Report"
        elif expiry_range == 'expiring_soon':
            # Medicines expiring in the next 90 days
            expiry_threshold = today + timedelta(days=90)
            medicines = Medicine.objects.filter(
                expiry_date__gte=today,
                expiry_date__lte=expiry_threshold
            ).order_by('expiry_date')
            report_title = "Medicines Expiring Soon Report"
        else:  # 'all'
            # Both expired and expiring soon
            expiry_threshold = today + timedelta(days=90)
            medicines = Medicine.objects.filter(
                expiry_date__lte=expiry_threshold
            ).order_by('expiry_date')
            report_title = "Medicines Expiry Report"

        # Calculate totals
        expired_count = sum(1 for m in medicines if m.expiry_date < today)
        expiring_soon_count = sum(1 for m in medicines if m.expiry_date >= today)
        total_value = sum(m.quantity * m.price for m in medicines)

        if file_format == 'excel':
            # Create Excel workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Expiry Report"

            # Define headers
            headers = ['Medicine Name', 'Category', 'Batch Number', 'Expiry Date',
                      'Days Until Expiry', 'Current Stock', 'Unit Price', 'Total Value', 'Status']

            # Add metadata
            metadata = {
                'Generated on': timezone.now().strftime('%Y-%m-%d %H:%M'),
                'Generated by': request.user.get_full_name() or request.user.username,
                'Total Items': str(medicines.count()),
                'Expired Items': str(expired_count),
                'Expiring Soon Items': str(expiring_soon_count),
                'Total Value at Risk': f"₱{total_value:,.2f}"
            }

            # Apply styling and get starting row
            data_row = apply_professional_styling(ws, report_title, metadata, headers)

            # Add data rows
            row_num = data_row
            for i, medicine in enumerate(medicines):
                # Calculate days until expiry (negative for expired items)
                days_until_expiry = (medicine.expiry_date - today).days

                # Calculate item value
                item_value = medicine.quantity * medicine.price

                # Determine status and styling
                if days_until_expiry < 0:
                    status = "EXPIRED"
                    row_type = 'critical'
                elif days_until_expiry <= 30:
                    status = f"EXPIRES IN {days_until_expiry} DAYS"
                    row_type = 'warning'
                else:
                    status = f"EXPIRES IN {days_until_expiry} DAYS"
                    row_type = 'even' if i % 2 == 1 else None

                # Format expiry date
                expiry_date_formatted = medicine.expiry_date.strftime('%Y-%m-%d')

                row_data = [
                    medicine.name,
                    medicine.category or 'Uncategorized',
                    medicine.batch_number or 'N/A',
                    expiry_date_formatted,
                    days_until_expiry,
                    medicine.quantity,
                    f"₱{medicine.price:,.2f}",
                    f"₱{item_value:,.2f}",
                    status
                ]

                row_num = apply_row_styling(ws, row_num, row_data, row_type)

            # Auto-adjust column widths
            auto_adjust_columns(ws)

            # Return Excel response
            return create_excel_response(wb, 'expiry_report')

        elif file_format == 'pdf':
            # Get PDF utilities
            create_pdf_styles, create_pdf_table, create_pdf_response = get_pdf_utils()

            # Create PDF
            styles = create_pdf_styles()
            elements = []

            # Add title
            elements.append(Paragraph(report_title, styles['title']))
            elements.append(Spacer(1, 0.25*inch))

            # Add metadata
            metadata_text = f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}<br/>"
            metadata_text += f"Generated by: {request.user.get_full_name() or request.user.username}<br/>"
            metadata_text += f"Total Items: {medicines.count()}<br/>"
            metadata_text += f"Expired Items: {expired_count}<br/>"
            metadata_text += f"Expiring Soon Items: {expiring_soon_count}<br/>"
            metadata_text += f"Total Value at Risk: ₱{total_value:,.2f}"

            elements.append(Paragraph(metadata_text, styles['metadata']))
            elements.append(Spacer(1, 0.25*inch))

            # Prepare table data
            table_data = [['Medicine Name', 'Category', 'Expiry Date', 'Days Until Expiry',
                          'Current Stock', 'Unit Price', 'Total Value', 'Status']]

            # Track rows for styling
            critical_rows = []
            warning_rows = []

            for i, medicine in enumerate(medicines):
                # Calculate days until expiry
                days_until_expiry = (medicine.expiry_date - today).days

                # Calculate item value
                item_value = medicine.quantity * medicine.price

                # Determine status and track for styling
                if days_until_expiry < 0:
                    status = "EXPIRED"
                    critical_rows.append(i + 1)  # +1 because header is row 0
                elif days_until_expiry <= 30:
                    status = f"EXPIRES IN {days_until_expiry} DAYS"
                    warning_rows.append(i + 1)
                else:
                    status = f"EXPIRES IN {days_until_expiry} DAYS"

                # Format expiry date
                expiry_date_formatted = medicine.expiry_date.strftime('%Y-%m-%d')

                table_data.append([
                    medicine.name,
                    medicine.category or 'Uncategorized',
                    expiry_date_formatted,
                    str(days_until_expiry),
                    str(medicine.quantity),
                    f"₱{medicine.price:,.2f}",
                    f"₱{item_value:,.2f}",
                    status
                ])

            # Create table style with conditional formatting
            table_style = [
                # Header styling
                ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.17, 0.24, 0.31)),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                # Data styling
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                ('ALIGN', (0, 1), (2, -1), 'LEFT'),  # Left align text columns
                ('ALIGN', (3, 1), (6, -1), 'RIGHT'),  # Right align numeric columns
                ('ALIGN', (7, 1), (7, -1), 'CENTER'),  # Center align status
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 1), (-1, -1), 6),
                # Grid styling
                ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.85, 0.85, 0.85))
            ]

            # Add conditional formatting for expired and expiring soon items
            for row in critical_rows:
                table_style.append(('BACKGROUND', (0, row), (-1, row), colors.Color(1.0, 0.8, 0.8)))

            for row in warning_rows:
                table_style.append(('BACKGROUND', (0, row), (-1, row), colors.Color(1.0, 0.95, 0.8)))

            # Create and add table
            table = create_pdf_table(table_data, style=table_style)
            elements.append(table)

            # Return PDF response
            return create_pdf_response(
                elements,
                report_title,
                'expiry_report',
                landscape_mode=True
            )

    except Exception as e:
        logger.error(f"Error generating expiry report: {str(e)}")
        raise

def generate_forecast_accuracy_report(request, time_period, file_format, forecast_method='all'):
    """Generate a forecast accuracy report in Excel or PDF format"""
    try:
        # Get models
        Medicine, Transaction, AuditTrail = get_models()
        from .models import Forecast

        # Get current date
        today = timezone.now().date()

        # Determine date range based on time period
        if time_period == 'last_month':
            start_date = today - timedelta(days=30)
            period_name = "Last 30 Days"
        elif time_period == 'last_3_months':
            start_date = today - timedelta(days=90)
            period_name = "Last 3 Months"
        elif time_period == 'last_6_months':
            start_date = today - timedelta(days=180)
            period_name = "Last 6 Months"
        elif time_period == 'last_year':
            start_date = today - timedelta(days=365)
            period_name = "Last Year"
        else:  # all_time
            start_date = today - timedelta(days=1095)  # 3 years
            period_name = "All Time"

        # Get forecasts from the specified period
        forecasts = Forecast.objects.filter(
            forecast_date__gte=start_date,
            forecast_date__lte=today
        ).select_related('medicine')

        # Filter by forecast method if specified
        if forecast_method != 'all':
            forecasts = forecasts.filter(method=forecast_method)
            method_display = forecast_method.upper()
        else:
            method_display = "All Methods"

        # Generate report title
        report_title = f"Forecast Accuracy Report - {period_name} ({method_display})"

        # Calculate accuracy metrics
        accuracy_data = []

        # Group forecasts by medicine and method
        medicine_method_forecasts = {}

        for forecast in forecasts:
            key = (forecast.medicine.id, forecast.method)
            if key not in medicine_method_forecasts:
                medicine_method_forecasts[key] = []
            medicine_method_forecasts[key].append(forecast)

        # Calculate accuracy metrics for each medicine and method
        for (medicine_id, method), medicine_forecasts in medicine_method_forecasts.items():
            # Get the medicine
            medicine = Medicine.objects.get(id=medicine_id)

            # Calculate accuracy metrics
            total_absolute_error = 0
            total_percentage_error = 0
            total_squared_error = 0
            total_forecasts = 0

            for forecast in medicine_forecasts:
                if forecast.actual_value is not None:
                    forecast_value = forecast.forecasted_value
                    actual_value = forecast.actual_value

                    # Calculate error metrics
                    absolute_error = abs(forecast_value - actual_value)
                    squared_error = (forecast_value - actual_value) ** 2

                    # Calculate percentage error (avoid division by zero)
                    if actual_value > 0:
                        percentage_error = (absolute_error / actual_value) * 100
                    else:
                        percentage_error = 100 if forecast_value > 0 else 0

                    total_forecasts += 1
                    total_absolute_error += absolute_error
                    total_squared_error += squared_error
                    total_percentage_error += percentage_error

            # Calculate average metrics
            if total_forecasts > 0:
                mae = total_absolute_error / total_forecasts
                mape = total_percentage_error / total_forecasts
                rmse = (total_squared_error / total_forecasts) ** 0.5
                accuracy = max(0, 100 - mape)  # Convert MAPE to accuracy percentage

                accuracy_data.append({
                    'medicine': medicine,
                    'method': method,
                    'forecasts': total_forecasts,
                    'mae': mae,
                    'mape': mape,
                    'rmse': rmse,
                    'accuracy': accuracy
                })

        # Sort by accuracy (descending)
        accuracy_data.sort(key=lambda x: x['accuracy'], reverse=True)

        if file_format == 'excel':
            # Create Excel workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Forecast Accuracy"

            # Define headers
            headers = ['Medicine', 'Method', 'Forecasts', 'Accuracy', 'MAE', 'MAPE', 'RMSE']

            # Add metadata
            metadata = {
                'Period': period_name,
                'Methods': method_display,
                'Generated on': timezone.now().strftime('%Y-%m-%d %H:%M'),
                'Generated by': request.user.get_full_name() or request.user.username,
                'Total Medicines': str(len(accuracy_data))
            }

            # Apply styling and get starting row
            data_row = apply_professional_styling(ws, report_title, metadata, headers)

            # Add data rows
            row_num = data_row

            # Accuracy-based colors
            high_accuracy_fill = PatternFill(start_color="E6F4EA", end_color="E6F4EA", fill_type="solid")
            medium_accuracy_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
            low_accuracy_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")

            # Add data rows
            row_num = 7
            for item in accuracy_data:
                # Determine row styling based on accuracy
                if item['accuracy'] >= 90:
                    row_fill = high_accuracy_fill
                elif item['accuracy'] >= 70:
                    row_fill = medium_accuracy_fill
                else:
                    row_fill = low_accuracy_fill

                row_data = [
                    item['medicine'].name,
                    item['method'].upper(),
                    item['forecasts'],
                    f"{item['accuracy']:.2f}%",
                    f"{item['mae']:.2f}",
                    f"{item['mape']:.2f}%",
                    f"{item['rmse']:.2f}"
                ]

                row_num = apply_row_styling(ws, row_num, row_data, row_fill)

            # Add summary if we have data
            if accuracy_data:
                ws.append([])
                row_num += 1

                # Calculate averages
                avg_accuracy = sum(item['accuracy'] for item in accuracy_data) / len(accuracy_data)
                avg_mae = sum(item['mae'] for item in accuracy_data) / len(accuracy_data)
                avg_mape = sum(item['mape'] for item in accuracy_data) / len(accuracy_data)
                avg_rmse = sum(item['rmse'] for item in accuracy_data) / len(accuracy_data)

                # Add summary rows with bold formatting
                apply_row_styling(ws, row_num, ['Average Accuracy:', f"{avg_accuracy:.2f}%"], None, True)
                row_num += 1
                apply_row_styling(ws, row_num, ['Average MAE:', f"{avg_mae:.2f}"], None, True)
                row_num += 1
                apply_row_styling(ws, row_num, ['Average MAPE:', f"{avg_mape:.2f}%"], None, True)
                row_num += 1
                apply_row_styling(ws, row_num, ['Average RMSE:', f"{avg_rmse:.2f}"], None, True)

            # Auto-adjust column widths
            auto_adjust_columns(ws)

            # Return Excel response
            return create_excel_response(wb, 'forecast_accuracy_report')

        elif file_format == 'pdf':
            # Get PDF utilities
            create_pdf_styles, create_pdf_table, create_pdf_response = get_pdf_utils()

            # Create PDF
            styles = create_pdf_styles()
            elements = []

            # Add title
            elements.append(Paragraph(report_title, styles['title']))
            elements.append(Spacer(1, 0.25*inch))

            # Add metadata
            metadata_text = f"Period: {period_name}<br/>"
            metadata_text += f"Methods: {method_display}<br/>"
            metadata_text += f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}<br/>"
            metadata_text += f"Generated by: {request.user.get_full_name() or request.user.username}<br/>"
            metadata_text += f"Total Medicines: {len(accuracy_data)}"

            elements.append(Paragraph(metadata_text, styles['metadata']))
            elements.append(Spacer(1, 0.25*inch))

            # Prepare table data
            table_data = [
                ['Medicine', 'Method', 'Forecasts', 'Accuracy', 'MAE', 'MAPE', 'RMSE']
            ]

            # Add accuracy data
            for item in accuracy_data:
                table_data.append([
                    item['medicine'].name,
                    item['method'].upper(),
                    str(item['forecasts']),
                    f"{item['accuracy']:.2f}%",
                    f"{item['mae']:.2f}",
                    f"{item['mape']:.2f}%",
                    f"{item['rmse']:.2f}"
                ])

            # Create table style with conditional formatting
            style = TableStyle([
                # Header styling
                ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.17, 0.24, 0.31)),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                # Data styling
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                ('ALIGN', (0, 1), (1, -1), 'LEFT'),  # Left align text columns
                ('ALIGN', (2, 1), (-1, -1), 'RIGHT'),  # Right align numeric columns
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('BOTTOMPADDING', (0, 1), (-1, -1), 6),
                # Grid styling
                ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.85, 0.85, 0.85))
            ])

            # Add row styling based on accuracy
            for i, item in enumerate(accuracy_data, 1):
                if item['accuracy'] >= 90:
                    style.add('BACKGROUND', (0, i), (-1, i), colors.HexColor('#E6F4EA'))
                elif item['accuracy'] >= 70:
                    style.add('BACKGROUND', (0, i), (-1, i), colors.HexColor('#FFF2CC'))
                else:
                    style.add('BACKGROUND', (0, i), (-1, i), colors.HexColor('#FFCCCC'))

            # Create and add table
            table = create_pdf_table(table_data, style=style)
            elements.append(table)

            # Add summary if we have data
            if accuracy_data:
                elements.append(Spacer(1, 0.25*inch))

                # Calculate averages
                avg_accuracy = sum(item['accuracy'] for item in accuracy_data) / len(accuracy_data)
                avg_mae = sum(item['mae'] for item in accuracy_data) / len(accuracy_data)
                avg_mape = sum(item['mape'] for item in accuracy_data) / len(accuracy_data)
                avg_rmse = sum(item['rmse'] for item in accuracy_data) / len(accuracy_data)

                summary_data = [
                    ['Metric', 'Value'],
                    ['Average Accuracy', f"{avg_accuracy:.2f}%"],
                    ['Average MAE', f"{avg_mae:.2f}"],
                    ['Average MAPE', f"{avg_mape:.2f}%"],
                    ['Average RMSE', f"{avg_rmse:.2f}"]
                ]

                summary_style = TableStyle([
                    ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.17, 0.24, 0.31)),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                    ('BACKGROUND', (0, 1), (-1, -1), colors.Color(0.95, 0.95, 0.95)),
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.85, 0.85, 0.85))
                ])

                summary_table = create_pdf_table(summary_data, style=summary_style, colWidths=[200, 100])
                elements.append(summary_table)

            # Return PDF response
            return create_pdf_response(
                elements,
                report_title,
                'forecast_accuracy_report',
                landscape_mode=True
            )

    except Exception as e:
        logger.error(f"Error generating forecast accuracy report: {str(e)}")
        raise

def generate_inventory_valuation_report(request, grouping, include_zero_stock, include_expired, file_format):
    """Generate an inventory valuation report in Excel or PDF format"""
    try:
        # Get models
        Medicine, Transaction, AuditTrail = get_models()

        # Get current date
        today = timezone.now().date()

        # Get all medicines
        medicines = Medicine.objects.all()

        # Apply filters
        if not include_zero_stock:
            medicines = medicines.filter(quantity__gt=0)

        if not include_expired:
            medicines = medicines.filter(expiry_date__gte=today)

        # Calculate total inventory value
        total_value = sum(medicine.quantity * medicine.price for medicine in medicines)

        if file_format == 'excel':
            # Create Excel workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Inventory Valuation"

            # Add report title and metadata
            report_title = "Inventory Valuation Report"

            # Add metadata
            metadata = {
                'Generated on': timezone.now().strftime('%Y-%m-%d %H:%M'),
                'Generated by': request.user.get_full_name() or request.user.username,
                'Total Items': str(medicines.count()),
                'Total Value': f"₱{total_value:,.2f}",
                'Grouping': grouping.capitalize() if grouping != 'none' else 'None'
            }

            # Group data based on selected grouping
            if grouping != 'none':
                # Create a dictionary to group medicines
                grouped_data = {}

                for medicine in medicines:
                    if grouping == 'category':
                        group_key = medicine.category or 'Uncategorized'
                    else:  # medicine_type
                        group_key = medicine.medicine_type or 'Uncategorized'

                    if group_key not in grouped_data:
                        grouped_data[group_key] = {
                            'items': [],
                            'total_value': 0,
                            'total_quantity': 0
                        }

                    item_value = medicine.quantity * medicine.price
                    grouped_data[group_key]['items'].append(medicine)
                    grouped_data[group_key]['total_value'] += item_value
                    grouped_data[group_key]['total_quantity'] += medicine.quantity

                # Sort groups by total value (descending)
                sorted_groups = sorted(grouped_data.items(), key=lambda x: x[1]['total_value'], reverse=True)

                # Define headers
                headers = ['Group', 'Total Value', 'Item Count', 'Total Quantity', '% of Total Value']

                # Apply styling and get starting row
                data_row = apply_professional_styling(ws, report_title, metadata, headers)

                # Add group summary rows
                row_num = data_row
                for group_name, group_data in sorted_groups:
                    group_value = group_data['total_value']
                    percentage = (group_value / total_value * 100) if total_value > 0 else 0

                    # Add group row
                    row_data = [
                        group_name,
                        f"₱{group_value:,.2f}",
                        len(group_data['items']),
                        group_data['total_quantity'],
                        f"{percentage:.2f}%"
                    ]

                    row_num = apply_row_styling(ws, row_num, row_data, None, True)

                    # Add a separator row
                    row_num += 1

                    # Add detailed headers for this group
                    detail_headers = ['Medicine Name', 'Quantity', 'Unit Price', 'Total Value', 'Expiry Date', 'Status']
                    for col_num, header in enumerate(detail_headers, 2):
                        cell = ws.cell(row=row_num, column=col_num, value=header)
                        cell.font = Font(bold=True, size=9)

                    row_num += 1

                    # Add medicine details for this group
                    for medicine in sorted(group_data['items'], key=lambda x: x.name):
                        item_value = medicine.quantity * medicine.price
                        status = ""

                        # Determine row styling based on status
                        if medicine.expiry_date < today:
                            status = "EXPIRED"
                            row_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
                        elif medicine.quantity <= medicine.reorder_level:
                            status = "LOW STOCK"
                            row_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
                        elif medicine.quantity == 0:
                            status = "OUT OF STOCK"
                            row_fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
                        else:
                            row_fill = None

                        # Add medicine row
                        row_data = [
                            "",  # Empty first column for indentation
                            medicine.name,
                            medicine.quantity,
                            f"₱{medicine.price:,.2f}",
                            f"₱{item_value:,.2f}",
                            medicine.expiry_date.strftime('%Y-%m-%d'),
                            status
                        ]

                        row_num = apply_row_styling(ws, row_num, row_data, row_fill)

                    # Add a separator row
                    row_num += 1
            else:
                # No grouping, just list all medicines
                headers = ['Medicine Name', 'Category', 'Quantity', 'Unit Price', 'Total Value', 'Expiry Date', 'Status']

                # Apply styling and get starting row
                data_row = apply_professional_styling(ws, report_title, metadata, headers)

                # Add data rows
                row_num = data_row
                for medicine in sorted(medicines, key=lambda x: x.name):
                    item_value = medicine.quantity * medicine.price
                    status = ""

                    # Determine row styling based on status
                    if medicine.expiry_date < today:
                        status = "EXPIRED"
                        row_fill = PatternFill(start_color="FFCCCC", end_color="FFCCCC", fill_type="solid")
                    elif medicine.quantity <= medicine.reorder_level:
                        status = "LOW STOCK"
                        row_fill = PatternFill(start_color="FFF2CC", end_color="FFF2CC", fill_type="solid")
                    elif medicine.quantity == 0:
                        status = "OUT OF STOCK"
                        row_fill = PatternFill(start_color="E6E6E6", end_color="E6E6E6", fill_type="solid")
                    else:
                        row_fill = None

                    # Add medicine row
                    row_data = [
                        medicine.name,
                        medicine.category or 'Uncategorized',
                        medicine.quantity,
                        f"₱{medicine.price:,.2f}",
                        f"₱{item_value:,.2f}",
                        medicine.expiry_date.strftime('%Y-%m-%d'),
                        status
                    ]

                    row_num = apply_row_styling(ws, row_num, row_data, row_fill)

            # Auto-adjust column widths
            auto_adjust_columns(ws)

            # Return Excel response
            return create_excel_response(wb, 'inventory_valuation_report')

        elif file_format == 'pdf':
            # Get PDF utilities
            create_pdf_styles, create_pdf_table, create_pdf_response = get_pdf_utils()

            # Create PDF
            styles = create_pdf_styles()
            elements = []

            # Add title
            report_title = "Inventory Valuation Report"
            elements.append(Paragraph(report_title, styles['title']))
            elements.append(Spacer(1, 0.25*inch))

            # Add metadata
            metadata_text = f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}<br/>"
            metadata_text += f"Generated by: {request.user.get_full_name() or request.user.username}<br/>"
            metadata_text += f"Total Items: {medicines.count()}<br/>"
            metadata_text += f"Total Value: ₱{total_value:,.2f}<br/>"
            metadata_text += f"Grouping: {grouping.capitalize() if grouping != 'none' else 'None'}"

            elements.append(Paragraph(metadata_text, styles['metadata']))
            elements.append(Spacer(1, 0.25*inch))

            # Group data based on selected grouping
            if grouping != 'none':
                # Create a dictionary to group medicines
                grouped_data = {}

                for medicine in medicines:
                    if grouping == 'category':
                        group_key = medicine.category or 'Uncategorized'
                    else:  # medicine_type
                        group_key = medicine.medicine_type or 'Uncategorized'

                    if group_key not in grouped_data:
                        grouped_data[group_key] = {
                            'items': [],
                            'total_value': 0,
                            'total_quantity': 0
                        }

                    item_value = medicine.quantity * medicine.price
                    grouped_data[group_key]['items'].append(medicine)
                    grouped_data[group_key]['total_value'] += item_value
                    grouped_data[group_key]['total_quantity'] += medicine.quantity

                # Sort groups by total value (descending)
                sorted_groups = sorted(grouped_data.items(), key=lambda x: x[1]['total_value'], reverse=True)

                # Add group summary table
                summary_data = [
                    ['Group', 'Total Value', 'Item Count', 'Total Quantity', '% of Total Value']
                ]

                for group_name, group_data in sorted_groups:
                    group_value = group_data['total_value']
                    percentage = (group_value / total_value * 100) if total_value > 0 else 0

                    summary_data.append([
                        group_name or 'Unspecified',
                        f"₱{group_value:,.2f}",
                        str(len(group_data['items'])),
                        str(group_data['total_quantity']),
                        f"{percentage:.2f}%"
                    ])

                # Create summary table style
                summary_style = TableStyle([
                    # Header styling
                    ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.17, 0.24, 0.31)),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 10),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 8),
                    # Data styling
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                    ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Left align group names
                    ('ALIGN', (1, 1), (-1, -1), 'RIGHT'),  # Right align numeric columns
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 9),
                    ('BOTTOMPADDING', (0, 1), (-1, -1), 6),
                    # Grid styling
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.85, 0.85, 0.85))
                ])

                # Create and add summary table
                summary_table = create_pdf_table(summary_data, style=summary_style)
                elements.append(summary_table)
                elements.append(Spacer(1, 0.25*inch))

                # Add detailed tables for each group
                for group_name, group_data in sorted_groups:
                    # Add group heading
                    elements.append(Paragraph(f"{group_name or 'Unspecified'} - ₱{group_data['total_value']:,.2f}", styles['heading2']))
                    elements.append(Spacer(1, 0.1*inch))

                    # Create table data
                    detail_data = [
                        ['Medicine Name', 'Quantity', 'Unit Price', 'Total Value', 'Expiry Date', 'Status']
                    ]

                    for medicine in sorted(group_data['items'], key=lambda x: x.name):
                        item_value = medicine.quantity * medicine.price
                        status = ""

                        if medicine.expiry_date < today:
                            status = "EXPIRED"
                        elif medicine.quantity <= medicine.reorder_level:
                            status = "LOW STOCK"
                        elif medicine.quantity == 0:
                            status = "OUT OF STOCK"

                        detail_data.append([
                            medicine.name,
                            str(medicine.quantity),
                            f"₱{medicine.price:,.2f}",
                            f"₱{item_value:,.2f}",
                            medicine.expiry_date.strftime('%Y-%m-%d'),
                            status
                        ])

                    # Create detail table style
                    detail_style = TableStyle([
                        # Header styling
                        ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.17, 0.24, 0.31)),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                        ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 9),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
                        # Data styling
                        ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                        ('ALIGN', (0, 1), (0, -1), 'LEFT'),  # Left align medicine names
                        ('ALIGN', (1, 1), (4, -1), 'RIGHT'),  # Right align numeric columns
                        ('ALIGN', (5, 1), (5, -1), 'CENTER'),  # Center align status
                        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 1), (-1, -1), 8),
                        ('BOTTOMPADDING', (0, 1), (-1, -1), 4),
                        # Grid styling
                        ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.85, 0.85, 0.85))
                    ])

                    # Add row styling based on status
                    for i, row in enumerate(detail_data[1:], 1):
                        status = row[5]
                        if status == "EXPIRED":
                            detail_style.add('BACKGROUND', (0, i), (-1, i), colors.Color(1.0, 0.8, 0.8))
                        elif status == "LOW STOCK":
                            detail_style.add('BACKGROUND', (0, i), (-1, i), colors.Color(1.0, 0.95, 0.8))
                        elif status == "OUT OF STOCK":
                            detail_style.add('BACKGROUND', (0, i), (-1, i), colors.Color(0.9, 0.9, 0.9))

                    # Create and add detail table
                    detail_table = create_pdf_table(detail_data, style=detail_style)
                    elements.append(detail_table)
                    elements.append(Spacer(1, 0.2*inch))
            else:
                # No grouping, just list all medicines
                data = [
                    ['Medicine Name', 'Category', 'Quantity', 'Unit Price', 'Total Value', 'Expiry Date', 'Status']
                ]

                for medicine in sorted(medicines, key=lambda x: x.name):
                    item_value = medicine.quantity * medicine.price
                    status = ""

                    if medicine.expiry_date < today:
                        status = "EXPIRED"
                    elif medicine.quantity <= medicine.reorder_level:
                        status = "LOW STOCK"
                    elif medicine.quantity == 0:
                        status = "OUT OF STOCK"

                    data.append([
                        medicine.name,
                        medicine.category or 'Uncategorized',
                        str(medicine.quantity),
                        f"₱{medicine.price:,.2f}",
                        f"₱{item_value:,.2f}",
                        medicine.expiry_date.strftime('%Y-%m-%d'),
                        status
                    ])

                # Create table style
                style = TableStyle([
                    # Header styling
                    ('BACKGROUND', (0, 0), (-1, 0), colors.Color(0.17, 0.24, 0.31)),
                    ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                    ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                    ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                    ('FONTSIZE', (0, 0), (-1, 0), 9),
                    ('BOTTOMPADDING', (0, 0), (-1, 0), 6),
                    # Data styling
                    ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                    ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                    ('ALIGN', (0, 1), (1, -1), 'LEFT'),  # Left align text columns
                    ('ALIGN', (2, 1), (4, -1), 'RIGHT'),  # Right align numeric columns
                    ('ALIGN', (6, 1), (6, -1), 'CENTER'),  # Center align status
                    ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                    ('FONTSIZE', (0, 1), (-1, -1), 8),
                    ('BOTTOMPADDING', (0, 1), (-1, -1), 4),
                    # Grid styling
                    ('GRID', (0, 0), (-1, -1), 0.5, colors.Color(0.85, 0.85, 0.85))
                ])

                # Add row styling based on status
                for i, row in enumerate(data[1:], 1):
                    status = row[6]
                    if status == "EXPIRED":
                        style.add('BACKGROUND', (0, i), (-1, i), colors.Color(1.0, 0.8, 0.8))
                    elif status == "LOW STOCK":
                        style.add('BACKGROUND', (0, i), (-1, i), colors.Color(1.0, 0.95, 0.8))
                    elif status == "OUT OF STOCK":
                        style.add('BACKGROUND', (0, i), (-1, i), colors.Color(0.9, 0.9, 0.9))

                # Create and add table
                table = create_pdf_table(data, style=style)
                elements.append(table)

            # Return PDF response
            return create_pdf_response(
                elements,
                report_title,
                'inventory_valuation_report',
                landscape_mode=True
            )

    except Exception as e:
        logger.error(f"Error generating inventory valuation report: {str(e)}")
        raise

def generate_transactions_report(request, start_date, end_date, file_format):
    """Generate a transactions report in Excel or PDF format"""
    try:
        # Get models
        Medicine, Transaction = get_models()

        # Get transactions data
        transactions = Transaction.objects.filter(
            transaction_date__range=[start_date, end_date]
        ).select_related('medicine').order_by('-transaction_date')

        # Calculate totals
        total_sales = transactions.filter(transaction_type='sale').aggregate(total=Sum('total_amount'))['total'] or 0
        total_purchases = transactions.filter(transaction_type='purchase').aggregate(total=Sum('total_amount'))['total'] or 0

        # Generate report title
        report_title = f"Transactions Report ({start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')})"

        if file_format == 'excel':
            # Create Excel workbook
            wb = Workbook()
            ws = wb.active
            ws.title = "Transactions Report"

            # Define headers
            headers = ['Date', 'Type', 'Medicine', 'Quantity', 'Unit Price',
                      'Total Amount', 'Reference', 'Notes']

            # Add metadata
            metadata = {
                'Period': f"{start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}",
                'Generated on': timezone.now().strftime('%Y-%m-%d %H:%M'),
                'Generated by': request.user.get_full_name() or request.user.username,
                'Total Transactions': str(transactions.count()),
                'Total Sales': f"₱{total_sales:,.2f}",
                'Total Purchases': f"₱{total_purchases:,.2f}"
            }

            # Apply styling and get starting row
            data_row = apply_professional_styling(ws, report_title, metadata, headers)

            # Add data rows
            row_num = data_row
            for i, transaction in enumerate(transactions):
                # Determine row type based on transaction type
                if transaction.transaction_type == 'sale':
                    row_type = 'even' if i % 2 == 1 else None
                else:
                    row_type = None

                row_data = [
                    transaction.transaction_date.strftime('%Y-%m-%d %H:%M'),
                    transaction.transaction_type.upper(),
                    transaction.medicine.name,
                    transaction.quantity,
                    f"₱{transaction.medicine.price:,.2f}",
                    f"₱{transaction.total_amount:,.2f}",
                    transaction.reference_number or 'N/A',
                    transaction.notes or ''
                ]

                row_num = apply_row_styling(ws, row_num, row_data, row_type)

            # Add summary rows
            row_num += 1  # Add space before summary

            # Sales summary
            sales_summary = ['Sales Summary', '', '', '', '', f"₱{total_sales:,.2f}", '', '']
            row_num = apply_row_styling(ws, row_num, sales_summary, 'total')

            # Purchases summary
            purchases_summary = ['Purchases Summary', '', '', '', '', f"₱{total_purchases:,.2f}", '', '']
            row_num = apply_row_styling(ws, row_num, purchases_summary, 'total')

            # Net summary
            net_summary = ['Net (Sales - Purchases)', '', '', '', '', f"₱{total_sales - total_purchases:,.2f}", '', '']
            row_num = apply_row_styling(ws, row_num, net_summary, 'total')

            # Auto-adjust column widths
            auto_adjust_columns(ws)

            # Return Excel response
            return create_excel_response(wb, 'transactions')

        elif file_format == 'pdf':
            # Get PDF utilities
            create_pdf_styles, create_pdf_table, create_pdf_response = get_pdf_utils()

            # Create PDF
            styles = create_pdf_styles()
            elements = []

            # Add title
            elements.append(Paragraph(report_title, styles['title']))
            elements.append(Spacer(1, 0.25*inch))

            # Add metadata
            metadata_text = f"Period: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}<br/>"
            metadata_text += f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}<br/>"
            metadata_text += f"Generated by: {request.user.get_full_name() or request.user.username}<br/>"
            metadata_text += f"Total Transactions: {transactions.count()}<br/>"
            metadata_text += f"Total Sales: ₱{total_sales:,.2f}<br/>"
            metadata_text += f"Total Purchases: ₱{total_purchases:,.2f}"

            elements.append(Paragraph(metadata_text, styles['metadata']))
            elements.append(Spacer(1, 0.25*inch))

            # Prepare table data
            table_data = [['Date', 'Type', 'Medicine', 'Quantity', 'Unit Price',
                          'Total Amount', 'Reference']]

            # Track sales rows for styling
            sales_rows = []
            purchase_rows = []

            for i, transaction in enumerate(transactions):
                # Track row for styling
                if transaction.transaction_type == 'sale':
                    sales_rows.append(i + 1)  # +1 because header is row 0
                else:
                    purchase_rows.append(i + 1)

                table_data.append([
                    transaction.transaction_date.strftime('%Y-%m-%d %H:%M'),
                    transaction.transaction_type.upper(),
                    transaction.medicine.name,
                    str(transaction.quantity),
                    f"₱{transaction.medicine.price:,.2f}",
                    f"₱{transaction.total_amount:,.2f}",
                    transaction.reference_number or 'N/A'
                ])

            # Add summary rows
            table_data.append(['Sales Summary', '', '', '', '', f"₱{total_sales:,.2f}", ''])
            table_data.append(['Purchases Summary', '', '', '', '', f"₱{total_purchases:,.2f}", ''])
            table_data.append(['Net (Sales - Purchases)', '', '', '', '', f"₱{total_sales - total_purchases:,.2f}", ''])

            # Create table style with conditional formatting
            table_style = [
                # Summary rows styling
                ('BACKGROUND', (0, -3), (-1, -1), colors.Color(0.93, 0.94, 0.95)),
                ('FONTNAME', (0, -3), (-1, -1), 'Helvetica-Bold'),
                ('LINEABOVE', (0, -3), (-1, -3), 1, colors.Color(0.17, 0.24, 0.31)),
            ]

            # Create and add table
            table = create_pdf_table(table_data, style=table_style)
            elements.append(table)

            # Return PDF response
            return create_pdf_response(
                elements,
                report_title,
                'transactions_report',
                landscape_mode=True
            )

    except Exception as e:
        logger.error(f"Error generating transactions report: {str(e)}")
        raise
