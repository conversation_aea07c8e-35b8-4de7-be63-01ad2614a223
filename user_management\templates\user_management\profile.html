{% extends 'base.html' %}
{% load base_filters %}
{% load static %}
{% load widget_tweaks %}

{% block title %}My Profile{% endblock %}

{% block extra_css %}
<style>
    .profile-container {
        max-width: 860px;
        margin: 0 auto;
        padding: 40px 20px;
    }

    .profile-header {
        margin-bottom: 32px;
        background: linear-gradient(135deg, #3b82f6, #1e40af);
        padding: 30px;
        border-radius: 12px;
        color: white;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .profile-title {
        font-size: 2rem;
        font-weight: 600;
        color: white;
        margin-bottom: 8px;
    }

    .profile-subtitle {
        color: rgba(255, 255, 255, 0.9);
        font-size: 0.95rem;
    }

    .profile-card {
        background: #FFFFFF;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        padding: 32px;
        margin-bottom: 24px;
        border: 1px solid #f0f0f0;
    }

    .profile-section {
        margin-bottom: 32px;
    }

    .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: #111827;
        margin-bottom: 24px;
        padding-bottom: 16px;
        border-bottom: 1px solid #E5E7EB;
        display: flex;
        align-items: center;
    }

    .section-title i {
        margin-right: 10px;
        color: #3b82f6;
    }

    .profile-picture-wrapper {
        position: relative;
        width: 150px;
        height: 150px;
        margin-bottom: 32px;
        margin-left: auto;
        margin-right: auto;
    }

    .profile-picture {
        width: 150px;
        height: 150px;
        object-fit: cover;
        border-radius: 50%;
        background: #F3F4F6;
        border: 4px solid white;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .profile-picture-overlay {
        position: absolute;
        bottom: 5px;
        right: 5px;
        background: #3b82f6;
        border: 2px solid white;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.2s ease;
        color: white;
    }

    .profile-picture-overlay:hover {
        background: #2563eb;
        transform: scale(1.05);
    }

    .form-group {
        margin-bottom: 24px;
        position: relative;
    }

    .form-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: #374151;
        margin-bottom: 8px;
    }

    .form-control {
        width: 100%;
        padding: 10px 14px;
        border: 1px solid #E5E7EB;
        border-radius: 6px;
        font-size: 0.95rem;
        transition: all 0.2s ease;
    }

    .form-control:focus {
        border-color: #3b82f6;
        outline: none;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }

    .form-control[readonly] {
        background-color: #F9FAFB;
        cursor: not-allowed;
        border-color: #E5E7EB;
    }

    .btn-primary {
        background: #3b82f6;
        color: #FFFFFF;
        border: none;
        border-radius: 6px;
        padding: 12px 24px;
        font-weight: 500;
        transition: all 0.2s ease;
        box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
    }

    .btn-primary:hover {
        background: #2563eb;
        transform: translateY(-1px);
        box-shadow: 0 4px 6px rgba(59, 130, 246, 0.4);
    }

    .btn-primary:active {
        transform: translateY(0);
        box-shadow: 0 1px 2px rgba(59, 130, 246, 0.4);
    }

    .alert {
        border-radius: 6px;
        padding: 14px 18px;
        margin-bottom: 24px;
        font-size: 0.95rem;
        display: flex;
        align-items: center;
    }

    .alert i {
        margin-right: 10px;
        font-size: 1.1rem;
    }

    .alert-success {
        background: #ECFDF5;
        border: 1px solid #A7F3D0;
        color: #065F46;
    }

    .alert-info {
        background: #EFF6FF;
        border: 1px solid #BFDBFE;
        color: #1E40AF;
    }

    .modal-content {
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }

    .modal-header {
        background-color: #F9FAFB;
        border-bottom: 1px solid #F3F4F6;
    }

    .modal-body {
        padding: 0;
    }

    .profile-modal-image {
        max-width: 100%;
        max-height: 70vh;
        object-fit: contain;
    }

    .readonly-notice {
        background-color: #F9FAFB;
        border-left: 4px solid #3b82f6;
        padding: 16px 20px;
        margin-bottom: 24px;
        font-size: 0.95rem;
        color: #4B5563;
        border-radius: 0 6px 6px 0;
        display: flex;
        align-items: center;
    }

    .readonly-notice i {
        font-size: 1.2rem;
        margin-right: 12px;
        color: #3b82f6;
    }

    .editable-notice {
        background-color: #FFFBEB;
        border-left: 4px solid #F59E0B;
        padding: 16px 20px;
        margin-bottom: 24px;
        font-size: 0.95rem;
        color: #92400E;
        border-radius: 0 6px 6px 0;
        display: flex;
        align-items: center;
    }

    .editable-notice i {
        font-size: 1.2rem;
        margin-right: 12px;
        color: #F59E0B;
    }

    .pending-badge {
        display: inline-block;
        background-color: #F59E0B;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        margin-left: 0.75rem;
        font-weight: 500;
    }

    .approval-status {
        font-size: 0.85rem;
        margin-top: 0.5rem;
        display: flex;
        align-items: center;
        background-color: #FEF3C7;
        padding: 8px 12px;
        border-radius: 6px;
    }

    .status-pending {
        color: #92400E;
    }

    .status-approved {
        color: #065F46;
        background-color: #D1FAE5;
    }

    .status-icon {
        margin-right: 0.5rem;
    }

    .approval-tooltip {
        position: absolute;
        top: 0;
        right: 0;
        font-size: 0.75rem;
        background: #FFFBEB;
        color: #92400E;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
    }

    .admin-badge {
        background-color: #3b82f6;
        color: white;
        font-size: 0.75rem;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        margin-left: 0.75rem;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="profile-container">
    <div class="profile-header">
        <h1 class="profile-title">
            Profile Settings
            {% if is_admin %}
            <span class="admin-badge"><i class="fas fa-shield-alt"></i> Admin Mode</span>
            {% endif %}
        </h1>
        <p class="profile-subtitle">
            {% if is_admin %}
            As an administrator, you can directly edit all profile information without approval.
            {% else %}
            You can update your profile picture instantly. Changes to contact information will require admin approval.
            {% endif %}
        </p>
    </div>

    {% if form %}
    <form method="post" enctype="multipart/form-data" novalidate id="profile-form">
        {% csrf_token %}

        <div class="profile-card">
            <!-- Notices based on user role -->
            {% if not is_admin %}
            <div class="readonly-notice">
                <i class="fas fa-info-circle"></i>
                Your personal information (name, birthday, etc.) can only be modified by administrators. Please contact support if you need to update this information.
            </div>

            <div class="editable-notice">
                <i class="fas fa-exclamation-triangle"></i>
                Changes to your contact information will be submitted for admin approval. The updates will be applied after verification.
            </div>
            {% else %}
            <div class="readonly-notice">
                <i class="fas fa-shield-alt"></i>
                As an administrator, you can directly edit all profile information. Changes will be applied immediately without requiring approval.
            </div>
            {% endif %}

            <div class="profile-section text-center">
                <h2 class="section-title"><i class="fas fa-user-circle"></i> Profile Picture</h2>
                <div class="profile-picture-wrapper">
                    {% if profile.profile_picture %}
                    <img src="{{ profile.profile_picture.url }}"
                         alt="Profile Picture"
                         class="profile-picture"
                         id="preview-profile-picture"
                         data-bs-toggle="modal"
                         data-bs-target="#profilePictureModal">
                    {% else %}
                    <div class="profile-picture d-flex align-items-center justify-content-center"
                         id="preview-profile-picture"
                         data-bs-toggle="modal"
                         data-bs-target="#profilePictureModal">
                        <i class="fas fa-user fa-3x text-gray-400"></i>
                    </div>
                    {% endif %}
                    <label for="{{ form.profile_picture.id_for_label }}" class="profile-picture-overlay">
                        <i class="fas fa-camera"></i>
                    </label>
                    {{ form.profile_picture|add_class:"d-none" }}
                </div>
                <p class="text-muted">Click on the camera icon to upload a new profile picture</p>
            </div>

            <div class="profile-section">
                <h2 class="section-title"><i class="fas fa-id-card"></i> Personal Information</h2>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">First Name</label>
                            {% if is_admin %}
                                {{ form.first_name|add_class:"form-control" }}
                            {% else %}
                                {{ form.first_name|add_class:"form-control"|attr:"readonly:readonly" }}
                            {% endif %}
                        </div>
                        <div class="form-group">
                            <label class="form-label">Middle Name</label>
                            {% if is_admin %}
                                {{ form.middle_name|add_class:"form-control" }}
                            {% else %}
                                {{ form.middle_name|add_class:"form-control"|attr:"readonly:readonly" }}
                            {% endif %}
                        </div>
                        <div class="form-group">
                            <label class="form-label">Last Name</label>
                            {% if is_admin %}
                                {{ form.last_name|add_class:"form-control" }}
                            {% else %}
                                {{ form.last_name|add_class:"form-control"|attr:"readonly:readonly" }}
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Birthday</label>
                            {% if is_admin %}
                                {{ form.birthday|add_class:"form-control" }}
                            {% else %}
                                {{ form.birthday|add_class:"form-control"|attr:"readonly:readonly" }}
                            {% endif %}
                        </div>
                        <div class="form-group">
                            <label class="form-label">Sex</label>
                            {% if is_admin %}
                                {{ form.sex|add_class:"form-control" }}
                            {% else %}
                                {{ form.sex|add_class:"form-control"|attr:"disabled:disabled" }}
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="profile-section">
                <h2 class="section-title">
                    <i class="fas fa-address-book"></i> Contact Information
                    {% if not is_admin %}
                    <span class="pending-badge">Requires Approval</span>
                    {% endif %}
                </h2>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Email</label>
                            {{ form.email|add_class:"form-control" }}
                            {% if profile.pending_email %}
                            <div class="approval-status status-pending">
                                <i class="fas fa-clock status-icon"></i>
                                Pending approval: {{ profile.pending_email }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label">Phone Number</label>
                            {{ form.phone_number|add_class:"form-control" }}
                            {% if profile.pending_phone_number %}
                            <div class="approval-status status-pending">
                                <i class="fas fa-clock status-icon"></i>
                                Pending approval: {{ profile.pending_phone_number }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-12">
                        <div class="form-group">
                            <label class="form-label">Address</label>
                            {{ form.address|add_class:"form-control" }}
                            {% if profile.pending_address %}
                            <div class="approval-status status-pending">
                                <i class="fas fa-clock status-icon"></i>
                                Pending approval: {{ profile.pending_address }}
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <div class="d-flex justify-content-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save me-2"></i>
                    {% if is_admin %}
                    Save Changes
                    {% else %}
                    Submit Changes
                    {% endif %}
                </button>
            </div>
        </div>
    </form>
    {% endif %}
</div>

<!-- Profile Picture Modal -->
<div class="modal fade" id="profilePictureModal" tabindex="-1">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header border-0">
                <h5 class="modal-title">Profile Picture</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {% if profile.profile_picture %}
                <img src="{{ profile.profile_picture.url }}"
                     alt="Profile Picture"
                     class="profile-modal-image"
                     id="modal-profile-picture">
                {% else %}
                <div class="profile-modal-image d-flex align-items-center justify-content-center bg-light"
                     id="modal-profile-picture">
                    <i class="fas fa-user fa-3x text-gray-400"></i>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Confirmation Modal -->
<div class="modal fade" id="confirmationModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="fas fa-exclamation-triangle text-warning me-2"></i>Confirm Changes</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <p class="mb-1"><strong>You are updating your contact information.</strong></p>
                        <p class="mb-0">These changes will be submitted for admin approval and may take time to be reflected in your profile.</p>
                    </div>
                </div>

                <div id="changes-summary" class="mb-3">
                    <h6 class="fw-bold">Summary of changes:</h6>
                    <ul class="list-group" id="changes-list">
                        <!-- Will be populated by JavaScript -->
                    </ul>
                </div>

                <p>Do you want to continue with these changes?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="confirmSubmit">
                    <i class="fas fa-check me-2"></i>Submit for Approval
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Profile picture preview
        const profilePictureInput = document.querySelector('input[type="file"]');
        const previewImage = document.getElementById('preview-profile-picture');
        const modalImage = document.getElementById('modal-profile-picture');

        profilePictureInput.addEventListener('change', function (e) {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function (e) {
                    if (previewImage.tagName.toLowerCase() === 'img') {
                        previewImage.src = e.target.result;
                    } else {
                        const newImg = document.createElement('img');
                        newImg.src = e.target.result;
                        newImg.alt = "Profile Picture";
                        newImg.className = "profile-picture";
                        newImg.id = "preview-profile-picture";
                        newImg.setAttribute('data-bs-toggle', 'modal');
                        newImg.setAttribute('data-bs-target', '#profilePictureModal');
                        previewImage.parentNode.replaceChild(newImg, previewImage);
                    }

                    if (modalImage.tagName.toLowerCase() === 'img') {
                        modalImage.src = e.target.result;
                    } else {
                        const newModalImg = document.createElement('img');
                        newModalImg.src = e.target.result;
                        newModalImg.alt = "Profile Picture";
                        newModalImg.className = "profile-modal-image";
                        newModalImg.id = "modal-profile-picture";
                        modalImage.parentNode.replaceChild(newModalImg, modalImage);
                    }
                }
                reader.readAsDataURL(this.files[0]);
            }
        });

        // Form submission with confirmation for contact information changes
        const form = document.getElementById('profile-form');
        const emailField = form.querySelector('input[name="email"]');
        const phoneField = form.querySelector('input[name="phone_number"]');
        const addressField = form.querySelector('textarea[name="address"]');

        // Store original values
        const originalEmail = emailField ? emailField.value : '';
        const originalPhone = phoneField ? phoneField.value : '';
        const originalAddress = addressField ? addressField.value : '';

        const confirmSubmitBtn = document.getElementById('confirmSubmit');
        const changesList = document.getElementById('changes-list');

        form.addEventListener('submit', function (e) {
            // Check if any contact information has changed
            const emailChanged = emailField && emailField.value !== originalEmail;
            const phoneChanged = phoneField && phoneField.value !== originalPhone;
            const addressChanged = addressField && addressField.value !== originalAddress;

            // Only show confirmation for non-admin users
            const isAdminMode = document.querySelector('.admin-badge') !== null;

            if (!isAdminMode && (emailChanged || phoneChanged || addressChanged)) {
                // Prevent form submission
                e.preventDefault();

                // Clear previous changes list
                changesList.innerHTML = '';

                // Add changes to the list
                if (emailChanged) {
                    addChangeToList('Email', originalEmail, emailField.value);
                }

                if (phoneChanged) {
                    addChangeToList('Phone Number', originalPhone, phoneField.value);
                }

                if (addressChanged) {
                    addChangeToList('Address', originalAddress, addressField.value);
                }

                // Show confirmation modal
                const confirmModal = new bootstrap.Modal(document.getElementById('confirmationModal'));
                confirmModal.show();
            }
        });

        // Function to add a change to the changes list
        function addChangeToList(fieldName, oldValue, newValue) {
            const li = document.createElement('li');
            li.className = 'list-group-item';

            const fieldTitle = document.createElement('div');
            fieldTitle.className = 'fw-bold';
            fieldTitle.textContent = fieldName;

            const oldValueEl = document.createElement('div');
            oldValueEl.className = 'text-muted small';
            oldValueEl.innerHTML = `<span class="fw-medium">From:</span> ${oldValue || '<em>empty</em>'}`;

            const newValueEl = document.createElement('div');
            newValueEl.className = 'text-primary';
            newValueEl.innerHTML = `<span class="fw-medium">To:</span> ${newValue || '<em>empty</em>'}`;

            li.appendChild(fieldTitle);
            li.appendChild(oldValueEl);
            li.appendChild(newValueEl);

            changesList.appendChild(li);
        }

        // Confirm button in modal
        if (confirmSubmitBtn) {
            confirmSubmitBtn.addEventListener('click', function () {
                form.submit();
            });
        }
    });
</script>
{% endblock %}
