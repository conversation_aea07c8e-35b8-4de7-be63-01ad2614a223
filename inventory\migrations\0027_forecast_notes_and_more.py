# Generated by Django 5.2 on 2025-04-26 11:37

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0026_forecast_has_seasonality_forecast_last_updated_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='forecast',
            name='notes',
            field=models.TextField(blank=True, help_text='Explanation of why this forecasting method was selected', null=True),
        ),
        migrations.AlterField(
            model_name='emailnotificationsetting',
            name='notification_type',
            field=models.CharField(choices=[('low_stock', 'Low Stock Alert'), ('expiring_soon', 'Expiring Soon Alert'), ('out_of_stock', 'Out of Stock Alert'), ('price_change', 'Price Change Alert')], max_length=20),
        ),
    ]
