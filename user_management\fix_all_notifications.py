from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from inventory.models import EmailNotificationSetting
from django.db import transaction
import logging

logger = logging.getLogger(__name__)
User = get_user_model()

class Command(BaseCommand):
    help = 'Ensure all users are subscribed to all notification types'

    def handle(self, *args, **options):
        try:
            with transaction.atomic():
                # Get all active users
                users = User.objects.filter(is_active=True)
                self.stdout.write(f"Found {users.count()} active users")

                # Get or create all notification types
                notification_types = [
                    EmailNotificationSetting.LOW_STOCK,
                    EmailNotificationSetting.OUT_OF_STOCK,
                    EmailNotificationSetting.EXPIRING_SOON,
                    EmailNotificationSetting.PRICE_CHANGE
                ]

                for notification_type in notification_types:
                    setting, created = EmailNotificationSetting.objects.get_or_create(
                        notification_type=notification_type,
                        defaults={'is_enabled': True}
                    )

                    # Clear existing recipients and add all active users
                    setting.recipients.clear()
                    setting.recipients.add(*users)

                    self.stdout.write(
                        self.style.SUCCESS(
                            f'Updated {notification_type} notifications with {users.count()} users'
                        )
                    )

                self.stdout.write(self.style.SUCCESS('Successfully fixed all notification settings'))

        except Exception as e:
            logger.error(f"Failed to fix notification settings: {str(e)}", exc_info=True)
            self.stdout.write(self.style.ERROR(f'Failed to fix notification settings: {str(e)}'))