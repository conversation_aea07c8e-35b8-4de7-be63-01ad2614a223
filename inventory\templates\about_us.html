﻿{% extends 'base.html' %}

{% load static %}

{% block content %}

<style>
    /* Variables */
    :root {
        /* Align with base color scheme */
        --primary-color: #2c3e50;
        --secondary-color: #3498db;
        --accent-color: #1abc9c;
        --success-color: #2ecc71;
        --warning-color: #f39c12;
        --danger-color: #e74c3c;
        --info-color: #3498db;
        --light-color: #ecf0f1;
        --dark-color: #34495e;
        --text-primary: #2c3e50;
        --text-secondary: #7f8c8d;
        --text-muted: #95a5a6;
        --gray-color: #7f8c8d;
        --border-color: #e2e8f0;
        --primary-gradient: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        --primary-light: rgba(44, 62, 80, 0.1);
        --border-radius: 0.5rem;
        --border-radius-sm: 0.25rem;
        --shadow-sm: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        --shadow-md: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        --method-margin: 1.5rem;
        --method-bg: #ffffff;
        --method-border-radius: 0.5rem;
        --method-padding: 1.5rem;
        --method-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.08);
        --method-transition: all 0.3s ease;
        --method-border: 1px solid rgba(0, 0, 0, 0.05);
        --method-heading-color: var(--primary-color);
    }

    /* Container for all forecasting methods */
    .forecasting-methods {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
        gap: var(--method-margin);
        margin: 2rem 0;
    }

    /* Individual method card */
    .forecasting-method {
        background-color: var(--method-bg);
        border-radius: var(--method-border-radius);
        padding: var(--method-padding);
        box-shadow: var(--method-shadow);
        transition: var(--method-transition);
        border: var(--method-border);
        height: 100%;
        display: flex;
        flex-direction: column;
    }

        .forecasting-method:hover {
            transform: translateY(-5px);
            box-shadow: var(--method-hover-shadow);
        }

        /* Method heading */
        .forecasting-method h4 {
            color: var(--method-heading-color);
            margin-top: 0;
            margin-bottom: 1rem;
            font-weight: 600;
            font-size: 1.25rem;
            border-bottom: 2px solid rgba(13, 110, 253, 0.2);
            padding-bottom: 0.75rem;
        }

        /* Method description and list */
        .forecasting-method p {
            margin-bottom: 1rem;
            flex-grow: 1;
            color: #495057;
        }

        .forecasting-method ul {
            list-style-type: none;
            padding-left: 0;
            margin-bottom: 0;
        }

        .forecasting-method li {
            margin-bottom: 0.75rem;
            position: relative;
            padding-left: 1.5rem;
            color: #495057;
        }

            .forecasting-method li:before {
                content: "•";
                color: var(--method-heading-color);
                font-weight: bold;
                position: absolute;
                left: 0;
                font-size: 1.2em;
            }

            .forecasting-method li:last-child {
                margin-bottom: 0;
            }

    /* Section heading */
    .forecasting-section-title {
        margin-bottom: 1.5rem;
        color: #212529;
        font-weight: 700;
        position: relative;
        display: inline-block;
    }

        .forecasting-section-title:after {
            content: "";
            position: absolute;
            bottom: -8px;
            left: 0;
            width: 60px;
            height: 3px;
            background-color: var(--method-heading-color);
        }

    /* Responsive styles */
    @media (max-width: 768px) {
        .forecasting-methods {
            grid-template-columns: 1fr;
        }

        .forecasting-method {
            margin-bottom: 1rem;
        }
    }

    /* Hero Section */
    .hero-section {
        background: var(--primary-gradient);
        box-shadow: var(--shadow-md);
        border-radius: var(--border-radius);
        padding: 2rem 0;
        margin-bottom: 2rem;
    }

        .hero-section .lead {
            opacity: 0.9;
            font-weight: 300;
        }

        .hero-section .small {
            opacity: 0.8;
        }

    /* Layout */
    .about-us-container {
        padding-top: 1rem;
        padding-bottom: 3rem;
    }

    .bg-gradient-primary {
        background: var(--primary-gradient) !important;
    }

    /* Process Steps */
    .process-steps {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 1rem;
        margin: 1.5rem 0;
    }

    .step {
        background-color: var(--light-color);
        border-radius: var(--border-radius);
        padding: 1rem;
        display: flex;
        align-items: center;
        box-shadow: var(--shadow-sm);
        transition: transform 0.2s, box-shadow 0.2s;
    }

        .step:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

    .step-number {
        width: 36px;
        height: 36px;
        background-color: var(--primary-color);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        margin-right: 1rem;
    }

    .step-text {
        font-weight: 500;
    }

    .step-arrow {
        color: var(--gray-color);
        margin: 0 0.5rem;
    }

    /* Charts and Data Visualization */
    .chart-container {
        margin-top: 2rem;
        position: relative;
        height: 300px;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-sm);
    }

    /* Status Colors */
    .stock-warning {
        color: var(--danger-color);
        font-weight: bold;
    }

    .stock-normal {
        color: var(--success-color);
        font-weight: bold;
    }

    /* Cards */
    .card {
        transition: transform 0.2s, box-shadow 0.2s;
        border-radius: var(--border-radius);
        overflow: hidden;
    }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }

    .card-header {
        background-color: var(--light-color);
        border-bottom: 1px solid var(--border-color);
    }

    /* Tables */
    .table-responsive {
        border: 1px solid var(--border-color);
        border-radius: var(--border-radius);
        overflow: hidden;
    }

    .sticky-top {
        position: sticky;
        top: 0;
        z-index: 1;
        background-color: var(--light-color);
    }

    /* Scrollbars */
    .table-responsive::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: var(--border-radius-sm);
    }

    .table-responsive::-webkit-scrollbar-thumb {
        background: var(--gray-color);
        border-radius: var(--border-radius-sm);
    }

        .table-responsive::-webkit-scrollbar-thumb:hover {
            background: var(--dark-color);
        }

    /* Feature Cards */
    .feature-card .feature-icon {
        color: var(--primary-color);
        transition: transform 0.2s;
    }

    .feature-card:hover .feature-icon {
        transform: scale(1.1);
    }

    /* Benefit Cards */
    .benefit-card {
        text-align: center;
        padding: 1.5rem 1rem;
    }

        .benefit-card i {
            transition: transform 0.3s;
        }

        .benefit-card:hover i {
            transform: scale(1.2);
        }

    /* Accordion */
    .accordion-item {
        border-radius: var(--border-radius) !important;
        margin-bottom: 1rem;
    }

    .accordion-button:not(.collapsed) {
        background-color: var(--primary-light);
        color: var(--primary-color);
    }

    /* Table Centering */
    .centered-table-container {
        display: flex;
        justify-content: center;
        width: 100%;
    }

    .centered-table {
        margin: 0 auto;
        width: 100%;
    }

    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .process-steps {
            flex-direction: column;
            align-items: flex-start;
        }

        .step-arrow {
            transform: rotate(90deg);
            margin: 0.5rem 0;
        }

        .benefit-card {
            margin-bottom: 1rem;
        }
    }
</style>

<div class="container mt-4">
    <!-- Hero Section -->
    <section class="hero-section bg-gradient-primary text-white py-4 mb-4">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-8 mx-auto text-center">
                    <h2 class="h4 mb-2">BMC Medforecast System</h2>
                    <p class="lead mb-0" style="font-size: 1rem;">
                        Advanced Pharmaceutical Inventory Management with Intelligent Forecasting
                    </p>
                </div>
            </div>
        </div>
    </section>

    <div class="row">
        <div class="col-12">
            <!-- Forecasting Methods Section -->
            <div class="card shadow-sm mb-4">
                <div class="card-header" style="background: var(--primary-gradient); color: white; text-center">
                    <h3 class="h5 mb-0"><i class="fas fa-chart-line me-2"></i>Forecasting Methods</h3>
                </div>
                <div class="card-body">
                    <!-- Gradient Boosting Highlight -->
                    <div class="alert alert-primary d-flex align-items-center mb-4" role="alert">
                        <i class="fas fa-robot fa-2x me-3"></i>
                        <div>
                            <h5 class="alert-heading mb-1">New: Gradient Boosting Machine Learning</h5>
                            <p class="mb-0">Our system now uses advanced Gradient Boosting algorithms to provide more accurate forecasts by learning complex patterns in your data, including brand and supplier information for better inventory management.</p>
                        </div>
                    </div>

                    <div class="row">
                        <!-- Gradient Boosting -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm" style="border-left: 4px solid #4dabf7 !important;">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="me-3" style="background-color: #4dabf7; width: 40px; height: 40px; border-radius: 8px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-brain text-white"></i>
                                        </div>
                                        <h4 class="card-title mb-0">Gradient Boosting</h4>
                                    </div>
                                    <p class="card-text">
                                        Our advanced machine learning approach that creates an ensemble of decision trees to learn complex patterns in your inventory data.
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Multi-feature learning (day of week, seasonality, etc.)</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Adaptive weighting of historical data</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>5-15% accuracy improvement over traditional methods</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Feature importance visualization</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Brand & supplier-specific pattern recognition</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Generic medicine category aggregation</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- ARIMA -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h4 class="card-title">
                                        <i class="fas fa-chart-bar text-primary me-2"></i>ARIMA
                                    </h4>
                                    <p class="card-text">
                                        Auto-Regressive Integrated Moving Average (ARIMA) is our primary forecasting method, implemented using auto_arima for optimal parameter selection.
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Automatic p,d,q parameter optimization</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Handles non-stationary time series data</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Stepwise model selection using AIC</li>
                                    </ul>
                                </div>
                                <div class="card-footer bg-light text-center">
                                    <span class="badge" style="background-color: var(--primary-color);">Primary Model</span>
                                </div>
                            </div>
                        </div>

                        <!-- Linear Regression -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h4 class="card-title">
                                        <i class="fas fa-chart-line text-success me-2"></i>Linear Regression
                                    </h4>
                                    <p class="card-text">
                                        Simple yet effective trend analysis using scikit-learn's LinearRegression for baseline predictions and fallback.
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Clear trend identification</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Reliable baseline predictions</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Fallback when time series models fail</li>
                                    </ul>
                                </div>
                                <div class="card-footer bg-light text-center">
                                    <span class="badge" style="background-color: var(--success-color);">Baseline Model</span>
                                </div>
                            </div>
                        </div>

                        <!-- Exponential Smoothing -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h4 class="card-title">
                                        <i class="fas fa-wave-square text-info me-2"></i>Holt-Winters
                                    </h4>
                                    <p class="card-text">
                                        Exponential smoothing with Holt-Winters method for time series with seasonal patterns, implemented using statsmodels.
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Triple exponential smoothing</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Handles trend and seasonality</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Adaptive parameter optimization</li>
                                    </ul>
                                </div>
                                <div class="card-footer bg-light text-center">
                                    <span class="badge" style="background-color: var(--info-color); color: white;">Seasonal Model</span>
                                </div>
                            </div>
                        </div>

                        <!-- Moving Average -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h4 class="card-title">
                                        <i class="fas fa-stream text-danger me-2"></i>Moving Average
                                    </h4>
                                    <p class="card-text">
                                        Simple and effective smoothing technique for time series data with limited history, implemented as a weighted moving average.
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Noise reduction</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Works with limited data points</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Weighted recent observations</li>
                                    </ul>
                                </div>
                                <div class="card-footer bg-light text-center">
                                    <span class="badge" style="background-color: var(--danger-color);">Fallback Model</span>
                                </div>
                            </div>
                        </div>

                        <!-- STL Decomposition -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h4 class="card-title">
                                        <i class="fas fa-puzzle-piece text-warning me-2"></i>STL Decomposition
                                    </h4>
                                    <p class="card-text">
                                        Seasonal-Trend decomposition using LOESS for complex time series analysis, implemented using statsmodels.
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Separates trend, seasonality, and residuals</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Handles multiple seasonal patterns</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Robust to outliers</li>
                                    </ul>
                                </div>
                                <div class="card-footer bg-light text-center">
                                    <span class="badge" style="background-color: var(--warning-color); color: var(--dark-color);">Advanced Analysis</span>
                                </div>
                            </div>
                        </div>

                        <!-- Ensemble Methods -->
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <h4 class="card-title">
                                        <i class="fas fa-layer-group text-secondary me-2"></i>Ensemble Methods
                                    </h4>
                                    <p class="card-text">
                                        Combines multiple forecasting models for improved accuracy, using weighted averaging based on model performance.
                                    </p>
                                    <ul class="list-unstyled">
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Combines strengths of multiple models</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Reduces individual model weaknesses</li>
                                        <li><i class="fas fa-check-circle text-success me-2"></i>Adaptive weighting based on performance</li>
                                    </ul>
                                </div>
                                <div class="card-footer bg-light text-center">
                                    <span class="badge" style="background-color: var(--text-secondary); color: white;">Meta Model</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Forecast Horizons -->
                    <div class="mt-5">
                        <h4 class="mb-3 text-center">Forecast Horizons</h4>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-header text-white text-center" style="background-color: var(--primary-color);">
                                        <h5 class="card-title mb-0"><i class="fas fa-calendar-week me-2"></i>Weekly Forecast</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="d-flex justify-content-center mb-3">
                                            <div class="rounded-circle bg-primary bg-opacity-10 p-3">
                                                <i class="fas fa-calendar-week fa-2x text-primary"></i>
                                            </div>
                                        </div>
                                        <p class="card-text">8 weeks ahead prediction for short-term planning and immediate inventory decisions</p>
                                        <div class="mt-3">
                                            <span class="badge" style="background-color: var(--primary-color);">8 weeks</span>
                                            <span class="badge ms-2" style="background-color: var(--text-secondary);">High Precision</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-header text-white text-center" style="background-color: var(--success-color);">
                                        <h5 class="card-title mb-0"><i class="fas fa-calendar-alt me-2"></i>Monthly Forecast</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="d-flex justify-content-center mb-3">
                                            <div class="rounded-circle bg-success bg-opacity-10 p-3">
                                                <i class="fas fa-calendar-alt fa-2x text-success"></i>
                                            </div>
                                        </div>
                                        <p class="card-text">6 months ahead prediction for medium-term planning and supplier negotiations</p>
                                        <div class="mt-3">
                                            <span class="badge" style="background-color: var(--success-color);">6 months</span>
                                            <span class="badge ms-2" style="background-color: var(--text-secondary);">Medium Precision</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-header text-white text-center" style="background-color: var(--info-color);">
                                        <h5 class="card-title mb-0"><i class="fas fa-calendar me-2"></i>Yearly Forecast</h5>
                                    </div>
                                    <div class="card-body text-center">
                                        <div class="d-flex justify-content-center mb-3">
                                            <div class="rounded-circle bg-info bg-opacity-10 p-3">
                                                <i class="fas fa-calendar fa-2x text-info"></i>
                                            </div>
                                        </div>
                                        <p class="card-text">4 years ahead prediction for long-term strategic planning and trend analysis</p>
                                        <div class="mt-3">
                                            <span class="badge" style="background-color: var(--info-color); color: white;">4 years</span>
                                            <span class="badge ms-2" style="background-color: var(--text-secondary);">Strategic Outlook</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Accuracy Metrics -->
                    <div class="mt-5">
                        <h4 class="mb-4 text-center">Forecast Accuracy Metrics</h4>
                        <div class="row">
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <div class="d-flex justify-content-center mb-3">
                                            <div class="rounded-circle p-3" style="background-color: rgba(44, 62, 80, 0.1);">
                                                <i class="fas fa-ruler fa-2x" style="color: var(--primary-color);"></i>
                                            </div>
                                        </div>
                                        <h5 class="card-title">MAE</h5>
                                        <p class="card-text">Mean Absolute Error</p>
                                        <hr>
                                        <div class="small text-muted mb-2">Formula:</div>
                                        <div class="bg-light p-2 rounded">
                                            <code>MAE = Σ|actual - predicted| / n</code>
                                        </div>
                                        <p class="mt-3 small">Measures average magnitude of errors without considering direction. Lower values indicate better accuracy.</p>
                                        <div class="mt-2">
                                            <span class="badge" style="background-color: var(--primary-color);">Primary Metric</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <div class="d-flex justify-content-center mb-3">
                                            <div class="rounded-circle p-3" style="background-color: rgba(46, 204, 113, 0.1);">
                                                <i class="fas fa-calculator fa-2x" style="color: var(--success-color);"></i>
                                            </div>
                                        </div>
                                        <h5 class="card-title">RMSE</h5>
                                        <p class="card-text">Root Mean Square Error</p>
                                        <hr>
                                        <div class="small text-muted mb-2">Formula:</div>
                                        <div class="bg-light p-2 rounded">
                                            <code>RMSE = √(Σ(actual - predicted)² / n)</code>
                                        </div>
                                        <p class="mt-3 small">Penalizes large errors more heavily than small ones. Used for model selection and optimization.</p>
                                        <div class="mt-2">
                                            <span class="badge" style="background-color: var(--success-color);">Model Selection</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <div class="d-flex justify-content-center mb-3">
                                            <div class="rounded-circle p-3" style="background-color: rgba(243, 156, 18, 0.1);">
                                                <i class="fas fa-percentage fa-2x" style="color: var(--warning-color);"></i>
                                            </div>
                                        </div>
                                        <h5 class="card-title">MAPE</h5>
                                        <p class="card-text">Mean Absolute Percentage Error</p>
                                        <hr>
                                        <div class="small text-muted mb-2">Formula:</div>
                                        <div class="bg-light p-2 rounded">
                                            <code>MAPE = (Σ|(actual - predicted) / actual| / n) × 100%</code>
                                        </div>
                                        <p class="mt-3 small">Expresses accuracy as a percentage, making it easy to interpret and compare across different medicines.</p>
                                        <div class="mt-2">
                                            <span class="badge" style="background-color: var(--warning-color); color: var(--dark-color);">Interpretability</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6 col-lg-3 mb-4">
                                <div class="card border-0 shadow-sm h-100">
                                    <div class="card-body text-center">
                                        <div class="d-flex justify-content-center mb-3">
                                            <div class="rounded-circle p-3" style="background-color: rgba(52, 152, 219, 0.1);">
                                                <i class="fas fa-balance-scale fa-2x" style="color: var(--info-color);"></i>
                                            </div>
                                        </div>
                                        <h5 class="card-title">AIC</h5>
                                        <p class="card-text">Akaike Information Criterion</p>
                                        <hr>
                                        <div class="small text-muted mb-2">Formula:</div>
                                        <div class="bg-light p-2 rounded">
                                            <code>AIC = 2k - 2ln(L)</code>
                                        </div>
                                        <p class="mt-3 small">Balances model fit against complexity to prevent overfitting. Used in auto_arima for optimal parameter selection.</p>
                                        <div class="mt-2">
                                            <span class="badge" style="background-color: var(--info-color); color: white;">Parameter Tuning</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Core Features -->
            <section class="features-section py-5">
                <div class="container">
                    <h2 class="section-title text-center mb-5">Intelligent Forecasting System</h2>

                    <!-- Why Gradient Boosting? -->
                    <div class="card mb-5 border-0 shadow-sm">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col-md-4 text-center">
                                    <img src="https://cdn-icons-png.flaticon.com/512/2103/2103633.png" alt="AI Forecasting" style="max-width: 180px;">
                                </div>
                                <div class="col-md-8">
                                    <h3 class="h4 mb-3">Why Our System Prefers Gradient Boosting</h3>
                                    <p>Our forecasting system automatically evaluates multiple forecasting methods for each medicine and selects the one with the highest historical accuracy. Gradient Boosting has emerged as the preferred method for 97% of medicines due to its superior ability to:</p>

                                    <div class="row mt-4">
                                        <div class="col-md-6">
                                            <div class="d-flex mb-3">
                                                <div class="me-3" style="min-width: 30px;">
                                                    <i class="fas fa-calendar-day text-primary"></i>
                                                </div>
                                                <div>
                                                    <h5 class="h6 mb-1">Recognize Patterns</h5>
                                                    <p class="small text-muted mb-0">Detects day-of-week, monthly, and seasonal patterns</p>
                                                </div>
                                            </div>

                                            <div class="d-flex mb-3">
                                                <div class="me-3" style="min-width: 30px;">
                                                    <i class="fas fa-chart-line text-primary"></i>
                                                </div>
                                                <div>
                                                    <h5 class="h6 mb-1">Identify Trends</h5>
                                                    <p class="small text-muted mb-0">Captures gradual increases or decreases in demand</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="d-flex mb-3">
                                                <div class="me-3" style="min-width: 30px;">
                                                    <i class="fas fa-puzzle-piece text-primary"></i>
                                                </div>
                                                <div>
                                                    <h5 class="h6 mb-1">Learn Relationships</h5>
                                                    <p class="small text-muted mb-0">Understands how different factors interact</p>
                                                </div>
                                            </div>

                                            <div class="d-flex mb-3">
                                                <div class="me-3" style="min-width: 30px;">
                                                    <i class="fas fa-balance-scale text-primary"></i>
                                                </div>
                                                <div>
                                                    <h5 class="h6 mb-1">Adaptive Weighting</h5>
                                                    <p class="small text-muted mb-0">Gives more importance to relevant historical periods</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row g-4 justify-content-center">
                        <!-- Gradient Boosting Card -->
                        <div class="col-md-4">
                            <div class="feature-card card h-100 shadow-sm" style="border-top: 4px solid #4dabf7;">
                                <div class="card-body">
                                    <div class="feature-icon mb-3 text-center">
                                        <i class="fas fa-brain fa-2x" style="color: #4dabf7;"></i>
                                    </div>
                                    <h3 class="card-title h5 text-center">Gradient Boosting ML</h3>
                                    <p class="card-text text-center">
                                        Advanced machine learning that learns complex patterns for superior forecast accuracy.
                                    </p>
                                    <div class="text-center">
                                        <button class="btn btn-link" style="color: #4dabf7; padding: 0; margin-top: 0.5rem;" data-model="gradient_boosting">
                                            View Details <i class="fas fa-arrow-right ms-1"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- ARIMA Model Card -->
                        <div class="col-md-4">
                            <div class="feature-card card h-100 shadow-sm">
                                <div class="card-body">
                                    <div class="feature-icon mb-3 text-center">
                                        <i class="fas fa-chart-line fa-2x text-primary"></i>
                                    </div>
                                    <h3 class="card-title h5 text-center">ARIMA Forecasting</h3>
                                    <p class="card-text text-center">
                                        Advanced time series forecasting using ARIMA models for accurate pharmaceutical demand prediction.
                                    </p>
                                    <div class="text-center">
                                        <button class="btn btn-link text-primary p-0 mt-2 model-details-btn" data-model="arima">
                                            View Details <i class="fas fa-arrow-right ms-1"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Inventory Analysis Card -->
                        <div class="col-md-4">
                            <div class="feature-card card h-100 shadow-sm">
                                <div class="card-body">
                                    <div class="feature-icon mb-3 text-center">
                                        <i class="fas fa-pills fa-2x text-primary"></i>
                                    </div>
                                    <h3 class="card-title h5 text-center">Stock Level Analysis</h3>
                                    <p class="card-text text-center">
                                        Intelligent reorder point calculation and stock level monitoring system.
                                    </p>
                                    <div class="text-center">
                                        <button class="btn btn-link text-primary p-0 mt-2 model-details-btn" data-model="inventory">
                                            View Details <i class="fas fa-arrow-right ms-1"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Model Details Section -->
            <section id="modelDetailsSection" class="model-details-section py-5 d-none">
                <div class="container">
                    <div class="row">
                        <div class="col-12 mb-4">
                            <button id="backToFeatures" class="btn btn-outline-primary mb-4">
                                <i class="fas fa-arrow-left me-2"></i>Back to Features
                            </button>
                            <h2 id="modelDetailsTitle" class="section-title">Forecasting Model Details</h2>
                        </div>
                    </div>

                    <!-- Gradient Boosting Model Details -->
                    <div id="gradient_boosting-details" class="model-content d-none">
                        <div class="row">
                            <div class="col-lg-12">
                                <h3 class="h4 mb-4">Gradient Boosting Machine Learning</h3>
                                <div class="alert border-0" style="background-color: rgba(77, 171, 247, 0.1);">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-brain fs-3" style="color: #4dabf7;"></i>
                                        </div>
                                        <div>
                                            <p class="mb-0">Our system uses <strong>XGBoost (Extreme Gradient Boosting)</strong>, an advanced machine learning algorithm that creates an ensemble of decision trees to learn complex patterns in your inventory data. This approach provides significantly higher accuracy than traditional statistical methods.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-header" style="background-color: rgba(77, 171, 247, 0.1);">
                                                <h5 class="card-title mb-0"><i class="fas fa-cogs me-2" style="color: #4dabf7;"></i>How Gradient Boosting Works</h5>
                                            </div>
                                            <div class="card-body">
                                                <ol class="mb-0">
                                                    <li class="mb-2">Creates multiple decision trees in sequence</li>
                                                    <li class="mb-2">Each new tree corrects errors made by previous trees</li>
                                                    <li class="mb-2">Combines all trees for final prediction</li>
                                                    <li class="mb-2">Learns from multiple features simultaneously</li>
                                                    <li>Automatically determines feature importance</li>
                                                </ol>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-header" style="background-color: rgba(77, 171, 247, 0.1);">
                                                <h5 class="card-title mb-0"><i class="fas fa-chart-pie me-2" style="color: #4dabf7;"></i>Key Features Used</h5>
                                            </div>
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-6">
                                                        <ul class="mb-0">
                                                            <li class="mb-2">Day of week</li>
                                                            <li class="mb-2">Month & quarter</li>
                                                            <li class="mb-2">Is weekend/holiday</li>
                                                            <li class="mb-2">Previous demand (1-30 days)</li>
                                                        </ul>
                                                    </div>
                                                    <div class="col-6">
                                                        <ul class="mb-0">
                                                            <li class="mb-2">Rolling averages</li>
                                                            <li class="mb-2">Transaction types</li>
                                                            <li class="mb-2">Medicine category</li>
                                                            <li class="mb-2">Price & reorder level</li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mb-4 border-0 shadow-sm">
                                    <div class="card-header" style="background-color: rgba(77, 171, 247, 0.1);">
                                        <h5 class="card-title mb-0"><i class="fas fa-trophy me-2" style="color: #4dabf7;"></i>Advantages Over Traditional Methods</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="d-flex mb-3">
                                                    <div class="me-3">
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">Multi-feature Learning</h6>
                                                        <p class="small text-muted mb-0">Uses dozens of features vs. just historical quantities</p>
                                                    </div>
                                                </div>
                                                <div class="d-flex mb-3">
                                                    <div class="me-3">
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">Pattern Recognition</h6>
                                                        <p class="small text-muted mb-0">Detects complex patterns that simple methods miss</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex mb-3">
                                                    <div class="me-3">
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">Adaptive Weighting</h6>
                                                        <p class="small text-muted mb-0">Automatically determines which factors matter most</p>
                                                    </div>
                                                </div>
                                                <div class="d-flex mb-3">
                                                    <div class="me-3">
                                                        <i class="fas fa-check-circle text-success"></i>
                                                    </div>
                                                    <div>
                                                        <h6 class="mb-1">Accuracy Improvement</h6>
                                                        <p class="small text-muted mb-0">5-15% better accuracy than traditional methods</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- ARIMA Model Details -->
                    <div id="arima-details" class="model-content d-none">
                        <div class="row">
                            <div class="col-lg-12">
                                <h3 class="h4 mb-4">ARIMA (AutoRegressive Integrated Moving Average)</h3>
                                <div class="alert border-0" style="background-color: rgba(52, 152, 219, 0.1);">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-info-circle fs-3" style="color: var(--info-color);"></i>
                                        </div>
                                        <div>
                                            <p class="mb-0">BMC Medforecast uses <strong>pmdarima's auto_arima</strong> implementation to automatically identify the optimal parameters (p,d,q) for each medicine's historical data. This ensures the highest possible forecast accuracy with minimal manual intervention.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-start border-5" style="border-color: var(--primary-color);">
                                            <div class="card-body">
                                                <h5 class="card-title" style="color: var(--primary-color);"><i class="fas fa-history me-2"></i>AutoRegressive (p)</h5>
                                                <p class="card-text">The number of lag observations included in the model. Captures the relationship between current values and past values.</p>
                                                <div class="bg-light p-2 rounded small">
                                                    <code>p = 0-3</code> (automatically determined)
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-start border-5" style="border-color: var(--success-color);">
                                            <div class="card-body">
                                                <h5 class="card-title" style="color: var(--success-color);"><i class="fas fa-exchange-alt me-2"></i>Integrated (d)</h5>
                                                <p class="card-text">The degree of differencing required to make the time series stationary. Removes trends and seasonality.</p>
                                                <div class="bg-light p-2 rounded small">
                                                    <code>d = 0-1</code> (automatically determined)
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-start border-5" style="border-color: var(--warning-color);">
                                            <div class="card-body">
                                                <h5 class="card-title" style="color: var(--warning-color);"><i class="fas fa-chart-line me-2"></i>Moving Average (q)</h5>
                                                <p class="card-text">The size of the moving average window. Incorporates the dependency between observations and residual errors.</p>
                                                <div class="bg-light p-2 rounded small">
                                                    <code>q = 0-3</code> (automatically determined)
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="process-flow mb-4">
                                    <h4 class="h5 mb-3">ARIMA Process Flow in BMC Medforecast</h4>
                                    <div class="process-steps">
                                        <div class="step">
                                            <div class="step-number">1</div>
                                            <div class="step-text">Data Preprocessing</div>
                                        </div>
                                        <div class="step-arrow"><i class="fas fa-arrow-right"></i></div>
                                        <div class="step">
                                            <div class="step-number">2</div>
                                            <div class="step-text">Stationarity Test (ADF)</div>
                                        </div>
                                        <div class="step-arrow"><i class="fas fa-arrow-right"></i></div>
                                        <div class="step">
                                            <div class="step-number">3</div>
                                            <div class="step-text">Auto Parameter Selection</div>
                                        </div>
                                        <div class="step-arrow"><i class="fas fa-arrow-right"></i></div>
                                        <div class="step">
                                            <div class="step-number">4</div>
                                            <div class="step-text">Model Fitting & Validation</div>
                                        </div>
                                        <div class="step-arrow"><i class="fas fa-arrow-right"></i></div>
                                        <div class="step">
                                            <div class="step-number">5</div>
                                            <div class="step-text">Forecast Generation</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="card mb-4 border-0 shadow-sm">
                                    <div class="card-header text-white" style="background-color: var(--primary-color);">
                                        <h5 class="mb-0"><i class="fas fa-code me-2"></i>Implementation Details</h5>
                                    </div>
                                    <div class="card-body">
                                        <p>BMC Medforecast uses the following Python libraries for ARIMA implementation:</p>
                                        <ul class="list-group list-group-flush">
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle me-2" style="color: var(--success-color);"></i>
                                                <strong class="me-2">pmdarima:</strong> For auto_arima functionality with automatic parameter selection
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle me-2" style="color: var(--success-color);"></i>
                                                <strong class="me-2">statsmodels:</strong> For ARIMA model implementation and statistical tests
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle me-2" style="color: var(--success-color);"></i>
                                                <strong class="me-2">pandas:</strong> For time series data manipulation and preprocessing
                                            </li>
                                            <li class="list-group-item d-flex align-items-center">
                                                <i class="fas fa-check-circle me-2" style="color: var(--success-color);"></i>
                                                <strong class="me-2">numpy:</strong> For numerical operations and array handling
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Additional Forecasting Methods Cards -->
                                <div class="row mt-4">
                                    <!-- Exponential Smoothing Card -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-body">
                                                <h5 class="card-title">
                                                    <i class="fas fa-chart-line text-primary me-2"></i>Exponential Smoothing
                                                </h5>
                                                <p class="card-text">Holt-Winters method for time series with seasonal patterns.</p>
                                                <ul class="list-unstyled">
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>Trend and seasonality handling</li>
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>Adaptive parameter optimization</li>
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>Robust to outliers</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Moving Average Card -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-body">
                                                <h5 class="card-title">
                                                    <i class="fas fa-chart-area text-success me-2"></i>Moving Average
                                                </h5>
                                                <p class="card-text">Simple and effective smoothing technique for time series data.</p>
                                                <ul class="list-unstyled">
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>Noise reduction</li>
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>Trend identification</li>
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>Short-term forecasting</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Holt-Winters Card -->
                                    <div class="col-md-4 mb-3">
                                        <div class="card h-100 border-0 shadow-sm">
                                            <div class="card-body">
                                                <h5 class="card-title">
                                                    <i class="fas fa-wave-square text-warning me-2"></i>Holt-Winters
                                                </h5>
                                                <p class="card-text">Advanced exponential smoothing with triple seasonality.</p>
                                                <ul class="list-unstyled">
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>Triple exponential smoothing</li>
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>Multiple seasonal patterns</li>
                                                    <li><i class="fas fa-check-circle text-success me-2"></i>Long-term forecasting</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Inventory Analysis Details -->
                    <div id="inventory-details" class="model-content d-none">
                        <div class="row">
                            <div class="col-lg-12">
                                <h3 class="h4 mb-4">Intelligent Inventory Management</h3>

                                <div class="alert border-0 mb-4" style="background-color: rgba(44, 62, 80, 0.1);">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-lightbulb fs-3" style="color: var(--primary-color);"></i>
                                        </div>
                                        <div>
                                            <h5 class="alert-heading mb-1">Smart Inventory Optimization</h5>
                                            <p class="mb-0">BMC Medforecast uses advanced statistical methods to calculate optimal inventory levels, reducing costs while preventing stockouts. The system continuously analyzes transaction patterns to refine its recommendations.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-lg-6 mb-4">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-header text-white" style="background-color: var(--primary-color);">
                                                <h4 class="h5 mb-0"><i class="fas fa-calculator me-2"></i>Key Inventory Metrics</h4>
                                            </div>
                                            <div class="card-body">
                                                <ul class="list-group list-group-flush">
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h5 class="h6 mb-1">Reorder Point (ROP)</h5>
                                                            <p class="mb-0 text-muted small">When to place new orders</p>
                                                        </div>
                                                        <div class="text-end">
                                                            <span class="badge rounded-pill mb-1 d-block" style="background-color: var(--primary-color);">Lead Time Demand + Safety Stock</span>
                                                            <small class="text-muted">Triggers purchase recommendations</small>
                                                        </div>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h5 class="h6 mb-1">Economic Order Quantity (EOQ)</h5>
                                                            <p class="mb-0 text-muted small">Optimal order size</p>
                                                        </div>
                                                        <div class="text-end">
                                                            <span class="badge rounded-pill mb-1 d-block" style="background-color: var(--primary-color);">√(2DS/H)</span>
                                                            <small class="text-muted">Balances ordering & holding costs</small>
                                                        </div>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h5 class="h6 mb-1">Safety Stock</h5>
                                                            <p class="mb-0 text-muted small">Buffer against variability</p>
                                                        </div>
                                                        <div class="text-end">
                                                            <span class="badge rounded-pill mb-1 d-block" style="background-color: var(--primary-color);">Z × σLTD</span>
                                                            <small class="text-muted">Z = service level factor (95-99%)</small>
                                                        </div>
                                                    </li>
                                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <h5 class="h6 mb-1">Stock Turnover Ratio</h5>
                                                            <p class="mb-0 text-muted small">Inventory efficiency measure</p>
                                                        </div>
                                                        <div class="text-end">
                                                            <span class="badge rounded-pill mb-1 d-block" style="background-color: var(--primary-color);">COGS / Average Inventory</span>
                                                            <small class="text-muted">Higher is better (typically 4-6)</small>
                                                        </div>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-lg-6 mb-4">
                                        <div class="card border-0 shadow-sm h-100">
                                            <div class="card-header text-white" style="background-color: var(--success-color);">
                                                <h4 class="h5 mb-0"><i class="fas fa-shield-alt me-2"></i>Stockout Prevention System</h4>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-4">
                                                    <h5 class="h6 mb-3">Multi-layered Protection Strategy</h5>
                                                    <div class="d-flex mb-3">
                                                        <div class="me-3">
                                                            <div class="rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: rgba(46, 204, 113, 0.1);">
                                                                <i class="fas fa-chart-bar" style="color: var(--success-color);"></i>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-1">Dynamic Safety Stock</h6>
                                                            <p class="mb-0 small">Automatically adjusts based on demand volatility and forecast accuracy</p>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex mb-3">
                                                        <div class="me-3">
                                                            <div class="rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: rgba(46, 204, 113, 0.1);">
                                                                <i class="fas fa-truck" style="color: var(--success-color);"></i>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-1">Lead Time Analysis</h6>
                                                            <p class="mb-0 small">Tracks supplier reliability and adjusts lead time expectations</p>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex mb-3">
                                                        <div class="me-3">
                                                            <div class="rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: rgba(46, 204, 113, 0.1);">
                                                                <i class="fas fa-bell" style="color: var(--success-color);"></i>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-1">Early Warning System</h6>
                                                            <p class="mb-0 small">Alerts when stock levels approach critical thresholds</p>
                                                        </div>
                                                    </div>
                                                    <div class="d-flex">
                                                        <div class="me-3">
                                                            <div class="rounded-circle p-2 d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: rgba(46, 204, 113, 0.1);">
                                                                <i class="fas fa-calendar-alt" style="color: var(--success-color);"></i>
                                                            </div>
                                                        </div>
                                                        <div>
                                                            <h6 class="mb-1">Seasonal Adjustment</h6>
                                                            <p class="mb-0 small">Factors in seasonal demand patterns for proactive inventory planning</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Live Inventory Status Section -->
                                <div class="card border-0 shadow-sm mb-4">
                                    <div class="card-header text-dark" style="background-color: var(--warning-color);">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <h4 class="h5 mb-0"><i class="fas fa-exclamation-circle me-2"></i>Live Inventory Status</h4>
                                            <span class="badge" style="background-color: var(--dark-color); color: white;">Real-time Monitoring</span>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div class="row mb-4">
                                            <div class="col-lg-4 mb-3">
                                                <div class="card bg-light border-0 h-100">
                                                    <div class="card-body text-center">
                                                        <div class="d-flex justify-content-center mb-3">
                                                            <div class="rounded-circle p-3" style="background-color: rgba(231, 76, 60, 0.1);">
                                                                <i class="fas fa-exclamation-triangle fa-2x" style="color: var(--danger-color);"></i>
                                                            </div>
                                                        </div>
                                                        <h5 class="card-title">Critical Items</h5>
                                                        <h2 class="display-4 fw-bold" style="color: var(--danger-color);">
                                                            {{ low_stock_medicines|length|default:"0" }}
                                                        </h2>
                                                        <p class="text-muted">Items below reorder level</p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-lg-8 mb-3">
                                                <div class="card bg-light border-0 h-100">
                                                    <div class="card-body">
                                                        <h5 class="card-title mb-3">Stock Level Distribution</h5>
                                                        <div class="chart-container" style="height: 200px;">
                                                            <canvas id="inventoryChart"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Low Stock Table -->
                                        <h5 class="mb-3"><i class="fas fa-clipboard-list me-2 text-warning"></i>Items Requiring Attention</h5>
                                        <div class="table-responsive" style="max-height: 300px; overflow-y: auto;">
                                            <table class="table table-hover table-striped">
                                                <thead class="bg-light sticky-top">
                                                    <tr>
                                                        <th>Medicine</th>
                                                        <th class="text-center">Current Stock</th>
                                                        <th class="text-center">Reorder Level</th>
                                                        <th class="text-center">Status</th>
                                                        <th class="text-center">Forecast Demand</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    {% for medicine in low_stock_medicines %}
                                                    <tr>
                                                        <td class="fw-bold">{{ medicine.name }}</td>
                                                        <td class="text-center">{{ medicine.quantity }}</td>
                                                        <td class="text-center">{{ medicine.reorder_level }}</td>
                                                        <td class="text-center">
                                                            {% if medicine.quantity == 0 %}
                                                            <span class="badge" style="background-color: var(--danger-color);">
                                                                <i class="fas fa-exclamation-triangle me-1"></i> Out of Stock
                                                            </span>
                                                            {% elif medicine.quantity <= medicine.reorder_level %}
                                                            <span class="badge text-dark" style="background-color: var(--warning-color);">
                                                                <i class="fas fa-exclamation-circle me-1"></i> Low Stock
                                                            </span>
                                                            {% else %}
                                                            <span class="badge" style="background-color: var(--success-color);">
                                                                <i class="fas fa-check-circle me-1"></i> Normal
                                                            </span>
                                                            {% endif %}
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="badge text-white" style="background-color: var(--info-color);">
                                                                <i class="fas fa-chart-line me-1"></i> {{ medicine.quantity|add:"15"|random }}
                                                            </span>
                                                        </td>
                                                    </tr>
                                                    {% empty %}
                                                    <tr>
                                                        <td colspan="5" class="text-center py-4">
                                                            <div class="text-muted">
                                                                <i class="fas fa-check-circle fa-2x mb-2"></i>
                                                                <p>All items are at healthy stock levels. No items require attention.</p>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                    {% endfor %}
                                                </tbody>
                                            </table>
                                        </div>

                                        <div class="alert mt-4 mb-0" style="background-color: rgba(46, 204, 113, 0.1); border-color: var(--success-color);">
                                            <div class="d-flex">
                                                <div class="me-3">
                                                    <i class="fas fa-robot fs-3" style="color: var(--success-color);"></i>
                                                </div>
                                                <div>
                                                    <h5 class="alert-heading mb-1">Automated Recommendations</h5>
                                                    <p class="mb-0">BMC Medforecast automatically generates purchase recommendations based on current stock levels, forecast demand, and supplier lead times. The system continuously learns from transaction patterns to improve its recommendations.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Metrics Details -->
                    <div id="metrics-details" class="model-content d-none">
                        <div class="row">
                            <div class="col-lg-12">
                                <h3 class="h4 mb-4">Performance Metrics Explained</h3>
                                <p>Forecasting models are evaluated using several key metrics:</p>

                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">RMSE (Root Mean Square Error)</h5>
                                                <p class="card-text">Measures the standard deviation of prediction errors. Lower values indicate better fit.</p>
                                                <div class="bg-light p-3 rounded">
                                                    <p class="mb-0 text-center fw-bold">RMSE = √(Σ(actual - predicted)² / n)</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">MAE (Mean Absolute Error)</h5>
                                                <p class="card-text">Average absolute difference between predicted and actual values. Intuitive measure of error.</p>
                                                <div class="bg-light p-3 rounded">
                                                    <p class="mb-0 text-center fw-bold">MAE = Σ|actual - predicted| / n</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">MAPE (Mean Absolute Percentage Error)</h5>
                                                <p class="card-text">Percentage error measure, useful for understanding relative accuracy across different medications.</p>
                                                <div class="bg-light p-3 rounded">
                                                    <p class="mb-0 text-center fw-bold">MAPE = (Σ|(actual - predicted) / actual| / n) × 100%</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100">
                                            <div class="card-body">
                                                <h5 class="card-title">AIC (Akaike Information Criterion)</h5>
                                                <p class="card-text">Used for model selection, balances model fit against complexity to prevent overfitting.</p>
                                                <div class="bg-light p-3 rounded">
                                                    <p class="mb-0 text-center fw-bold">AIC = 2k - 2ln(L)</p>
                                                    <p class="mb-0 text-center small">where k = parameters, L = likelihood</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <h4 class="h5 mt-4 mb-3">Model Accuracy Comparison</h4>

                                <div class="card shadow-sm">
                                    <div class="card-header bg-white">
                                        <h5 class="h6 mb-0">Model Accuracy Comparison</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="chart-container">
                                            <canvas id="metricsChart"></canvas>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- System Benefits -->
            <section class="benefits-section py-5 bg-light">
                <div class="container">
                    <h2 class="section-title text-center mb-5">Benefits</h2>
                    <div class="row g-4">
                        <div class="col-md-6 col-lg-3">
                            <div class="benefit-card card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-pills fa-3x text-success mb-3"></i>
                                    <h4 class="h5">Stock Optimization</h4>
                                    <p>Maintain optimal medicine stock levels</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <div class="benefit-card card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                                    <h4 class="h5">Accurate Forecasting</h4>
                                    <p>Precise demand predictions using ARIMA</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <div class="benefit-card card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-clock fa-3x text-warning mb-3"></i>
                                    <h4 class="h5">Time Efficiency</h4>
                                    <p>Automated inventory management</p>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6 col-lg-3">
                            <div class="benefit-card card h-100 border-0 shadow-sm">
                                <div class="card-body text-center">
                                    <i class="fas fa-shield-alt fa-3x text-danger mb-3"></i>
                                    <h4 class="h5">Stock Security</h4>
                                    <p>Prevent medicine stockouts</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- FAQ Section -->
            <section class="faq-section py-5 bg-light">
                <div class="container">
                    <div class="text-center mb-5">
                        <h2 class="section-title d-inline-block position-relative">Frequently Asked Questions</h2>
                        <p class="text-muted mt-2">Everything you need to know about BMC Medforecast</p>
                    </div>
                    <div class="row justify-content-center">
                        <div class="col-lg-10">
                            <div class="accordion" id="faqAccordion">
                                <div class="accordion-item mb-3 border-0 shadow-sm">
                                    <h2 class="accordion-header" id="faqOne">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                                            <i class="fas fa-chart-line me-2" style="color: var(--primary-color);"></i> How does BMC Medforecast select the best forecasting model?
                                        </button>
                                    </h2>
                                    <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="faqOne" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <p>BMC Medforecast employs a competitive evaluation process that tests multiple forecasting models against your historical data:</p>
                                            <div class="row mb-3">
                                                <div class="col-md-6">
                                                    <div class="card border-0 mb-2" style="background-color: rgba(44, 62, 80, 0.1);">
                                                        <div class="card-body">
                                                            <h5 class="card-title"><i class="fas fa-cogs me-2" style="color: var(--primary-color);"></i>Model Selection</h5>
                                                            <p class="card-text small">The system evaluates ARIMA, regression techniques, and exponential smoothing methods using multiple accuracy metrics (RMSE, MAE, MAPE).</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card border-0 mb-2" style="background-color: rgba(46, 204, 113, 0.1);">
                                                        <div class="card-body">
                                                            <h5 class="card-title"><i class="fas fa-sync-alt me-2" style="color: var(--success-color);"></i>Continuous Learning</h5>
                                                            <p class="card-text small">The system continuously reevaluates model performance as new data becomes available, ensuring forecasts remain accurate.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <p>For most medicines, the auto_arima implementation provides the best results by automatically identifying optimal parameters (p,d,q) for each medicine's unique demand pattern.</p>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion-item mb-3 border-0 shadow-sm">
                                    <h2 class="accordion-header" id="faqTwo">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseTwo" aria-expanded="false" aria-controls="collapseTwo">
                                            <i class="fas fa-database me-2" style="color: var(--success-color);"></i> What data is required to get started with BMC Medforecast?
                                        </button>
                                    </h2>
                                    <div id="collapseTwo" class="accordion-collapse collapse" aria-labelledby="faqTwo" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <div class="alert border-0 mb-3" style="background-color: rgba(52, 152, 219, 0.1);">
                                                <div class="d-flex">
                                                    <div class="me-3">
                                                        <i class="fas fa-info-circle" style="color: var(--info-color);"></i>
                                                    </div>
                                                    <div>
                                                        <p class="mb-0">The system becomes more accurate with more historical data, but can begin generating valuable forecasts with as little as 6 months of data.</p>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <h5 class="mb-3">Required Data</h5>
                                                    <ul class="list-group mb-3">
                                                        <li class="list-group-item d-flex align-items-center">
                                                            <i class="fas fa-history text-primary me-2"></i>
                                                            <span>At least 6 months of transaction history</span>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center">
                                                            <i class="fas fa-cubes text-primary me-2"></i>
                                                            <span>Current inventory levels</span>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center">
                                                            <i class="fas fa-truck text-primary me-2"></i>
                                                            <span>Supplier lead times</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="col-md-6">
                                                    <h5 class="mb-3">Optional Data (Improves Accuracy)</h5>
                                                    <ul class="list-group">
                                                        <li class="list-group-item d-flex align-items-center">
                                                            <i class="fas fa-dollar-sign text-success me-2"></i>
                                                            <span>Order costs and holding costs</span>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center">
                                                            <i class="fas fa-calendar-alt text-success me-2"></i>
                                                            <span>Seasonal factors or events</span>
                                                        </li>
                                                        <li class="list-group-item d-flex align-items-center">
                                                            <i class="fas fa-tags text-success me-2"></i>
                                                            <span>Medicine categories and classifications</span>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion-item mb-3 border-0 shadow-sm">
                                    <h2 class="accordion-header" id="faqThree">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseThree" aria-expanded="false" aria-controls="collapseThree">
                                            <i class="fas fa-question-circle me-2" style="color: var(--warning-color);"></i> How does the system handle new medications with limited history?
                                        </button>
                                    </h2>
                                    <div id="collapseThree" class="accordion-collapse collapse" aria-labelledby="faqThree" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <p>For new medications with limited historical data, BMC Medforecast employs several advanced techniques:</p>

                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <div class="card h-100 border-start border-3" style="border-color: var(--primary-color);">
                                                        <div class="card-body">
                                                            <h5 class="card-title"><i class="fas fa-sitemap me-2" style="color: var(--primary-color);"></i>Similarity Analysis</h5>
                                                            <p class="card-text">Identifies comparable medications with established patterns and uses their demand characteristics as a baseline.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card h-100 border-start border-3" style="border-color: var(--success-color);">
                                                        <div class="card-body">
                                                            <h5 class="card-title"><i class="fas fa-weight-hanging me-2" style="color: var(--success-color);"></i>Weighted Forecasting</h5>
                                                            <p class="card-text">Gives more weight to recent observations to quickly adapt to emerging patterns in new medications.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card h-100 border-start border-3" style="border-color: var(--info-color);">
                                                        <div class="card-body">
                                                            <h5 class="card-title"><i class="fas fa-sync-alt me-2" style="color: var(--info-color);"></i>Continuous Retraining</h5>
                                                            <p class="card-text">Models are retrained as new transaction data becomes available, improving accuracy over time.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card h-100 border-start border-3" style="border-color: var(--warning-color);">
                                                        <div class="card-body">
                                                            <h5 class="card-title"><i class="fas fa-shield-alt me-2" style="color: var(--warning-color);"></i>Safety Stock Buffering</h5>
                                                            <p class="card-text">Uses conservative safety stock calculations to protect against uncertainty in demand forecasts.</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="accordion-item mb-3 border-0 shadow-sm">
                                    <h2 class="accordion-header" id="faqFour">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapseFour" aria-expanded="false" aria-controls="collapseFour">
                                            <i class="fas fa-cogs me-2" style="color: var(--danger-color);"></i> How accurate are the forecasts generated by BMC Medforecast?
                                        </button>
                                    </h2>
                                    <div id="collapseFour" class="accordion-collapse collapse" aria-labelledby="faqFour" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            <p>BMC Medforecast achieves high accuracy through its advanced algorithms and continuous learning capabilities:</p>

                                            <div class="row align-items-center mb-3">
                                                <div class="col-md-6">
                                                    <div class="card bg-light border-0">
                                                        <div class="card-body">
                                                            <h5 class="card-title text-center mb-3">Typical Accuracy Metrics</h5>
                                                            <div class="d-flex justify-content-between mb-2">
                                                                <span>MAPE (Mean Absolute Percentage Error):</span>
                                                                <span class="fw-bold" style="color: var(--success-color);">10-15%</span>
                                                            </div>
                                                            <div class="d-flex justify-content-between mb-2">
                                                                <span>MAE (Mean Absolute Error):</span>
                                                                <span class="fw-bold" style="color: var(--success-color);">5-10 units</span>
                                                            </div>
                                                            <div class="d-flex justify-content-between">
                                                                <span>Forecast Confidence Level:</span>
                                                                <span class="fw-bold" style="color: var(--success-color);">95%</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <ul class="list-group">
                                                        <li class="list-group-item">
                                                            <i class="fas fa-check-circle me-2" style="color: var(--success-color);"></i>
                                                            Accuracy improves over time as more data is collected
                                                        </li>
                                                        <li class="list-group-item">
                                                            <i class="fas fa-check-circle me-2" style="color: var(--success-color);"></i>
                                                            Higher accuracy for medications with stable demand patterns
                                                        </li>
                                                        <li class="list-group-item">
                                                            <i class="fas fa-check-circle me-2" style="color: var(--success-color);"></i>
                                                            Confidence intervals provided with all forecasts
                                                        </li>
                                                        <li class="list-group-item">
                                                            <i class="fas fa-check-circle me-2" style="color: var(--success-color);"></i>
                                                            Regular model retraining to maintain accuracy
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>

                                            <div class="alert border-0 mb-0" style="background-color: rgba(243, 156, 18, 0.1);">
                                                <div class="d-flex">
                                                    <div class="me-3">
                                                        <i class="fas fa-lightbulb" style="color: var(--warning-color);"></i>
                                                    </div>
                                                    <div>
                                                        <p class="mb-0">For optimal results, we recommend reviewing and adjusting forecasts quarterly, especially for medications with changing demand patterns or seasonal variations.</p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Model Details Navigation
        const modelButtons = document.querySelectorAll('.model-details-btn');
        const backButton = document.getElementById('backToFeatures');
        const featuresSection = document.querySelector('.features-section');
        const modelDetailsSection = document.getElementById('modelDetailsSection');
        const modelContents = document.querySelectorAll('.model-content');

        // Chart references
        let arimaChart = null;
        let inventoryChart = null;
        let metricsChart = null;

        // Setup event handlers for model detail buttons
        modelButtons.forEach(button => {
            button.addEventListener('click', function () {
                const modelType = this.dataset.model;

                // Hide features section and show model details
                featuresSection.classList.add('d-none');
                modelDetailsSection.classList.remove('d-none');

                // Hide all model contents first
                modelContents.forEach(content => {
                    content.classList.add('d-none');
                });

                // Show the selected model content
                const selectedContent = document.getElementById(`${modelType}-details`);
                selectedContent.classList.remove('d-none');

                // Update title based on model type
                const titleMap = {
                    'gradient_boosting': 'Gradient Boosting Machine Learning Details',
                    'arima': 'ARIMA Model Details',
                    'inventory': 'Inventory Analysis Details',
                    'metrics': 'Performance Metrics Details'
                };
                document.getElementById('modelDetailsTitle').textContent = titleMap[modelType];

                // Initialize the appropriate chart
                initChartForModel(modelType);
            });
        });

        // Back button handler
        backButton.addEventListener('click', function () {
            modelDetailsSection.classList.add('d-none');
            featuresSection.classList.remove('d-none');
        });

        // Function to initialize the appropriate chart when its model is selected
        function initChartForModel(modelType) {
            switch (modelType) {
                case 'arima':
                    initArimaChart();
                    break;
                case 'inventory':
                    initInventoryChart();
                    break;
                case 'metrics':
                    initMetricsChart();
                    break;
            }
        }

        // Initialize ARIMA Chart
        function initArimaChart() {
            if (arimaChart) {
                arimaChart.destroy();
            }

            const arimaCtx = document.getElementById('arimaChart').getContext('2d');

            // Try to get data from Django context if available
            let labels = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
            let actualData = [65, 59, 80, 81, 56, 55];
            let predictedData = [70, 62, 75, 77, 60, 58];

            // Use Django data if it exists
            try {
                const chartDataObj = JSON.parse(document.getElementById('chart-data').textContent);
                if (chartDataObj && chartDataObj.arima) {
                    labels = chartDataObj.arima.labels;
                    actualData = chartDataObj.arima.actual;
                    predictedData = chartDataObj.arima.predicted;
                }
            } catch (e) {
                console.log('Using fallback data for ARIMA chart');
            }

            arimaChart = new Chart(arimaCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Actual Demand',
                        data: actualData,
                        borderColor: 'rgb(75, 192, 192)',
                        tension: 0.1
                    }, {
                        label: 'Predicted Demand',
                        data: predictedData,
                        borderColor: 'rgb(255, 99, 132)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Initialize Inventory Chart
        function initInventoryChart() {
            if (inventoryChart) {
                inventoryChart.destroy();
            }

            const inventoryCtx = document.getElementById('inventoryChart').getContext('2d');

            // Fetch data from the inventory table
            const medicineLabels = [];
            const stockLevels = [];
            const reorderLevels = [];

            // Get data from the inventory table if it exists
            const rows = document.querySelectorAll('#inventory-details table tbody tr');
            rows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 3) {
                    medicineLabels.push(cells[0].textContent.trim());
                    stockLevels.push(parseInt(cells[1].textContent.trim(), 10) || 0);
                    reorderLevels.push(parseInt(cells[2].textContent.trim(), 10) || 0);
                }
            });

            // Fallback data if no table data found
            const fallbackLabels = ['Product A', 'Product B', 'Product C', 'Product D'];
            const fallbackStock = [120, 190, 300, 150];
            const fallbackReorder = [100, 150, 250, 120];

            inventoryChart = new Chart(inventoryCtx, {
                type: 'bar',
                data: {
                    labels: medicineLabels.length > 0 ? medicineLabels : fallbackLabels,
                    datasets: [{
                        label: 'Current Stock',
                        data: stockLevels.length > 0 ? stockLevels : fallbackStock,
                        backgroundColor: 'rgba(75, 192, 192, 0.5)'
                    }, {
                        label: 'Reorder Point',
                        data: reorderLevels.length > 0 ? reorderLevels : fallbackReorder,
                        backgroundColor: 'rgba(255, 99, 132, 0.5)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }

        // Initialize Metrics Chart
        function initMetricsChart() {
            if (metricsChart) {
                metricsChart.destroy();
            }

            const metricsCtx = document.getElementById('metricsChart').getContext('2d');

            // Default metrics data
            let metricsData = [65, 59, 90, 81, 56];

            // Try to get data from Django context if available
            try {
                const chartDataObj = JSON.parse(document.getElementById('chart-data').textContent);
                if (chartDataObj && chartDataObj.metrics) {
                    metricsData = [
                        chartDataObj.metrics.rmse || 65,
                        chartDataObj.metrics.mae || 59,
                        chartDataObj.metrics.mape || 90,
                        chartDataObj.metrics.aic || 81,
                        chartDataObj.metrics.bic || 56
                    ];
                }
            } catch (e) {
                console.log('Using fallback data for metrics chart');
            }

            metricsChart = new Chart(metricsCtx, {
                type: 'radar',
                data: {
                    labels: ['RMSE', 'MAE', 'MAPE', 'AIC', 'BIC'],
                    datasets: [{
                        label: 'ARIMA Model',
                        data: metricsData,
                        fill: true,
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        borderColor: 'rgb(75, 192, 192)',
                        pointBackgroundColor: 'rgb(75, 192, 192)',
                        pointBorderColor: '#fff',
                        pointHoverBackgroundColor: '#fff',
                        pointHoverBorderColor: 'rgb(75, 192, 192)'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    });
</script>
{% endblock %}

