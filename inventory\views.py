﻿from django.shortcuts import render, redirect, get_object_or_404
import numpy as np
from .models import Medicine, Transaction, Forecast, AuditTrail, StockMovement, MovementAnalysis, GenericMedicine
from .forms import MedicineForm, TransactionForm, ForecastForm, StockMovementForm
from django.contrib import messages
from django.db.models import Avg, F, Sum, Count, Q, Case, When
from django.utils import timezone
from django.db import transaction
from django.http import JsonResponse
from .utils import (
    generate_forecast, train_model, make_prediction,
    calculate_mae, calculate_rmse, calculate_average_mape,
    generate_forecast_for_month
)
import json
from decimal import Decimal
from django.http import JsonResponse
from .forecasting import predict_demand,calculate_accuracy, simple_linear_regression, multiple_linear_regression, polynomial_regression, ridge_regression, lasso_regression, elastic_net_regression
from django.contrib.contenttypes.models import ContentType
import pandas as pd
from sklearn.model_selection import train_test_split
from datetime import timedelta
from django.db.models import Sum, Avg
from django.contrib.auth.decorators import login_required

from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.db.models import Q
from django.contrib.humanize.templatetags.humanize import intcomma

from django.http import HttpResponse
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
from openpyxl.utils import get_column_letter
import io

from django.http import FileResponse
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter
from io import BytesIO
from django.views.decorators.http import require_http_methods, require_POST
from .utils import generate_excel_report, import_medicines_from_csv, import_transactions_from_csv
import pandas as pd
import numpy as np
import logging
import csv
from .forecasting import get_forecasting_results, update_forecasts

@login_required
def home(request):
    try:
        # Get current date and 30 days ago
        today = timezone.now()
        thirty_days_ago = today - timedelta(days=30)

        # Get monthly data for the past 6 months
        months = []
        actual_sales = []
        forecast_values = []

        # Calculate sales and forecasts for the last 6 months
        for i in range(5, -1, -1):
            month_start = today.replace(day=1) - timedelta(days=30 * i)
            month_end = (month_start.replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1)

            # Get actual sales for this month
            month_sales = Transaction.objects.filter(
                transaction_type='sale',
                transaction_date__range=(month_start, month_end)
            ).aggregate(total=Sum('total_amount'))['total'] or 0

            months.append(month_start.strftime('%b %Y'))
            actual_sales.append(float(month_sales))

            # Get forecast for the current and future months only
            if i <= 1:  # Only for current month and last month
                forecast = Transaction.objects.filter(
                    transaction_type='sale',
                    transaction_date__range=(month_start, month_end)
                ).aggregate(
                    total=Sum('total_amount')
                )['total'] or 0

                # Add 10% projected growth for demonstration
                # In a real application, you would use your actual forecasting algorithm
                forecast_value = float(forecast) * 1.1
                forecast_values.append(forecast_value)
            else:
                forecast_values.append(None)

        # Add next month's forecast
        next_month_start = (today.replace(day=1) + timedelta(days=32)).replace(day=1)
        months.append(next_month_start.strftime('%b %Y'))

        # Calculate next month's forecast based on current month's trend
        last_month_sales = actual_sales[-1]
        next_month_forecast = last_month_sales * 1.1  # Assuming 10% growth
        forecast_values.append(float(next_month_forecast))

        # Get forecast metrics
        forecasts = Forecast.objects.all()
        avg_accuracy = forecasts.aggregate(Avg('accuracy'))['accuracy__avg'] or 85
        avg_mape = forecasts.aggregate(Avg('mape'))['mape__avg'] or 15
        avg_mae = forecasts.aggregate(Avg('mae'))['mae__avg'] or 5

        # Calculate forecast accuracy change (simulated for now)
        forecast_accuracy_change = 2.5  # Simulated improvement
        forecast_mape_change = -1.2  # Simulated improvement (negative is good for MAPE)
        forecast_mae_change = -0.8  # Simulated improvement

        # Calculate Gradient Boosting adoption rate
        try:
            total_forecasts = Forecast.objects.count()
            if total_forecasts > 0:
                gb_forecasts = Forecast.objects.filter(forecast_method='gradient_boosting').count()
                gb_adoption_rate = round((gb_forecasts / total_forecasts) * 100)
            else:
                gb_adoption_rate = 97  # Default value if no forecasts exist
        except Exception as e:
            logging.error(f"Error calculating GB adoption rate: {str(e)}")
            gb_adoption_rate = 97  # Default fallback value

        # Prepare the context
        context = {
            'total_medicines': Medicine.objects.count(),
            'low_stock_count': Medicine.objects.filter(
                quantity__lte=F('reorder_level')
            ).count(),

            'total_sales': Transaction.objects.filter(
                transaction_type='sale',
                transaction_date__gte=thirty_days_ago
            ).aggregate(
                total=Sum('total_amount')
            )['total'] or 0,

            'patients_served': Transaction.objects.filter(
                transaction_type='sale',
                transaction_date__gte=thirty_days_ago
            ).values('customer_type').distinct().count(),

            'recent_transactions': Transaction.objects.select_related('medicine').filter(
                transaction_date__isnull=False
            ).order_by('-transaction_date')[:5],

            'low_stock_medicines': Medicine.objects.filter(
                quantity__lte=F('reorder_level')
            ).select_related(),

            'category_data': json.dumps({
                'labels': [cat['category'] or 'Uncategorized' for cat in
                          Medicine.objects.values('category').annotate(count=Count('id'))],
                'data': [cat['count'] for cat in
                        Medicine.objects.values('category').annotate(count=Count('id'))]
            }),

            'forecast_data': json.dumps({
                'labels': months,
                'actual': actual_sales,
                'forecast': forecast_values,
                'lower_bound': [val * 0.85 if val is not None else None for val in forecast_values],
                'upper_bound': [val * 1.15 if val is not None else None for val in forecast_values]
            }),

            # Add forecast metrics
            'forecast_accuracy': round(avg_accuracy, 1),
            'forecast_accuracy_change': forecast_accuracy_change,
            'forecast_mape': round(avg_mape, 1),
            'forecast_mape_change': forecast_mape_change,
            'forecast_mae': round(avg_mae, 1),
            'forecast_mae_change': forecast_mae_change,
            'forecast_horizon': 30,
            'gb_adoption_rate': gb_adoption_rate
        }

        return render(request, 'home.html', context)

    except Exception as e:
        print(f"Error in home view: {str(e)}")
        import traceback
        traceback.print_exc()

        # Return minimal context in case of error
        return render(request, 'home.html', {
            'error_message': str(e),
            'total_medicines': 0,
            'low_stock_count': 0,
            'total_sales': 0,
            'patients_served': 0,
            'recent_transactions': [],
            'low_stock_medicines': [],
            'forecast_accuracy': 85,
            'forecast_accuracy_change': 2.5,
            'forecast_mape': 15,
            'forecast_mape_change': -1.2,
            'forecast_mae': 5,
            'forecast_mae_change': -0.8,
            'forecast_horizon': 30,
            'gb_adoption_rate': 97,
            'category_data': json.dumps({'labels': [], 'data': []}),
            'forecast_data': json.dumps({
                'labels': [],
                'actual': [],
                'forecast': [],
                'lower_bound': [],
                'upper_bound': []
            })
        })

@login_required
def medicine_list(request):
    # Get the sorting parameter from the URL, default to 'name' if not specified
    sort_by = request.GET.get('sort_by', 'name')

    # Get the search query from the URL
    search_query = request.GET.get('search', '')

    # Filter medicines based on the search query
    medicines = Medicine.objects.filter(
        Q(name__icontains=search_query) |
        Q(category__icontains=search_query) |
        Q(brand__icontains=search_query) |
        Q(supplier__icontains=search_query)
    )

    # Apply sorting
    if sort_by in ['name', '-name', 'category', '-category', 'quantity', '-quantity',
                  'brand', '-brand', 'supplier', '-supplier', 'price', '-price']:
        medicines = medicines.order_by(sort_by)
    else:
        medicines = medicines.order_by('name')  # Default sorting

    # Get the page number from the URL, default to 1 if not specified
    page = request.GET.get('page', 1)

    # Create a Paginator object with 10 items per page
    paginator = Paginator(medicines, 10)

    try:
        # Get the requested page
        medicines = paginator.page(page)
    except PageNotAnInteger:
        # If page is not an integer, deliver the first page
        medicines = paginator.page(1)
    except EmptyPage:
        # If page is out of range (e.g., 9999), deliver the last page of results
        medicines = paginator.page(paginator.num_pages)

    # Render the template with the medicines and sorting parameter
    return render(request, 'medicine_list.html', {'medicines': medicines, 'sort_by': sort_by, 'search_query': search_query})



def download_medicines_excel(request):
    # Create a workbook and select the default worksheet
    output = io.BytesIO()
    workbook = Workbook()
    worksheet = workbook.active

    # Add headers
    worksheet.append(['Name', 'Brand', 'Supplier', 'Generic Medicine', 'Quantity', 'Price', 'Category'])

    # Add medicines data
    medicines = Medicine.objects.select_related('generic_medicine').all()
    for medicine in medicines:
        worksheet.append([
            medicine.name,
            medicine.brand,
            medicine.supplier,
            medicine.generic_medicine.name if medicine.generic_medicine else 'N/A',
            medicine.quantity,
            medicine.price,
            medicine.category
        ])

    # Apply styling
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="2C3E50", end_color="2C3E50", fill_type="solid")

    for cell in worksheet[1]:
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal='center')

    # Auto-adjust column widths
    for column in worksheet.columns:
        max_length = 0
        column_letter = get_column_letter(column[0].column)
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        worksheet.column_dimensions[column_letter].width = adjusted_width

    # Save the workbook to the output stream
    workbook.save(output)
    output.seek(0)

    # Create an HTTP response with the output stream as the content
    response = HttpResponse(output, content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response['Content-Disposition'] = 'attachment; filename=medicines.xlsx'

    return response





@login_required
def medicine_detail(request, pk):
    medicine = get_object_or_404(Medicine, pk=pk)

    # Add expiration information to context
    context = {
        'medicine': medicine,
        'is_expired': medicine.is_expired(),
        'days_until_expiration': medicine.days_until_expiration(),
    }

    # Add expiration warning messages
    if medicine.expiration_date:
        days_until_expiration = medicine.days_until_expiration()
        if medicine.is_expired():
            messages.error(request, f'This medicine has expired on {medicine.expiration_date}')
        elif days_until_expiration <= 30:
            messages.warning(request, f'This medicine will expire in {days_until_expiration} days')

    # Find alternative medicines by category
    category_alternatives = Medicine.objects.filter(
        category=medicine.category
    ).exclude(
        id=medicine.id
    ).order_by('-quantity')[:5]

    # Find alternative medicines by brand
    brand_alternatives = Medicine.objects.filter(
        brand=medicine.brand
    ).exclude(
        id=medicine.id
    ).order_by('-quantity')[:5]

    # Find alternative medicines by supplier
    supplier_alternatives = Medicine.objects.filter(
        supplier=medicine.supplier
    ).exclude(
        id=medicine.id
    ).order_by('-quantity')[:5]

    # Find alternative medicines by generic medicine
    generic_alternatives = []
    if medicine.generic_medicine:
        generic_alternatives = Medicine.objects.filter(
            generic_medicine=medicine.generic_medicine
        ).exclude(
            id=medicine.id
        ).order_by('-quantity')[:5]

    # Add alternatives to context
    context.update({
        'category_alternatives': category_alternatives,
        'brand_alternatives': brand_alternatives,
        'supplier_alternatives': supplier_alternatives,
        'generic_alternatives': generic_alternatives,
    })

    return render(request, 'medicine_detail.html', context)


@login_required
def transaction_list(request):
    # Get current timezone-aware datetime
    current_time = timezone.now()

    # Get filter parameters
    search = request.GET.get('search', '').strip()
    filter_type = request.GET.get('filter', 'all')
    date_filter = request.GET.get('date_filter', 'all')
    page = request.GET.get('page', 1)

    # Base queryset with optimized loading
    transactions = Transaction.objects.select_related('medicine').only(
        'id', 'quantity', 'transaction_date', 'transaction_type',
        'medicine__name'
    ).order_by('-transaction_date')

    # Apply search filter
    if search:
        transactions = transactions.filter(
            Q(medicine__name__icontains=search) |
            Q(customer_name__icontains=search)
        )

    # Apply date filter
    if date_filter == 'today':
        today = timezone.now().date()
        transactions = transactions.filter(transaction_date__date=today)
    elif date_filter == 'week':
        week_ago = timezone.now().date() - timedelta(days=7)
        transactions = transactions.filter(transaction_date__date__gte=week_ago)
    elif date_filter == 'month':
        month_ago = timezone.now().date() - timedelta(days=30)
        transactions = transactions.filter(transaction_date__date__gte=month_ago)

    # Apply quantity filter
    if filter_type == 'high':
        transactions = transactions.filter(quantity__gte=500)
    elif filter_type == 'low':
        transactions = transactions.filter(quantity__lt=500)

    # Paginate results
    paginator = Paginator(transactions, 10)  # Show 10 transactions per page
    page_obj = paginator.get_page(page)

    context = {
        'transactions': page_obj,
        'current_time': current_time,
        'search': search,
        'filter_type': filter_type,
        'date_filter': date_filter,
        'total_transactions': transactions.count(),
    }

    return render(request, 'transaction_list.html', context)



@login_required
def transaction_detail(request, pk):
    transaction = get_object_or_404(Transaction, pk=pk)
    return render(request, 'transaction_detail.html', {'transaction': transaction})

@login_required
def inventory_list(request):
    # Get sort parameter from URL, default to 'name'
    sort_by = request.GET.get('sort_by', 'name')
    # Get search parameter from URL
    search_query = request.GET.get('search', '')

    # Base queryset with ordering
    medicine_list = Medicine.objects.all()

    # Apply search if provided
    if search_query:
        medicine_list = medicine_list.filter(
            Q(name__icontains=search_query) |
            Q(category__icontains=search_query)
        )

    # Apply sorting
    if sort_by in ['name', '-name', 'category', '-category', 'quantity', '-quantity']:
        medicine_list = medicine_list.order_by(sort_by)
    else:
        medicine_list = medicine_list.order_by('name')  # Default sorting

    # Pagination
    paginator = Paginator(medicine_list, 10)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)

    # Initialize alerts dictionary
    alerts = {}
    for medicine in medicine_list:
        alerts[medicine.pk] = []

        # Check for overstock
        if medicine.quantity > medicine.reorder_level + medicine.reorder_quantity:
            alerts[medicine.pk].append({
                'type': 'overstock',
                'message': f'Overstock alert. Current quantity ({medicine.quantity}) exceeds reorder level ({medicine.reorder_level}) plus reorder quantity ({medicine.reorder_quantity}).'
            })

        # Check for reorder needed
        if medicine.quantity <= medicine.reorder_level:
            alerts[medicine.pk].append({
                'type': 'reorder',
                'message': f'Reorder alert. Current quantity ({medicine.quantity}) is below or equal to reorder level ({medicine.reorder_level}).'
            })

        # Check for critical level
        if medicine.quantity <= (medicine.reorder_level * 0.5):
            alerts[medicine.pk].append({
                'type': 'critical',
                'message': f'Critical alert. Current quantity ({medicine.quantity}) is at critical level!'
            })

    # Get recent audit trails
    audit_trails = AuditTrail.objects.all().order_by('-created_at')[:10]  # Get only last 10 entries

    context = {
        'page_obj': page_obj,
        'alerts': alerts,
        'audit_trails': audit_trails,
        'search_query': search_query,
        'sort_by': sort_by,
        'current_page': page_number
    }

    return render(request, 'inventory_list.html', context)





@login_required
def audit_trail_list(request):
    audit_trails = AuditTrail.objects.all().order_by('-created_at')

    # Search functionality
    search_query = request.GET.get('search', '')
    if search_query:
        audit_trails = audit_trails.filter(
            Q(content_type__model__icontains=search_query) |
            Q(action__icontains=search_query) |
            Q(object_id__icontains=search_query)
        )

    # Pagination
    paginator = Paginator(audit_trails, 10)  # Show 10 items per page
    page_number = request.GET.get('page', 1)

    try:
        page_obj = paginator.page(page_number)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    context = {
        'audit_trails': page_obj,
        'page_obj': page_obj,
        'audit_trail_count': audit_trails.count(),
        'search_query': search_query,
        'current_datetime': timezone.now(),
    }

    return render(request, 'audit_trail_list.html', context)

@login_required
def transaction_audit_trail(request, pk):
    transaction = get_object_or_404(Transaction, pk=pk)
    audit_trail = transaction.audit_trail

    if audit_trail is None:
        messages.error(request, 'No audit trail found for this transaction.')
        return redirect('transaction_list')

    # Fetch related transactions
    related_transactions = audit_trail.transactions.all()

    # Fetch related medicines
    medicine_content_type = ContentType.objects.get_for_model(Medicine)
    related_medicines = Medicine.objects.filter(
        id__in=AuditTrail.objects.filter(
            content_type=medicine_content_type,
            object_id__in=related_transactions.values_list('medicine_id', flat=True)
        ).values_list('object_id', flat=True)
    )

    # Fetch related forecasts
    forecast_content_type = ContentType.objects.get_for_model(Forecast)
    related_forecasts = Forecast.objects.filter(
        id__in=AuditTrail.objects.filter(
            content_type=forecast_content_type,
            object_id__in=Forecast.objects.filter(medicine__in=related_medicines).values_list('id', flat=True)
        ).values_list('object_id', flat=True)
    )

    return render(request, 'transaction_audit_trail.html', {
        'audit_trail': audit_trail,
        'transaction': transaction,
        'related_transactions': related_transactions,
        'related_medicines': related_medicines,
        'related_forecasts': related_forecasts,
    })





def calculate_forecasts(request):
    medicines = Medicine.objects.all()
    for medicine in medicines:
        forecasted_quantity = predict_demand(medicine)
        Forecast.objects.create(medicine=medicine, forecast_date=timezone.now(), predicted_quantity=forecasted_quantity)
    return render(request, 'forecast_calculated.html')

def add_quantity(request, pk):
    medicine = get_object_or_404(Medicine, pk=pk)
    if request.method == 'POST':
        quantity = int(request.POST.get('quantity'))
        medicine.quantity += quantity
        medicine.save()
        content_type = ContentType.objects.get_for_model(medicine)
        AuditTrail.objects.create(content_object=medicine, content_type=content_type, object_id=medicine.pk, action='add_quantity')
        messages.success(request, f'Added {quantity} units of {medicine.name} to the inventory.')
        return redirect('medicine_list')
    return render(request, 'add_quantity.html', {'medicine': medicine})


from django.shortcuts import render, redirect, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Avg, Min, Max, Sum, F
from django.utils import timezone
from django.contrib import messages
from datetime import datetime, timedelta
import logging
from django.core.cache import cache
from django.views.decorators.cache import cache_page
from .models import Medicine, Transaction, Forecast, AuditTrail
from .forms import MedicineForm, TransactionForm, ForecastForm
from django.contrib.contenttypes.models import ContentType
from .utils import (
    generate_forecast,
    calculate_mae,
    calculate_rmse,
    calculate_average_mape,
    batch_generate_forecasts
)
from .forecasting import (
    analyze_quarterly_patterns,
    predict_demand,
    simple_linear_regression,
    multiple_linear_regression,
    polynomial_regression,
    ridge_regression,
    lasso_regression,
    elastic_net_regression
)

logger = logging.getLogger(__name__)

from django.utils import timezone
from django.core.paginator import Paginator
from .models import Forecast

@login_required
def forecast_list(request):
    try:
        # Get search query from URL
        search_query = request.GET.get('search', '')

        # Base queryset with medicine data prefetched
        forecasts = Forecast.objects.select_related('medicine').all()

        # Debug print
        logger.info(f"Initial forecast count: {forecasts.count()}")

        # Apply search if provided
        if search_query:
            forecasts = forecasts.filter(
                Q(medicine__name__icontains=search_query) |
                Q(medicine__category__icontains=search_query)
            )
            logger.info(f"Forecast count after search filter '{search_query}': {forecasts.count()}")

        # Order by forecast date
        forecasts = forecasts.order_by('-forecast_date')

        # Pagination
        paginator = Paginator(forecasts, 10)  # 10 items per page
        page_number = request.GET.get('page')
        page_obj = paginator.get_page(page_number)

        # Debug print for paginated results
        for forecast in page_obj:
            logger.debug(f"Forecast: {forecast.medicine.name if forecast.medicine else 'No medicine'} - {forecast.forecast_date}")

        context = {
            'page_obj': page_obj,
            'total_forecasts': forecasts.count(),
            'search_query': search_query,
            'current_page': page_number or 1,
            'total_pages': paginator.num_pages
        }

        return render(request, 'forecast_list.html', context)

    except Exception as e:
        logger.error(f"Error in forecast_list view: {str(e)}", exc_info=True)
        messages.error(request, f"An error occurred while loading forecasts. Please try again.")
        return render(request, 'forecast_list.html', {
            'error': "Unable to load forecasts at this time.",
            'search_query': search_query
        })

def calculate_forecast_accuracy(medicine):
    """
    Calculate forecast accuracy for a medicine based on historical predictions
    """
    try:
        # Get last month's forecast
        last_month = timezone.now().date() - timedelta(days=30)
        forecast = Forecast.objects.filter(
            medicine=medicine,
            forecast_date__gte=last_month
        ).first()

        if not forecast:
            return 0

        # Get actual consumption
        actual_consumption = Transaction.objects.filter(
            medicine=medicine,
            transaction_date__gte=last_month
        ).aggregate(
            total=Sum('quantity')
        )['total'] or 0

        predicted = forecast.predicted_quantity

        if predicted == 0:
            return 0

        accuracy = (1 - abs(actual_consumption - predicted) / predicted) * 100
        return min(max(accuracy, 0), 100)  # Clamp between 0 and 100

    except Exception as e:
        print(f"Error calculating forecast accuracy: {e}")
        return 0

from django.shortcuts import render
from django.db import transaction, OperationalError, connection
from django.core.cache import cache
from django.contrib.auth.decorators import login_required
from django.views.decorators.http import require_http_methods
from django.http import JsonResponse
from django.utils import timezone
import logging
from .models import Medicine, Forecast
from .utils import batch_generate_forecasts

logger = logging.getLogger(__name__)

@login_required
@require_http_methods(["POST"])
def generate_forecasts(request):
    """
    Generate new forecasts for all medicines with sequential processing
    to avoid connection issues
    """
    forecasts_created = 0
    needs_ordering = 0
    errors = []

    try:
        LOCK_KEY = 'generating_forecasts'
        if cache.get(LOCK_KEY):
            return JsonResponse({
                'status': 'warning',
                'message': 'Forecast generation already in progress. Please wait.'
            })

        cache.set(LOCK_KEY, True, timeout=600)

        # Get all medicine IDs
        medicine_pks = list(Medicine.objects.values_list('pk', flat=True))

        # Process medicines one at a time
        for medicine_pk in medicine_pks:
            try:
                with transaction.atomic():
                    # Get medicine with minimal query
                    medicine = Medicine.objects.select_for_update().only(
                        'id', 'name', 'quantity', 'reorder_level'
                    ).get(pk=medicine_pk)

                    # Generate forecast
                    forecast_result = batch_generate_forecasts([medicine_pk])[0]

                    if not isinstance(forecast_result, tuple):
                        logger.error(f"Invalid forecast format for medicine {medicine_pk}")
                        continue

                    predicted_quantity = forecast_result[0]
                    metrics = {
                        'mae': forecast_result[1] if len(forecast_result) > 1 else 0,
                        'rmse': forecast_result[2] if len(forecast_result) > 2 else 0,
                        'mape': forecast_result[3] if len(forecast_result) > 3 else 0
                    }

                    # Calculate values
                    accuracy = (100 - metrics['mape']) if metrics['mape'] <= 100 else 0
                    current_stock = medicine.quantity
                    reorder_level = medicine.reorder_level
                    recommended_order_quantity = max(0, reorder_level - current_stock + predicted_quantity)

                    # Update or create forecast
                    Forecast.objects.update_or_create(
                        medicine=medicine,
                        forecast_date__date=timezone.now().date(),
                        defaults={
                            'predicted_quantity': predicted_quantity,
                            'mae': metrics['mae'],
                            'rmse': metrics['rmse'],
                            'mape': metrics['mape'],
                            'accuracy': accuracy,
                            'forecast_method': 'ARIMA',
                            'forecast_date': timezone.now(),
                            'recommended_order_quantity': recommended_order_quantity,
                            'confidence_lower': max(0, predicted_quantity * 0.8),
                            'confidence_upper': predicted_quantity * 1.2
                        }
                    )

                    forecasts_created += 1
                    if recommended_order_quantity > 0:
                        needs_ordering += 1

            except Exception as e:
                logger.error(f"Error processing forecast for medicine {medicine_pk}: {str(e)}")
                errors.append(f"Medicine {medicine_pk}: {str(e)}")
                connection.close()

        return JsonResponse({
            'status': 'success',
            'message': f'Successfully generated {forecasts_created} forecasts',
            'total_processed': forecasts_created,
            'needs_ordering': needs_ordering,
            'timestamp': timezone.now().isoformat(),
            'warnings': errors if errors else None
        })

    except Exception as e:
        logger.error(f"Error in forecast generation: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e),
            'total_processed': forecasts_created,
            'needs_ordering': needs_ordering
        }, status=500)

    finally:
        cache.delete(LOCK_KEY)
        connection.close()



from django.shortcuts import render
from django.core.paginator import Paginator
from .models import Medicine, Forecast
from django.db.models import Q

import statistics
from dateutil.relativedelta import relativedelta
from django.shortcuts import render, get_object_or_404
from django.http import HttpResponse
from django.db.models import Sum, Avg
from django.conf import settings
from .models import Medicine, Forecast, Transaction
from .utils import generate_forecast, export_forecast_report
from .forecasting import analyze_quarterly_patterns, get_forecasting_results
import json
import logging
from datetime import datetime, timedelta
import pandas as pd
from django.db.models.functions import TruncMonth,TruncWeek

from django.db.models import Avg, Min, Max, Sum, F, StdDev, Count
from dateutil.relativedelta import relativedelta
from django.db import models
# Configure logger
logger = logging.getLogger(__name__)

@login_required
def forecast_detail(request, pk):
    """
    View for displaying detailed forecast information for a specific forecast.
    """
    try:
        forecast = get_object_or_404(Forecast, pk=pk)
        medicine = forecast.medicine
        current_date = timezone.now()
        logger.info(f"Accessing forecast detail for medicine: {medicine.name} (Forecast ID: {pk})")

        # Get current date and start date (last 180 days)
        current_date = timezone.now()
        start_date = current_date - timedelta(days=180)

        # Query transactions with proper timezone awareness
        transactions = Transaction.objects.filter(
            medicine=medicine,
            transaction_date__gte=start_date,
            transaction_type='sale'
        ).order_by('-transaction_date')

        # Quarterly Analysis
        quarterly_data = {}
        for i in range(4):  # Last 4 quarters
            quarter_start = current_date - relativedelta(months=3 * (i + 1))
            quarter_end = current_date - relativedelta(months=3 * i)

            quarter_transactions = transactions.filter(
                transaction_date__gte=quarter_start,
                transaction_date__lt=quarter_end
            )

            total_quantity = quarter_transactions.aggregate(
                total=models.Sum('quantity')
            )['total'] or 0

            quarter_label = f"Q{4 - i} {quarter_start.year}"
            quarterly_data[quarter_label] = total_quantity

        # Find peak and low quarters
        if quarterly_data:
            peak_quarter = max(quarterly_data.items(), key=lambda x: x[1])
            low_quarter = min(quarterly_data.items(), key=lambda x: x[1])

            # Calculate quarter-over-quarter growth
            quarters = list(quarterly_data.items())
            qoq_growth = []
            for i in range(1, len(quarters)):
                if quarters[i-1][1] != 0:  # Avoid division by zero
                    growth = ((quarters[i][1] - quarters[i-1][1]) / quarters[i-1][1]) * 100
                    qoq_growth.append({
                        'period': f"{quarters[i-1][0]} to {quarters[i][0]}",
                        'growth': round(growth, 1)
                    })

            quarterly_analysis = {
                'data': quarterly_data,
                'peak_quarter': {
                    'period': peak_quarter[0],
                    'value': peak_quarter[1]
                },
                'low_quarter': {
                    'period': low_quarter[0],
                    'value': low_quarter[1]
                },
                'qoq_growth': qoq_growth
            }
        else:
            quarterly_analysis = None



        # Calculate monthly demand for past 6 months
        monthly_demand = {}
        for i in range(5, -1, -1):  # Loop from 5 to 0 (past 6 months)
            # Calculate month start and end dates
            month_start = current_date.replace(day=1, hour=0, minute=0, second=0, microsecond=0) - relativedelta(months=i)
            if i == 0:
                month_end = current_date
            else:
                month_end = (month_start + relativedelta(months=1)) - timedelta(microseconds=1)

            # Query transactions for this month
            month_transactions = transactions.filter(
                transaction_date__gte=month_start,
                transaction_date__lte=month_end
            )

            # Calculate total quantity for the month
            month_quantity = month_transactions.aggregate(
                total_quantity=models.Sum('quantity')
            )['total_quantity'] or 0

            # Store in monthly demand dictionary
            month_key = month_start.strftime('%b %Y')
            monthly_demand[month_key] = month_quantity

        # Calculate forecast accuracy
        forecast_accuracy = None
        actual_demand = 0
        thirty_days_ago = current_date - timedelta(days=30)

        recent_transactions = transactions.filter(
            transaction_date__gte=thirty_days_ago
        )
        actual_demand = recent_transactions.aggregate(
            total=models.Sum('quantity')
        )['total'] or 0

        if actual_demand > 0 and forecast.predicted_quantity is not None:
            forecast_accuracy = max(0, min(100, (
                1 - abs(forecast.predicted_quantity - actual_demand) / actual_demand
            ) * 100))
            forecast_accuracy = round(forecast_accuracy, 1)

        # Generate insights
        insights = []

        # Stock level insight
        if medicine.quantity < medicine.reorder_level:
            insights.append({
                'type': 'warning',
                'message': f"Current stock ({medicine.quantity}) is below reorder level ({medicine.reorder_level})"
            })

        # Forecast accuracy insight
        if forecast_accuracy is not None:
            if forecast_accuracy < 70:
                insights.append({
                    'type': 'warning',
                    'message': f"Low forecast accuracy ({forecast_accuracy}%). Consider reviewing the forecast method."
                })
            elif forecast_accuracy > 90:
                insights.append({
                    'type': 'success',
                    'message': f"High forecast accuracy ({forecast_accuracy}%)"
                })

        # Demand trend insight
        if monthly_demand:
            current_month = list(monthly_demand.values())[0]
            prev_month = list(monthly_demand.values())[1] if len(monthly_demand) > 1 else 0
            if current_month > prev_month * 1.2:  # 20% increase
                insights.append({
                    'type': 'info',
                    'message': "Significant increase in demand this month"
                })

        # Calculate recommended order quantity
        recommended_order_quantity = max(0, medicine.reorder_level - medicine.quantity + (forecast.predicted_quantity or 0))

        context = {
            'medicine': medicine,
            'forecast': forecast,
            'transactions': transactions[:10],  # Latest 10 transactions
            'monthly_demand': monthly_demand,
            'quarterly_analysis': quarterly_analysis,
            'forecast_accuracy': forecast_accuracy,
            'actual_demand': actual_demand,
            'insights': insights,
            'recommended_order_quantity': recommended_order_quantity,
            'transaction_count': transactions.count(),
            'current_quantity': medicine.quantity,
            'reorder_level': medicine.reorder_level,
            'page_title': f"Forecast Detail - {medicine.name}",
            'start_date': start_date,
            'end_date': current_date,
        }

        return render(request, 'forecast_detail.html', context)

    except Exception as e:
        logger.error(f"Error in forecast_detail view for forecast ID {pk}: {str(e)}", exc_info=True)
        messages.error(request, "An error occurred while displaying the forecast details.")
        return redirect('forecast_list')







@login_required
def create_medicine(request):
    if request.method == 'POST':
        form = MedicineForm(request.POST)
        if form.is_valid():
            medicine = form.save(commit=False)

            # Add brand and supplier information
            medicine.brand = request.POST.get('brand', '')
            medicine.supplier = request.POST.get('supplier', '')

            # Save the medicine
            medicine.save()

            # Find or create generic medicine
            from .models import GenericMedicine
            normalized_name = medicine.name.lower().strip()
            generic_medicine, created = GenericMedicine.objects.get_or_create(
                name__iexact=normalized_name,
                defaults={
                    'name': medicine.name,
                    'category': medicine.category,
                    'description': f"Generic {medicine.name}"
                }
            )

            # Link medicine to generic medicine
            medicine.generic_medicine = generic_medicine
            medicine.save()

            messages.success(request, 'Medicine created successfully!')
            return redirect('medicine_list')
    else:
        form = MedicineForm()
    return render(request, 'create_medicine.html', {'form': form})

@login_required
def update_medicine_price(request):
    """
    API endpoint to update prices directly (for both medicines and transactions)
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            medicine_id = data.get('medicine_id')
            transaction_id = data.get('transaction_id')
            new_price = data.get('price')

            if (not medicine_id and not transaction_id) or new_price is None:
                return JsonResponse({'success': False, 'error': 'Missing ID or price'})

            # Convert price to Decimal
            try:
                new_price = Decimal(str(new_price))
                if new_price < 0:
                    return JsonResponse({'success': False, 'error': 'Price cannot be negative'})
            except:
                return JsonResponse({'success': False, 'error': 'Invalid price format'})

            # Handle transaction price update
            if transaction_id:
                # Get the transaction
                transaction = get_object_or_404(Transaction, pk=transaction_id)
                medicine = transaction.medicine
                old_price = medicine.price
                old_total = transaction.total_amount

                # Update the medicine price
                medicine.price = new_price
                medicine.save()

                # Update the transaction total amount
                transaction.total_amount = new_price * transaction.quantity
                transaction.save()

                # Log the price change in the audit trail
                content_type = ContentType.objects.get_for_model(medicine)
                AuditTrail.objects.create(
                    content_object=medicine,
                    content_type=content_type,
                    object_id=medicine.pk,
                    action='update_price'
                )

                # Send notifications about the price change
                logger.info(f"Calling send_price_change_notification for {medicine.name} in transaction update")
                medicine.send_price_change_notification(old_price, new_price)
                logger.info(f"Price change notification sent successfully for {medicine.name} in transaction update")

                return JsonResponse({
                    'success': True,
                    'message': f'Price updated successfully from ₱{old_price} to ₱{new_price}',
                    'new_price': float(new_price),
                    'new_total': float(transaction.total_amount),
                    'old_total': float(old_total)
                })

            # Handle medicine price update
            else:
                # Get the medicine
                medicine = get_object_or_404(Medicine, pk=medicine_id)
                old_price = medicine.price

                # Update the price
                medicine.price = new_price
                medicine.save()

                # Log the price change in the audit trail
                content_type = ContentType.objects.get_for_model(medicine)
                AuditTrail.objects.create(
                    content_object=medicine,
                    content_type=content_type,
                    object_id=medicine.pk,
                    action='update_price'
                )

                # Send notifications about the price change
                logger.info(f"Calling send_price_change_notification for {medicine.name}")
                medicine.send_price_change_notification(old_price, new_price)
                logger.info(f"Price change notification sent successfully for {medicine.name}")

                return JsonResponse({
                    'success': True,
                    'message': f'Price updated successfully from ₱{old_price} to ₱{new_price}',
                    'new_price': float(new_price)
                })

        except (Medicine.DoesNotExist, Transaction.DoesNotExist):
            return JsonResponse({'success': False, 'error': 'Item not found'})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})




class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)  # or str(obj)
        return super().default(obj)


def get_medicine_quantity(request, pk):
    medicine = get_object_or_404(Medicine, pk=pk)
    return JsonResponse({'quantity': medicine.quantity})





def save_transaction(request):
    cart = request.session.get('cart', [])
    customer_info = request.session.get('customer_info', {})

    if not customer_info:
        messages.error(request, 'Customer information is missing.')
        return redirect('create_transaction')

    # Validate total inventory availability before processing
    inventory_check = {}
    for item in cart:
        medicine_id = item['medicine_id']
        if medicine_id not in inventory_check:
            inventory_check[medicine_id] = {
                'required': item['quantity'],
                'medicine': Medicine.objects.get(id=medicine_id)
            }
        else:
            inventory_check[medicine_id]['required'] += item['quantity']

    # Check if we have enough stock for all items
    for medicine_id, data in inventory_check.items():
        if data['required'] > data['medicine'].quantity:
            messages.error(request,
                f'Insufficient stock for {data["medicine"].name}. Available: {data["medicine"].quantity}, Required: {data["required"]}')
            return redirect('create_transaction')

    # Process the transaction
    try:
        with transaction.atomic():
            for item in cart:
                medicine = Medicine.objects.get(id=item['medicine_id'])
                Transaction.objects.create(
                    medicine=medicine,
                    transaction_date=timezone.now(),
                    quantity=item['quantity'],
                    transaction_type='sale',
                    customer_name=customer_info.get('customer_name'),
                    patient_number=customer_info.get('patient_number'),
                    customer_type=customer_info.get('customer_type'),
                    total_amount=item['amount']
                )
                # Update inventory
                medicine.quantity -= item['quantity']
                medicine.save()

        request.session['cart'] = []
        request.session['customer_info'] = {}
        messages.success(request, 'Transaction saved successfully!')
        return redirect('transaction_list')

    except Exception as e:
        messages.error(request, f'Error saving transaction: {str(e)}')
        return redirect('create_transaction')
@login_required
def create_transaction(request):
    if 'cart' not in request.session:
        request.session['cart'] = []
    if 'customer_info' not in request.session:
        request.session['customer_info'] = {}

    if request.method == 'POST':
        form = TransactionForm(request.POST)
        form.request = request  # Add this line to enable warning messages
        if form.is_valid():
            try:
                transaction = form.save(commit=False)
                medicine = transaction.medicine
                requested_quantity = transaction.quantity

                # Check if medicine is already in cart
                cart = request.session.get('cart', [])
                existing_quantity = sum(
                    item['quantity'] for item in cart
                    if item['medicine_id'] == medicine.id
                )

                # Validate total quantity against available stock
                if (existing_quantity + requested_quantity) > medicine.quantity:
                    messages.error(
                        request,
                        f'Cannot add {requested_quantity} units. Only {medicine.quantity - existing_quantity} units available.'
                    )
                    return redirect('create_transaction')

                # Check for reorder level warning
                remaining_stock = medicine.quantity - (existing_quantity + requested_quantity)
                if remaining_stock < medicine.reorder_level:
                    messages.warning(
                        request,
                        f'Warning: Adding this quantity will bring {medicine.name} below reorder level. Remaining stock will be {remaining_stock} units.'
                    )

                # Update cart with validated data
                cart_item = {
                    'medicine': medicine.name,
                    'quantity': requested_quantity,
                    'description': medicine.description,
                    'amount': float(requested_quantity * medicine.price),
                    'medicine_id': medicine.id
                }

                # Update customer info if not already saved
                if not request.session['customer_info']:
                    request.session['customer_info'] = {
                        'customer_name': transaction.customer_name,
                        'patient_number': transaction.patient_number,
                        'customer_type': 'number' if transaction.patient_number else 'name'
                    }

                request.session['cart'].append(cart_item)
                request.session.modified = True

                messages.success(request, f'Added {requested_quantity} units of {medicine.name} to cart')
                return redirect('create_transaction')

            except Exception as e:
                messages.error(request, f'Error processing transaction: {str(e)}')
                return redirect('create_transaction')
    else:
        form = TransactionForm(initial=request.session.get('customer_info', {}))
        form.request = request  # Add this line for GET requests too

    # Prepare context data
    cart = request.session.get('cart', [])
    context = {
        'form': form,
        'cart': cart,
        'total_quantity': sum(item['quantity'] for item in cart),
        'total_price': sum(item['amount'] for item in cart)
    }

    return render(request, 'create_transaction.html', context)






def cancel_transaction(request):
    request.session['cart'] = []
    request.session['customer_info'] = {}
    messages.info(request, 'Transaction canceled.')
    return redirect('create_transaction')

def remove_from_cart(request, index):
    cart = request.session.get('cart', [])
    if 0 <= index < len(cart):
        try:
            item = cart.pop(index)
            # Check if medicine_id exists in the cart item
            if 'medicine_id' in item:
                medicine = Medicine.objects.get(id=item['medicine_id'])
            else:
                # Fallback to using first medicine with matching name if no ID
                medicine = Medicine.objects.filter(name=item['medicine']).first()

            if medicine:
                medicine.quantity += item['quantity']
                medicine.save()
                messages.success(request, f'Removed {item["quantity"]} units of {medicine.name} from cart')
            else:
                messages.error(request, 'Medicine not found')

        except Medicine.DoesNotExist:
            messages.error(request, 'Medicine not found')
        except Exception as e:
            messages.error(request, f'Error removing item: {str(e)}')

    request.session['cart'] = cart
    request.session.modified = True
    return redirect('create_transaction')





from django.shortcuts import render
from .models import Medicine, Transaction
from django.utils import timezone
from datetime import timedelta, datetime
import pandas as pd
import numpy as np
from django.db.models import Min, Max

@login_required
def medicine_analysis_view(request):
    try:
        # Get all medicines
        medicines = Medicine.objects.all()

        # Calculate date range with timezone awareness
        two_years_ago = timezone.now().replace(hour=0, minute=0, second=0, microsecond=0) - timedelta(days=730)

        # Get filter parameters
        selected_medicine_id = request.GET.get('medicine_id', None)
        selected_year = request.GET.get('year', None)
        selected_quarter = request.GET.get('quarter', None)

        # Get available years more safely
        transaction_dates = Transaction.objects.aggregate(
            min_date=Min('transaction_date'),
            max_date=Max('transaction_date')
        )

        if transaction_dates['min_date'] and transaction_dates['max_date']:
            start_year = transaction_dates['min_date'].year
            end_year = transaction_dates['max_date'].year
            available_years = list(range(start_year, end_year + 1))
        else:
            available_years = []

        # Filter medicines if a specific one is selected
        if selected_medicine_id and selected_medicine_id != 'all':
            try:
                medicines = medicines.filter(id=int(selected_medicine_id))
            except ValueError:
                medicines = Medicine.objects.none()

        medicine_analyses = []

        for medicine in medicines:
            # Get transactions with explicit date filtering
            transactions = Transaction.objects.filter(
                medicine=medicine,
                transaction_date__gte=two_years_ago
            ).order_by('transaction_date')

            # Apply year filter if selected
            if selected_year and selected_year != 'all':
                try:
                    year = int(selected_year)
                    transactions = transactions.filter(transaction_date__year=year)
                except ValueError:
                    continue

            # Convert to values list
            transactions_list = list(transactions.values('transaction_date', 'quantity'))

            if not transactions_list:
                continue

            # Convert to DataFrame more safely
            df = pd.DataFrame(transactions_list)
            df['transaction_date'] = pd.to_datetime(df['transaction_date'], errors='coerce')
            # Drop any rows where date conversion failed
            df = df.dropna(subset=['transaction_date'])

            if df.empty:
                continue

            df['quarter'] = df['transaction_date'].dt.quarter
            df['year'] = df['transaction_date'].dt.year
            df['month'] = df['transaction_date'].dt.month

            # Apply quarter filter if selected
            if selected_quarter and selected_quarter != 'all':
                try:
                    quarter = int(selected_quarter)
                    df = df[df['quarter'] == quarter]
                    if df.empty:
                        continue
                except ValueError:
                    continue

            # Quarterly demand analysis
            quarterly_demand = df.groupby(['year', 'quarter'])['quantity'].sum()

            # Handle empty quarterly_demand
            if quarterly_demand.empty:
                continue

            quarterly_labels = [f"{year} Q{quarter}" for year, quarter in quarterly_demand.index]

            # Monthly average analysis
            monthly_avg = df.groupby('month')['quantity'].mean().round(2)
            month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                          'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
            monthly_data = []

            for month in range(1, 13):
                if month in monthly_avg.index:
                    monthly_data.append(float(monthly_avg[month]))
                else:
                    monthly_data.append(0)

            # Seasonal Patterns
            seasonal_patterns = df.groupby('quarter')['quantity'].agg([
                'mean', 'std', 'min', 'max'
            ]).round(2).reset_index()

            # Ensure all quarters are represented
            existing_quarters = set(seasonal_patterns['quarter'].astype(int))
            missing_quarters = []
            for q in range(1, 5):
                if q not in existing_quarters:
                    missing_quarters.append(q)

            if missing_quarters:
                missing_df = pd.DataFrame({
                    'quarter': missing_quarters,
                    'mean': [0] * len(missing_quarters),
                    'std': [0] * len(missing_quarters),
                    'min': [0] * len(missing_quarters),
                    'max': [0] * len(missing_quarters)
                })
                seasonal_patterns = pd.concat([seasonal_patterns, missing_df], ignore_index=True)

            seasonal_patterns = seasonal_patterns.sort_values('quarter')

            # Trend Analysis
            if len(quarterly_demand) >= 4:
                recent_quarters = quarterly_demand[-4:]
                trend = "increasing" if recent_quarters.is_monotonic_increasing else \
                       "decreasing" if recent_quarters.is_monotonic_decreasing else \
                       "fluctuating"
            else:
                trend = "insufficient data"

            # Year-over-year growth
            yoy_growth = 0
            if len(quarterly_demand) >= 8:
                recent_sum = quarterly_demand[-4:].sum()
                previous_sum = quarterly_demand[-8:-4].sum()
                # Avoid division by zero
                if previous_sum > 0:
                    yoy_growth = ((recent_sum / previous_sum) - 1) * 100

            # Peak month and quarter
            peak_month_idx = None
            if not monthly_avg.empty:
                peak_month_idx = monthly_avg.idxmax() if monthly_avg.max() > 0 else None
            peak_month = month_names[peak_month_idx-1] if peak_month_idx else "N/A"

            # Find peak quarter safely
            peak_quarter = "N/A"
            low_quarter = "N/A"
            if not seasonal_patterns.empty and seasonal_patterns['mean'].max() > 0:
                peak_quarter = seasonal_patterns.loc[seasonal_patterns['mean'].idxmax(), 'quarter']
                low_quarter = seasonal_patterns.loc[seasonal_patterns['mean'].idxmin(), 'quarter']

            # Generate recommendations
            recommendations = []
            if trend == "increasing":
                recommendations.extend([
                    "Consider increasing stock levels",
                    "Review and adjust reorder points upward"
                ])
            elif trend == "decreasing":
                recommendations.extend([
                    "Monitor stock levels closely",
                    "Consider adjusting reorder quantities downward"
                ])
            elif trend == "insufficient data":
                recommendations.append("Collect more data for better trend analysis")

            if peak_quarter != "N/A" and low_quarter != "N/A":
                recommendations.append(f"Plan for increased demand in Q{peak_quarter}")
                recommendations.append(f"Consider inventory optimization in Q{low_quarter}")

            medicine_analyses.append({
                'medicine': medicine,
                'quarterly_labels': quarterly_labels,
                'quarterly_data': quarterly_demand.values.tolist(),
                'monthly_labels': month_names,
                'monthly_data': monthly_data,
                'seasonal_patterns': seasonal_patterns.to_dict('records'),
                'trend': trend,
                'yoy_growth': f"{yoy_growth:.1f}%" if isinstance(yoy_growth, (int, float)) else "N/A",
                'peak_quarter': peak_quarter,
                'peak_month': peak_month,
                'recommendations': recommendations
            })

        context = {
            'medicine_analyses': medicine_analyses,
            'all_medicines': Medicine.objects.all(),
            'available_years': available_years,
            'two_years_ago': two_years_ago,
            'today': timezone.now().date(),
            'selected_medicine_id': selected_medicine_id,
            'selected_year': selected_year,
            'selected_quarter': selected_quarter
        }

        return render(request, 'medicine_analysis.html', context)

    except Exception as e:
        # Add basic error handling
        context = {
            'error_message': str(e),

            'available_years': available_years if 'available_years' in locals() else [],
            'two_years_ago': timezone.now().date() - timedelta(days=730),
            'today': timezone.now().date(),
            'selected_medicine_id': selected_medicine_id if 'selected_medicine_id' in locals() else None,
            'selected_year': selected_year if 'selected_year' in locals() else None,
            'selected_quarter': selected_quarter if 'selected_quarter' in locals() else None
        }
        return render(request, 'medicine_analysis.html', context)




















from django.shortcuts import render, get_object_or_404
from .forecasting import generate_medicine_forecast
from .models import Medicine
from django.contrib import messages
import logging

logger = logging.getLogger(__name__)

@login_required
def medicine_forecast_detail(request, medicine_id):
    """
    Display detailed forecasts for a specific medicine
    """
    try:
        medicine = get_object_or_404(Medicine, pk=medicine_id)

        # Get the latest forecast from the database to get the forecast method
        latest_forecast = Forecast.objects.filter(
            medicine=medicine
        ).order_by('-forecast_date').first()

        # Get the forecast method from the database
        forecast_method = latest_forecast.get_forecast_method_display() if latest_forecast and latest_forecast.forecast_method else "Auto"

        # For trend analysis
        trend_direction = latest_forecast.trend_direction if latest_forecast else "stable"
        trend_strength = latest_forecast.trend_strength if latest_forecast else 0.0
        has_seasonality = latest_forecast.has_seasonality if latest_forecast else False
        peak_period = latest_forecast.peak_period if latest_forecast else ""

        # Generate forecasts
        forecasts = generate_medicine_forecast(medicine)

        # Process forecasts for template
        processed_forecasts = {
            'weekly': [],
            'monthly': [],
            'yearly': []
        }

        # Process weekly forecasts
        if forecasts.get('weekly'):
            processed_forecasts['weekly'] = [
                {
                    'date': forecast['date'],  # Already formatted in generate_medicine_forecast
                    'value': round(forecast['value'], 2),
                    'lower': round(forecast['lower'], 2),
                    'upper': round(forecast['upper'], 2)
                }
                for forecast in forecasts['weekly']
            ]

        # Process monthly forecasts
        if forecasts.get('monthly'):
            processed_forecasts['monthly'] = [
                {
                    'date': forecast['date'],  # Already formatted in generate_medicine_forecast
                    'value': round(forecast['value'], 2),
                    'lower': round(forecast['lower'], 2),
                    'upper': round(forecast['upper'], 2)
                }
                for forecast in forecasts['monthly']
            ]

        # Process yearly forecasts
        if forecasts.get('yearly'):
            processed_forecasts['yearly'] = [
                {
                    'date': forecast['date'],  # Already formatted in generate_medicine_forecast
                    'value': round(forecast['value'], 2),
                    'lower': round(forecast['lower'], 2),
                    'upper': round(forecast['upper'], 2)
                }
                for forecast in forecasts['yearly']
            ]

        # Get seasonal analysis if available
        seasonal_analysis = analyze_quarterly_patterns(medicine) if 'analyze_quarterly_patterns' in globals() else None

        # Calculate recommended order quantity
        recommended_order = max(0, medicine.reorder_level - medicine.quantity + (
            processed_forecasts['monthly'][0]['value'] if processed_forecasts['monthly'] else 0
        ))

        context = {
            'medicine': medicine,
            'forecasts': processed_forecasts,
            'forecast_method': forecast_method,  # Add the forecast method to the context
            'data_quality': forecasts.get('data_quality', {
                'points': 0,
                'range_days': 0,
                'completeness': '0%'
            }),
            'trend_direction': trend_direction,
            'trend_strength': trend_strength,
            'has_seasonality': has_seasonality,
            'peak_period': peak_period,
            'seasonal_analysis': seasonal_analysis,
            'recommended_order': recommended_order,
            'page_title': f"Forecast Details for {medicine.name}"
        }

        return render(request, 'medicine_forecast_detail.html', context)

    except Exception as e:
        logger.error(f"Error in medicine_forecast_detail for medicine {medicine_id}: {str(e)}")
        messages.error(request, "An error occurred while generating forecasts.")
        return redirect('medicine_list')







import pmdarima as pm

from django.shortcuts import render
from .forecasting import generate_arima_forecast
from .forecasting import generate_comprehensive_forecast
from .models import Medicine
import numpy as np
import logging

logger = logging.getLogger(__name__)

@login_required
def arima_forecast_view(request):
    if request.method == 'POST':
        try:
            # Get historical data
            historical_data = Medicine.objects.values_list('quantity', flat=True)
            data = np.array(historical_data, dtype=float)

            if len(data) < 2:
                return render(request, 'arima_forecast.html', {
                    'error': 'Insufficient data for forecasting. Need at least 2 data points.'
                })

            # Generate forecasts
            forecast_results = generate_comprehensive_forecast(data)

            # Ensure ARIMA results are properly formatted
            if 'arima' in forecast_results:
                arima_result = forecast_results['arima']
                if 'error' not in arima_result:
                    arima_result.update({
                        'accuracy': {
                            'accuracy': arima_result.get('accuracy', 0.0),
                            'mse': arima_result.get('mse', 0.0),
                            'rmse': arima_result.get('rmse', 0.0),
                            'mape': arima_result.get('mape', 0.0)
                        }
                    })

            context = {
                'forecast_results': forecast_results,
                'has_arima_summary': 'model_summary' in forecast_results.get('arima', {}),
                'historical_data': data.tolist(),
                'dates': [str(i) for i in range(len(data))],
                'forecast_periods': 35  # Match the default forecast period
            }

            return render(request, 'arima_forecast.html', context)

        except Exception as e:
            logger.error(f"Forecast generation error: {str(e)}", exc_info=True)
            return render(request, 'arima_forecast.html', {
                'error': 'An error occurred while generating forecasts.'
            })

    return render(request, 'arima_forecast.html')












def calculate_forecast_value(historical_sales):
    """Calculate the predicted value based on historical sales using multiple models"""
    try:
        # Convert historical sales to time series
        sales_data = pd.DataFrame(
            historical_sales.values('transaction_date', 'quantity')
        ).set_index('transaction_date')

        # Resample to daily frequency and fill missing values
        daily_sales = sales_data.resample('D')['quantity'].sum().fillna(0)

        # Use the ARIMA model from your existing code
        model = pm.auto_arima(
            daily_sales,
            start_p=0, start_q=0,
            max_p=3, max_q=3,
            m=1,  # non-seasonal
            d=None,  # let the model determine d
            seasonal=False,
            start_P=0,
            D=0,
            trace=False,
            error_action='ignore',
            suppress_warnings=True,
            stepwise=True
        )

        # Generate forecast for next period
        forecast, conf_int = model.predict(n_periods=1, return_conf_int=True)
        predicted_value = max(0, float(forecast[0]))

        return predicted_value

    except Exception as e:
        logger.error(f"Forecast calculation error: {str(e)}")
        # Fallback to simple average if ARIMA fails
        recent_sales = historical_sales.values('quantity').order_by('-transaction_date')[:30]
        if not recent_sales:
            return 0
        return sum(sale['quantity'] for sale in recent_sales) / len(recent_sales)

def calculate_confidence_bounds(predicted_value):
    """Calculate the confidence interval for the forecast using statistical methods"""
    try:
        # Get historical volatility
        historical_std = Transaction.objects.filter(
            transaction_type='sale',
            transaction_date__gte=timezone.now() - timedelta(days=90)
        ).aggregate(std=StdDev('quantity'))['std'] or 0

        # Calculate confidence intervals using 1.96 for 95% confidence level
        confidence_factor = 1.96
        margin = historical_std * confidence_factor

        lower_bound = max(0, predicted_value - margin)
        upper_bound = predicted_value + margin

        return {
            'lower': float(lower_bound),
            'upper': float(upper_bound)
        }

    except Exception as e:
        logger.error(f"Confidence bound calculation error: {str(e)}")
        # Fallback to simple percentage-based bounds
        return {
            'lower': max(0, predicted_value * 0.8),  # 20% below prediction
            'upper': predicted_value * 1.2  # 20% above prediction
        }




from openpyxl.styles import NamedStyle, Font, PatternFill, Alignment
from reportlab.lib.styles import ParagraphStyle
from django.db.models import Sum, F
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, landscape
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from io import BytesIO


@login_required
def reports_dashboard(request):
    if request.method == 'POST':
        report_type = request.POST.get('report_type')
        file_format = request.POST.get('file_format')
        start_date = timezone.now() - timedelta(days=int(request.POST.get('date_range', 30)))
        end_date = timezone.now()

        try:
            if report_type == 'sales':
                # Generate Sales Report
                sales_data = Transaction.objects.filter(
                    transaction_type='sale',
                    transaction_date__range=[start_date, end_date]
                ).select_related('medicine')

                if file_format == 'excel':
                    wb = Workbook()
                    ws = wb.active
                    ws.title = "Sales Report"

                    # Headers
                    headers = ['Date', 'Medicine', 'Quantity', 'Total Amount',
                              'Customer Type', 'Payment Method']
                    ws.append(headers)

                    # Data
                    for sale in sales_data:
                        ws.append([
                            sale.transaction_date.strftime('%Y-%m-%d'),
                            sale.medicine.name,
                            sale.quantity,
                            sale.total_amount,
                            sale.customer_type,
                            getattr(sale, 'payment_method', 'N/A')  # Use getattr in case payment_method doesn't exist
                        ])

                    # Add summary at the bottom
                    total_sales = sales_data.aggregate(Sum('total_amount'))['total_amount__sum'] or 0
                    ws.append([])
                    ws.append(['Total Sales', '', '', total_sales])

                    response = HttpResponse(
                        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )
                    response['Content-Disposition'] = f'attachment; filename=sales_report_{timezone.now().date()}.xlsx'
                    wb.save(response)
                    return response

                elif file_format == 'pdf':
                    buffer = BytesIO()
                    doc = SimpleDocTemplate(buffer, pagesize=landscape(letter))
                    elements = []

                    # Add title
                    styles = getSampleStyleSheet()
                    elements.append(Paragraph(f"Sales Report ({start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')})",
                                             styles['Heading1']))
                    elements.append(Spacer(1, 20))

                    # Prepare data
                    data = [['Date', 'Medicine', 'Quantity', 'Total Amount', 'Customer Type', 'Payment Method']]
                    for sale in sales_data:
                        data.append([
                            sale.transaction_date.strftime('%Y-%m-%d'),
                            sale.medicine.name,
                            str(sale.quantity),
                            f"₱{sale.total_amount:.2f}",
                            sale.customer_type,
                            getattr(sale, 'payment_method', 'N/A')
                        ])

                    # Create table
                    table = Table(data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 14),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 1), (-1, -1), 12),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ]))

                    elements.append(table)

                    # Build PDF
                    doc.build(elements)
                    pdf = buffer.getvalue()
                    buffer.close()

                    # Create response
                    response = HttpResponse(content_type='application/pdf')
                    response['Content-Disposition'] = f'attachment; filename=sales_report_{timezone.now().date()}.pdf'
                    response.write(pdf)
                    return response

            elif report_type == 'forecast':
                try:
                    date_range = int(request.POST.get('date_range', 30))
                    end_date = timezone.now()
                    forecast_end_date = end_date + timedelta(days=date_range)

                    # Get all medicines with their latest forecasts
                    forecasts = Forecast.objects.filter(
                        forecast_date__date=timezone.now().date()
                    ).select_related('medicine').order_by('medicine__name')

                    # Debug logging
                    logger.info(f"Found {forecasts.count()} forecasts for date {timezone.now().date()}")

                    if not forecasts.exists():
                        messages.warning(request, "No forecast data available for today.")
                        return redirect('reports_dashboard')

                    if file_format == 'excel':
                        wb = Workbook()
                        ws = wb.active
                        ws.title = "Forecast Report"

                        # Headers
                        headers = [
                            'Medicine',
                            'Category',
                            'Current Stock',
                            'Reorder Level',
                            'Recommended Order',
                            'Predicted Quantity',
                            'Accuracy (%)',
                            'Confidence Range'
                        ]
                        ws.append(headers)

                        # Data
                        row_count = 0
                        for forecast in forecasts:
                            try:
                                medicine = forecast.medicine
                                row_data = [
                                    str(medicine.name),
                                    str(medicine.category),
                                    int(medicine.quantity),
                                    int(medicine.reorder_level),
                                    int(forecast.recommended_order_quantity or 0),
                                    round(float(forecast.predicted_quantity or 0), 2),
                                    round(float(forecast.accuracy or 0), 2),
                                    f"{round(float(forecast.confidence_lower or 0))} - {round(float(forecast.confidence_upper or 0))}"
                                ]
                                ws.append(row_data)
                                row_count += 1
                            except Exception as row_error:
                                logger.error(f"Error adding row for forecast {forecast.id}: {str(row_error)}")
                                continue

                        logger.info(f"Added {row_count} rows to Excel report")

                        # Apply styles
                        header_style = NamedStyle(name=f'header_style_{timezone.now().timestamp()}')  # Unique name
                        header_style.font = Font(bold=True)
                        header_style.fill = PatternFill(start_color='4e73df', end_color='4e73df', fill_type='solid')
                        header_style.alignment = Alignment(horizontal='center')

                        for cell in ws[1]:
                            cell.style = header_style

                        # Adjust column widths
                        for column in ws.columns:
                            max_length = 0
                            for cell in column:
                                try:
                                    max_length = max(max_length, len(str(cell.value)))
                                except:
                                    pass
                            ws.column_dimensions[column[0].column_letter].width = max_length + 2

                        # Create response
                        response = HttpResponse(
                            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        )
                        filename = f'forecast_report_{timezone.now().date()}.xlsx'
                        response['Content-Disposition'] = f'attachment; filename={filename}'

                        # Save workbook
                        try:
                            wb.save(response)
                            logger.info(f"Successfully generated Excel report: {filename}")
                            return response
                        except Exception as save_error:
                            logger.error(f"Error saving Excel file: {str(save_error)}")
                            raise

                    elif file_format == 'pdf':
                        buffer = BytesIO()
                        doc = SimpleDocTemplate(buffer, pagesize=landscape(letter))
                        elements = []

                        # Title
                        styles = getSampleStyleSheet()
                        title_style = ParagraphStyle(
                            'CustomTitle',
                            parent=styles['Heading1'],
                            fontSize=16,
                            textColor=colors.HexColor('#4e73df')
                        )
                        elements.append(Paragraph("Forecast Report", title_style))
                        elements.append(Spacer(1, 20))

                        # Table data matching forecast_list.html
                        data = [[
                            'Medicine',
                            'Category',
                            'Current Stock',
                            'Reorder Level',
                            'Recommended Order',
                            'Predicted Quantity',
                            'Accuracy (%)',
                            'Confidence Range'
                        ]]

                        for forecast in forecasts:
                            medicine = forecast.medicine
                            data.append([
                                medicine.name,
                                medicine.category,
                                str(medicine.quantity),
                                str(medicine.reorder_level),
                                str(forecast.recommended_order_quantity),
                                str(round(forecast.predicted_quantity, 2)),
                                str(round(forecast.accuracy, 2)),
                                f"{round(forecast.confidence_lower)} - {round(forecast.confidence_upper)}"
                            ])

                        # Table style
                        table = Table(data)
                        table.setStyle(TableStyle([
                            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#4e73df')),
                            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                            ('FONTSIZE', (0, 0), (-1, 0), 12),
                            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                            ('BACKGROUND', (0, 1), (-1, -1), colors.white),
                            ('GRID', (0, 0), (-1, -1), 1, colors.black),
                            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                        ]))

                        elements.append(table)
                        doc.build(elements)
                        pdf = buffer.getvalue()
                        buffer.close()

                        response = HttpResponse(content_type='application/pdf')
                        response['Content-Disposition'] = f'attachment; filename=forecast_report_{timezone.now().date()}.pdf'
                        response.write(pdf)
                        return response

                except Exception as e:
                    logger.error(f"Error generating forecast report: {str(e)}")
                    messages.error(request, f"Error generating report: {str(e)}")
                    return redirect('reports_dashboard')

            elif report_type == 'inventory':
                # Generate Inventory Report
                medicines = Medicine.objects.all()

                if file_format == 'excel':
                    wb = Workbook()
                    ws = wb.active
                    ws.title = "Inventory Report"

                    # Headers
                    headers = ['Medicine', 'Category', 'Current Stock', 'Reorder Level',
                              'Price', 'Total Value', 'Status']
                    ws.append(headers)

                    for medicine in medicines:
                        status = "Low Stock" if medicine.quantity <= medicine.reorder_level else "In Stock"
                        total_value = medicine.quantity * medicine.price
                        ws.append([
                            medicine.name,
                            medicine.category,
                            medicine.quantity,
                            medicine.reorder_level,
                            medicine.price,
                            total_value,
                            status
                        ])

                    response = HttpResponse(
                        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )
                    response['Content-Disposition'] = f'attachment; filename=inventory_report_{timezone.now().date()}.xlsx'
                    wb.save(response)
                    return response

                elif file_format == 'pdf':
                    buffer = BytesIO()
                    doc = SimpleDocTemplate(buffer, pagesize=landscape(letter))
                    elements = []

                    styles = getSampleStyleSheet()
                    elements.append(Paragraph("Inventory Report", styles['Heading1']))
                    elements.append(Spacer(1, 20))

                    data = [['Medicine', 'Category', 'Current Stock', 'Reorder Level',
                            'Price', 'Total Value', 'Status']]

                    for medicine in medicines:
                        status = "Low Stock" if medicine.quantity <= medicine.reorder_level else "In Stock"
                        total_value = medicine.quantity * medicine.price
                        data.append([
                            medicine.name,
                            medicine.category,
                            str(medicine.quantity),
                            str(medicine.reorder_level),
                            f"₱{medicine.price:.2f}",
                            f"₱{total_value:.2f}",
                            status
                        ])

                    table = Table(data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 1), (-1, -1), 10),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black),
                        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ]))

                    elements.append(table)
                    doc.build(elements)
                    pdf = buffer.getvalue()
                    buffer.close()

                    response = HttpResponse(content_type='application/pdf')
                    response['Content-Disposition'] = f'attachment; filename=inventory_report_{timezone.now().date()}.pdf'
                    response.write(pdf)
                    return response

            elif report_type == 'transactions':
                # Generate Transactions Report
                transactions = Transaction.objects.filter(
                    transaction_date__range=[start_date, end_date]
                ).select_related('medicine')

                if file_format == 'excel':
                    wb = Workbook()
                    ws = wb.active
                    ws.title = "Transactions Report"

                    # Headers
                    headers = ['Date', 'Type', 'Medicine', 'Quantity', 'Price',
                              'Total Amount', 'Reference']
                    ws.append(headers)

                    for trans in transactions:
                        ws.append([
                            trans.transaction_date.strftime('%Y-%m-%d'),
                            trans.transaction_type,
                            trans.medicine.name,
                            trans.quantity,
                            trans.medicine.price,
                            trans.total_amount,
                            getattr(trans, 'reference_number', 'N/A')
                        ])

                    response = HttpResponse(
                        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    )
                    response['Content-Disposition'] = f'attachment; filename=transactions_report_{timezone.now().date()}.xlsx'
                    wb.save(response)
                    return response

                elif file_format == 'pdf':
                    buffer = BytesIO()
                    doc = SimpleDocTemplate(buffer, pagesize=landscape(letter))
                    elements = []

                    styles = getSampleStyleSheet()
                    title = f"Transactions Report ({start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')})"
                    elements.append(Paragraph(title, styles['Heading1']))
                    elements.append(Spacer(1, 20))

                    data = [['Date', 'Type', 'Medicine', 'Quantity', 'Price',
                            'Total Amount', 'Reference']]

                    for trans in transactions:
                        data.append([
                            trans.transaction_date.strftime('%Y-%m-%d'),
                            trans.transaction_type,
                            trans.medicine.name,
                            str(trans.quantity),
                            f"₱{trans.medicine.price:.2f}",
                            f"₱{trans.total_amount:.2f}",
                            getattr(trans, 'reference_number', 'N/A')
                        ])

                    table = Table(data)
                    table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                        ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                        ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                        ('FONTSIZE', (0, 0), (-1, 0), 12),
                        ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                        ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                        ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
                        ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 1), (-1, -1), 10),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black),
                        ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
                    ]))

                    elements.append(table)
                    doc.build(elements)
                    pdf = buffer.getvalue()
                    buffer.close()

                    response = HttpResponse(content_type='application/pdf')
                    response['Content-Disposition'] = f'attachment; filename=transactions_report_{timezone.now().date()}.pdf'
                    response.write(pdf)
                    return response

        except Exception as e:
            messages.error(request, f"Error generating report: {str(e)}")
            return redirect('reports_dashboard')

    # GET request - render the dashboard
    context = {
        'total_sales': Transaction.objects.filter(
            transaction_type='sale',
            transaction_date__gte=timezone.now() - timedelta(days=30)
        ).aggregate(Sum('total_amount'))['total_amount__sum'] or 0,
        'total_medicines': Medicine.objects.count(),
        'low_stock_count': Medicine.objects.filter(quantity__lte=F('reorder_level')).count(),
        'total_transactions': Transaction.objects.filter(
            transaction_date__gte=timezone.now() - timedelta(days=30)
        ).count(),
    }

    return render(request, 'reports_dashboard.html', context)

def calculate_forecast_with_cache(medicine, historical_sales, forecast_end_date):
    """
    Calculate forecast with caching to improve performance
    Uses existing forecast data from the system
    """
    cache_key = f'forecast_medicine_{medicine.id}_{forecast_end_date.date()}'
    cached_result = cache.get(cache_key)

    if cached_result:
        return cached_result

    try:
        # Get existing forecast from the database
        forecast = Forecast.objects.filter(
            medicine=medicine,
            forecast_date__date=timezone.now().date()
        ).first()

        if not forecast:
            return {
                'medicine_name': medicine.name,
                'category': medicine.category,
                'current_stock': medicine.quantity,
                'date': forecast_end_date,
                'predicted_value': 0,
                'lower_bound': forecast.confidence_lower if forecast else 0,
                'upper_bound': forecast.confidence_upper if forecast else 0,
                'recommended_order': forecast.recommended_order_quantity if forecast else 0,
                'accuracy': forecast.accuracy if forecast else 0,
                'forecast_note': 'No forecast available'
            }

        result = {
            'medicine_name': medicine.name,
            'category': medicine.category,
            'current_stock': medicine.quantity,
            'date': forecast_end_date,
            'predicted_value': forecast.predicted_quantity,
            'lower_bound': forecast.confidence_lower,
            'upper_bound': forecast.confidence_upper,
            'recommended_order': forecast.recommended_order_quantity,
            'accuracy': forecast.accuracy,
            'forecast_note': 'Based on existing forecast'
        }

        # Cache the result for 1 hour
        cache.set(cache_key, result, 3600)
        return result

    except Exception as e:
        logger.error(f"Error retrieving forecast for medicine {medicine.id}: {str(e)}")
        return {
            'medicine_name': medicine.name,
            'category': medicine.category,
            'current_stock': medicine.quantity,
            'date': forecast_end_date,
            'predicted_value': 0,
            'lower_bound': 0,
            'upper_bound': 0,
            'recommended_order': 0,
            'accuracy': 0,
            'forecast_note': f'Error retrieving forecast'
        }






def about_us(request):
    try:
        # Get recent and historical forecasts
        recent_forecasts = Forecast.objects.filter(
            forecast_date__gte=timezone.now() - timedelta(days=30)
        ).order_by('-forecast_date')

        historical_forecasts = Forecast.objects.filter(
            forecast_date__gte=timezone.now() - timedelta(days=180)
        )

        # Get low stock medicines
        low_stock_medicines = Medicine.objects.filter(
            quantity__lte=F('reorder_level')
        ).order_by('quantity')

        # Define forecasting strengths
        forecasting_strengths = {
            'ARIMA': 'Trend Identification',
            'SARIMA': 'Seasonal Patterns',
            'exponential': 'Trend & Seasonality',
            'moving_avg': 'Short-term Trends'
        }

        # Pre-defined metrics based on the exact results
        forecast_metrics = {
            'arima': {
                'rmse': 10.13,
                'mae': None,
                'mape': None,
                'strength': 'Trend Identification'
            },
            'sarima': {
                'rmse': 10.13,
                'mae': None,
                'mape': None,
                'strength': 'Seasonal Patterns'
            },
            'exp_smoothing': {
                'rmse': None,
                'mae': None,
                'mape': None,
                'strength': 'Trend & Seasonality'
            },
            'moving_avg': {
                'rmse': None,
                'mae': None,
                'mape': None,
                'strength': 'Short-term Trends'
            }
        }

        # Prepare chart data
        forecast_data = {
            'dates': [f.forecast_date.strftime('%Y-%m-%d') for f in recent_forecasts],
            'actual': [float(f.predicted_quantity) for f in recent_forecasts],
            'predicted': [float(f.predicted_quantity) for f in recent_forecasts]
        }

        context = {
            'low_stock_medicines': low_stock_medicines,
            'forecast_data': {
                'dates': json.dumps(forecast_data['dates']),
                'actual': json.dumps(forecast_data['actual']),
                'predicted': json.dumps(forecast_data['predicted'])
            },
            'live_forecast_data': {
                'dates': json.dumps(forecast_data['dates']),
                'actual': json.dumps(forecast_data['actual']),
                'predicted': json.dumps(forecast_data['predicted'])
            },
            'metrics': {
                'avg_accuracy': None,
                'avg_rmse': 10.13,
                'confidence_interval': 95
            },
            'forecast_metrics': forecast_metrics,
            'chart_data': {
                'arima': {
                    'metrics': forecast_metrics['arima']
                }
            },
            'data_range_days': 180,
            'sample_forecasts': recent_forecasts[:5]
        }

        return render(request, 'about_us.html', context)

    except Exception as e:
        logger.error(f"Error in about_us view: {str(e)}")
        return render(request, 'about_us.html', {
            'low_stock_medicines': [],
            'forecast_data': {'dates': [], 'actual': [], 'predicted': []},
            'live_forecast_data': {'dates': [], 'actual': [], 'predicted': []},
            'metrics': {'avg_accuracy': None, 'avg_rmse': 0, 'confidence_interval': 95},
            'forecast_metrics': {
                'arima': {'rmse': 0, 'mae': None, 'mape': None, 'strength': 'Trend Identification'},
                'sarima': {'rmse': 0, 'mae': None, 'mape': None, 'strength': 'Seasonal Patterns'},
                'exp_smoothing': {'rmse': None, 'mae': None, 'mape': None, 'strength': 'Trend & Seasonality'},
                'moving_avg': {'rmse': None, 'mae': None, 'mape': None, 'strength': 'Short-term Trends'}
            },
            'chart_data': {
                'arima': {
                    'metrics': {'rmse': 0, 'mae': None, 'mape': None}
                }
            },
            'data_range_days': 180,
            'sample_forecasts': [],
            'error': str(e)
        })











    from django.http import JsonResponse

def search_customer(request):
    name = request.GET.get('name')
    patient_number = request.GET.get('patient_number')

    try:
        if name:
            # Search by name
            transaction = Transaction.objects.filter(
                customer_name__icontains=name
            ).latest('transaction_date')

            return JsonResponse({
                'found': True,
                'customer_name': transaction.customer_name,
                'patient_number': transaction.patient_number,
                'customer_type': transaction.customer_type
            })

        elif patient_number:
            # Search by patient number
            transaction = Transaction.objects.filter(
                patient_number__iexact=patient_number
            ).latest('transaction_date')

            return JsonResponse({
                'found': True,
                'customer_name': transaction.customer_name,
                'patient_number': transaction.patient_number,
                'customer_type': transaction.customer_type
            })

        return JsonResponse({'found': False})

    except Transaction.DoesNotExist:
        return JsonResponse({'found': False})


def get_medicine_details(request, pk):
    """
    API endpoint to get detailed information about a medicine
    """
    try:
        medicine = get_object_or_404(Medicine, pk=pk)

        # Get current stock level
        current_stock = medicine.quantity

        # Get reorder level
        reorder_level = medicine.reorder_level

        # Check if stock is low
        is_low_stock = current_stock <= reorder_level

        # Get expiry date - handle both expiry_date and expiration_date field names
        expiry_date = None
        if hasattr(medicine, 'expiry_date') and medicine.expiry_date:
            expiry_date = medicine.expiry_date.strftime('%Y-%m-%d')
        elif hasattr(medicine, 'expiration_date') and medicine.expiration_date:
            expiry_date = medicine.expiration_date.strftime('%Y-%m-%d')

        # Get price
        price = float(medicine.price)

        # Get medicine type display name
        medicine_type_display = dict(Medicine.MEDICINE_TYPES).get(medicine.medicine_type, 'Other')

        # Get recent transactions
        recent_transactions = Transaction.objects.filter(
            medicine=medicine
        ).order_by('-transaction_date')[:5].values('transaction_date', 'quantity', 'transaction_type')

        # Format the transactions
        formatted_transactions = []
        for t in recent_transactions:
            formatted_transactions.append({
                'date': t['transaction_date'].strftime('%Y-%m-%d %H:%M') if t['transaction_date'] else None,
                'quantity': t['quantity'],
                'type': t['transaction_type']
            })

        # Return the data as JSON
        return JsonResponse({
            'id': medicine.id,
            'name': medicine.name,
            'category': medicine.category,
            'medicine_type': medicine.medicine_type,
            'medicine_type_display': medicine_type_display,
            'quantity': current_stock,  # Add quantity field for compatibility
            'current_stock': current_stock,
            'reorder_level': reorder_level,
            'is_low_stock': is_low_stock,
            'expiry_date': expiry_date,
            'price': price,
            'brand': medicine.brand or '',
            'supplier': medicine.supplier or '',
            'generic_medicine': medicine.generic_medicine.name if medicine.generic_medicine else None,
            'recent_transactions': formatted_transactions
        })
    except Exception as e:
        # Return error response with details
        return JsonResponse({
            'error': 'Error loading medicine details',
            'details': str(e)
        }, status=500)

def search_medicines(request):
    """
    API endpoint to search for medicines
    """
    # Accept both 'q' and 'term' parameters for compatibility
    query = request.GET.get('q', request.GET.get('term', ''))

    if not query or len(query) < 2:
        return JsonResponse({'results': [], 'medicines': []})

    # Search for medicines matching the query
    medicines = Medicine.objects.filter(
        Q(name__icontains=query) |
        Q(category__icontains=query) |
        Q(medicine_type__icontains=query) |
        Q(brand__icontains=query) |
        Q(supplier__icontains=query)
    ).values('id', 'name', 'category', 'quantity', 'price', 'medicine_type', 'reorder_level', 'brand', 'supplier')[:10]

    # Format the results
    results = []
    for medicine in medicines:
        medicine_data = {
            'id': medicine['id'],
            'name': medicine['name'],
            'category': medicine['category'] or 'Uncategorized',
            'quantity': medicine['quantity'],
            'price': float(medicine['price']),
            'medicine_type': medicine.get('medicine_type', 'other'),
            'reorder_level': medicine.get('reorder_level', 0),
            'brand': medicine.get('brand', ''),
            'supplier': medicine.get('supplier', '')
        }
        results.append(medicine_data)

    # Return both 'results' and 'medicines' for compatibility
    return JsonResponse({
        'results': results,
        'medicines': results
    })

def update_cart_quantity(request):
    """
    API endpoint to update the quantity of an item in the cart
    """
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            index = data.get('index')
            quantity = data.get('quantity')

            if index is None or quantity is None:
                return JsonResponse({'success': False, 'error': 'Missing index or quantity'})

            # Get the cart from the session
            cart = request.session.get('cart', [])

            # Check if the index is valid
            if index < 0 or index >= len(cart):
                return JsonResponse({'success': False, 'error': 'Invalid index'})

            # Update the quantity
            cart[index]['quantity'] = int(quantity)

            # Recalculate the amount
            medicine_id = cart[index]['medicine_id']
            medicine = Medicine.objects.get(id=medicine_id)
            cart[index]['amount'] = float(medicine.price) * int(quantity)

            # Save the updated cart to the session
            request.session['cart'] = cart

            # Calculate the total amount
            total_amount = sum(item['amount'] for item in cart)

            return JsonResponse({
                'success': True,
                'item_amount': cart[index]['amount'],
                'total_amount': total_amount
            })

        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)})

    return JsonResponse({'success': False, 'error': 'Invalid request method'})


def check_duplicate_customers(request):
    """API endpoint to check for duplicate customer names"""
    name = request.GET.get('name', '').strip()

    if not name or len(name) < 3:
        return JsonResponse({'duplicates': []})

    # Find customers with similar names
    from django.db.models import Count
    from user_management.models import Customer
    from django.utils import timezone

    # First try to find customers in the Customer model
    similar_customers = Customer.objects.filter(
        customer_name__icontains=name
    ).order_by('customer_name')

    # If we found customers in the Customer model, use those
    if similar_customers.exists():
        customer_dict = {}
        for customer in similar_customers:
            # Get the most recent transaction date for this customer
            latest_transaction = Transaction.objects.filter(
                customer_name=customer.customer_name
            ).order_by('-transaction_date').first()

            # Use the transaction date if available, otherwise use the customer's last_transaction_date
            transaction_date = None
            if latest_transaction and latest_transaction.transaction_date:
                transaction_date = latest_transaction.transaction_date
            elif customer.last_transaction_date:
                transaction_date = customer.last_transaction_date
            else:
                transaction_date = timezone.now()  # Default to current time if no transaction date available

            # Convert to local timezone if it's in UTC
            if timezone.is_aware(transaction_date):
                transaction_date = timezone.localtime(transaction_date)

            customer_type_display = dict(Customer.CUSTOMER_TYPES).get(customer.customer_type, 'Unknown')
            customer_dict[customer.customer_name] = {
                'id': customer.id,  # Use the actual customer ID
                'customer_name': customer.customer_name,
                'patient_number': customer.patient_number,
                'customer_type': customer.customer_type,
                'customer_type_display': customer_type_display,
                'last_transaction_date': transaction_date.strftime('%b %d, %Y %I:%M %p') if transaction_date else None,
                'count': 1  # Default count
            }
    else:
        # Fallback to transactions if no customers found
        similar_transactions = Transaction.objects.filter(
            customer_name__icontains=name
        ).values('customer_name', 'patient_number', 'customer_type', 'transaction_date')\
        .annotate(count=Count('customer_name'))\
        .order_by('-transaction_date')

        # Group by exact customer name to find duplicates
        customer_dict = {}
        for t in similar_transactions:
            if t['customer_name'] not in customer_dict:
                customer_type_display = dict(Transaction.CUSTOMER_CHOICES).get(t['customer_type'], 'Unknown')
                # Get transaction date and convert to local timezone if needed
                transaction_date = t['transaction_date']
                if transaction_date and timezone.is_aware(transaction_date):
                    transaction_date = timezone.localtime(transaction_date)

                customer_dict[t['customer_name']] = {
                    'id': t['customer_name'],  # Using name as ID since we don't have customer IDs
                    'customer_name': t['customer_name'],
                    'patient_number': t['patient_number'],
                    'customer_type': t['customer_type'],
                    'customer_type_display': customer_type_display,
                    'last_transaction_date': transaction_date.strftime('%b %d, %Y %I:%M %p') if transaction_date else None,
                    'count': t['count']
                }

    # Convert to list and sort by similarity to search term
    duplicates = list(customer_dict.values())

    # Sort by exact match first, then by partial matches
    exact_matches = [c for c in duplicates if c['customer_name'].lower() == name.lower()]
    partial_matches = [c for c in duplicates if c['customer_name'].lower() != name.lower()]

    # Sort partial matches by relevance (contains the search term as a whole word first)
    import re
    word_matches = []
    other_matches = []

    for match in partial_matches:
        if re.search(r'\b' + re.escape(name.lower()) + r'\b', match['customer_name'].lower()):
            word_matches.append(match)
        else:
            other_matches.append(match)

    # Combine the results in order of relevance
    sorted_duplicates = exact_matches + word_matches + other_matches

    return JsonResponse({'duplicates': sorted_duplicates[:10]})


@login_required
def forecast_excel_view(request):
    """Generate forecast Excel report"""
    # Get date range for forecast
    date_range = int(request.GET.get('date_range', 30))
    forecast_end_date = timezone.now() + timedelta(days=date_range)

    # Get all medicines
    medicines = Medicine.objects.all().order_by('name')

    # Create Excel workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Forecast Report"

    # Define headers
    headers = ['Medicine Name', 'Category', 'Current Stock', 'Reorder Level', 'Predicted Demand', 'Recommended Order', 'Forecast Method']

    # Add title
    title = f"Forecast Report (Next {date_range} days)"
    ws['A1'] = title
    ws.merge_cells(f'A1:{get_column_letter(len(headers))}1')
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = Alignment(horizontal="center")

    # Add metadata
    ws['A2'] = f"Report Period: Next {date_range} days"
    ws['A3'] = f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}"
    ws['A4'] = f"Generated by: {request.user.get_full_name() or request.user.username}"

    # Add header row
    row_num = 6
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=row_num, column=col_num, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="2C3E50", end_color="2C3E50", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")
        cell.font = Font(bold=True, color="FFFFFF")

    # Add data rows
    row_num += 1
    total_predicted = 0
    total_recommended = 0

    for i, medicine in enumerate(medicines):
        # Get forecast data
        try:
            forecast_data = get_forecasting_results(medicine)
            predicted_demand = forecast_data.get('predicted_demand', 0)
            recommended_order = forecast_data.get('recommended_order_quantity', 0)
            forecast_method = forecast_data.get('model_used', 'N/A')

            total_predicted += predicted_demand
            total_recommended += recommended_order

            # Add row data
            ws.cell(row=row_num, column=1, value=medicine.name)
            ws.cell(row=row_num, column=2, value=medicine.category)
            ws.cell(row=row_num, column=3, value=medicine.quantity)
            ws.cell(row=row_num, column=4, value=medicine.reorder_level)
            ws.cell(row=row_num, column=5, value=round(predicted_demand, 1))
            ws.cell(row=row_num, column=6, value=round(recommended_order, 1))
            ws.cell(row=row_num, column=7, value=forecast_method)

            # Apply alternating row styling
            if i % 2 == 1:
                for col in range(1, len(headers) + 1):
                    ws.cell(row=row_num, column=col).fill = PatternFill(start_color="ECF0F1", end_color="ECF0F1", fill_type="solid")

            row_num += 1
        except Exception as e:
            print(f"Error generating forecast for {medicine.name}: {str(e)}")
            continue

    # Add total row
    row_num += 1
    ws.cell(row=row_num, column=1, value="Total")
    ws.cell(row=row_num, column=5, value=round(total_predicted, 1))
    ws.cell(row=row_num, column=6, value=round(total_recommended, 1))

    # Style total row
    for col in range(1, len(headers) + 1):
        cell = ws.cell(row=row_num, column=col)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="ECF0F1", end_color="ECF0F1", fill_type="solid")

    # Auto-adjust column widths
    for i, col in enumerate(ws.columns, 1):
        max_length = 0
        column = get_column_letter(i)
        for cell in col:
            if cell.value and not isinstance(cell, type(ws.merged_cells)):
                max_length = max(max_length, len(str(cell.value)))
        adjusted_width = max(max_length + 2, 10)
        ws.column_dimensions[column].width = adjusted_width

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    response['Content-Disposition'] = f'attachment; filename="forecast_report_{timestamp}.xlsx"'
    wb.save(response)
    return response


@login_required
def direct_forecast_excel(request):
    """Generate a direct forecast Excel report"""
    # Get all medicines with their latest forecasts
    forecasts = Forecast.objects.filter(
        forecast_date__date=timezone.now().date()
    ).select_related('medicine')

    # Create workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Forecast Report"

    # Define headers
    headers = ['Medicine Name', 'Category', 'Current Stock', 'Reorder Level', 'Recommended Order', 'Predicted Demand', 'Accuracy', 'Confidence Interval']

    # Add title
    title = "Forecast Report"
    ws['A1'] = title
    ws.merge_cells(f'A1:{get_column_letter(len(headers))}1')
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = Alignment(horizontal="center")

    # Add metadata
    ws['A2'] = f"Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M')}"
    ws['A3'] = f"Generated by: {request.user.get_full_name() or request.user.username}"

    # Add header row
    row_num = 5
    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=row_num, column=col_num, value=header)
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="2C3E50", end_color="2C3E50", fill_type="solid")
        cell.alignment = Alignment(horizontal="center")
        cell.font = Font(bold=True, color="FFFFFF")

    # Add data rows
    row_num += 1

    for i, forecast in enumerate(forecasts):
        try:
            medicine = forecast.medicine

            # Add row data
            ws.cell(row=row_num, column=1, value=medicine.name)
            ws.cell(row=row_num, column=2, value=medicine.category)
            ws.cell(row=row_num, column=3, value=medicine.quantity)
            ws.cell(row=row_num, column=4, value=medicine.reorder_level)
            ws.cell(row=row_num, column=5, value=int(forecast.recommended_order_quantity or 0))
            ws.cell(row=row_num, column=6, value=round(float(forecast.predicted_quantity or 0), 2))
            ws.cell(row=row_num, column=7, value=round(float(forecast.accuracy or 0), 2))
            ws.cell(row=row_num, column=8, value=f"{round(float(forecast.confidence_lower or 0))} - {round(float(forecast.confidence_upper or 0))}")

            # Apply alternating row styling
            if i % 2 == 1:
                for col in range(1, len(headers) + 1):
                    ws.cell(row=row_num, column=col).fill = PatternFill(start_color="ECF0F1", end_color="ECF0F1", fill_type="solid")

            row_num += 1
        except Exception as row_error:
            print(f"Error adding row for forecast {forecast.id}: {str(row_error)}")
            continue

    # Auto-adjust column widths
    for i, col in enumerate(ws.columns, 1):
        max_length = 0
        column = get_column_letter(i)
        try:
            for cell in col:
                if cell.value:
                    max_length = max(max_length, len(str(cell.value)))
            adjusted_width = max(max_length + 2, 10)
            ws.column_dimensions[column].width = adjusted_width
        except:
            pass

    # Create response
    try:
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        filename = f'forecast_report_{timezone.now().date()}.xlsx'
        response['Content-Disposition'] = f'attachment; filename="{filename}"'
        wb.save(response)
        return response
    except Exception as save_error:
        print(f"Error saving Excel file: {str(save_error)}")
        return HttpResponse("Error generating Excel file", status=500)


@login_required
def simple_excel_view(request):
    """Generate a simple Excel file"""
    # Create workbook
    wb = Workbook()
    ws = wb.active
    ws.title = "Simple Excel"

    # Add title
    ws['A1'] = "Simple Excel Report"
    ws['A1'].font = Font(bold=True, size=16)
    ws['A1'].alignment = Alignment(horizontal="center")

    # Add data
    ws['A3'] = "Generated on:"
    ws['B3'] = timezone.now().strftime('%Y-%m-%d %H:%M')

    ws['A4'] = "Generated by:"
    ws['B4'] = request.user.get_full_name() or request.user.username

    # Create response
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')
    response['Content-Disposition'] = f'attachment; filename="simple_excel_{timestamp}.xlsx"'
    wb.save(response)
    return response


# File Upload Views
@login_required
def upload_medicine_form(request):
    """Display the medicine upload form."""
    return render(request, 'upload_medicine.html')

@login_required
@require_POST
def upload_medicine(request):
    """Handle medicine CSV upload."""
    if 'csv_file' not in request.FILES:
        messages.error(request, "No file was uploaded.")
        return redirect('upload_medicine_form')

    csv_file = request.FILES['csv_file']

    # Check file extension - be more flexible with extensions
    if not csv_file.name.lower().endswith(('.csv', '.txt')):
        messages.error(request, "File must be a CSV document (with .csv or .txt extension).")
        return redirect('upload_medicine_form')

    try:
        results = import_medicines_from_csv(csv_file, request.user)

        # Log results
        logging.info(
            f"Medicine import results: Created {results['created']}, Updated {results['updated']}, "
            f"Skipped {results.get('skipped', 0)}, Errors {len(results['errors'])}, Warnings {len(results.get('warnings', []))}"
        )

        # Set success message
        if results['created'] > 0 or results['updated'] > 0:
            messages.success(
                request,
                f"Successfully processed {results['created'] + results['updated']} medicines. "
                f"Created: {results['created']}, Updated: {results['updated']}."
            )

        # Set skipped message if any
        if results.get('skipped', 0) > 0:
            messages.info(
                request,
                f"{results['skipped']} duplicate medicines were skipped."
            )

        # Set warning messages
        if results.get('warnings', []):
            # Group warnings by type to avoid too many messages
            if len(results['warnings']) > 5:
                messages.warning(
                    request,
                    f"{len(results['warnings'])} warnings occurred during import. Check the logs for details."
                )
            else:
                for warning in results['warnings']:
                    messages.warning(request, warning)

        # Set error messages
        if results['errors']:
            if len(results['errors']) > 5:
                messages.error(
                    request,
                    f"{len(results['errors'])} errors occurred during import. Check the logs for details."
                )
            else:
                for error in results['errors']:
                    messages.error(request, error)

        # If requested as AJAX, return JSON response
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse(results)

        # If no medicines were processed successfully, stay on the upload page
        if results['created'] == 0 and results['updated'] == 0:
            return redirect('upload_medicine_form')

        return redirect('medicine_list')

    except Exception as e:
        logging.error(f"Error importing medicines: {str(e)}")
        messages.error(request, f"An error occurred: {str(e)}")

        # If requested as AJAX, return JSON response
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({'error': str(e)}, status=400)

        return redirect('upload_medicine_form')

@login_required
def upload_transaction_form(request):
    """Display the transaction upload form."""
    return render(request, 'upload_transaction.html')

@login_required
@require_POST
def upload_transaction(request):
    """Handle transaction CSV upload."""
    if 'csv_file' not in request.FILES:
        messages.error(request, "No file was uploaded.")
        return redirect('upload_transaction_form')

    csv_file = request.FILES['csv_file']

    # Check file extension - be more flexible with extensions
    if not csv_file.name.lower().endswith(('.csv', '.txt')):
        messages.error(request, "File must be a CSV document (with .csv or .txt extension).")
        return redirect('upload_transaction_form')

    try:
        results = import_transactions_from_csv(csv_file, request.user)

        # Log results with historical data categorization
        active_historical = results.get('active_historical_transactions', 0)
        reference_historical = results.get('reference_historical_transactions', 0)
        ancient_skipped = results.get('ancient_transactions', 0)

        logging.info(
            f"Transaction import results: Created {results['created']}, "
            f"Skipped {results.get('skipped', 0)}, Errors {len(results['errors'])}, "
            f"Warnings {len(results.get('warnings', []))}"
        )

        # Log historical data summary
        if active_historical > 0 or reference_historical > 0 or ancient_skipped > 0:
            logging.info(
                f"Historical data summary: "
                f"Active historical (7d-1y): {active_historical}, "
                f"Reference historical (1y-3y): {reference_historical}, "
                f"Too old (>3y): {ancient_skipped}"
            )

        # Log that transactions will be included in the next forecast run
        logging.info("Transactions imported successfully and will be included in the next forecast generation at 7:00 PM")

        # Note: We don't immediately update forecasts as they are scheduled to run at 7:00 PM daily
        # This ensures consistency in the forecasting process

        # Set success message
        if results['created'] > 0:
            # Get historical data counts
            active_historical = results.get('active_historical_transactions', 0)
            reference_historical = results.get('reference_historical_transactions', 0)

            # Create message based on time of day
            current_time = timezone.now().time()
            if current_time.hour < 19:  # Before 7:00 PM
                base_message = (
                    f"Successfully created {results['created']} transactions. "
                    f"Forecasts will be updated during today's 7:00 PM scheduled run."
                )
            else:  # After 7:00 PM
                base_message = (
                    f"Successfully created {results['created']} transactions. "
                    f"Forecasts will be updated during tomorrow's 7:00 PM scheduled run."
                )

            # Add historical data info if applicable
            if active_historical > 0 or reference_historical > 0:
                historical_message = (
                    f" {active_historical} transactions from 7 days to 1 year will be used for forecasting. "
                    f"{reference_historical} transactions from 1-3 years will be stored as reference only."
                )
                messages.success(request, base_message + historical_message)
            else:
                messages.success(request, base_message)

        # Set skipped message if any
        if results.get('skipped', 0) > 0:
            messages.info(
                request,
                f"{results['skipped']} duplicate or invalid transactions were skipped."
            )

        # Set warning messages
        if results.get('warnings', []):
            # Group warnings by type to avoid too many messages
            if len(results['warnings']) > 5:
                messages.warning(
                    request,
                    f"{len(results['warnings'])} warnings occurred during import. "
                    f"Some transactions may be historical (older than 7 days) and will only be used for forecasting."
                )
            else:
                for warning in results['warnings']:
                    messages.warning(request, warning)

        # Set error messages
        if results['errors']:
            if len(results['errors']) > 5:
                messages.error(
                    request,
                    f"{len(results['errors'])} errors occurred during import. Check the logs for details."
                )
            else:
                for error in results['errors']:
                    messages.error(request, error)

        # If requested as AJAX, return JSON response
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse(results)

        # If no transactions were processed successfully, stay on the upload page
        if results['created'] == 0:
            return redirect('upload_transaction_form')

        return redirect('transaction_list')

    except Exception as e:
        logging.error(f"Error importing transactions: {str(e)}")
        messages.error(request, f"An error occurred: {str(e)}")

        # If requested as AJAX, return JSON response
        if request.headers.get('x-requested-with') == 'XMLHttpRequest':
            return JsonResponse({'error': str(e)}, status=400)

        return redirect('upload_transaction_form')

@login_required
def download_medicine_template(request):
    """Download a CSV template for medicine uploads."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="medicine_upload_template.csv"'

    writer = csv.writer(response)
    writer.writerow(['name', 'category', 'price', 'quantity', 'reorder_level', 'reorder_quantity', 'expiration_date', 'description'])
    writer.writerow(['Medicine Name 1', 'Category 1', '100.00', '50', '10', '20', '2025-12-31', 'Description for medicine 1'])
    writer.writerow(['Medicine Name 2', 'Category 2', '200.00', '30', '5', '10', '2025-12-31', 'Description for medicine 2'])

    return response

@login_required
def download_transaction_template(request):
    """Download a CSV template for transaction uploads."""
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="transaction_upload_template.csv"'

    writer = csv.writer(response)
    writer.writerow(['medicine_name', 'transaction_type', 'quantity', 'customer_name', 'patient_number', 'customer_type', 'transaction_date', 'transaction_time'])

    # Add example rows with actual medicine names if available
    medicines = Medicine.objects.all()[:2]

    if medicines:
        for i, medicine in enumerate(medicines):
            writer.writerow([
                medicine.name,  # Use medicine name instead of ID
                'sale' if i % 2 == 0 else 'purchase',
                '5' if i % 2 == 0 else '10',
                'John Doe' if i % 2 == 0 else 'Jane Smith',
                f'P{12345 + i}',
                'out_patient' if i % 2 == 0 else 'in_patient',
                '2025-04-26',
                '14:30:00' if i % 2 == 0 else '15:45:00'
            ])
    else:
        # Add example rows with placeholder medicine names
        writer.writerow(['Paracetamol', 'sale', '5', 'John Doe', 'P12345', 'out_patient', '2025-04-26', '14:30:00'])
        writer.writerow(['Amoxicillin', 'purchase', '10', 'Jane Smith', 'P67890', 'in_patient', '2025-04-26', '15:45:00'])

    return response


# Stock Movement Views
@login_required
def stock_movement_list(request):
    """
    Display a list of all stock movements with filtering and sorting options.
    """
    # Get filter parameters
    search = request.GET.get('search', '').strip()
    movement_type = request.GET.get('movement_type', 'all')
    date_filter = request.GET.get('date_filter', 'all')
    brand_filter = request.GET.get('brand', '')
    supplier_filter = request.GET.get('supplier', '')
    sort_by = request.GET.get('sort_by', '-movement_date')
    page = request.GET.get('page', 1)

    # Base queryset with optimized loading
    movements = StockMovement.objects.select_related('medicine', 'medicine__generic_medicine', 'performed_by').order_by('-movement_date')

    # Apply search filter
    if search:
        # Split the search query into individual terms
        search_terms = search.split()

        # Start with all movements
        search_query = Q()

        # Add each term to the search query
        for term in search_terms:
            term_query = (
                Q(medicine__name__icontains=term) |
                Q(medicine__category__icontains=term) |
                Q(medicine__description__icontains=term) |
                Q(medicine__generic_medicine__name__icontains=term) |
                Q(brand__icontains=term) |
                Q(supplier__icontains=term) |
                Q(reference_number__icontains=term) |
                Q(notes__icontains=term)
            )
            search_query |= term_query

        # Apply the search query
        movements = movements.filter(search_query)

    # Apply movement type filter
    if movement_type != 'all':
        movements = movements.filter(movement_type=movement_type)

    # Apply date filter
    if date_filter == 'today':
        today = timezone.now().date()
        movements = movements.filter(movement_date__date=today)
    elif date_filter == 'week':
        week_ago = timezone.now().date() - timedelta(days=7)
        movements = movements.filter(movement_date__date__gte=week_ago)
    elif date_filter == 'month':
        month_ago = timezone.now().date() - timedelta(days=30)
        movements = movements.filter(movement_date__date__gte=month_ago)
    elif date_filter == 'quarter':
        quarter_ago = timezone.now().date() - timedelta(days=90)
        movements = movements.filter(movement_date__date__gte=quarter_ago)

    # Apply brand filter
    if brand_filter:
        movements = movements.filter(brand__icontains=brand_filter)

    # Apply supplier filter
    if supplier_filter:
        movements = movements.filter(supplier__icontains=supplier_filter)

    # Apply sorting
    if sort_by in ['movement_date', '-movement_date', 'medicine__name', '-medicine__name',
                  'quantity', '-quantity', 'brand', '-brand', 'supplier', '-supplier',
                  'movement_type', '-movement_type']:
        movements = movements.order_by(sort_by)

    # Get unique brands, suppliers, and categories for filters
    all_brands = StockMovement.objects.exclude(brand='').values_list('brand', flat=True).distinct()
    all_suppliers = StockMovement.objects.exclude(supplier='').values_list('supplier', flat=True).distinct()
    all_categories = Medicine.objects.exclude(category='').values_list('category', flat=True).distinct()

    # Get all medicines and generic medicines for the search modal
    all_medicines = Medicine.objects.all().order_by('name')
    all_generics = GenericMedicine.objects.all().order_by('name')

    # Paginate results
    paginator = Paginator(movements, 10)  # Show 10 movements per page
    try:
        page_obj = paginator.page(page)
    except PageNotAnInteger:
        page_obj = paginator.page(1)
    except EmptyPage:
        page_obj = paginator.page(paginator.num_pages)

    # Get movement statistics
    total_movements = movements.count()
    total_quantity = movements.aggregate(total=Sum('quantity'))['total'] or 0

    # Get movement type counts for chart
    movement_type_counts = movements.values('movement_type').annotate(count=Count('id'))
    movement_type_data = {
        'labels': [dict(StockMovement.MOVEMENT_TYPES).get(mt['movement_type'], mt['movement_type']) for mt in movement_type_counts],
        'data': [mt['count'] for mt in movement_type_counts]
    }

    context = {
        'movements': page_obj,
        'total_movements': total_movements,
        'total_quantity': total_quantity,
        'search': search,
        'movement_type': movement_type,
        'date_filter': date_filter,
        'brand_filter': brand_filter,
        'supplier_filter': supplier_filter,
        'sort_by': sort_by,
        'all_brands': all_brands,
        'all_suppliers': all_suppliers,
        'all_categories': all_categories,
        'all_medicines': all_medicines,
        'all_generics': all_generics,
        'movement_type_data': json.dumps(movement_type_data),
        'movement_types': StockMovement.MOVEMENT_TYPES,
    }

    return render(request, 'stock_movement_list.html', context)


@login_required
def create_stock_movement(request):
    """
    Create a new stock movement.
    """
    if request.method == 'POST':
        form = StockMovementForm(request.POST, user=request.user)
        if form.is_valid():
            movement = form.save()
            messages.success(request, f'Stock movement for {movement.medicine.name} recorded successfully!')

            # Update movement analysis for the medicine
            try:
                analysis, created = MovementAnalysis.objects.get_or_create(medicine=movement.medicine)
                analysis.update_analysis()
            except Exception as e:
                messages.warning(request, f'Movement recorded but analysis update failed: {str(e)}')

            return redirect('stock_movement_list')
    else:
        # Pre-fill medicine if provided in URL
        initial = {}
        medicine_id = request.GET.get('medicine')
        if medicine_id:
            try:
                medicine = Medicine.objects.get(pk=medicine_id)
                initial['medicine'] = medicine
                if medicine.brand:
                    initial['brand'] = medicine.brand
                if medicine.supplier:
                    initial['supplier'] = medicine.supplier
            except Medicine.DoesNotExist:
                pass

        form = StockMovementForm(initial=initial, user=request.user)

    context = {
        'form': form,
        'medicine_id': medicine_id if 'medicine_id' in locals() else None,
    }

    return render(request, 'create_stock_movement.html', context)


@login_required
def stock_movement_detail(request, pk):
    """
    Display details of a specific stock movement.
    """
    movement = get_object_or_404(StockMovement, pk=pk)

    # Get related movements for the same medicine
    related_movements = StockMovement.objects.filter(
        medicine=movement.medicine
    ).exclude(
        pk=movement.pk
    ).order_by('-movement_date')[:5]

    context = {
        'movement': movement,
        'related_movements': related_movements,
    }

    return render(request, 'stock_movement_detail.html', context)


@login_required
def medicine_movement_analysis(request, pk):
    """
    Display movement analysis for a specific medicine.
    """
    medicine = get_object_or_404(Medicine, pk=pk)

    # Get or create movement analysis
    analysis, created = MovementAnalysis.objects.get_or_create(medicine=medicine)

    # Update the analysis if it's new or outdated
    if created or (timezone.now() - analysis.last_updated).days > 1:
        analysis.update_analysis()

    # Get all movements for this medicine
    movements = StockMovement.objects.filter(medicine=medicine).order_by('-movement_date')

    # If no movements exist, create a sample movement to ensure data is displayed
    if not movements.exists():
        # Create a sample purchase movement
        sample_movement = StockMovement(
            medicine=medicine,
            movement_date=timezone.now() - timedelta(days=30),
            quantity=10,
            movement_type='purchase',
            brand=medicine.brand or '',
            supplier=medicine.supplier or '',
            reference_number='SAMPLE-001',
            notes='Sample movement for demonstration',
            unit_price=medicine.price,
            total_value=medicine.price * 10
        )
        sample_movement.save()

        # Create a sample sale movement
        sample_movement2 = StockMovement(
            medicine=medicine,
            movement_date=timezone.now() - timedelta(days=15),
            quantity=-5,
            movement_type='sale',
            brand=medicine.brand or '',
            supplier=medicine.supplier or '',
            reference_number='SAMPLE-002',
            notes='Sample movement for demonstration',
            unit_price=medicine.price,
            total_value=medicine.price * 5
        )
        sample_movement2.save()

        # Refresh the movements queryset
        movements = StockMovement.objects.filter(medicine=medicine).order_by('-movement_date')

        # Update the analysis
        analysis.update_analysis()

    # Get movement data for charts
    monthly_data = []
    quarterly_data = []

    # Calculate monthly movement data for the past 12 months
    for i in range(11, -1, -1):
        month_start = timezone.now().replace(day=1) - timedelta(days=30 * i)
        month_end = (month_start.replace(day=1) + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        month_movements = movements.filter(movement_date__range=(month_start, month_end))
        month_quantity = abs(month_movements.aggregate(total=Sum('quantity'))['total'] or 0)

        monthly_data.append({
            'month': month_start.strftime('%b %Y'),
            'quantity': month_quantity
        })

    # Calculate quarterly movement data for the past 4 quarters
    for i in range(3, -1, -1):
        quarter_start = timezone.now().replace(day=1) - timedelta(days=90 * i)
        quarter_end = quarter_start + timedelta(days=90)

        quarter_movements = movements.filter(movement_date__range=(quarter_start, quarter_end))
        quarter_quantity = abs(quarter_movements.aggregate(total=Sum('quantity'))['total'] or 0)

        quarterly_data.append({
            'quarter': f'Q{4-i} {quarter_start.year}',
            'quantity': quarter_quantity
        })

    # Get movement type distribution
    movement_types = movements.values('movement_type').annotate(count=Count('id'))
    movement_type_data = {
        'labels': [dict(StockMovement.MOVEMENT_TYPES).get(mt['movement_type'], mt['movement_type']) for mt in movement_types],
        'data': [mt['count'] for mt in movement_types]
    }

    # Get brand distribution
    brand_data = movements.exclude(brand='').values('brand').annotate(count=Count('id')).order_by('-count')

    # Get supplier distribution
    supplier_data = movements.exclude(supplier='').values('supplier').annotate(count=Count('id')).order_by('-count')

    context = {
        'medicine': medicine,
        'analysis': analysis,
        'movements': movements[:10],  # Show only the 10 most recent movements
        'total_movements': movements.count(),
        'monthly_data': json.dumps([{
            'month': item['month'],
            'quantity': item['quantity']
        } for item in monthly_data]),
        'quarterly_data': json.dumps([{
            'quarter': item['quarter'],
            'quantity': item['quantity']
        } for item in quarterly_data]),
        'movement_type_data': json.dumps(movement_type_data),
        'brand_data': brand_data[:5],  # Top 5 brands
        'supplier_data': supplier_data[:5],  # Top 5 suppliers
    }

    return render(request, 'medicine_movement_analysis.html', context)


@login_required
def movement_analysis_dashboard(request):
    """
    Display a dashboard with movement analysis for all medicines.
    """
    # Get filter parameters
    movement_class = request.GET.get('movement_class', 'all')
    category_filter = request.GET.get('category', '')
    brand_filter = request.GET.get('brand', '')
    supplier_filter = request.GET.get('supplier', '')
    search = request.GET.get('search', '')

    # Base queryset
    analyses = MovementAnalysis.objects.select_related('medicine').all()

    # Apply filters
    if movement_class != 'all':
        analyses = analyses.filter(movement_class=movement_class)

    if category_filter:
        analyses = analyses.filter(medicine__category__icontains=category_filter)

    if brand_filter:
        analyses = analyses.filter(medicine__brand__icontains=brand_filter)

    if supplier_filter:
        analyses = analyses.filter(medicine__supplier__icontains=supplier_filter)

    if search:
        analyses = analyses.filter(
            Q(medicine__name__icontains=search) |
            Q(medicine__brand__icontains=search) |
            Q(medicine__supplier__icontains=search)
        )

    # Get medicines without movement analysis
    medicines_without_analysis = Medicine.objects.exclude(
        id__in=MovementAnalysis.objects.values_list('medicine_id', flat=True)
    )

    # Create movement analyses for medicines that don't have one
    if medicines_without_analysis.exists():
        for medicine in medicines_without_analysis:
            analysis = MovementAnalysis.objects.create(medicine=medicine)
            analysis.update_analysis()

    # Get movement class distribution
    class_distribution = analyses.values('movement_class').annotate(count=Count('id'))
    class_data = {
        'labels': [dict(MovementAnalysis.MOVEMENT_CLASSES).get(c['movement_class'], c['movement_class']) for c in class_distribution],
        'data': [c['count'] for c in class_distribution]
    }

    # Get top moving medicines
    top_movers = analyses.filter(monthly_movement_rate__gt=0).order_by('-monthly_movement_rate')[:10]

    # Get medicines with no movement
    no_movement = analyses.filter(movement_class='D').order_by('medicine__name')

    # Get categories with highest movement
    category_movement = analyses.values('medicine__category').annotate(
        avg_rate=Avg('monthly_movement_rate')
    ).exclude(medicine__category='').order_by('-avg_rate')[:5]

    # Get brands with highest movement
    brand_movement = analyses.values('top_brand').annotate(
        count=Count('id')
    ).exclude(top_brand='').order_by('-count')[:5]

    # Get suppliers with highest movement
    supplier_movement = analyses.values('top_supplier').annotate(
        count=Count('id')
    ).exclude(top_supplier='').order_by('-count')[:5]

    # Get all medicines for the search modal
    all_medicines = Medicine.objects.all().order_by('name')
    all_generics = GenericMedicine.objects.all().order_by('name')

    context = {
        'analyses': analyses,
        'movement_class': movement_class,
        'category_filter': category_filter,
        'brand_filter': brand_filter,
        'supplier_filter': supplier_filter,
        'search': search,
        'class_data': json.dumps(class_data),
        'top_movers': top_movers,
        'no_movement': no_movement[:10],  # Show only the first 10
        'total_no_movement': no_movement.count(),
        'category_movement': category_movement,
        'brand_movement': brand_movement,
        'supplier_movement': supplier_movement,
        'movement_classes': MovementAnalysis.MOVEMENT_CLASSES,
        'categories': Medicine.objects.exclude(category='').values_list('category', flat=True).distinct(),
        'brands': Medicine.objects.exclude(brand='').values_list('brand', flat=True).distinct(),
        'suppliers': Medicine.objects.exclude(supplier='').values_list('supplier', flat=True).distinct(),
        'all_medicines': all_medicines,
        'all_generics': all_generics,
    }

    return render(request, 'movement_analysis_dashboard.html', context)


@login_required
def get_medicine_details_api(request, pk):
    """
    API endpoint to get medicine details for the stock movement form.
    """
    try:
        medicine = Medicine.objects.get(pk=pk)
        data = {
            'brand': medicine.brand or '',
            'supplier': medicine.supplier or '',
            'quantity': medicine.quantity,
            'price': float(medicine.price),
        }
        return JsonResponse(data)
    except Medicine.DoesNotExist:
        return JsonResponse({'error': 'Medicine not found'}, status=404)


@login_required
def update_all_movement_analyses(request):
    """
    Update all movement analyses.
    """
    if request.method == 'POST':
        try:
            # Get all medicines
            medicines = Medicine.objects.all()
            updated_count = 0

            for medicine in medicines:
                analysis, created = MovementAnalysis.objects.get_or_create(medicine=medicine)
                analysis.update_analysis()
                updated_count += 1

            messages.success(request, f'Successfully updated movement analysis for {updated_count} medicines.')
        except Exception as e:
            messages.error(request, f'Error updating movement analyses: {str(e)}')

    return redirect('movement_analysis_dashboard')