{% extends "admin/base_site.html" %}
{% load i18n admin_urls %}

{% block content %}
<div id="content-main">
    <form method="post">
        {% csrf_token %}
        <fieldset class="module aligned">
            <h2>Archive User {{ user.username }}</h2>
            <p class="warning" style="color: red; font-weight: bold;">
                Warning: This will delete the user from the system but keep their information in the archives.
            </p>
            <p>
                Are you sure you want to archive the user <strong>{{ user.username }}</strong>?
            </p>
        </fieldset>

        <div class="submit-row">
            <input type="submit" value="Archive User" class="default" name="_archive">
            <a href="{% url 'admin:auth_user_changelist' %}" class="button cancel-link">Cancel</a>
        </div>
    </form>
</div>
{% endblock %}
