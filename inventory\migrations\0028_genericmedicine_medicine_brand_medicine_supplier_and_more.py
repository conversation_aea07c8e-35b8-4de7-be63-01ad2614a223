# Generated by Django 5.2 on 2025-04-26 15:24

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0027_forecast_notes_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='GenericMedicine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('category', models.Char<PERSON>ield(max_length=255)),
                ('description', models.TextField(blank=True)),
            ],
            options={
                'verbose_name': 'Generic Medicine',
                'verbose_name_plural': 'Generic Medicines',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='medicine',
            name='brand',
            field=models.CharField(blank=True, help_text='Brand name (e.g., Tylenol, Panadol)', max_length=255),
        ),
        migrations.AddField(
            model_name='medicine',
            name='supplier',
            field=models.CharField(blank=True, help_text='Supplier or manufacturer (e.g., Johnson & Johnson, GlaxoSmithKline)', max_length=255),
        ),
        migrations.AddField(
            model_name='medicine',
            name='generic_medicine',
            field=models.ForeignKey(blank=True, help_text='The generic medicine category this specific medicine belongs to', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='variants', to='inventory.genericmedicine'),
        ),
    ]
