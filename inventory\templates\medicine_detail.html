﻿{% extends 'base.html' %}

{% block content %}
<div class="container-fluid px-4 py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-gray-800">Medicine Details</h1>
                <div>
                    <a href="{% url 'medicine_movement_analysis' medicine.id %}" class="btn btn-info btn-sm me-2">
                        <i class="fas fa-chart-line me-1"></i>Movement Analysis
                    </a>
                    <a href="{% url 'medicine_list' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-arrow-left me-1"></i>Back to List
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Information Card - Full Width -->
        <div class="col-12">
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-capsules me-2"></i>{{ medicine.name }}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <th class="fw-bold text-muted">Name</th>
                                        <td>{{ medicine.name }}</td>
                                    </tr>
                                    <tr>
                                        <th class="fw-bold text-muted">Brand</th>
                                        <td>{{ medicine.brand }}</td>
                                    </tr>
                                    <tr>
                                        <th class="fw-bold text-muted">Supplier</th>
                                        <td>{{ medicine.supplier }}</td>
                                    </tr>
                                    <tr>
                                        <th class="fw-bold text-muted">Generic Medicine</th>
                                        <td>
                                            {% if medicine.generic_medicine %}
                                            {{ medicine.generic_medicine.name }}
                                            {% else %}
                                            <span class="text-muted">Not assigned</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th class="fw-bold text-muted">Description</th>
                                        <td>{{ medicine.description }}</td>
                                    </tr>
                                    <tr>
                                        <th class="fw-bold text-muted">Category</th>
                                        <td>{{ medicine.category }}</td>
                                    </tr>
                                    <tr>
                                        <th class="fw-bold text-muted">Price</th>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <span id="price-display">₱{{ medicine.price }}</span>
                                                <button type="button" id="edit-price-btn" class="btn btn-sm btn-outline-primary ms-2">
                                                    <i class="fas fa-edit"></i> Edit
                                                </button>
                                            </div>
                                            <div id="price-update-status" class="mt-1"></div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title text-primary mb-3">Inventory Status</h6>
                                    <div class="row text-center">
                                        <div class="col-md-4">
                                            <p class="mb-1 text-muted small">Current Quantity</p>
                                            <h4 class="mb-0 {% if medicine.quantity <= medicine.reorder_level %}text-danger{% else %}text-success{% endif %}">
                                                {{ medicine.quantity }}
                                                {% if medicine.quantity <= medicine.reorder_level %}
                                                <span class="badge bg-danger ms-2">Reorder Needed</span>
                                                {% endif %}
                                            </h4>
                                        </div>
                                        <div class="col-md-4">
                                            <p class="mb-1 text-muted small">Reorder Level</p>
                                            <h4 class="mb-0">{{ medicine.reorder_level }}</h4>
                                        </div>
                                        <div class="col-md-4">
                                            <p class="mb-1 text-muted small">Reorder Quantity</p>
                                            <h4 class="mb-0">{{ medicine.reorder_quantity }}</h4>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Expiration Information Card - Full Width -->
            <div class="card shadow-sm mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-clock me-2"></i>Expiration Information
                    </h6>
                </div>
                <div class="card-body">
                    {% if medicine.expiration_date %}
                    <div class="row">
                        <div class="col-md-6">
                            <p class="mb-2">
                                <strong>Expiration Date:</strong>
                                <span class="{% if is_expired %}text-danger{% elif days_until_expiration <= 30 %}text-warning{% else %}text-success{% endif %}">
                                    {{ medicine.expiration_date|date:"F d, Y" }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            {% if is_expired %}
                            <div class="alert alert-danger mb-0">
                                <i class="fas fa-exclamation-triangle me-2"></i>This medicine has expired!
                            </div>
                            {% elif days_until_expiration <= 30 %}
                            <div class="alert alert-warning mb-0">
                                <i class="fas fa-exclamation-circle me-2"></i>This medicine will expire in {{ days_until_expiration }} days.
                            </div>
                            {% else %}
                            <div class="alert alert-success mb-0">
                                <i class="fas fa-check-circle me-2"></i>{{ days_until_expiration }} days until expiration.
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% else %}
                    <p class="text-muted mb-0">
                        <i class="fas fa-info-circle me-2"></i>No expiration date set
                    </p>
                    {% endif %}
                </div>
            </div>

            <!-- Alternative Medicines Card -->
            <div class="card shadow-sm">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-exchange-alt me-2"></i>Alternative Medicines
                    </h6>
                </div>
                <div class="card-body">
                    <ul class="nav nav-tabs" id="alternativeTabs" role="tablist">
                        {% if generic_alternatives %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="generic-tab" data-bs-toggle="tab" data-bs-target="#generic" type="button" role="tab" aria-controls="generic" aria-selected="true">
                                Generic Alternatives
                            </button>
                        </li>
                        {% endif %}
                        {% if category_alternatives %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link {% if not generic_alternatives %}active{% endif %}" id="category-tab" data-bs-toggle="tab" data-bs-target="#category" type="button" role="tab" aria-controls="category" aria-selected="{% if not generic_alternatives %}true{% else %}false{% endif %}">
                                Same Category
                            </button>
                        </li>
                        {% endif %}
                        {% if brand_alternatives %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link {% if not generic_alternatives and not category_alternatives %}active{% endif %}" id="brand-tab" data-bs-toggle="tab" data-bs-target="#brand" type="button" role="tab" aria-controls="brand" aria-selected="{% if not generic_alternatives and not category_alternatives %}true{% else %}false{% endif %}">
                                Same Brand
                            </button>
                        </li>
                        {% endif %}
                        {% if supplier_alternatives %}
                        <li class="nav-item" role="presentation">
                            <button class="nav-link {% if not generic_alternatives and not category_alternatives and not brand_alternatives %}active{% endif %}" id="supplier-tab" data-bs-toggle="tab" data-bs-target="#supplier" type="button" role="tab" aria-controls="supplier" aria-selected="{% if not generic_alternatives and not category_alternatives and not brand_alternatives %}true{% else %}false{% endif %}">
                                Same Supplier
                            </button>
                        </li>
                        {% endif %}
                    </ul>

                    <div class="tab-content pt-3" id="alternativeTabsContent">
                        {% if generic_alternatives %}
                        <div class="tab-pane fade show active" id="generic" role="tabpanel" aria-labelledby="generic-tab">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Name</th>
                                            <th>Brand</th>
                                            <th>Supplier</th>
                                            <th>Quantity</th>
                                            <th>Price</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for alt in generic_alternatives %}
                                        <tr>
                                            <td>{{ alt.name }}</td>
                                            <td>{{ alt.brand }}</td>
                                            <td>{{ alt.supplier }}</td>
                                            <td class="{% if alt.quantity <= alt.reorder_level %}text-danger{% endif %}">
                                                {{ alt.quantity }}
                                                {% if alt.quantity <= alt.reorder_level %}
                                                <span class="badge bg-danger">Low</span>
                                                {% endif %}
                                            </td>
                                            <td>₱{{ alt.price }}</td>
                                            <td>
                                                <a href="{% url 'medicine_detail' alt.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="6" class="text-center">No generic alternatives found</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endif %}

                        {% if category_alternatives %}
                        <div class="tab-pane fade {% if not generic_alternatives %}show active{% endif %}" id="category" role="tabpanel" aria-labelledby="category-tab">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Name</th>
                                            <th>Brand</th>
                                            <th>Supplier</th>
                                            <th>Quantity</th>
                                            <th>Price</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for alt in category_alternatives %}
                                        <tr>
                                            <td>{{ alt.name }}</td>
                                            <td>{{ alt.brand }}</td>
                                            <td>{{ alt.supplier }}</td>
                                            <td class="{% if alt.quantity <= alt.reorder_level %}text-danger{% endif %}">
                                                {{ alt.quantity }}
                                                {% if alt.quantity <= alt.reorder_level %}
                                                <span class="badge bg-danger">Low</span>
                                                {% endif %}
                                            </td>
                                            <td>₱{{ alt.price }}</td>
                                            <td>
                                                <a href="{% url 'medicine_detail' alt.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="6" class="text-center">No category alternatives found</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endif %}

                        {% if brand_alternatives %}
                        <div class="tab-pane fade {% if not generic_alternatives and not category_alternatives %}show active{% endif %}" id="brand" role="tabpanel" aria-labelledby="brand-tab">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Name</th>
                                            <th>Category</th>
                                            <th>Supplier</th>
                                            <th>Quantity</th>
                                            <th>Price</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for alt in brand_alternatives %}
                                        <tr>
                                            <td>{{ alt.name }}</td>
                                            <td>{{ alt.category }}</td>
                                            <td>{{ alt.supplier }}</td>
                                            <td class="{% if alt.quantity <= alt.reorder_level %}text-danger{% endif %}">
                                                {{ alt.quantity }}
                                                {% if alt.quantity <= alt.reorder_level %}
                                                <span class="badge bg-danger">Low</span>
                                                {% endif %}
                                            </td>
                                            <td>₱{{ alt.price }}</td>
                                            <td>
                                                <a href="{% url 'medicine_detail' alt.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="6" class="text-center">No brand alternatives found</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endif %}

                        {% if supplier_alternatives %}
                        <div class="tab-pane fade {% if not generic_alternatives and not category_alternatives and not brand_alternatives %}show active{% endif %}" id="supplier" role="tabpanel" aria-labelledby="supplier-tab">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Name</th>
                                            <th>Category</th>
                                            <th>Brand</th>
                                            <th>Quantity</th>
                                            <th>Price</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for alt in supplier_alternatives %}
                                        <tr>
                                            <td>{{ alt.name }}</td>
                                            <td>{{ alt.category }}</td>
                                            <td>{{ alt.brand }}</td>
                                            <td class="{% if alt.quantity <= alt.reorder_level %}text-danger{% endif %}">
                                                {{ alt.quantity }}
                                                {% if alt.quantity <= alt.reorder_level %}
                                                <span class="badge bg-danger">Low</span>
                                                {% endif %}
                                            </td>
                                            <td>₱{{ alt.price }}</td>
                                            <td>
                                                <a href="{% url 'medicine_detail' alt.id %}" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-eye me-1"></i>View
                                                </a>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="6" class="text-center">No supplier alternatives found</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        {% endif %}

                        {% if not generic_alternatives and not category_alternatives and not brand_alternatives and not supplier_alternatives %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No alternative medicines found
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const editPriceBtn = document.getElementById('edit-price-btn');
        const priceDisplay = document.getElementById('price-display');
        const priceUpdateStatus = document.getElementById('price-update-status');
        const medicineId = {{ medicine.id }};
        const currentPrice = {{ medicine.price }};

        // Handle price edit button click
        editPriceBtn.addEventListener('click', function() {
            // Create modal for price editing
            const modalHtml = `
                <div class="modal fade" id="editPriceModal" tabindex="-1" aria-labelledby="editPriceModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-primary text-white">
                                <h5 class="modal-title" id="editPriceModalLabel">Update Medicine Price</h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label for="new-price" class="form-label">New Price (₱)</label>
                                    <input type="number" class="form-control" id="new-price" value="${currentPrice}" min="0.01" step="0.01">
                                    <div class="form-text">Enter the new price for this medicine</div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-primary" id="save-price-btn">Save Changes</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Add modal to the document
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Initialize and show the modal
            const priceModal = new bootstrap.Modal(document.getElementById('editPriceModal'));
            priceModal.show();

            // Handle save button click
            document.getElementById('save-price-btn').addEventListener('click', function() {
                const newPrice = document.getElementById('new-price').value;

                if (parseFloat(newPrice) <= 0) {
                    alert('Price must be greater than zero');
                    return;
                }

                // Update the price via AJAX
                updateMedicinePrice(medicineId, newPrice, priceModal);
            });
        });

        // Function to update medicine price
        function updateMedicinePrice(medicineId, newPrice, modal) {
            // Show loading indicator
            priceUpdateStatus.innerHTML = '<div class="spinner-border spinner-border-sm text-primary" role="status"><span class="visually-hidden">Loading...</span></div> Updating price...';

            fetch('{% url "update_medicine_price" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({
                    medicine_id: medicineId,
                    price: newPrice
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Update the price display
                    priceDisplay.textContent = '₱' + data.new_price;

                    // Show success message
                    priceUpdateStatus.innerHTML = '<div class="alert alert-success py-1 px-2 mb-0"><i class="fas fa-check-circle me-1"></i>' + data.message + '</div>';

                    // Close the modal
                    modal.hide();

                    // Remove the modal from DOM after hiding
                    document.getElementById('editPriceModal').addEventListener('hidden.bs.modal', function() {
                        this.remove();
                    });

                    // Clear the success message after 5 seconds
                    setTimeout(() => {
                        priceUpdateStatus.innerHTML = '';
                    }, 5000);
                } else {
                    // Show error message
                    priceUpdateStatus.innerHTML = '<div class="alert alert-danger py-1 px-2 mb-0"><i class="fas fa-exclamation-circle me-1"></i>Error: ' + data.error + '</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                priceUpdateStatus.innerHTML = '<div class="alert alert-danger py-1 px-2 mb-0"><i class="fas fa-exclamation-circle me-1"></i>Error updating price. Please try again.</div>';
            });
        }
    });
</script>
{% endblock %}