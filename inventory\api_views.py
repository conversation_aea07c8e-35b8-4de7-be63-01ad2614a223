from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from .models import Medicine
import logging

logger = logging.getLogger(__name__)

# API views for inventory app

@require_http_methods(["GET"])
def get_medicines(request):
    """API endpoint to get all medicines"""
    try:
        # Get search query from request if provided
        search_query = request.GET.get('search', '')

        # Filter medicines by name if search query is provided
        if search_query:
            medicines = Medicine.objects.filter(name__icontains=search_query).values('id', 'name')
        else:
            medicines = Medicine.objects.all().values('id', 'name')

        return JsonResponse(list(medicines), safe=False)
    except Exception as e:
        logger.error(f"Error in get_medicines API: {str(e)}")
        return JsonResponse({"error": str(e)}, status=500)


