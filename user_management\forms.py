from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.models import User
from .models import UserProfile

class UserRegistrationForm(UserCreationForm):
    """
    Form for registering new users with additional profile fields.
    Extends Django's UserCreationForm to capture profile information during registration.
    """
    first_name = forms.CharField(
        max_length=30,
        required=True,
        error_messages={'required': 'First name is required'}
    )
    middle_name = forms.Char<PERSON>ield(max_length=30, required=False)
    last_name = forms.CharField(
        max_length=30,
        required=True,
        error_messages={'required': 'Last name is required'}
    )
    email = forms.EmailField(
        required=True,
        error_messages={
            'required': 'Email address is required',
            'invalid': 'Please enter a valid email address'
        }
    )
    birthday = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True,
        error_messages={'required': 'Birthday is required'}
    )
    sex = forms.ChoiceField(
        choices=UserProfile.GENDER_CHOICES,
        required=True,
        error_messages={'required': 'Please select your gender'}
    )
    phone_number = forms.CharField(max_length=15, required=False)

    class Meta:
        model = User
        fields = ("username", "first_name", "middle_name", "last_name", "email",
                  "birthday", "sex", "phone_number", "password1", "password2")

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise forms.ValidationError('This email address is already in use.')
        return email

    def clean_username(self):
        username = self.cleaned_data.get('username')
        if User.objects.filter(username=username).exists():
            raise forms.ValidationError('This username is already taken.')
        return username

    def clean(self):
        cleaned_data = super().clean()
        password1 = cleaned_data.get('password1')
        password2 = cleaned_data.get('password2')

        if password1 and password2 and password1 != password2:
            self.add_error('password2', 'The two password fields must match.')

        return cleaned_data

    def save(self, commit=True):
        # Create the User instance
        user = super().save(commit=False)
        user.first_name = self.cleaned_data["first_name"]
        user.last_name = self.cleaned_data["last_name"]
        user.email = self.cleaned_data["email"]

        if commit:
            user.save()

            # Get or create the UserProfile
            profile, created = UserProfile.objects.get_or_create(user=user)
            profile.middle_name = self.cleaned_data["middle_name"]
            profile.birthday = self.cleaned_data["birthday"]
            profile.sex = self.cleaned_data["sex"]
            profile.phone_number = self.cleaned_data["phone_number"]
            profile.save()

        return user


class UserProfileUpdateForm(forms.ModelForm):
    """
    Form for updating user profiles.
    Handles both User model and UserProfile model fields.
    """
    first_name = forms.CharField(
        max_length=30,
        required=True,
        error_messages={'required': 'First name is required'}
    )
    middle_name = forms.CharField(max_length=30, required=False)
    last_name = forms.CharField(
        max_length=30,
        required=True,
        error_messages={'required': 'Last name is required'}
    )
    email = forms.EmailField(
        required=True,
        error_messages={
            'required': 'Email address is required',
            'invalid': 'Please enter a valid email address'
        }
    )
    phone_number = forms.CharField(max_length=17, required=False)
    address = forms.CharField(widget=forms.Textarea(attrs={'rows': 3}), required=False)
    profile_picture = forms.ImageField(required=False, widget=forms.FileInput(attrs={
        'class': 'd-none',
        'accept': 'image/*'
    }))

    class Meta:
        model = UserProfile
        fields = [
            'first_name', 'middle_name', 'last_name', 'email',
            'birthday', 'sex', 'phone_number', 'address', 'profile_picture'
        ]
        widgets = {
            'birthday': forms.DateInput(attrs={'type': 'date'}),
        }

    def __init__(self, *args, **kwargs):
        # Extract is_admin flag
        self.is_admin = kwargs.pop('is_admin', False)
        super().__init__(*args, **kwargs)

        # Initialize form fields with values from the User model
        if self.instance and self.instance.user:
            self.fields['first_name'].initial = self.instance.user.first_name
            self.fields['last_name'].initial = self.instance.user.last_name
            self.fields['email'].initial = self.instance.user.email

    def clean_email(self):
        email = self.cleaned_data.get('email')
        # Only validate if the email has changed
        if email and self.instance and self.instance.user:
            user_id = self.instance.user.id
            if User.objects.filter(email=email).exclude(id=user_id).exists():
                raise forms.ValidationError('This email address is already in use.')
        return email

    def save(self, commit=True):
        # Save the UserProfile instance
        profile = super().save(commit=False)

        # Get cleaned data
        email = self.cleaned_data.get('email')
        phone_number = self.cleaned_data.get('phone_number')
        address = self.cleaned_data.get('address')

        # If admin user, update directly
        if self.is_admin:
            # Update the associated User model fields
            if profile.user:
                profile.user.first_name = self.cleaned_data["first_name"]
                profile.user.last_name = self.cleaned_data["last_name"]
                profile.user.email = email

                # Update profile fields directly
                profile.phone_number = phone_number
                profile.address = address

                # Clear any pending changes
                profile.pending_email = None
                profile.pending_phone_number = None
                profile.pending_address = None
                profile.pending_changes_date = None

                if commit:
                    profile.user.save()
                    profile.save()
        else:
            # For regular users, store changes as pending
            # Update non-sensitive fields directly
            if profile.user:
                profile.user.first_name = self.cleaned_data["first_name"]
                profile.user.last_name = self.cleaned_data["last_name"]

                # Store sensitive fields as pending
                if email != profile.user.email:
                    profile.pending_email = email

                if phone_number != profile.phone_number:
                    profile.pending_phone_number = phone_number

                if address != profile.address:
                    profile.pending_address = address

                # Set pending changes date if any pending changes exist
                if (profile.pending_email or profile.pending_phone_number or
                    profile.pending_address):
                    profile.pending_changes_date = timezone.now()

                if commit:
                    profile.user.save()
                    profile.save()

                    # Create notification for admin if there are pending changes
                    if (profile.pending_email or profile.pending_phone_number or
                        profile.pending_address):
                        from .models import UserNotification
                        # Notify all admin users
                        for admin in User.objects.filter(is_staff=True):
                            UserNotification.objects.create(
                                user=admin,
                                title="Profile Update Request",
                                message=f"User {profile.user.username} has requested profile changes that require approval.",
                                notification_type="INFO"
                            )

        return profile