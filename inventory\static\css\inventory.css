/* Professional Inventory Management System - Core Styles */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-color: #dee2e6;
    --text-color: #495057;
    --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --border-radius: 6px;
    --mobile-padding: 12px;
}

/* Base Container */
.inventory-table {
    box-shadow: var(--shadow);
    border-radius: var(--border-radius);
    overflow: hidden;
    width: 100%;
    background-color: #fff;
    margin-bottom: 1.5rem;
    position: relative; /* For positioning context */
}

/* Table Core */
.table {
    width: 100%;
    margin-bottom: 0;
    border-collapse: separate;
    border-spacing: 0;
}

    .table thead th {
        background-color: var(--primary-color);
        color: white;
        padding: 14px 16px;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        border: none;
        position: sticky;
        top: 0;
        z-index: 10;
    }

        .table thead th:first-child {
            border-top-left-radius: var(--border-radius);
        }

        .table thead th:last-child {
            border-top-right-radius: var(--border-radius);
        }

    .table tbody tr {
        transition: var(--transition);
    }

        .table tbody tr:hover {
            background-color: rgba(52, 152, 219, 0.05);
        }

        .table tbody tr:nth-child(even) {
            background-color: rgba(0, 0, 0, 0.02);
        }

    .table tbody td {
        padding: 12px 16px;
        vertical-align: middle;
        border-top: 1px solid var(--border-color);
        color: var(--text-color);
        font-size: 0.95rem;
    }

/* Status Badges */
.badge {
    padding: 6px 12px;
    font-weight: 500;
    border-radius: 30px;
    display: inline-block;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    transition: var(--transition);
}

.badge-success {
    background-color: rgba(46, 204, 113, 0.15);
    color: var(--success-color);
    border: 1px solid rgba(46, 204, 113, 0.3);
}

.badge-warning {
    background-color: rgba(243, 156, 18, 0.15);
    color: var(--warning-color);
    border: 1px solid rgba(243, 156, 18, 0.3);
}

.badge-danger {
    background-color: rgba(231, 76, 60, 0.15);
    color: var(--danger-color);
    border: 1px solid rgba(231, 76, 60, 0.3);
}

/* Modal Styling */
.modal-content {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    max-height: 100vh; /* Prevent overflow on mobile */
    overflow-y: auto;
}

.modal-header {
    background-color: var(--primary-color);
    color: white;
    border-bottom: none;
    padding: 1.25rem 1.5rem;
    border-top-left-radius: var(--border-radius);
    border-top-right-radius: var(--border-radius);
    position: relative; /* For positioning close button */
}

.modal-title {
    font-weight: 600;
    font-size: 1.25rem;
    margin-right: 20px; /* Space for close button */
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    padding: 1.25rem 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: flex-end;
}

.btn-close {
    color: white;
    opacity: 0.8;
    position: absolute;
    right: 1.25rem;
    top: 50%;
    transform: translateY(-50%);
}

    .btn-close:hover {
        opacity: 1;
    }

/* Pagination Controls */
.pagination {
    display: flex;
    justify-content: center;
    margin: 1.5rem 0;
    flex-wrap: wrap;
    gap: 5px;
}

.page-item {
    margin: 0 2px;
}

.page-link {
    border-radius: var(--border-radius);
    color: var(--primary-color);
    border: 1px solid var(--border-color);
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    transition: var(--transition);
}

    .page-link:hover {
        background-color: var(--primary-color);
        color: white;
        border-color: var(--primary-color);
    }

.page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.page-item.disabled .page-link {
    color: #adb5bd;
    pointer-events: none;
}

/* Buttons */
.btn {
    border-radius: var(--border-radius);
    font-weight: 500;
    padding: 8px 16px;
    transition: var(--transition);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

/* Responsive Design */
@media screen and (max-width: 992px) {
    .inventory-table {
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
}

@media screen and (max-width: 768px) {
    .container, .container-fluid {
        padding-left: var(--mobile-padding);
        padding-right: var(--mobile-padding);
    }

    .table {
        display: block;
    }

        .table thead {
            display: none;
        }

        .table tbody tr {
            display: block;
            margin-bottom: 1rem;
            border: 1px solid var(--border-color);
            border-radius: var(--border-radius);
            background-color: white;
            box-shadow: var(--shadow);
            position: relative; /* For action buttons positioning */
        }

        .table tbody td {
            display: flex;
            justify-content: space-between;
            align-items: center;
            text-align: right;
            padding: 12px 16px;
            border-top: none;
            border-bottom: 1px solid var(--border-color);
            flex-wrap: wrap; /* Allow wrapping for small screens */
            word-break: break-word; /* Handle long content */
        }

            .table tbody td:last-child {
                border-bottom: none;
            }

            .table tbody td::before {
                content: attr(data-label);
                font-weight: 600;
                margin-right: 1rem;
                text-align: left;
                color: var(--primary-color);
                flex-basis: 40%;
                flex-shrink: 0;
            }

    /* Action buttons on mobile */
    .td-actions {
        justify-content: flex-start !important;
        margin-top: 8px;
        width: 100%;
    }

    .badge {
        padding: 6px 10px;
    }

    .modal-dialog {
        margin: 0.5rem auto;
        max-width: calc(100% - 1rem);
        height: auto;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        max-height: 90vh;
    }

    .modal-content {
        max-height: 90vh;
    }

    /* Fixed position modals for mobile */
    .modal {
        padding: 0 !important;
    }

    /* Improve form elements on mobile */
    input, select, textarea {
        font-size: 16px !important; /* Prevent zoom on iOS */
    }

    /* Touch-friendly form controls */
    .form-control {
        height: 44px;
        padding: 10px 12px;
    }

    .form-check-input {
        width: 22px;
        height: 22px;
        margin-top: 2px;
    }
}

@media screen and (max-width: 576px) {
    /* Even smaller screens */
    :root {
        --mobile-padding: 10px;
    }

    .inventory-table {
        margin-bottom: 1rem;
    }

    .page-link {
        min-width: 36px;
        height: 36px;
        font-size: 0.9rem;
    }

    .badge {
        font-size: 0.75rem;
        padding: 5px 8px;
    }

    .table tbody td {
        flex-direction: column;
        align-items: flex-start;
        padding: 10px 12px;
    }

        .table tbody td::before {
            margin-bottom: 0.5rem;
            margin-right: 0;
            width: 100%;
        }

    .modal-header,
    .modal-footer,
    .modal-body {
        padding: 1rem var(--mobile-padding);
    }

    /* Fixed position sticky header for mobile */
    .table-fixed-header {
        position: sticky;
        top: 0;
        z-index: 100;
        background-color: var(--light-color);
        padding: 10px var(--mobile-padding);
        border-bottom: 1px solid var(--border-color);
    }

    /* Full width buttons on small screens */
    .btn-block-xs {
        display: block;
        width: 100%;
        margin-bottom: 0.5rem;
    }

    /* Better touch targets */
    .btn {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    /* Adjust spacing for small screens */
    .row {
        margin-left: -10px;
        margin-right: -10px;
    }

    .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
    .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
    .col-sm, .col-md, .col-lg, .col-xl {
        padding-left: 10px;
        padding-right: 10px;
    }

    /* Fix for iOS position:fixed issues */
    .modal {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        -webkit-overflow-scrolling: touch;
    }

    /* Fix for iOS safe areas */
    .modal-dialog {
        margin-top: env(safe-area-inset-top, 0.5rem);
        margin-bottom: env(safe-area-inset-bottom, 0.5rem);
        max-height: calc(100vh - env(safe-area-inset-top, 0) - env(safe-area-inset-bottom, 0) - 1rem);
    }
}

/* Small phone screens */
@media screen and (max-width: 375px) {
    .badge {
        display: inline-block;
        width: 100%;
        text-align: center;
        margin-bottom: 5px;
    }

    .page-link {
        min-width: 32px;
        height: 32px;
        font-size: 0.8rem;
    }

    .pagination .page-item:not(.active):not(:first-child):not(:last-child) {
        display: none;
    }
}

/* Print Optimization */
@media print {
    .inventory-table {
        box-shadow: none;
    }

    .table thead th {
        background-color: #f8f9fa !important;
        color: black !important;
        border-bottom: 2px solid #dee2e6;
    }

    .badge {
        border: 1px solid #000;
        background: none !important;
        color: black !important;
    }

    .table tbody tr:nth-child(even) {
        background-color: #f8f9fa;
    }

    .pagination,
    .modal,
    .btn {
        display: none !important;
    }
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
    }
}

/* Browser Compatibility Fixes */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    box-sizing: border-box;
}

/* Fix for mobile browsers that hide the address bar */
html, body {
    height: 100%;
    overflow-x: hidden;
}

/* Fix for position:fixed issues on mobile browsers */
.modal-open {
    position: fixed;
    width: 100%;
}
