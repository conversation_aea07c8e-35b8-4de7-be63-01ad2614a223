from django.core.management.base import BaseCommand
from django.db.models import Max, Q
from inventory.models import Transaction
from user_management.models import Customer
from django.db import IntegrityError


class Command(BaseCommand):
    help = 'Import customers from existing transactions into the Customer model'

    def handle(self, *args, **options):
        # Get unique combinations of customer name and patient number from transactions
        unique_customers = Transaction.objects.values('customer_name', 'patient_number', 'customer_type').distinct()

        # Track statistics
        total_imported = 0
        already_exists = 0
        errors = 0

        # Process each unique customer
        for customer_data in unique_customers:
            customer_name = customer_data['customer_name']
            patient_number = customer_data['patient_number']
            customer_type = customer_data['customer_type']

            if not customer_name:
                continue

            # Check if this customer already exists by name
            customer_exists = Customer.objects.filter(
                customer_name=customer_name
            ).exists()

            if customer_exists:
                already_exists += 1
                continue

            # Get the latest transaction date for this customer
            latest_transaction = Transaction.objects.filter(
                customer_name=customer_name
            ).aggregate(latest=Max('transaction_date'))

            try:
                # Create the customer record
                Customer.objects.create(
                    customer_name=customer_name,
                    patient_number=patient_number,
                    customer_type=customer_type,
                    last_transaction_date=latest_transaction['latest']
                )
                total_imported += 1

            except IntegrityError as e:
                # Handle duplicate patient numbers by making them unique
                if 'patient_number' in str(e):
                    try:
                        # Try creating with a modified patient number
                        if patient_number:
                            modified_number = f"{patient_number}-{total_imported}"
                        else:
                            modified_number = None

                        Customer.objects.create(
                            customer_name=customer_name,
                            patient_number=modified_number,
                            customer_type=customer_type,
                            last_transaction_date=latest_transaction['latest']
                        )
                        total_imported += 1
                        self.stdout.write(self.style.WARNING(
                            f"Modified patient number for {customer_name} to avoid duplication"
                        ))
                    except Exception as inner_e:
                        self.stdout.write(self.style.ERROR(
                            f"Failed to import customer {customer_name}: {str(inner_e)}"
                        ))
                        errors += 1
                else:
                    self.stdout.write(self.style.ERROR(
                        f"Failed to import customer {customer_name}: {str(e)}"
                    ))
                    errors += 1

        self.stdout.write(
            self.style.SUCCESS(
                f'Successfully imported {total_imported} customers from transactions. '
                f'{already_exists} customers already existed. '
                f'{errors} errors encountered.'
            )
        )
