// Forecast Detail JavaScript
// Enhances the medicine forecast detail page with interactive charts and data visualizations

document.addEventListener('DOMContentLoaded', function () {
    // Initialize primary charts
    initializeDemandChart();
    initializeQuarterlyChart();
    initializeStockLevelChart();
    // Initialize forecast accuracy gauge
    initializeAccuracyGauge();
    initializeTooltips();
});

/**
 * Initializes the primary demand and forecast comparison chart
 */
function initializeDemandChart() {
    const ctx = document.getElementById('demandChart');

    // If the canvas doesn't exist in the DOM, return early
    if (!ctx) return;

    // Extract data from the page
    const medicineName = document.querySelector('.card-body strong')?.textContent || 'Medicine';
    const predictedQuantity = parseFloat(document.querySelectorAll('.card-body table tr')[1]?.querySelectorAll('td strong')[0]?.textContent || 0);
    const actualDemand = parseFloat(document.querySelectorAll('.p-3.bg-light h4')[1]?.textContent || 0);

    // Monthly demand data (extract from table if available)
    let monthlyData = [];
    const monthlyTable = document.querySelector('.table.table-sm.table-bordered');
    if (monthlyTable) {
        const rows = monthlyTable.querySelectorAll('tbody tr');
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            if (cells.length >= 2) {
                const month = cells[0].textContent;
                const quantity = parseFloat(cells[1].textContent);
                if (!isNaN(quantity)) {
                    monthlyData.push({ month, quantity });
                }
            }
        });
    }

    // Default data if monthly data couldn't be extracted
    if (monthlyData.length === 0) {
        const currentDate = new Date();
        for (let i = 5; i >= 0; i--) {
            const date = new Date(currentDate);
            date.setMonth(currentDate.getMonth() - i);
            const month = date.toLocaleString('default', { month: 'short' });
            monthlyData.push({
                month: month,
                quantity: Math.floor(Math.random() * 50) + 50
            });
        }
    }

    // Create chart
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: monthlyData.map(item => item.month),
            datasets: [{
                label: 'Monthly Demand',
                data: monthlyData.map(item => item.quantity),
                borderColor: '#3498db',
                backgroundColor: 'rgba(52, 152, 219, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3498db',
                pointRadius: 4,
                pointHoverRadius: 6
            }, {
                label: 'Forecast',
                data: monthlyData.map(() => predictedQuantity),
                borderColor: '#e74c3c',
                backgroundColor: 'transparent',
                borderWidth: 2,
                borderDash: [5, 5],
                pointStyle: 'circle',
                pointRadius: 0,
                pointHoverRadius: 4,
                pointBackgroundColor: '#e74c3c'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Demand Trend vs Forecast',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false,
                    callbacks: {
                        label: function (context) {
                            let label = context.dataset.label || '';
                            if (label) {
                                label += ': ';
                            }
                            if (context.parsed.y !== null) {
                                label += context.parsed.y.toFixed(1);
                            }
                            return label;
                        }
                    }
                },
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Month'
                    }
                }
            }
        }
    });
}

/**
 * Initializes the quarterly analysis chart
 */
function initializeQuarterlyChart() {
    const ctx = document.getElementById('quarterlyChart');

    // If the canvas doesn't exist in the DOM, return early
    if (!ctx) return;

    // Try to get data from the hidden script tag
    let quarterlyData;
    try {
        const dataScript = document.getElementById('quarterly-data');
        if (dataScript) {
            quarterlyData = JSON.parse(dataScript.textContent);
        }
    } catch (e) {
        console.error('Error parsing quarterly data:', e);
    }

    // If we couldn't get the data, extract from the table or use placeholder data
    if (!quarterlyData) {
        quarterlyData = {
            quarters: ['Q1', 'Q2', 'Q3', 'Q4'],
            averages: [65, 59, 80, 81],
            stdDevs: [5, 4, 7, 6]
        };

        // Try to extract from table
        const quarterlyTable = document.querySelector('.table.table-hover');
        if (quarterlyTable) {
            const rows = quarterlyTable.querySelectorAll('tbody tr');
            if (rows.length > 0) {
                quarterlyData.quarters = [];
                quarterlyData.averages = [];
                quarterlyData.stdDevs = [];

                rows.forEach(row => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 3) {
                        const quarter = cells[0].textContent.trim();
                        const average = parseFloat(cells[1].textContent);
                        const stdDev = parseFloat(cells[2].textContent);

                        if (!isNaN(average)) {
                            quarterlyData.quarters.push(quarter);
                            quarterlyData.averages.push(average);
                            quarterlyData.stdDevs.push(isNaN(stdDev) ? 0 : stdDev);
                        }
                    }
                });
            }
        }
    }

    // Create the chart
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: quarterlyData.quarters,
            datasets: [{
                label: 'Average Demand',
                data: quarterlyData.averages,
                backgroundColor: 'rgba(52, 152, 219, 0.7)',
                borderColor: '#3498db',
                borderWidth: 1,
                borderRadius: 5,
                yAxisID: 'y'
            }, {
                label: 'Standard Deviation',
                data: quarterlyData.stdDevs,
                type: 'line',
                backgroundColor: 'transparent',
                borderColor: '#e74c3c',
                borderWidth: 2,
                pointBackgroundColor: '#e74c3c',
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'Quarterly Demand Patterns',
                    font: {
                        size: 16,
                        weight: 'bold'
                    }
                },
                tooltip: {
                    mode: 'index',
                    intersect: false
                },
                legend: {
                    position: 'top',
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Average Demand'
                    }
                },
                y1: {
                    beginAtZero: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false
                    },
                    title: {
                        display: true,
                        text: 'Standard Deviation'
                    }
                }
            }
        }
    });
}

/**
 * Initializes the stock level chart
 */
function initializeStockLevelChart() {
    const ctx = document.getElementById('stockLevelChart');

    // If the canvas doesn't exist in the DOM, return early
    if (!ctx) return;

    // Try to get the stock data from the hidden elements or data attributes
    let currentStock, reorderLevel;

    console.log('Initializing stock level chart');

    // First try to get from data attributes if they exist
    if (ctx.hasAttribute('data-current-stock') && ctx.hasAttribute('data-reorder-level')) {
        currentStock = parseFloat(ctx.getAttribute('data-current-stock'));
        reorderLevel = parseFloat(ctx.getAttribute('data-reorder-level'));
        console.log('Got stock data from data attributes:', currentStock, reorderLevel);
    } else {
        // Otherwise try to find the values in the DOM
        try {
            // Look for the values in the new layout
            const stockElements = document.querySelectorAll('.stock-level-analysis h4');
            console.log('Found stock elements:', stockElements.length);

            if (stockElements.length >= 2) {
                // Extract numbers from text content
                currentStock = parseFloat(stockElements[0].textContent.match(/\d+(\.\d+)?/)[0]);
                reorderLevel = parseFloat(stockElements[1].textContent.match(/\d+(\.\d+)?/)[0]);
                console.log('Got stock data from DOM elements:', currentStock, reorderLevel);
            } else {
                // Try to get from the global variables if they were defined in the template
                console.log('Window stock variables:', window.currentStock, window.reorderLevel);
                currentStock = window.currentStock !== undefined ? window.currentStock : 0;
                reorderLevel = window.reorderLevel !== undefined ? window.reorderLevel : 0;
                console.log('Got stock data from global variables:', currentStock, reorderLevel);
            }
        } catch (e) {
            console.error('Error extracting stock data:', e);
            // Default values if extraction fails
            currentStock = 0;
            reorderLevel = 0;
        }
    }

    // Ensure we have valid numbers
    currentStock = isNaN(currentStock) ? 0 : currentStock;
    reorderLevel = isNaN(reorderLevel) ? 0 : reorderLevel;

    console.log('Final stock data:', currentStock, reorderLevel);

    // Create the chart
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Current Stock', 'Reorder Level'],
            datasets: [{
                label: 'Quantity',
                data: [currentStock, reorderLevel],
                backgroundColor: [
                    currentStock > reorderLevel ? 'rgba(39, 174, 96, 0.7)' : 'rgba(231, 76, 60, 0.7)',
                    'rgba(243, 156, 18, 0.7)'
                ],
                borderColor: [
                    currentStock > reorderLevel ? '#27ae60' : '#e74c3c',
                    '#f39c12'
                ],
                borderWidth: 1,
                borderRadius: 5
            }]
        },
        options: {
            indexAxis: 'y',
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: false,
                    text: 'Stock Level vs. Reorder Point',
                    font: {
                        size: 14,
                        weight: 'bold'
                    }
                },
                tooltip: {
                    callbacks: {
                        label: function (context) {
                            return `Quantity: ${context.raw}`;
                        }
                    }
                },
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Quantity'
                    }
                }
            }
        }
    });
}

/**
 * Initializes the forecast accuracy gauge
 */
function initializeAccuracyGauge() {
    const ctx = document.getElementById('accuracyGauge');

    // If the canvas doesn't exist in the DOM, return early
    if (!ctx) return;

    // Get the accuracy value from the page or use a default
    let accuracyValue = 85;

    // Try to get the accuracy from a data attribute if it exists
    if (ctx.hasAttribute('data-accuracy')) {
        accuracyValue = parseFloat(ctx.getAttribute('data-accuracy'));
    } else {
        // Try to find the accuracy value in the DOM
        try {
            const accuracyElement = document.querySelector('.forecast-accuracy-value');
            if (accuracyElement) {
                // Extract number from text content
                const match = accuracyElement.textContent.match(/\d+(\.\d+)?/);
                if (match) {
                    accuracyValue = parseFloat(match[0]);
                }
            }
        } catch (e) {
            console.error('Error extracting accuracy value:', e);
        }
    }

    // Ensure we have a valid number between 0 and 100
    accuracyValue = isNaN(accuracyValue) ? 85 : Math.min(100, Math.max(0, accuracyValue));

    // Determine color based on accuracy value
    let color;
    if (accuracyValue < 70) {
        color = '#e74c3c'; // Red for poor accuracy
    } else if (accuracyValue < 85) {
        color = '#f39c12'; // Orange/yellow for moderate accuracy
    } else {
        color = '#27ae60'; // Green for good accuracy
    }

    // Create the gauge chart
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [accuracyValue, 100 - accuracyValue],
                backgroundColor: [
                    color,
                    'rgba(200, 200, 200, 0.2)'
                ],
                borderWidth: 0,
                circumference: 180,
                rotation: 270
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '80%',
            plugins: {
                tooltip: {
                    enabled: false
                },
                legend: {
                    display: false
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true
            }
        },
        plugins: [{
            id: 'accuracyText',
            afterDraw: function(chart) {
                const width = chart.width;
                const height = chart.height;
                const ctx = chart.ctx;

                ctx.restore();
                ctx.font = 'bold 24px Arial';
                ctx.textBaseline = 'middle';
                ctx.textAlign = 'center';
                ctx.fillStyle = color;

                // Draw the accuracy value
                ctx.fillText(accuracyValue.toFixed(1) + '%', width / 2, height - 30);

                // Draw the label
                ctx.font = '14px Arial';
                ctx.fillStyle = '#666';
                ctx.fillText('Accuracy', width / 2, height - 10);

                ctx.save();
            }
        }]
    });
}

/**
 * Initializes Bootstrap tooltips throughout the page
 */
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * Utility function to format numbers with commas
 */
function formatNumber(num) {
    return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,');
}

/**
 * Utility function to get a color based on a value compared to a threshold
 */
function getStatusColor(value, threshold, invert = false) {
    if (invert) {
        return value > threshold ? '#e74c3c' : '#27ae60';
    } else {
        return value > threshold ? '#27ae60' : '#e74c3c';
    }
}
