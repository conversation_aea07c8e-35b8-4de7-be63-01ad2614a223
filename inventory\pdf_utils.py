from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, landscape
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.pdfgen import canvas
from io import BytesIO
from django.utils import timezone
from django.http import HttpResponse
import logging
import os
from django.conf import settings

logger = logging.getLogger(__name__)

# Define professional color scheme (RGB values)
COLORS = {
    'primary': colors.Color(0.17, 0.24, 0.31),  # Dark blue for headers
    'secondary': colors.Color(0.20, 0.60, 0.86),  # Light blue for alternating rows
    'accent': colors.Color(0.15, 0.68, 0.38),  # Green for positive values
    'warning': colors.Color(0.95, 0.61, 0.07),  # Orange for warnings
    'danger': colors.Color(0.90, 0.30, 0.24),  # Red for critical/negative values
    'light': colors.Color(0.93, 0.94, 0.95),  # Light gray for alternating rows
    'dark': colors.Color(0.20, 0.29, 0.37),  # Dark blue-gray for footers
    'white': colors.white,  # White for text on dark backgrounds
    'black': colors.black,  # Black for normal text
}

def create_pdf_styles():
    """Create and return a dictionary of custom styles for PDF reports"""
    styles = getSampleStyleSheet()

    # Title style
    title_style = ParagraphStyle(
        name='ReportTitle',
        parent=styles['Heading1'],
        fontSize=16,
        textColor=COLORS['primary'],
        alignment=1,  # Center alignment
        spaceAfter=12
    )

    # Heading styles
    heading1_style = ParagraphStyle(
        name='Heading1',
        parent=styles['Heading1'],
        fontSize=14,
        textColor=COLORS['primary'],
        spaceAfter=10
    )

    heading2_style = ParagraphStyle(
        name='Heading2',
        parent=styles['Heading2'],
        fontSize=12,
        textColor=COLORS['primary'],
        spaceAfter=8
    )

    # Normal text style
    normal_style = ParagraphStyle(
        name='Normal',
        parent=styles['Normal'],
        fontSize=10,
        spaceAfter=6
    )

    # Metadata style
    metadata_style = ParagraphStyle(
        name='Metadata',
        parent=styles['Normal'],
        fontSize=9,
        textColor=COLORS['dark'],
        spaceAfter=2
    )

    # Footer style
    footer_style = ParagraphStyle(
        name='Footer',
        parent=styles['Normal'],
        fontSize=8,
        textColor=COLORS['dark'],
        alignment=1  # Center alignment
    )

    # Return all styles in a dictionary
    return {
        'title': title_style,
        'heading1': heading1_style,
        'heading2': heading2_style,
        'normal': normal_style,
        'metadata': metadata_style,
        'footer': footer_style,
        'base': styles
    }

def add_page_number(canvas, doc):
    """Add page number to each page"""
    canvas.saveState()
    canvas.setFont('Helvetica', 8)
    canvas.setFillColor(COLORS['dark'])

    # Add page number
    page_num = canvas.getPageNumber()
    text = f"Page {page_num}"
    canvas.drawRightString(
        doc.pagesize[0] - 0.5*inch,
        0.5*inch,
        text
    )

    # Add footer with timestamp
    timestamp = timezone.now().strftime('%Y-%m-%d %H:%M:%S')
    canvas.drawCentredString(
        doc.pagesize[0]/2,
        0.5*inch,
        f"Generated on {timestamp}"
    )

    # Add logo if available
    logo_path = os.path.join(settings.STATIC_ROOT, 'img', 'logo.png')
    if os.path.exists(logo_path):
        canvas.drawImage(
            logo_path,
            0.5*inch,
            doc.pagesize[1] - 0.75*inch,
            width=1*inch,
            height=0.5*inch,
            preserveAspectRatio=True
        )

    canvas.restoreState()

def create_pdf_table(data, colWidths=None, style=None):
    """Create a professionally styled table for PDF reports

    Args:
        data: List of lists containing table data (first row is headers)
        colWidths: List of column widths or None for auto
        style: Additional TableStyle commands to apply

    Returns:
        Table object ready to add to PDF
    """
    # Create the table
    table = Table(data, colWidths=colWidths)

    # Define base style
    base_style = [
        # Header row styling
        ('BACKGROUND', (0, 0), (-1, 0), COLORS['primary']),
        ('TEXTCOLOR', (0, 0), (-1, 0), COLORS['white']),
        ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
        ('ALIGNMENT', (0, 0), (-1, 0), 'CENTER'),
        ('BOTTOMPADDING', (0, 0), (-1, 0), 8),

        # Table borders
        ('GRID', (0, 0), (-1, -1), 0.5, COLORS['light']),
        ('BOX', (0, 0), (-1, -1), 1, COLORS['primary']),

        # Alternate row colors
        ('ROWBACKGROUNDS', (0, 1), (-1, -1), [COLORS['white'], COLORS['light']]),

        # Align numeric columns to the right
        ('ALIGNMENT', (1, 1), (-1, -1), 'RIGHT'),

        # Padding
        ('TOPPADDING', (0, 0), (-1, -1), 6),
        ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ('LEFTPADDING', (0, 0), (-1, -1), 6),
        ('RIGHTPADDING', (0, 0), (-1, -1), 6),
    ]

    # Add custom style if provided
    if style:
        base_style.extend(style)

    # Apply the style
    table.setStyle(TableStyle(base_style))

    return table

def create_pdf_response(elements, title, filename, pagesize=letter, landscape_mode=False):
    """Create an HTTP response with a PDF file

    Args:
        elements: List of flowable elements to add to the PDF
        title: The document title
        filename: The filename for the downloaded file
        pagesize: The page size (default: letter)
        landscape_mode: Whether to use landscape orientation

    Returns:
        HttpResponse with the PDF file
    """
    try:
        # Create buffer and PDF document
        buffer = BytesIO()

        # Set page size based on orientation
        if landscape_mode:
            doc_pagesize = landscape(pagesize)
        else:
            doc_pagesize = pagesize

        # Create the PDF document
        doc = SimpleDocTemplate(
            buffer,
            pagesize=doc_pagesize,
            title=title,
            author="BMC MedForecast System",
            leftMargin=0.5*inch,
            rightMargin=0.5*inch,
            topMargin=0.75*inch,
            bottomMargin=0.75*inch
        )

        # Build the PDF
        doc.build(
            elements,
            onFirstPage=lambda canvas, doc: add_page_number(canvas, doc),
            onLaterPages=lambda canvas, doc: add_page_number(canvas, doc)
        )

        # Get the value from the buffer
        pdf_value = buffer.getvalue()
        buffer.close()

        # Create the HTTP response with the correct MIME type for PDF files
        response = HttpResponse(content_type='application/pdf')

        # Generate a timestamp for the filename
        timestamp = timezone.now().strftime('%Y%m%d_%H%M%S')

        # Set the Content-Disposition header with the correct filename and extension
        pdf_filename = f"{filename}_{timestamp}.pdf"
        response['Content-Disposition'] = f'attachment; filename="{pdf_filename}"'

        # Write the PDF content to the response
        response.write(pdf_value)

        return response

    except Exception as e:
        logger.error(f"Error creating PDF response: {str(e)}")
        raise
