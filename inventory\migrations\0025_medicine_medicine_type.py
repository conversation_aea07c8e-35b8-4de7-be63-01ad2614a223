# Generated by Django 4.2.9 on 2025-04-13 09:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0024_emailnotificationsetting'),
    ]

    operations = [
        migrations.AddField(
            model_name='medicine',
            name='medicine_type',
            field=models.CharField(choices=[('tablet', 'Tablet'), ('capsule', 'Capsule'), ('syrup', 'Syrup'), ('injection', 'Injection'), ('cream', 'Cream/Ointment'), ('drops', 'Drops'), ('inhaler', 'Inhaler'), ('powder', 'Powder'), ('suppository', 'Suppository'), ('patch', 'Patch'), ('other', 'Other')], default='tablet', help_text='Type of medicine formulation', max_length=20),
        ),
    ]
