from django.apps import AppConfig
import os
from django.conf import settings

class UserManagementConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'user_management'

    def ready(self):
        # Import signals
        import user_management.signals

        # Create media directories if they don't exist
        profile_pics_path = os.path.join(settings.MEDIA_ROOT, 'profile_pics')
        os.makedirs(profile_pics_path, exist_ok=True)