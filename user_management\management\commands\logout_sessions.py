from django.core.management.base import BaseCommand
from django.contrib.sessions.models import Session
from django.contrib.auth.models import User
from django.utils import timezone

class Command(BaseCommand):
    help = 'Logout users by clearing sessions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--all',
            action='store_true',
            help='Logout all users',
        )
        parser.add_argument(
            '--username',
            type=str,
            help='Logout specific user by username',
        )
        parser.add_argument(
            '--expired',
            action='store_true',
            help='Clear only expired sessions',
        )

    def handle(self, *args, **options):
        if options['all']:
            # Delete all sessions
            count, _ = Session.objects.all().delete()
            self.stdout.write(
                self.style.SUCCESS(f'Successfully deleted all {count} sessions')
            )
            return

        if options['username']:
            try:
                # Get user and delete their sessions
                user = User.objects.get(username=options['username'])
                count, _ = Session.objects.filter(
                    session_data__contains=str(user.id)
                ).delete()
                self.stdout.write(
                    self.style.SUCCESS(
                        f'Successfully deleted {count} sessions for user "{user.username}"'
                    )
                )
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(f'User "{options["username"]}" not found')
                )
            return

        if options['expired']:
            # Delete expired sessions
            count, _ = Session.objects.filter(expire_date__lt=timezone.now()).delete()
            self.stdout.write(
                self.style.SUCCESS(f'Successfully deleted {count} expired sessions')
            )
            return

        # If no options specified, show help
        self.stdout.write(self.style.WARNING('Please specify one of the following options:'))
        self.stdout.write('  --all: Logout all users')
        self.stdout.write('  --username <username>: Logout specific user')
        self.stdout.write('  --expired: Clear expired sessions')