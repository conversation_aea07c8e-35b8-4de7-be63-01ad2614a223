﻿import pandas as pd
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import (
    mean_absolute_error,
    mean_absolute_percentage_error,
   root_mean_squared_error,
    mean_squared_error
    # Import the new function
)
from django.db.models import Avg, Sum, Count
from django.utils import timezone
from .models import Transaction, Forecast, Medicine
import numpy as np
from sklearn.model_selection import TimeSeriesSplit
from concurrent.futures import ThreadPoolExecutor
from datetime import timedelta


def generate_forecast(medicine_pk):
    medicine = Medicine.objects.get(pk=medicine_pk)
    current_time = timezone.localtime()
    transactions = Transaction.objects.filter(
        medicine=medicine,
         transaction_date__gte=current_time - timezone.timedelta(days=30)
    )

    if transactions.count() < 2:
        average_quantity = transactions.aggregate(Avg('quantity'))['quantity__avg'] or 0
        return average_quantity, None  # Return None for MSE if not enough data

    df = pd.DataFrame(list(transactions.values()))
    df['transaction_date'] = pd.to_datetime(df['transaction_date'])
    df.set_index('transaction_date', inplace=True)

    # Localize the index to match Django's timezone
    df.index = df.index.tz_convert(timezone.get_current_timezone())
    # Handle duplicate dates by aggregating quantities
    df = df.groupby(df.index).sum()

    # Ensure the index has a frequency
    df = df.asfreq('D', method='ffill')

    df['days_since_start'] = (df.index - df.index.min()).days

    X = df[['days_since_start']]
    y = df['quantity']

    if len(X) < 2:  # Prevent errors when splitting data
        return y.mean(), None

    # Dynamically set number of splits based on data size
    n_splits = min(5, len(X) - 1)  # Ensure at least 1 sample per fold
    tscv = TimeSeriesSplit(n_splits=n_splits)
    model = RandomForestRegressor()
    maes, rmses, mapes = [], [], []

    try:
        for train_index, test_index in tscv.split(X):
            if len(train_index) == 0 or len(test_index) == 0:
                continue

            X_train, X_test = X.iloc[train_index], X.iloc[test_index]
            y_train, y_test = y.iloc[train_index], y.iloc[test_index]

            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)

            maes.append(mean_absolute_error(y_test, y_pred))
            rmses.append(root_mean_squared_error(y_test, y_pred))  # Use new function
            mapes.append(mean_absolute_percentage_error(y_test, y_pred))

        # Only calculate metrics if we have successful splits
        if maes:
            mae = np.mean(maes)
            rmse = np.mean(rmses)
            mape = np.mean(mapes)
        else:
            # Fallback to simple train-test split if cross-validation fails
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, shuffle=False)
            model.fit(X_train, y_train)
            y_pred = model.predict(X_test)
            mae = mean_absolute_error(y_test, y_pred)
            rmse = root_mean_squared_error(y_test, y_pred)  # Use new function
            mape = mean_absolute_percentage_error(y_test, y_pred)

        # Fit final model on all data for prediction
        model.fit(X, y)
        next_period = pd.DataFrame({'days_since_start': [df['days_since_start'].max() + 1]})
        predicted_quantity = model.predict(next_period)[0]

        return predicted_quantity, mae, rmse, mape

    except Exception as e:
        logger.error(f"Error in generate_forecast: {str(e)}")
        # Fallback to simple average if all else fails
        return float(y.mean()), None, None, None





import logging

logger = logging.getLogger(__name__)

def batch_generate_forecasts(medicine_pks):
    results = []
    with ThreadPoolExecutor() as executor:
        futures = [executor.submit(generate_forecast, pk) for pk in medicine_pks]
        for future in futures:
            results.append(future.result())
    return results

def train_model(X_train, y_train):
    model = RandomForestRegressor()
    model.fit(X_train, y_train)
    y_pred = model.predict(X_train)  # Use X_train to predict on the training set for simplicity
    mse = mean_squared_error(y_train, y_pred)
    logger.info(f'MSE: {mse}')  # This will log the MSE to the configured log destination
    return model, mse

def make_prediction(model, X_test):
    prediction = model.predict(X_test)
    return prediction[0]






def calculate_mae(medicines):
    """Calculate Mean Absolute Error across all medicines"""
    total_mae = 0
    count = 0
    for medicine in medicines:
        forecast = generate_forecast(medicine.pk)
        if isinstance(forecast, tuple) and len(forecast) >= 2 and forecast[1] is not None:
            total_mae += forecast[1]
            count += 1
    return round(total_mae / count if count > 0 else 0, 2)

def calculate_rmse(medicines):
    """Calculate Root Mean Square Error across all medicines"""
    total_rmse = 0
    count = 0
    for medicine in medicines:
        forecast = generate_forecast(medicine.pk)
        if isinstance(forecast, tuple) and len(forecast) >= 3 and forecast[2] is not None:
            total_rmse += forecast[2]
            count += 1
    return round(total_rmse / count if count > 0 else 0, 2)

def calculate_average_mape(medicines):
    """Calculate average Mean Absolute Percentage Error"""
    total_mape = 0
    count = 0
    for medicine in medicines:
        forecast = generate_forecast(medicine.pk)
        if isinstance(forecast, tuple) and len(forecast) >= 4 and forecast[3] is not None:
            total_mape += forecast[3]
            count += 1
    return round(total_mape / count if count > 0 else 0, 2)

def generate_forecast_for_month(month):
    """Generate forecast for a specific month"""
    current_year = timezone.now().year
    start_date = timezone.datetime(current_year, month, 1)
    end_date = (start_date + timezone.timedelta(days=32)).replace(day=1) - timezone.timedelta(days=1)

    medicines = Medicine.objects.all()
    total_forecast = 0

    for medicine in medicines:
        transactions = Transaction.objects.filter(
            medicine=medicine,
            transaction_date__range=(start_date, end_date)
        )
        if transactions.exists():
            forecast = generate_forecast(medicine.pk)
            if isinstance(forecast, tuple):
                total_forecast += forecast[0]

    return total_forecast






from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font
from tempfile import NamedTemporaryFile
from .models import Medicine


def export_forecast_report(medicine_id):
    """
    Generate and export forecast report for a specific medicine
    """
    try:
        medicine = Medicine.objects.get(id=medicine_id)
        forecasts = medicine.forecast_set.all().order_by('-created_at')

        # Create a new workbook and select the active sheet
        wb = Workbook()
        ws = wb.active
        ws.title = "Forecast Report"

        # Add headers
        headers = [
            'Medicine Name',
            'Category',
            'Current Stock',
            'Reorder Level',
            'Forecast Date',
            'Predicted Quantity',
            'MAE',
            'RMSE',
            'MAPE (%)'
        ]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        # Add data
        current_row = 2
        for forecast in forecasts:
            row_data = [
                medicine.name,
                medicine.category,
                medicine.quantity,
                medicine.reorder_level,
                forecast.created_at.strftime('%Y-%m-%d %H:%M'),
                forecast.predicted_quantity,
                round(forecast.mae, 2) if forecast.mae else None,
                round(forecast.rmse, 2) if forecast.rmse else None,
                round(forecast.mape, 2) if forecast.mape else None
            ]

            for col, value in enumerate(row_data, 1):
                ws.cell(row=current_row, column=col, value=value)
            current_row += 1

        # Style the worksheet
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 15

        header_font = Font(bold=True)
        for cell in ws[1]:
            cell.font = header_font

        # Create a temporary file
        with NamedTemporaryFile() as tmp:
            wb.save(tmp.name)
            tmp.seek(0)
            file_data = tmp.read()

        return file_data

    except Medicine.DoesNotExist:
        raise ValueError(f"Medicine with ID {medicine_id} not found")
    except Exception as e:
        raise Exception(f"Error generating forecast report: {str(e)}")





import openpyxl
from openpyxl.styles import Font, PatternFill, Alignment
from django.utils import timezone

def analyze_quarterly_patterns(medicine):
    """
    Analyze quarterly sales patterns for a medicine to detect seasonality
    """
    # Get transactions from the last 2 years
    start_date = timezone.now() - timedelta(days=730)  # 2 years
    transactions = Transaction.objects.filter(
        medicine=medicine,
        transaction_date__gte=start_date,
        transaction_type='sale'
    )

    if transactions.count() < 12:  # Need at least 12 transactions for meaningful analysis
        # Return default values if not enough data
        return {
            'Q1': {'avg_demand': 0, 'percentage': 25},
            'Q2': {'avg_demand': 0, 'percentage': 25},
            'Q3': {'avg_demand': 0, 'percentage': 25},
            'Q4': {'avg_demand': 0, 'percentage': 25}
        }

    # Convert to DataFrame for easier analysis
    df = pd.DataFrame(list(transactions.values('transaction_date', 'quantity')))
    df['transaction_date'] = pd.to_datetime(df['transaction_date'])

    # Extract quarter information
    df['quarter'] = df['transaction_date'].dt.quarter
    df['year'] = df['transaction_date'].dt.year

    # Group by quarter and calculate average demand
    quarterly_demand = df.groupby('quarter')['quantity'].mean().to_dict()

    # Calculate total demand
    total_demand = sum(quarterly_demand.values())

    # Calculate percentage for each quarter
    result = {}
    for quarter in range(1, 5):
        avg_demand = quarterly_demand.get(quarter, 0)
        percentage = (avg_demand / total_demand * 100) if total_demand > 0 else 25
        result[f'Q{quarter}'] = {
            'avg_demand': avg_demand,
            'percentage': percentage
        }

    return result


def generate_excel_report(critical_forecasts, components, start_date, end_date):
    """Generate Excel report for inventory data"""
    wb = openpyxl.Workbook()
    ws = wb.active
    ws.title = "Inventory Report"

    # Header styling
    header_font = Font(bold=True, color="FFFFFF")
    header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

    # Add report title
    ws['A1'] = f"Inventory Report ({start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')})"
    ws.merge_cells('A1:G1')
    ws['A1'].font = Font(bold=True, size=14)
    ws['A1'].alignment = Alignment(horizontal="center")

    # Headers
    headers = [
        "Medicine Name",
        "Current Stock",
        "Weekly Demand",
        "Monthly Demand",
        "Stock Coverage (Days)",
        "Recommended Order",
        "Trend"
    ]

    for col, header in enumerate(headers, 1):
        cell = ws.cell(row=3, column=col)
        cell.value = header
        cell.font = header_font
        cell.fill = header_fill
        cell.alignment = Alignment(horizontal="center")

    # Data rows
    row = 4
    for forecast in critical_forecasts:
        ws.cell(row=row, column=1).value = forecast['medicine'].name
        ws.cell(row=row, column=2).value = forecast['medicine'].quantity
        ws.cell(row=row, column=3).value = round(forecast['weekly_demand'], 2)
        ws.cell(row=row, column=4).value = round(forecast['monthly_demand'], 2)
        ws.cell(row=row, column=5).value = round(forecast['stock_coverage_days'], 1)
        ws.cell(row=row, column=6).value = forecast['recommended_order_quantity']
        ws.cell(row=row, column=7).value = forecast['trend']

        # Highlight critical items
        if forecast['stock_coverage_days'] < 7:  # Critical threshold
            for col in range(1, 8):
                cell = ws.cell(row=row, column=col)
                cell.fill = PatternFill(start_color="FFD9D9", end_color="FFD9D9", fill_type="solid")

        row += 1

    # Auto-adjust column widths
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = (max_length + 2)
        ws.column_dimensions[column].width = adjusted_width

    return wb.save('inventory_report.xlsx')


# Excel Report Styling Utilities
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from django.utils import timezone
from django.http import HttpResponse


def apply_professional_styling(ws, report_title, metadata, headers):
    """Apply professional styling to an Excel worksheet with title, metadata, and headers.

    Args:
        ws: The worksheet to style
        report_title: The title of the report
        metadata: Dictionary of metadata key-value pairs
        headers: List of column headers

    Returns:
        The row number where data should start being added
    """
    # Add and style title
    ws.append([report_title])
    ws.merge_cells(f'A1:{chr(64 + len(headers))}1')
    ws['A1'].font = Font(size=16, bold=True)
    ws['A1'].alignment = Alignment(horizontal='center')

    # Add metadata
    row = 2
    for key, value in metadata.items():
        ws.append([key, value])
        ws[f'A{row}'].font = Font(bold=True)
        row += 1

    # Add blank row
    ws.append([])
    row += 1

    # Add headers with styling
    header_fill = PatternFill(start_color="2c3e50", end_color="2c3e50", fill_type="solid")
    header_font = Font(color="FFFFFF", bold=True)

    # Add headers
    header_row = ws.row_dimensions[row]
    header_row.height = 20

    for col_num, header in enumerate(headers, 1):
        cell = ws.cell(row=row, column=col_num, value=header)
        cell.fill = header_fill
        cell.font = header_font
        cell.alignment = Alignment(horizontal='center', vertical='center')

    # Return the row where data should start
    return row + 1


def apply_row_styling(ws, row_num, data, row_fill=None, bold=False):
    """Apply styling to a row of data.

    Args:
        ws: The worksheet
        row_num: The row number to add data to
        data: List of data values for the row
        row_fill: Optional PatternFill to apply to the row
        bold: Whether to make the text bold

    Returns:
        The next row number (row_num + 1)
    """
    for col_num, value in enumerate(data, 1):
        cell = ws.cell(row=row_num, column=col_num, value=value)
        if row_fill:
            cell.fill = row_fill
        if bold:
            cell.font = Font(bold=True)

    return row_num + 1


def auto_adjust_columns(ws):
    """Auto-adjust column widths based on content.

    Args:
        ws: The worksheet to adjust
    """
    for col in ws.columns:
        max_length = 0
        column = col[0].column_letter
        for cell in col:
            if cell.value:
                try:
                    max_length = max(max_length, len(str(cell.value)))
                except:
                    pass
        adjusted_width = min(max_length + 2, 50)  # Cap width at 50 to prevent too wide columns
        ws.column_dimensions[column].width = adjusted_width


def create_excel_response(wb, report_name):
    """Create an HTTP response with the Excel file.

    Args:
        wb: The workbook to save
        report_name: The base name of the report (without extension)

    Returns:
        HttpResponse with the Excel file
    """
    response = HttpResponse(
        content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    )
    response['Content-Disposition'] = f'attachment; filename={report_name}_report_{timezone.now().date()}.xlsx'
    wb.save(response)
    return response


# CSV Import Functions
import csv
import io
import datetime
from django.db import transaction as db_transaction
from django.core.exceptions import ValidationError
from .models import create_low_stock_notification, create_out_of_stock_notification

def parse_date(date_str):
    """Parse date string in various formats, with preference for YYYY-MM-DD."""
    if not date_str:
        return None

    date_str = date_str.strip()

    # List of date formats to try
    date_formats = [
        '%Y-%m-%d',    # YYYY-MM-DD (standard)
        '%d/%m/%Y',    # DD/MM/YYYY (common in many countries)
        '%m/%d/%Y',    # MM/DD/YYYY (US format)
        '%d-%m-%Y',    # DD-MM-YYYY
        '%m-%d-%Y',    # MM-DD-YYYY
        '%d.%m.%Y',    # DD.MM.YYYY (European format)
        '%Y/%m/%d',    # YYYY/MM/DD (alternative ISO)
    ]

    for date_format in date_formats:
        try:
            return datetime.datetime.strptime(date_str, date_format).date()
        except ValueError:
            continue

    # If we get here, none of the formats worked
    raise ValidationError(
        f"Invalid date format: {date_str}. "
        f"Supported formats include: YYYY-MM-DD, DD/MM/YYYY, MM/DD/YYYY, etc."
    )

def parse_time(time_str):
    """Parse time string in various formats."""
    if not time_str:
        return None

    time_str = time_str.strip()

    # List of time formats to try
    time_formats = [
        '%H:%M:%S',    # HH:MM:SS (standard)
        '%H:%M',       # HH:MM (common)
        '%I:%M:%S %p', # HH:MM:SS AM/PM
        '%I:%M %p',    # HH:MM AM/PM
        '%H.%M.%S',    # HH.MM.SS
        '%H.%M',       # HH.MM
    ]

    for time_format in time_formats:
        try:
            return datetime.datetime.strptime(time_str, time_format).time()
        except ValueError:
            continue

    # If we get here, none of the formats worked
    raise ValidationError(
        f"Invalid time format: {time_str}. "
        f"Supported formats include: HH:MM:SS, HH:MM, HH:MM:SS AM/PM, etc."
    )

def parse_float(value_str):
    """Parse float value, handling different formats."""
    if not value_str:
        return 0.0
    try:
        # Remove any currency symbols and commas
        cleaned_str = value_str.strip().replace('₱', '').replace(',', '')
        return float(cleaned_str)
    except ValueError:
        raise ValidationError(f"Invalid number format: {value_str}")

def parse_int(value_str):
    """Parse integer value, handling different formats."""
    if not value_str:
        return 0
    try:
        # Remove any commas
        cleaned_str = value_str.strip().replace(',', '')
        return int(float(cleaned_str))
    except ValueError:
        raise ValidationError(f"Invalid integer format: {value_str}")

@db_transaction.atomic
def import_medicines_from_csv(csv_file, user=None):
    """
    Import medicines from a CSV file.

    Expected CSV format:
    name,category,price,quantity,reorder_level,reorder_quantity,expiration_date,description

    Note: The user parameter is kept for compatibility but not used directly.
    """
    csv_file_wrapper = io.TextIOWrapper(csv_file, encoding='utf-8')
    csv_reader = csv.DictReader(csv_file_wrapper)

    results = {
        'created': 0,
        'updated': 0,
        'skipped': 0,
        'errors': [],
        'warnings': [],
        'medicines': []
    }

    required_fields = ['name', 'category', 'price', 'quantity']

    # Track medicines processed in this batch to detect duplicates
    processed_medicines = set()

    for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 to account for header row
        try:
            # Check for required fields
            missing_fields = [field for field in required_fields if field not in row or not row[field]]
            if missing_fields:
                missing_fields_str = ", ".join(missing_fields)
                results['errors'].append(f"Row {row_num}: Missing required fields: {missing_fields_str}")
                continue

            # Parse data
            name = row['name'].strip()

            # Check for duplicate in the same file
            if name.lower() in processed_medicines:
                warning_msg = f"Row {row_num}: Duplicate medicine '{name}' found in the file. Using the first occurrence."
                results['warnings'].append(warning_msg)
                results['skipped'] += 1
                continue

            # Add to processed set
            processed_medicines.add(name.lower())

            # Continue with parsing other fields
            category = row['category'].strip()

            # Try to parse price with flexible handling
            try:
                price = parse_float(row['price'])
            except ValidationError as e:
                # Try to clean the price string and retry
                price_str = row['price'].replace('$', '').replace('₱', '').replace(',', '').strip()
                try:
                    price = float(price_str)
                except ValueError:
                    raise ValidationError(f"Invalid price format: {row['price']}")

            # Parse quantity with flexible handling
            try:
                quantity = parse_int(row['quantity'])
            except ValidationError:
                # Try to clean the quantity string and retry
                quantity_str = row['quantity'].replace(',', '').strip()
                try:
                    quantity = int(float(quantity_str))
                except ValueError:
                    raise ValidationError(f"Invalid quantity format: {row['quantity']}")

            # Parse optional fields with defaults
            try:
                reorder_level = parse_int(row.get('reorder_level', '10'))
            except ValidationError:
                reorder_level = 10  # Default if parsing fails
                results['warnings'].append(f"Row {row_num}: Invalid reorder_level format, using default (10)")

            try:
                reorder_quantity = parse_int(row.get('reorder_quantity', '20'))
            except ValidationError:
                reorder_quantity = 20  # Default if parsing fails
                results['warnings'].append(f"Row {row_num}: Invalid reorder_quantity format, using default (20)")

            # Parse expiration date with flexible handling
            try:
                expiration_date = parse_date(row.get('expiration_date', ''))
            except ValidationError as e:
                expiration_date = None
                results['warnings'].append(f"Row {row_num}: {str(e)} Using no expiration date.")

            description = row.get('description', '').strip()

            # Check if medicine already exists
            existing_medicine = Medicine.objects.filter(name__iexact=name).first()

            if existing_medicine:
                # Update existing medicine
                old_price = existing_medicine.price
                old_quantity = existing_medicine.quantity

                existing_medicine.category = category
                existing_medicine.price = price
                existing_medicine.reorder_level = reorder_level
                existing_medicine.reorder_quantity = reorder_quantity

                # Only update expiration date if provided
                if expiration_date:
                    existing_medicine.expiration_date = expiration_date

                if description:
                    existing_medicine.description = description

                # Handle quantity change as a transaction
                if quantity != old_quantity:
                    quantity_change = quantity - old_quantity
                    transaction_type = 'in' if quantity_change > 0 else 'out'

                    Transaction.objects.create(
                        medicine=existing_medicine,
                        transaction_type=transaction_type,
                        quantity=abs(quantity_change),
                        customer_name="System Import",
                        customer_type="system",
                        transaction_date=timezone.now(),
                        total_amount=abs(quantity_change) * existing_medicine.price
                    )

                    # Update the quantity
                    existing_medicine.quantity = quantity

                existing_medicine.save()

                # Send price change notification if price changed
                if old_price != price:
                    existing_medicine.send_price_change_notification(old_price, price)

                results['updated'] += 1
                logger.info(f"Updated existing medicine: {name}")

                results['medicines'].append({
                    'id': existing_medicine.id,
                    'name': existing_medicine.name,
                    'created': False
                })
            else:
                # Create new medicine
                new_medicine = Medicine.objects.create(
                    name=name,
                    category=category,
                    price=price,
                    quantity=quantity,
                    reorder_level=reorder_level,
                    reorder_quantity=reorder_quantity,
                    expiration_date=expiration_date,
                    description=description
                )

                results['created'] += 1
                logger.info(f"Created new medicine: {name}")

                # Notifications for new medicine are handled by the Medicine.save() method

                results['medicines'].append({
                    'id': new_medicine.id,
                    'name': new_medicine.name,
                    'created': True
                })

        except Exception as e:
            error_msg = f"Error in row {row_num}: {str(e)}"
            results['errors'].append(error_msg)
            logger.error(error_msg)

    return results

@db_transaction.atomic
def import_transactions_from_csv(csv_file, user=None):
    """
    Import transactions from a CSV file.

    Expected CSV format:
    medicine_name,transaction_type,quantity,customer_name,patient_number,customer_type,transaction_date,transaction_time

    Note: The user parameter is kept for compatibility but not used directly.
    """
    csv_file_wrapper = io.TextIOWrapper(csv_file, encoding='utf-8')
    csv_reader = csv.DictReader(csv_file_wrapper)

    results = {
        'created': 0,
        'skipped': 0,
        'errors': [],
        'warnings': [],
        'transactions': []
    }

    required_fields = ['medicine_name', 'transaction_type', 'quantity', 'customer_name']

    for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 to account for header row
        try:
            # Check for required fields
            missing_fields = [field for field in required_fields if field not in row or not row[field]]
            if missing_fields:
                missing_fields_str = ", ".join(missing_fields)
                results['errors'].append(f"Row {row_num}: Missing required fields: {missing_fields_str}")
                continue

            # Parse medicine name
            medicine_name = row['medicine_name'].strip()
            medicine = None

            # Try to find by name (expected scenario)
            medicine = Medicine.objects.filter(name__iexact=medicine_name).first()

            if not medicine:
                # If not found by name, try to parse as integer ID for backward compatibility
                try:
                    medicine_id = parse_int(medicine_name)
                    try:
                        medicine = Medicine.objects.get(id=medicine_id)
                        results['warnings'].append(f"Row {row_num}: Found medicine with ID {medicine_id} ({medicine.name}).")
                    except Medicine.DoesNotExist:
                        raise ValidationError(f"Medicine with name '{medicine_name}' does not exist.")
                except ValidationError:
                    # If it's not a valid ID and not a valid name, it doesn't exist
                    raise ValidationError(f"Medicine with name '{medicine_name}' does not exist.")

            # Parse transaction type with flexible handling
            transaction_type = row['transaction_type'].strip().lower()

            # Map the transaction type to the model's expected values
            original_type = transaction_type
            if transaction_type in ['in', 'input', 'add', 'incoming', 'receive', 'received', 'purchase', 'purchased']:
                # Map 'in' to 'purchase' for the database
                transaction_type = 'purchase'
                logger.info(f"Row {row_num}: Transaction type '{original_type}' mapped to 'purchase'")
            elif transaction_type in ['out', 'output', 'remove', 'outgoing', 'dispense', 'dispensed', 'sale', 'sold']:
                # Map 'out' to 'sale' for the database
                transaction_type = 'sale'
                logger.info(f"Row {row_num}: Transaction type '{original_type}' mapped to 'sale'")
            else:
                results['warnings'].append(f"Row {row_num}: Unrecognized transaction type '{transaction_type}', defaulting to 'purchase'.")
                transaction_type = 'purchase'
                logger.info(f"Row {row_num}: Unrecognized transaction type '{original_type}', defaulted to 'purchase'")

            # Parse quantity with flexible handling
            try:
                quantity = parse_int(row['quantity'])
                if quantity <= 0:
                    results['warnings'].append(f"Row {row_num}: Quantity must be positive. Using absolute value of {abs(quantity)}.")
                    quantity = abs(quantity)
            except ValidationError:
                # Try to clean the quantity string and retry
                quantity_str = row['quantity'].replace(',', '').strip()
                try:
                    quantity = int(float(quantity_str))
                    if quantity <= 0:
                        results['warnings'].append(f"Row {row_num}: Quantity must be positive. Using absolute value of {abs(quantity)}.")
                        quantity = abs(quantity)
                except ValueError:
                    raise ValidationError(f"Invalid quantity format: {row['quantity']}")

            customer_name = row['customer_name'].strip()
            patient_number = row.get('patient_number', '').strip()

            # Parse customer type with flexible handling
            customer_type = row.get('customer_type', 'out_patient').strip().lower()
            logger.info(f"Row {row_num}: Original customer_type='{customer_type}'")

            if customer_type in ['in_patient', 'inpatient', 'in patient', 'in-patient']:
                customer_type = 'in_patient'
                logger.info(f"Row {row_num}: Customer type mapped to 'in_patient'")
            elif customer_type in ['out_patient', 'outpatient', 'out patient', 'out-patient']:
                customer_type = 'out_patient'
                logger.info(f"Row {row_num}: Customer type mapped to 'out_patient'")
            elif customer_type in ['system', 'auto', 'automatic', 'import']:
                customer_type = 'system'
                logger.info(f"Row {row_num}: Customer type mapped to 'system'")
            else:
                results['warnings'].append(f"Row {row_num}: Unrecognized customer type '{customer_type}', defaulting to 'out_patient'.")
                customer_type = 'out_patient'
                logger.info(f"Row {row_num}: Unrecognized customer type, defaulted to 'out_patient'")

            # Parse dates and times with flexible handling
            try:
                transaction_date = parse_date(row.get('transaction_date', '')) or timezone.now().date()
            except ValidationError as e:
                transaction_date = timezone.now().date()
                results['warnings'].append(f"Row {row_num}: {str(e)} Using today's date.")

            try:
                transaction_time = parse_time(row.get('transaction_time', '')) or timezone.now().time()
            except ValidationError as e:
                transaction_time = timezone.now().time()
                results['warnings'].append(f"Row {row_num}: {str(e)} Using current time.")

            # Check if this is a duplicate transaction (same medicine, type, date, time, quantity)
            existing_transaction = Transaction.objects.filter(
                medicine=medicine,
                transaction_type=transaction_type,  # Now using the correct transaction type
                quantity=quantity,
                transaction_date=transaction_date,
                customer_name=customer_name
            ).first()

            if existing_transaction:
                results['warnings'].append(
                    f"Row {row_num}: Duplicate transaction for {medicine.name} on {transaction_date} with quantity {quantity}. Skipping."
                )
                results['skipped'] += 1
                continue

            # Check transaction date age
            current_date = timezone.now().date()
            seven_days_ago = current_date - timezone.timedelta(days=7)
            one_year_ago = current_date - timezone.timedelta(days=365)
            three_years_ago = current_date - timezone.timedelta(days=365*3)

            # Determine transaction age category
            is_recent_transaction = transaction_date >= seven_days_ago
            is_active_historical = transaction_date < seven_days_ago and transaction_date >= one_year_ago
            is_reference_historical = transaction_date < one_year_ago and transaction_date >= three_years_ago
            is_ancient_transaction = transaction_date < three_years_ago

            # Define is_old_transaction for use in inventory updates
            # IMPORTANT: We're forcing all transactions to be treated as current for inventory updates
            # This ensures that all transactions affect inventory regardless of date
            is_old_transaction = False  # Force all transactions to update inventory
            logger.info(f"Row {row_num}: Transaction date: {transaction_date}, Seven days ago: {seven_days_ago}, Forcing is_old_transaction to FALSE")

            # Skip transactions older than 3 years
            if is_ancient_transaction:
                # Track in results but don't log individual warnings
                if 'ancient_transactions' not in results:
                    results['ancient_transactions'] = 0
                results['ancient_transactions'] += 1
                results['skipped'] += 1
                continue

            # Track transaction age categories for summary reporting
            if is_active_historical:
                if 'active_historical_transactions' not in results:
                    results['active_historical_transactions'] = 0
                results['active_historical_transactions'] += 1
            elif is_reference_historical:
                if 'reference_historical_transactions' not in results:
                    results['reference_historical_transactions'] = 0
                results['reference_historical_transactions'] += 1

            # Check if there's enough stock for 'sale' transactions (only for current transactions)
            if not is_old_transaction and transaction_type == 'sale' and medicine.quantity < quantity:
                if medicine.quantity > 0:
                    results['warnings'].append(
                        f"Row {row_num}: Not enough stock for {medicine.name}. Available: {medicine.quantity}, Requested: {quantity}. "
                        f"Adjusting to available quantity: {medicine.quantity}."
                    )
                    quantity = medicine.quantity
                else:
                    results['errors'].append(
                        f"Row {row_num}: Cannot process outbound transaction for {medicine.name}. No stock available."
                    )
                    continue

            # Create the transaction
            # Note: transaction_date is a DateTimeField, so we need to combine date and time
            transaction_datetime = datetime.datetime.combine(transaction_date, transaction_time)

            transaction = Transaction.objects.create(
                medicine=medicine,
                transaction_type=transaction_type,  # Now using the correct transaction type
                quantity=quantity,
                customer_name=customer_name,
                patient_number=patient_number,
                customer_type=customer_type,
                transaction_date=transaction_datetime,
                total_amount=quantity * medicine.price
            )

            # Update medicine quantity only for transactions within the last 7 days
            if not is_old_transaction:
                old_quantity = medicine.quantity
                logger.info(f"Row {row_num}: Updating inventory for {medicine.name}, transaction_type={transaction_type}, old_quantity={old_quantity}, quantity={quantity}")

                # IMPORTANT: For upload_transaction.html, we're forcing all transactions to behave like 'sale' transactions
                # This ensures consistency with create_transaction.html which always deducts from inventory
                medicine.quantity -= quantity
                logger.info(f"Row {row_num}: FORCED SALE BEHAVIOR - Subtracting {quantity} from inventory regardless of transaction type. New quantity: {medicine.quantity}")

                medicine.save()

                # Create notification for low stock if needed
                if medicine.quantity <= medicine.reorder_level and old_quantity > medicine.reorder_level:
                    # Use the global notification functions instead of a method
                    create_low_stock_notification(medicine)
                    medicine.send_low_stock_notification()

                # Create notification for out of stock if needed
                if medicine.quantity == 0 and old_quantity > 0:
                    # Use the global notification functions instead of a method
                    create_out_of_stock_notification(medicine)
                    medicine.send_out_of_stock_notification()
            else:
                # For old transactions, we don't update inventory but log it
                logger.info(f"Historical transaction for {medicine.name} on {transaction_date} added for forecasting purposes only")

            results['created'] += 1
            results['transactions'].append({
                'id': transaction.id,
                'medicine': medicine.name,
                'type': transaction_type,
                'quantity': quantity
            })

            logger.info(f"Created transaction for {medicine.name}: {transaction_type} {quantity} units")

        except Exception as e:
            error_msg = f"Error in row {row_num}: {str(e)}"
            results['errors'].append(error_msg)
            logger.error(error_msg)

    return results
