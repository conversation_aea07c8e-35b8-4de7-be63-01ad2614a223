{% load static %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Medicine Inventory Management System - Efficient medicine tracking and forecasting">
    <meta name="theme-color" content="#2c3e50">
    <title>{% block title %}Medicine Inventory Management System{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{% static 'img/favicon.svg' %}">
    <link rel="alternate icon" type="image/x-icon" href="{% static 'img/favicon.ico' %}">

    <!-- Preload critical assets -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" as="style">

    <!-- CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- DataTables CSS & JS -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/buttons/2.2.2/css/buttons.bootstrap5.min.css">

    <!-- jQuery first, then DataTables -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.bootstrap5.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.3/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/pdfmake.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdfmake/0.1.53/vfs_fonts.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/2.2.2/js/buttons.print.min.js"></script>

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/base.css' %}">

    <style>






        /* ======================================
        ADMIN DASHBOARD - PROFESSIONAL STYLES
        ====================================== */

        :root {
            /* Color Palette */
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #1abc9c;
            --success-color: #2ecc71;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #34495e;
            --text-primary: #2c3e50;
            --text-secondary: #7f8c8d;
            --text-muted: #95a5a6;
            --border-color: #e2e8f0;
            /* Gradients */
            --primary-gradient: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
            --background-gradient: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            /* Layout Variables - INCREASED SIZES */
            --sidebar-width: 300px; /* Increased from 250px */
            --collapsed-sidebar-width: 80px; /* Increased from 60px */
            --header-height: 70px; /* Increased from 56px */
            --content-padding: 1.5rem; /* Increased from 1.25rem */
            --sidebar-transition: all 0.3s ease;
            /* Typography */
            --font-primary: 'Segoe UI', Roboto, -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Arial, sans-serif;
            /* Shadows */
            --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.08);
            --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            /* Border Radius */
            --border-radius-sm: 0.25rem;
            --border-radius-md: 0.375rem;
            --border-radius-lg: 0.5rem;
        }

        /* Global Reset and Base Styles */
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        html, body {
            height: 100%;
            overflow-x: hidden;
            font-family: var(--font-primary);
            font-size: 16px;
            color: var(--text-primary);
            line-height: 1.5;
        }

        body {
            background: var(--background-gradient);
            background-attachment: fixed;
        }

        /* ======================================
        LAYOUT STRUCTURE
        ====================================== */

        .page-wrapper {
            display: flex;
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        .content-wrapper {
            flex: 1;
            margin-left: var(--sidebar-width);
            transition: var(--sidebar-transition);
            width: calc(100% - var(--sidebar-width));
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            background-color: #f8fafc;
        }

        .sidebar-wrapper.collapsed + .content-wrapper {
            margin-left: var(--collapsed-sidebar-width);
            width: calc(100% - var(--collapsed-sidebar-width));
        }

        .main-content {
            flex: 1;
            padding: var(--content-padding);
            padding-top: calc(var(--header-height) + var(--content-padding)); /* Adjusted for fixed header */
            overflow-y: auto;
        }

        /* ======================================
        SIDEBAR STYLES - INCREASED DIMENSIONS
        ====================================== */

        .sidebar-wrapper {
            width: var(--sidebar-width);
            position: fixed;
            height: 100%;
            background: var(--primary-gradient);
            z-index: 1040;
            transition: var(--sidebar-transition);
            left: 0;
            box-shadow: var(--shadow-md);
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
        }

            .sidebar-wrapper.collapsed {
                width: var(--collapsed-sidebar-width);
            }

        .sidebar {
            height: 100%;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        /* Sidebar Header - INCREASED SIZES */
        .sidebar-header {
            padding: 1.25rem 1.5rem; /* Increased from 1rem */
            display: flex;
            align-items: center;
            justify-content: space-between;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            height: var(--header-height);
            min-height: var(--header-height);
        }

        .sidebar-brand {
            color: #fff;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.4rem; /* Increased from 1.2rem */
            display: flex;
            align-items: center;
        }

        .sidebar-logo {
            width: 36px; /* Increased from 28px */
            height: 36px; /* Increased from 28px */
            object-fit: contain;
        }

        .sidebar-logo-text {
            margin-left: 1rem; /* Increased from 0.75rem */
            transition: opacity 0.3s ease, width 0.3s ease, margin 0.3s ease;
            white-space: nowrap;
            letter-spacing: 0.5px;
        }

        .sidebar-wrapper.collapsed .sidebar-logo-text {
            opacity: 0;
            width: 0;
            margin-left: 0;
            overflow: hidden;
        }

        .btn-close-sidebar {
            color: rgba(255, 255, 255, 0.7);
            background: transparent;
            border: none;
            font-size: 1rem; /* Increased from 0.875rem */
            padding: 0.25rem;
            transition: color 0.2s ease;
        }

            .btn-close-sidebar:hover {
                color: #fff;
            }

        /* Sidebar Body - INCREASED PADDING */
        .sidebar-body {
            flex: 1;
            overflow-y: auto;
            padding-top: 1rem; /* Increased from 0.75rem */
            padding-bottom: 1rem; /* Increased from 0.75rem */
        }

            .sidebar-body::-webkit-scrollbar {
                width: 5px; /* Increased from 4px */
            }

            .sidebar-body::-webkit-scrollbar-track {
                background: rgba(255, 255, 255, 0.05);
            }

            .sidebar-body::-webkit-scrollbar-thumb {
                background-color: rgba(255, 255, 255, 0.2);
                border-radius: 10px;
            }

        /* Navigation Menu - LARGER BUTTONS AND TEXT */
        .sidebar .nav-pills {
            display: flex;
            flex-direction: column;
            gap: 3px; /* Increased from 2px */
        }

            .sidebar .nav-pills .nav-item {
                position: relative;
            }

            .sidebar .nav-pills .nav-link {
                border-radius: 0;
                color: rgba(255, 255, 255, 0.85);
                padding: 1rem 1.5rem; /* Increased from 0.75rem 1.25rem */
                transition: all 0.2s ease;
                display: flex;
                align-items: center;
                position: relative;
                white-space: nowrap;
                font-size: 1rem; /* Increased from 0.875rem */
                font-weight: 400;
                letter-spacing: 0.3px;
                border-left: 4px solid transparent; /* Increased from 3px */
            }

                .sidebar .nav-pills .nav-link:hover {
                    background-color: rgba(255, 255, 255, 0.08);
                    color: #fff;
                }

                .sidebar .nav-pills .nav-link.active {
                    background-color: rgba(255, 255, 255, 0.12);
                    color: #fff;
                    font-weight: 500;
                    border-left: 4px solid var(--accent-color); /* Increased from 3px */
                }

                .sidebar .nav-pills .nav-link i {
                    margin-right: 1rem; /* Increased from 0.75rem */
                    font-size: 1.25rem; /* Increased from 1rem */
                    width: 24px; /* Increased from 20px */
                    text-align: center;
                    opacity: 0.9;
                }

                .sidebar .nav-pills .nav-link span {
                    transition: opacity 0.3s ease, width 0.3s ease;
                }

        /* Collapsed Sidebar Navigation - ADJUSTED FOR LARGER SIZE */
        .sidebar-wrapper.collapsed .nav-pills .nav-link {
            padding: 1rem 0; /* Increased from 0.75rem 0 */
            justify-content: center;
        }

            .sidebar-wrapper.collapsed .nav-pills .nav-link i {
                margin-right: 0;
                font-size: 1.5rem; /* Increased from 1.25rem */
            }

            .sidebar-wrapper.collapsed .nav-pills .nav-link span {
                opacity: 0;
                width: 0;
                overflow: hidden;
            }

        /* Submenu Styles - LARGER TEXT AND SPACING */
        .submenu-arrow {
            transition: transform 0.3s ease, opacity 0.3s ease;
            position: absolute;
            right: 1.25rem; /* Increased from 1rem */
            font-size: 0.875rem; /* Increased from 0.75rem */
            opacity: 0.7;
        }

        .sidebar-menu-toggle[aria-expanded="true"] .submenu-arrow {
            transform: rotate(90deg);
        }

        .sidebar-wrapper.collapsed .submenu-arrow {
            opacity: 0;
            display: none;
        }

        .submenu {
            background-color: rgba(0, 0, 0, 0.15);
            padding-left: 0.75rem; /* Increased from 0.5rem */
            transition: var(--sidebar-transition);
        }

        .submenu-link {
            padding: 0.8rem 1.25rem 0.8rem 2rem !important; /* Increased from 0.6rem 1rem 0.6rem 1.75rem */
            font-size: 0.9375rem !important; /* Increased from 0.8125rem */
            color: rgba(255, 255, 255, 0.75) !important;
        }

            .submenu-link:hover {
                color: #fff !important;
            }

            .submenu-link.active {
                background-color: rgba(255, 255, 255, 0.1) !important;
                color: #fff !important;
                font-weight: 500 !important;
            }

        /* Collapsed Sidebar Tooltips - LARGER SIZE */
        .sidebar-wrapper.collapsed .nav-link:hover::after {
            content: attr(data-title);
            position: absolute;
            left: calc(var(--collapsed-sidebar-width) - 5px);
            top: 50%;
            transform: translateY(-50%);
            background: var(--dark-color);
            color: white;
            padding: 0.625rem 1rem; /* Increased from 0.5rem 0.75rem */
            border-radius: var(--border-radius-sm);
            font-size: 0.875rem; /* Increased from 0.75rem */
            white-space: nowrap;
            z-index: 1070;
            box-shadow: var(--shadow-sm);
        }

        /* ======================================
        USER PROFILE SECTION - IMPROVED & FIXED
        ====================================== */
        .user-info-container {
            color: #fff;
            transition: var(--sidebar-transition);
            margin-top: auto; /* Push to bottom of sidebar */
            padding: 1.25rem;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            width: 100%; /* Ensure full width */
        }

        .user-profile {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%; /* Ensure full width */
        }

        /* IMPROVED IMAGE CENTERING AND CONSISTENCY */
        .user-avatar-wrapper {
            margin-bottom: 1rem;
            width: 100%; /* Full width container */
            display: flex;
            justify-content: center; /* Center horizontally */
            position: relative; /* For positioning indicators */
        }

        .user-avatar {
            position: relative;
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            overflow: hidden; /* Ensure content stays within the circle */
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

            .user-avatar img {
                width: 100%;
                height: 100%;
                object-fit: cover;
                transition: transform 0.3s ease;
            }

            /* Hover effect for avatar */
            .user-avatar:hover {
                box-shadow: 0 0 0 3px var(--accent-color);
            }

                .user-avatar:hover img {
                    transform: scale(1.05);
                }

        /* IMPROVED NO AVATAR STATE */
        .no-avatar {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 70px;
            height: 70px;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }

            .no-avatar i {
                font-size: 2rem;
                color: rgba(255, 255, 255, 0.7);
                transition: all 0.3s ease;
            }

            .no-avatar:hover {
                background-color: rgba(255, 255, 255, 0.15);
                box-shadow: 0 0 0 3px var(--accent-color);
            }

                .no-avatar:hover i {
                    color: rgba(255, 255, 255, 0.9);
                }

        /* IMPROVED USER DETAILS SECTION */
        .user-details {
            text-align: center;
            margin-bottom: 1rem;
            width: 100%;
            transition: opacity 0.3s ease;
        }

        .user-name {
            font-weight: 600;
            font-size: 1.125rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%; /* Constrain to parent width */
            margin-bottom: 0.5rem;
            padding: 0 0.5rem; /* Add padding to prevent text hitting the edges */
            position: relative; /* For gradient overlay */
        }

            /* Add gradient fade for very long names */
            .user-name:after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 25px;
                height: 100%;
                background: linear-gradient(to right, rgba(44, 62, 80, 0), rgba(44, 62, 80, 1));
                opacity: 0;
                transition: opacity 0.3s ease;
                pointer-events: none;
            }

            .user-name.overflow:after {
                opacity: 1;
            }

        .user-role {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: 30px;
            display: inline-block;
            transition: background-color 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 90%; /* Prevent overflow */
        }

        .sidebar-divider {
            border-color: rgba(255, 255, 255, 0.1);
            margin: 0.75rem 0;
            width: 100%;
        }

        /* IMPROVED SIGN OUT BUTTON */
        .btn-sign-out {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            border: 1px solid rgba(231, 76, 60, 0.4);
            background-color: rgba(231, 76, 60, 0.08);
            color: #fff;
            border-radius: var(--border-radius-md);
            padding: 0.625rem 1rem;
            font-weight: 500;
            transition: all 0.2s ease;
            text-decoration: none;
            width: 100%;
            font-size: 0.9375rem;
        }

            .btn-sign-out:hover, .btn-sign-out:focus {
                background-color: rgba(231, 76, 60, 0.2);
                border-color: rgba(231, 76, 60, 0.6);
                transform: translateY(-1px);
                box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            }

            .btn-sign-out:active {
                transform: translateY(0);
            }

            .btn-sign-out i {
                font-size: 1rem;
                transition: transform 0.2s ease;
            }

            .btn-sign-out:hover i {
                transform: translateX(2px);
            }

        /* COLLAPSED SIDEBAR USER SECTION - FIXED */
        .sidebar-wrapper.collapsed .user-info-container {
            padding: 0.75rem 0.5rem;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .sidebar-wrapper.collapsed .user-avatar-wrapper {
            margin-bottom: 0.5rem;
        }

        .sidebar-wrapper.collapsed .user-avatar,
        .sidebar-wrapper.collapsed .no-avatar {
            width: 44px;
            height: 44px;
        }

            .sidebar-wrapper.collapsed .no-avatar i {
                font-size: 1.5rem;
            }

        .sidebar-wrapper.collapsed .user-details {
            display: none;
        }

        .sidebar-wrapper.collapsed .sidebar-divider {
            margin: 0.5rem 0;
            width: 60%;
        }

        /* FIXED LOGOUT BUTTON IN COLLAPSED STATE */
        .sidebar-wrapper.collapsed .btn-sign-out {
            width: 44px;
            height: 44px;
            padding: 0;
            border-radius: 50%;
            background-color: rgba(231, 76, 60, 0.1);
            border: 1px solid rgba(231, 76, 60, 0.3);
        }

            .sidebar-wrapper.collapsed .btn-sign-out i {
                margin: 0;
                font-size: 1.25rem;
            }

            .sidebar-wrapper.collapsed .btn-sign-out span {
                display: none;
            }

            .sidebar-wrapper.collapsed .btn-sign-out:hover i {
                transform: none;
            }

        /* Status indicator for online/offline */
        .status-indicator {
            position: absolute;
            bottom: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            border: 2px solid var(--primary-color);
            z-index: 2;
        }

        .status-online {
            background-color: var(--success-color);
        }

        .status-offline {
            background-color: var(--text-muted);
        }

        .status-busy {
            background-color: var(--warning-color);
        }

        /* ======================================
        TOP NAVBAR STYLES - FIXED POSITION
        ====================================== */

        .top-navbar {
            background-color: #fff;
            padding: 0 1.5rem;
            box-shadow: var(--shadow-md); /* Increased shadow for fixed header */
            position: fixed; /* Changed from sticky to fixed */
            top: 0;
            right: 0;
            left: var(--sidebar-width); /* Align with sidebar */
            z-index: 1030; /* Increased z-index */
            height: var(--header-height);
            display: flex;
            align-items: center;
            transition: var(--sidebar-transition);
            width: calc(100% - var(--sidebar-width)); /* Set width to match content area */
        }

        /* Adjust header when sidebar is collapsed */
        .sidebar-wrapper.collapsed ~ .content-wrapper .top-navbar {
            left: var(--collapsed-sidebar-width);
            width: calc(100% - var(--collapsed-sidebar-width));
        }

        .sidebar-toggle {
            background: transparent;
            border: none;
            color: var(--text-primary);
            font-size: 1.375rem;
            cursor: pointer;
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: color 0.2s ease;
            margin-right: 1rem;
        }

            .sidebar-toggle:hover {
                color: var(--primary-color);
            }

        .page-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        /* Notifications Dropdown - LARGER ELEMENTS */
        .dropdown {
            position: relative;
        }

        .dropdown-menu {
            min-width: 320px;
            padding: 0.75rem 0;
            font-size: 1rem;
            border: none;
            border-radius: var(--border-radius-md);
            box-shadow: var(--shadow-lg);
        }

        .dropdown-header {
            padding: 1rem 1.25rem;
            font-weight: 600;
            color: var(--text-primary);
            border-bottom: 1px solid var(--border-color);
        }

        .dropdown-item {
            padding: 0.75rem 1.25rem;
            color: var(--text-secondary);
        }

            .dropdown-item:hover {
                background-color: rgba(52, 152, 219, 0.05);
                color: var(--primary-color);
            }

        .dropdown-divider {
            margin: 0.75rem 0;
            border-top: 1px solid var(--border-color);
        }

        .user-avatar-small {
            border: 1px solid rgba(0, 0, 0, 0.1);
            object-fit: cover;
            width: 38px;
            height: 38px;
        }

        /* Notification Icon in Navbar - LARGER */
        #notificationsDropdown i {
            font-size: 1.25rem;
        }

        /* Badge Styling - SLIGHTLY LARGER */
        .badge {
            font-weight: 500;
            padding: 0.4em 0.7em;
            font-size: 0.8125em;
            border-radius: 10rem;
        }

        .bg-primary {
            background-color: var(--primary-color) !important;
        }

        .bg-secondary {
            background-color: var(--text-secondary) !important;
        }

        .bg-danger {
            background-color: var(--danger-color) !important;
        }

        /* ======================================
        MOBILE RESPONSIVE STYLES - IMPROVED
        ====================================== */

        @media (max-width: 991.98px) {
            .content-wrapper {
                margin-left: 0;
                width: 100%;
            }

            .sidebar-wrapper {
                transform: translateX(-100%);
                box-shadow: none;
            }

                .sidebar-wrapper.show {
                    transform: translateX(0);
                    box-shadow: var(--shadow-lg);
                    width: 85% !important;
                    max-width: 320px;
                }

            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                z-index: 1039;
                display: none;
            }

                .sidebar-overlay.show {
                    display: block;
                }

            /* IMPROVED MOBILE SIDEBAR DISPLAY */
            .sidebar-wrapper.show .sidebar-logo-text,
            .sidebar-wrapper.show .nav-pills .nav-link span,
            .sidebar-wrapper.show .submenu-arrow {
                opacity: 1;
                width: auto;
                height: auto;
                margin-left: 1rem;
                display: block;
            }

            .sidebar-wrapper.show .user-details {
                opacity: 1;
                display: block;
                width: 100%;
                text-align: center;
            }

            .sidebar-wrapper.show .user-name,
            .sidebar-wrapper.show .user-role {
                display: block;
            }

            .sidebar-wrapper.show .user-avatar-wrapper {
                margin-bottom: 1rem;
            }

            .sidebar-wrapper.show .user-avatar,
            .sidebar-wrapper.show .no-avatar {
                width: 70px;
                height: 70px;
            }

                .sidebar-wrapper.show .no-avatar i {
                    font-size: 2rem;
                }

            .sidebar-wrapper.show .nav-pills .nav-link {
                padding: 1rem 1.5rem;
                justify-content: flex-start;
            }

                .sidebar-wrapper.show .nav-pills .nav-link i {
                    margin-right: 1rem;
                }

            /* Ensure logout button is full width on mobile */
            .sidebar-wrapper.show .btn-sign-out {
                width: 100%;
                height: auto;
                padding: 0.625rem 1rem;
                border-radius: var(--border-radius-md);
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.5rem;
            }

                .sidebar-wrapper.show .btn-sign-out i {
                    margin-right: 0.5rem;
                    font-size: 1rem;
                }

                .sidebar-wrapper.show .btn-sign-out span {
                    display: inline;
                }

            /* Adjust fixed header for mobile */
            .top-navbar {
                left: 0;
                width: 100%;
            }

            /* Adjust content padding for fixed header */
            .main-content {
                padding-top: calc(var(--header-height) + 1rem);
            }

            /* Prevent body scroll when sidebar is open */
            body.sidebar-open {
                overflow: hidden;
            }
        }

        /* Mobile Sidebar Overlay */
        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1030;
            opacity: 0;
            transition: opacity 0.3s ease;
            backdrop-filter: blur(2px);
        }

            .sidebar-overlay.show {
                display: block;
                opacity: 1;
            }

        /* Special handling for smaller mobile devices */
        @media (max-width: 576px) {
            .sidebar-wrapper.show {
                width: 85% !important;
                max-width: 300px;
            }

            .top-navbar {
                padding: 0 1rem;
            }

            .page-title {
                font-size: 1.125rem;
            }

            .main-content {
                padding: 1rem;
                padding-top: calc(var(--header-height) + 0.75rem);
            }

            /* Make images a bit smaller on very small screens */
            .user-avatar,
            .no-avatar {
                width: 60px;
                height: 60px;
            }

            /* Adjust logout button for small screens */
            .btn-sign-out {
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
            }
        }

        /* ======================================
        UTILITY CLASSES
        ====================================== */

        .text-center {
            text-align: center !important;
        }

        .rounded-circle {
            border-radius: 50% !important;
        }

        .w-100 {
            width: 100% !important;
        }

        .mb-1 {
            margin-bottom: 0.25rem !important;
        }

        .mb-2 {
            margin-bottom: 0.5rem !important;
        }

        .mb-3 {
            margin-bottom: 1rem !important;
        }

        .me-2 {
            margin-right: 0.5rem !important;
        }

        .me-3 {
            margin-right: 1rem !important;
        }

        .my-2 {
            margin-top: 0.5rem !important;
            margin-bottom: 0.5rem !important;
        }

        .p-0 {
            padding: 0 !important;
        }

        .p-3 {
            padding: 1rem !important;
        }

        .px-2 {
            padding-left: 0.5rem !important;
            padding-right: 0.5rem !important;
        }

        .d-flex {
            display: flex !important;
        }

        .d-none {
            display: none !important;
        }

        .d-md-block {
            display: none !important;
        }

        .d-md-none {
            display: block !important;
        }

        .position-absolute {
            position: absolute !important;
        }

        .position-relative {
            position: relative !important;
        }

        .align-items-center {
            align-items: center !important;
        }

        .justify-content-between {
            justify-content: space-between !important;
        }

        .top-0 {
            top: 0 !important;
        }

        .start-100 {
            left: 100% !important;
        }

        .translate-middle {
            transform: translate(-50%, -50%) !important;
        }

        .flex-direction-column {
            flex-direction: column !important;
        }

        /* Media query for medium devices */
        @media (min-width: 768px) {
            .d-md-block {
                display: block !important;
            }

            .d-md-none {
                display: none !important;
            }
        }

        /* ======================================
        ANIMATION UTILITIES
        ====================================== */

        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        .fade-in {
            animation: fadeIn 0.3s ease;
        }


        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(26, 188, 156, 0.4);
            }

            70% {
                box-shadow: 0 0 0 6px rgba(26, 188, 156, 0);
            }

            100% {
                box-shadow: 0 0 0 0 rgba(26, 188, 156, 0);
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes slideIn {
            from {
                transform: translateY(20px);
                opacity: 0;
            }

            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        .slide-in {
            animation: slideIn 0.4s ease forwards;
        }

        /* Avatar loading animation */
        @keyframes avatarShimmer {
            0% {
                background-position: -200px 0;
            }

            100% {
                background-position: 200px 0;
            }
        }

        .avatar-loading {
            background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 100%);
            background-size: 400px 100%;
            animation: avatarShimmer 1.5s infinite;
        }

        /* Smooth fade transition for hover states */
        .hover-fade {
            transition: opacity 0.2s ease;
        }

            .hover-fade:hover {
                opacity: 0.8;
            }

        /* Subtle scale animation */
        @keyframes scaleIn {
            from {
                transform: scale(0.95);
            }

            to {
                transform: scale(1);
            }
        }

        .scale-in {
            animation: scaleIn 0.3s ease forwards;
        }

        /* ======================================
        PRINT STYLES
        ====================================== */


        /* ======================================
        PRINT STYLES
        ====================================== */

        @media print {
            html, body {
                background: none !important;
                color: black !important;
            }

            .sidebar-wrapper,
            .top-navbar,
            .sidebar-toggle,
            form button[type="submit"],
            .btn-sign-out,
            .action-buttons,
            .dropdown {
                display: none !important;
            }

            .content-wrapper {
                margin-left: 0 !important;
                width: 100% !important;
            }

            .main-content {
                padding: 0.5cm !important;
                width: 100% !important;
            }

            .card {
                box-shadow: none !important;
                border: 1px solid #ddd !important;
                break-inside: avoid;
                page-break-inside: avoid;
            }

            .card-header {
                background-color: #f8f9fa !important;
                color: black !important;
                border-bottom: 1px solid #ddd !important;
            }

            /* Ensure tables print well */
            table {
                width: 100% !important;
                border-collapse: collapse !important;
            }

                table th {
                    background-color: #f8f9fa !important;
                    color: black !important;
                    border: 1px solid #ddd !important;
                }

                table td {
                    border: 1px solid #ddd !important;
                }

            /* Ensure images print at reasonable sizes */
            img {
                max-width: 100% !important;
                max-height: 15cm !important;
            }

            /* Add URLs after links for reference */
            a[href]:after {
                content: " (" attr(href) ")";
                font-size: 0.8em;
                font-weight: normal;
            }

            /* Don't show internal links or javascript links */
            a[href^="#"]:after,
            a[href^="javascript:"]:after {
                content: "";
            }

            /* Avoid page breaks inside critical elements */
            h1, h2, h3, h4, h5, h6,
            .user-profile,
            .data-section {
                page-break-after: avoid;
                break-after: avoid;
            }

            /* Print background colors and images - Fixed for compatibility */
            * {
                -webkit-print-color-adjust: exact !important; /* Chrome, Safari */
                print-color-adjust: exact !important; /* Firefox */
            }

            /* Remove shadow effects */
            * {
                text-shadow: none !important;
                box-shadow: none !important;
            }

            /* Add page numbers */
            @page {
                margin: 1cm;
            }

            @page :first {
                margin-top: 2cm;
            }
        }

        /* ======================================
        ACCESSIBILITY ENHANCEMENTS
        ====================================== */

        /* High contrast mode support */
        @media (prefers-contrast: more) {
            :root {
                --primary-color: #000;
                --secondary-color: #0056b3;
                --accent-color: #006600;
                --text-primary: #000;
                --text-secondary: #333;
                --border-color: #000;
            }

            .sidebar-wrapper {
                background: #000;
            }

            .btn-sign-out {
                background-color: #c00;
                color: white;
                border: 2px solid black;
            }

            .user-avatar,
            .no-avatar {
                border: 2px solid black;
            }
        }

        /* Reduced motion preferences */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.001ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.001ms !important;
                scroll-behavior: auto !important;
            }

            .user-avatar:hover img {
                transform: none;
            }

            .btn-sign-out:hover {
                transform: none;
            }

                .btn-sign-out:hover i {
                    transform: none;
                }
        }

        /* Skip navigation for keyboard users */
        .skip-to-content {
            position: absolute;
            top: -40px;
            left: 0;
            background: var(--primary-color);
            color: white;
            padding: 8px 16px;
            z-index: 9999;
            transition: top 0.3s;
        }

            .skip-to-content:focus {
                top: 0;
            }

        /* Focus visibility improvements */
        *:focus {
            outline: 2px solid var(--accent-color) !important;
            outline-offset: 2px !important;
        }

        /* ======================================
        BROWSER COMPATIBILITY FIXES
        ====================================== */

        /* Fix for IE11 flexbox issues */
        @media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
            .content-wrapper {
                display: block;
            }

            .user-profile {
                text-align: center;
            }

            .sidebar .nav-pills .nav-link {
                display: block;
            }
        }

        /* iOS input zoom fix */
        @supports (-webkit-touch-callout: none) {
            input, select, textarea {
                font-size: 16px !important;
            }
        }

        /* Fix for Safari overflow issues */
        @supports (-webkit-overflow-scrolling: touch) {
            .sidebar-body {
                -webkit-overflow-scrolling: touch;
            }

            .main-content {
                -webkit-overflow-scrolling: touch;
            }
        }






        .notification-dropdown {
            width: 320px;
            max-width: 100%;
        }

        .notification-item {
            display: flex;
            padding: 0.75rem 1rem;
            border-left: 3px solid transparent;
            transition: background-color 0.2s;
        }

            .notification-item:hover {
                background-color: var(--bs-light);
            }

            .notification-item.unread {
                border-left-color: var(--bs-primary);
                background-color: rgba(var(--bs-primary-rgb), 0.05);
            }

        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }

        .notification-content {
            flex: 1;
        }

        .notification-title {
            font-weight: 500;
            margin-bottom: 0.25rem;
        }

        .notification-text {
            font-size: 0.875rem;
            color: var(--bs-gray-600);
            margin-bottom: 0.25rem;
        }

        .notification-time {
            font-size: 0.75rem;
            color: var(--bs-gray-500);
        }

        .search-results {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border-radius: 0.375rem;
            border: 1px solid rgba(0,0,0,0.1);
            margin-top: 0.5rem;
            display: none;
            z-index: 1000;
        }

        .search-result-group {
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

            .search-result-group:last-child {
                border-bottom: none;
            }

        .search-result-heading {
            padding: 0.5rem 1rem;
            font-size: 0.875rem;
            font-weight: 600;
            background-color: rgba(0,0,0,0.02);
        }

        .search-result-item {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            text-decoration: none;
            color: inherit;
            transition: background-color 0.2s;
        }

            .search-result-item:hover {
                background-color: rgba(0,0,0,0.02);
            }

        .search-result-details {
            margin-left: 1rem;
        }

        .search-result-title {
            font-weight: 500;
        }

        .search-clear {
            display: none;
        }

        .search-container {
            position: relative;
            min-width: 300px;
        }






        .notification-dropdown {
            min-width: 320px;
            max-width: 400px;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }

        .notification-item {
            padding: 0.75rem 1rem;
            border-left: 3px solid transparent;
            transition: all 0.2s ease;
            cursor: pointer;
        }

            .notification-item:not(:last-child) {
                border-bottom: 1px solid var(--bs-border-color);
            }

            .notification-item.unread {
                background-color: rgba(var(--bs-primary-rgb), 0.05);
                border-left-color: var(--bs-primary);
            }

            .notification-item:hover {
                background-color: rgba(var(--bs-primary-rgb), 0.08);
            }

        .notification-icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }

        .notification-content {
            flex: 1;
            min-width: 0;
        }

        .notification-title {
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.25rem;
            color: var(--bs-gray-900);
        }

        .notification-message {
            font-size: 0.8125rem;
            color: var(--bs-gray-600);
            margin-bottom: 0.25rem;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .notification-time {
            font-size: 0.75rem;
            color: var(--bs-gray-500);
        }

        .notification-type-info .notification-icon {
            background-color: rgba(var(--bs-info-rgb), 0.1);
            color: var(--bs-info);
        }

        .notification-type-warning .notification-icon {
            background-color: rgba(var(--bs-warning-rgb), 0.1);
            color: var(--bs-warning);
        }

        .notification-type-error .notification-icon {
            background-color: rgba(var(--bs-danger-rgb), 0.1);
            color: var(--bs-danger);
        }

        .notification-type-success .notification-icon {
            background-color: rgba(var(--bs-success-rgb), 0.1);
            color: var(--bs-success);
        }






        /* Add this to your existing <style> section */

        /* Notification Modal Styles */
        .notification-modal-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }

        .notification-type-info .notification-modal-icon {
            background-color: rgba(var(--bs-info-rgb), 0.1);
            color: var(--bs-info);
        }

        .notification-type-warning .notification-modal-icon {
            background-color: rgba(var(--bs-warning-rgb), 0.1);
            color: var(--bs-warning);
        }

        .notification-type-error .notification-modal-icon {
            background-color: rgba(var(--bs-danger-rgb), 0.1);
            color: var(--bs-danger);
        }

        .notification-type-success .notification-modal-icon {
            background-color: rgba(var(--bs-success-rgb), 0.1);
            color: var(--bs-success);
        }

        .notification-detail-content {
            padding: 1rem;
        }

            .notification-detail-content .notification-message {
                white-space: pre-wrap;
                color: var(--bs-gray-700);
            }

        .notification-metadata {
            padding-top: 1rem;
            margin-top: 1rem;
            border-top: 1px solid var(--bs-gray-200);
        }

        #notificationDetailModal .modal-header {
            padding: 1rem 1.5rem;
        }

        #notificationDetailModal .modal-body {
            padding: 1.5rem;
        }

        #notificationDetailModal .modal-footer {
            padding: 1rem 1.5rem;
        }

        #notificationDetailModal .notification-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--bs-gray-900);
            margin-bottom: 0.5rem;
        }

        #notificationDetailModal .type-label {
            text-transform: capitalize;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.875rem;
        }

        #notificationDetailModal .notification-time {
            color: var(--bs-gray-600);
        }

        #notificationDetailModal .btn-sm {
            padding: 0.375rem 0.75rem;
        }

        #notificationDetailModal .notification-modal-icon i {
            font-size: 1.25rem;
        }

        /* Animation for modal */
        .modal.fade .modal-dialog {
            transition: transform 0.2s ease-out;
        }

        .modal.fade .modal-dialog {
            transform: scale(0.95);
        }

        .modal.show .modal-dialog {
            transform: none;
        }





        /* Add these notification type-specific styles */
        .notification-type-inventory .notification-modal-icon {
            background-color: rgba(var(--bs-primary-rgb), 0.1);
            color: var(--bs-primary);
        }

        .notification-type-expiry .notification-modal-icon {
            background-color: rgba(var(--bs-warning-rgb), 0.1);
            color: var(--bs-warning);
        }

        .notification-type-system .notification-modal-icon {
            background-color: rgba(var(--bs-info-rgb), 0.1);
            color: var(--bs-info);
        }

        .notification-type-default .notification-modal-icon {
            background-color: rgba(var(--bs-secondary-rgb), 0.1);
            color: var(--bs-secondary);
        }

        /* Update the notification modal icon styles */
        .notification-modal-icon {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
        }

            .notification-modal-icon i {
                font-size: 1.5rem; /* Increased size for better visibility */
            }

        /* Add hover effect for better interactivity */
        .notification-item:hover .notification-modal-icon {
            transform: scale(1.05);
            transition: transform 0.2s ease;
        }

        /* Add styles for unread notifications */
        .notification-item.unread .notification-modal-icon {
            position: relative;
        }

            .notification-item.unread .notification-modal-icon::after {
                content: '';
                position: absolute;
                top: 0;
                right: 0;
                width: 10px;
                height: 10px;
                background-color: var(--bs-primary);
                border-radius: 50%;
                border: 2px solid #fff;
            }
    </style>

    {% block extra_css %}{% endblock %}

    <!-- Progressive Web App meta tags -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
</head>

<body>
    <div class="page-wrapper">


        <!-- Sidebar Navigation -->
        <div class="sidebar-wrapper" id="sidebar-wrapper">
            <div class="sidebar">
                <div class="sidebar-header">
                    <a href="{% url 'home' %}" class="sidebar-brand">
                        <i class="fas fa-capsules fa-lg"></i>
                        <span class="sidebar-logo-text">Medforecast</span>
                    </a>
                    <button id="sidebarClose" class="btn-close d-md-none" aria-label="Close sidebar"></button>
                </div>



                <div class="sidebar-body">
                    <ul class="nav nav-pills flex-column">
                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}" href="{% url 'home' %}">
                                <i class="fas fa-tachometer-alt me-2"></i>
                                <span>Dashboard</span>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link sidebar-menu-toggle {% if request.resolver_match.url_name == 'inventory_list' or request.resolver_match.url_name == 'medicine_list' %}active{% endif %}"
                               data-bs-toggle="collapse"
                               href="#collapseInventory"
                               role="button"
                               aria-expanded="{% if 'inventory' in request.path or 'medicine' in request.path %}true{% else %}false{% endif %}"
                               aria-controls="collapseInventory">
                                <i class="fas fa-boxes me-2"></i>
                                <span>Inventory</span>
                                <i class="fas fa-chevron-right submenu-arrow"></i>
                            </a>
                            <div class="collapse {% if 'inventory' in request.path or 'medicine' in request.path %}show{% endif %}" id="collapseInventory">
                                <ul class="nav nav-pills flex-column submenu">
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if request.resolver_match.url_name == 'inventory_list' %}active{% endif %}" href="{% url 'inventory_list' %}">
                                            <i class="fas fa-list-alt me-2"></i>
                                            <span>Inventory Overview</span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if request.resolver_match.url_name == 'medicine_list' %}active{% endif %}" href="{% url 'medicine_list' %}">
                                            <i class="fas fa-pills me-2"></i>
                                            <span>Medicines</span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if request.resolver_match.url_name == 'create_medicine' %}active{% endif %}" href="{% url 'create_medicine' %}">
                                            <i class="fas fa-plus-circle me-2"></i>
                                            <span>Add Medicine</span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if request.resolver_match.url_name == 'stock_movement_list' %}active{% endif %}" href="{% url 'stock_movement_list' %}">
                                            <i class="fas fa-exchange-alt me-2"></i>
                                            <span>Stock Movements</span>
                                        </a>
                                    </li>

                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if request.resolver_match.url_name == 'movement_analysis_dashboard' %}active{% endif %}" href="{% url 'movement_analysis_dashboard' %}">
                                            <i class="fas fa-chart-line me-2"></i>
                                            <span>Movement Analysis</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link sidebar-menu-toggle {% if 'transaction' in request.path %}active{% endif %}"
                               data-bs-toggle="collapse"
                               href="#collapseTransactions"
                               role="button"
                               aria-expanded="{% if 'transaction' in request.path %}true{% else %}false{% endif %}"
                               aria-controls="collapseTransactions">
                                <i class="fas fa-exchange-alt me-2"></i>
                                <span>Transactions</span>
                                <i class="fas fa-chevron-right submenu-arrow"></i>
                            </a>
                            <div class="collapse {% if 'transaction' in request.path %}show{% endif %}" id="collapseTransactions">
                                <ul class="nav nav-pills flex-column submenu">
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if request.resolver_match.url_name == 'transaction_list' and not request.GET.type %}active{% endif %}" href="{% url 'transaction_list' %}">
                                            <i class="fas fa-list me-2"></i>
                                            <span>All Transactions</span>
                                        </a>


                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if request.resolver_match.url_name == 'create_transaction' %}active{% endif %}" href="{% url 'create_transaction' %}">
                                            <i class="fas fa-plus-circle me-2"></i>
                                            <span>New Transaction</span>
                                        </a>
                                    </li>
                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link sidebar-menu-toggle {% if 'forecast' in request.path or 'analysis' in request.path %}active{% endif %}"
                               data-bs-toggle="collapse"
                               href="#collapseAnalytics"
                               role="button"
                               aria-expanded="{% if 'forecast' in request.path or 'analysis' in request.path %}true{% else %}false{% endif %}"
                               aria-controls="collapseAnalytics">
                                <i class="fas fa-chart-line me-2"></i>
                                <span>Analytics</span>
                                <i class="fas fa-chevron-right submenu-arrow"></i>
                            </a>
                            <div class="collapse {% if 'forecast' in request.path or 'analysis' in request.path %}show{% endif %}" id="collapseAnalytics">
                                <ul class="nav nav-pills flex-column submenu">
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if request.resolver_match.url_name == 'forecast_list' %}active{% endif %}" href="{% url 'forecast_list' %}">
                                            <i class="fas fa-chart-bar me-2"></i>
                                            <span>Forecasts</span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if request.resolver_match.url_name == 'medicine_analysis' %}active{% endif %}" href="{% url 'medicine_analysis' %}">
                                            <i class="fas fa-microscope me-2"></i>
                                            <span>Medicine Analysis</span>
                                        </a>
                                    </li>

                                </ul>
                            </div>
                        </li>

                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'audit_trail_list' %}active{% endif %}" href="{% url 'audit_trail_list' %}">
                                <i class="fas fa-history me-2"></i>
                                <span>Audit Trail</span>
                            </a>
                        </li>


                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'reports_dashboard' %}active{% endif %}" href="{% url 'reports_dashboard' %}">
                                <i class="fas fa-file-alt me-2"></i>
                                <span>Reports</span>
                            </a>
                        </li>


                        <li class="nav-item">
                            <a class="nav-link {% if request.resolver_match.url_name == 'about_us' %}active{% endif %}" href="{% url 'about_us' %}">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>About</span>
                            </a>
                        </li>


                        {% if user.is_staff %}
                        <li class="nav-item">
                            <a class="nav-link sidebar-menu-toggle {% if 'admin' in request.path or 'users' in request.path %}active{% endif %}"
                               data-bs-toggle="collapse"
                               href="#collapseAdmin"
                               role="button"
                               aria-expanded="{% if 'admin' in request.path or 'users' in request.path %}true{% else %}false{% endif %}"
                               aria-controls="collapseAdmin">
                                <i class="fas fa-user-shield me-2"></i>
                                <span>Administration</span>
                                <i class="fas fa-chevron-right submenu-arrow"></i>
                            </a>
                            <div class="collapse {% if 'admin' in request.path or 'users' in request.path %}show{% endif %}" id="collapseAdmin">
                                <ul class="nav nav-pills flex-column submenu">
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link" href="/admin/">
                                            <i class="fas fa-cog me-2"></i>
                                            <span>Django Admin</span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if '/admin/user_management/' in request.path %}active{% endif %}" href="/admin/user_management/">
                                            <i class="fas fa-users me-2"></i>
                                            <span>User Management</span>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link submenu-link {% if 'customers' in request.path %}active{% endif %}" href="{% url 'customer_list' %}">
                                            <i class="fas fa-user-tag me-2"></i>
                                            <span>Customer Management</span>
                                        </a>
                                    </li>

                                </ul>
                            </div>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </div>
        <!-- Overlay for mobile sidebar -->
        <div class="sidebar-overlay" id="sidebarOverlay"></div>

        <!-- Main Content Area -->
        <div class="content-wrapper">
            <!-- Top Navigation Bar -->
            <div class="top-navbar">
                <button class="sidebar-toggle d-md-none" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>

                <h4 class="page-title d-none d-md-block">{% block page_title %} Medicine Inventory Forecast{% endblock %}</h4>

                <div class="ms-auto d-flex align-items-center">


                    <!-- Notifications Dropdown -->
                    <div class="dropdown me-3">
                        <a class="nav-link position-relative" href="#" id="notificationsDropdown"
                           role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-bell"></i>
                            <span id="notificationCount" class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                                <span class="notification-count">0</span>
                                <span class="visually-hidden">unread notifications</span>
                            </span>
                        </a>
                        <!-- Inside your notifications dropdown menu -->
                        <div class="dropdown-menu dropdown-menu-end notification-dropdown shadow-lg" aria-labelledby="notificationDropdown">
                            <div class="notification-list">
                                {% for notification in notifications %}
                                <div class="notification-item d-flex align-items-start p-3 border-bottom {% if not notification.is_read %}unread{% endif %}"
                                     data-notification-id="{{ notification.id }}"
                                     data-notification-type="{{ notification.notification_type }}"
                                     data-medicine-id="{{ notification.medicine_id|default:'' }}"
                                     data-url="{{ notification.url|default:'#' }}">
                                    <div class="notification-icon me-3">
                                        <i class="fas fa-{{ notification.get_icon }} notification-type-{{ notification.notification_type|lower }}"></i>
                                    </div>
                                    <div class="notification-content flex-grow-1">
                                        <div class="d-flex justify-content-between align-items-center mb-1">
                                            <h6 class="notification-title mb-0">{{ notification.title }}</h6>
                                            <small class="text-muted">{{ notification.created_at|timesince }}</small>
                                        </div>
                                        <p class="notification-message mb-1">{{ notification.message }}</p>
                                        <div class="notification-actions">
                                            {% if notification.medicine_id %}
                                            <a href="#"
                                               class="btn btn-sm btn-link text-primary p-0 view-details-link"
                                               data-bs-toggle="modal"
                                               data-bs-target="#notificationDetailModal"
                                               data-notification-id="{{ notification.id }}"
                                               data-medicine-id="{{ notification.medicine_id }}">
                                                View Details <i class="fas fa-arrow-right ms-1"></i>
                                            </a>
                                            {% elif notification.url %}
                                            <a href="{{ notification.url }}"
                                               class="btn btn-sm btn-link text-primary p-0">
                                                View Details <i class="fas fa-arrow-right ms-1"></i>
                                            </a>
                                            {% endif %}
                                        </div>
                                    </div>
                                    <button class="btn btn-link text-danger delete-notification p-0 ms-2"
                                            data-notification-id="{{ notification.id }}">
                                        <i class="fas fa-times"></i>
                                    </button>
                                </div>
                                {% empty %}
                                <div class="text-center p-3">
                                    <i class="fas fa-bell-slash text-muted mb-2" style="font-size: 1.5rem;"></i>
                                    <p class="text-muted mb-0">No notifications</p>
                                </div>
                                {% endfor %}
                            </div>
                            <div class="notification-footer text-center p-2 border-top">
                                <a href="{% url 'notifications' %}" class="text-primary">View All Notifications</a>
                            </div>
                        </div>
                    </div>
                    <!-- User Profile Dropdown -->
                    <div class="dropdown">
                        <a class="nav-link d-flex align-items-center" href="#" id="userDropdown"
                           role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            {% if user.is_authenticated %}
                            {% if user.profile.profile_picture %}
                            <img src="{{ user.profile.profile_picture.url }}"
                                 class="rounded-circle border"
                                 width="32" height="32"
                                 alt="{{ user.username }}"
                                 style="object-fit: cover;">
                            {% else %}
                            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center"
                                 style="width: 32px; height: 32px; border: 1px solid rgba(0, 0, 0, 0.1);">
                                {{ user.username|make_list|first|upper }}
                            </div>
                            {% endif %}
                            <div class="d-flex flex-column ms-2">
                                <span class="fw-semibold" style="font-size: 0.9rem; color: var(--text-primary);">{{ user.get_full_name|default:user.username }}</span>
                            </div>
                            {% else %}
                            <!-- Default avatar for non-logged in users -->
                            <div class="rounded-circle bg-secondary text-white d-flex align-items-center justify-content-center"
                                 style="width: 32px; height: 32px; border: 1px solid rgba(0, 0, 0, 0.1);">
                                <i class="fas fa-user"></i>
                            </div>
                            <span class="ms-2" style="font-size: 0.9rem; color: var(--text-secondary);">Guest</span>
                            {% endif %}
                        </a>
                        <!-- MEDUSA-style dropdown menu with proper styling -->
                        <ul class="dropdown-menu dropdown-menu-end"
                            aria-labelledby="userDropdown"
                            style="min-width: 200px;
               border-radius: var(--border-radius-md);
               border: 1px solid var(--border-color, #dee2e6);
               box-shadow: 0 6px 12px rgba(0, 0, 0, .175);
               padding: 5px 0;
               margin: 2px 0 0;
               font-size: 0.9rem;">
                            {% if user.is_authenticated %}
                            <li>
                                <a class="dropdown-item" href="{% url 'profile' %}"
                                   style="padding: 8px 20px; color: var(--text-primary, #212529); white-space: nowrap; font-weight: 400;">
                                    <i class="fas fa-user me-2" style="width: 16px; text-align: center; color: var(--text-secondary, #6c757d);"></i>
                                    Profile
                                </a>
                            </li>
                            <li><hr class="dropdown-divider" style="margin: 8px 0; border-top: 1px solid var(--border-color, #dee2e6);"></li>
                            <li>
                                <form method="post" action="{% url 'logout' %}">
                                    {% csrf_token %}
                                    <button type="submit" class="dropdown-item"
                                            style="padding: 8px 20px; width: 100%; text-align: left; background: none; border: none; cursor: pointer; color: var(--danger-color, #dc3545); font-weight: 400;">
                                        <i class="fas fa-sign-out-alt me-2" style="width: 16px; text-align: center;"></i>
                                        Logout
                                    </button>
                                </form>
                            </li>
                            {% else %}
                            <li>
                                <a class="dropdown-item" href="{% url 'login' %}"
                                   style="padding: 8px 20px; color: var(--text-primary, #212529); white-space: nowrap; font-weight: 400;">
                                    <i class="fas fa-sign-in-alt me-2" style="width: 16px; text-align: center; color: var(--text-secondary, #6c757d);"></i>
                                    Login
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'register' %}"
                                   style="padding: 8px 20px; color: var(--text-primary, #212529); white-space: nowrap; font-weight: 400;">
                                    <i class="fas fa-user-plus me-2" style="width: 16px; text-align: center; color: var(--text-secondary, #6c757d);"></i>
                                    Register
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Global Search Component -->
            <div id="searchContainer" class="search-container position-relative">
                <div class="input-group">
                    <span class="input-group-text bg-transparent border-end-0">
                        <i class="fas fa-search text-muted"></i>
                    </span>
                    <input type="text" id="globalSearch" class="form-control border-start-0 ps-0" placeholder="Search medicines, reports, transactions..." aria-label="Search">
                    <button class="btn btn-outline-secondary search-clear border-start-0" type="button">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <!-- Search Results Container -->
                <div id="searchResults" class="search-results shadow">
                    <!-- Results will be populated by JavaScript -->
                </div>
                <!-- Main Content -->
                <div class="main-content">
                    {% if messages %}
                    <div class="container-fluid mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    {% block content %}{% endblock %}
                </div>

                <footer class="footer">
                    <div class="container">
                        <div class="row">
                            <div class="col-12 text-center py-3">
                                <span class="text-muted">© {% now "Y" %} BMC Medforecast. All rights reserved.</span>
                            </div>
                        </div>
                    </div>
                </footer>
            </div>
        </div>
    </div>
    <!-- JavaScript -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <script>



        /**
         * MedInventory Enhanced Sidebar and Notification System
         * Provides improved sidebar functionality, search system, and real-time notifications
         * Version 1.0
         */

        document.addEventListener('DOMContentLoaded', function () {
            // Initialize the enhanced sidebar and notification manager
            window.app = new AppManager();

            // Initialize Bootstrap tooltips and popovers
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
            popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl);
            });
        });

        /**
         * Main App Manager Class
         * Coordinates between sidebar, notifications, and other modules
         */
        class AppManager {
            // Add a utility method to close all submenus except the current one
            static closeOtherSubmenus(currentSubmenuId) {
                const openSubmenus = document.querySelectorAll('.sidebar-wrapper .collapse.show');
                openSubmenus.forEach(submenu => {
                    // Skip the current target
                    if (!currentSubmenuId || submenu.id !== currentSubmenuId) {
                        // Find the toggle for this submenu
                        const submenuToggle = document.querySelector(`[href="#${submenu.id}"]`);
                        if (submenuToggle) {
                            // Try to use Bootstrap's collapse API
                            try {
                                const bsCollapse = bootstrap.Collapse.getInstance(submenu);
                                if (bsCollapse) {
                                    bsCollapse.hide();
                                } else {
                                    // Fallback if no instance exists
                                    submenu.classList.remove('show');
                                }
                            } catch (e) {
                                // Direct DOM manipulation fallback
                                submenu.classList.remove('show');
                            }
                            // Update aria-expanded attribute
                            submenuToggle.setAttribute('aria-expanded', 'false');
                        }
                    }
                });
            }
            constructor() {
                // Initialize components
                this.sidebar = new EnhancedSidebarManager();
                this.notifications = new NotificationSystem();

                // Make methods accessible to window for global access
                window.toggleSidebar = this.sidebar.toggleSidebar.bind(this.sidebar);
                window.showToast = this.showToast.bind(this);
                window.refreshNotificationDropdown = this.notifications.fetchNotifications.bind(this.notifications);
            }

            /**
             * Show toast notification
             * @param {string} title - Toast title
             * @param {string} message - Toast message
             * @param {string} type - Toast type (success, info, warning, error)
             */
            showToast(title, message, type = 'info') {
                // Create toast container if it doesn't exist
                let toastContainer = document.getElementById('toast-container');
                if (!toastContainer) {
                    toastContainer = document.createElement('div');
                    toastContainer.id = 'toast-container';
                    toastContainer.className = 'position-fixed top-0 end-0 p-3';
                    toastContainer.style.zIndex = '1060';
                    document.body.appendChild(toastContainer);
                }

                // Create a unique ID for this toast
                const toastId = 'toast-' + Date.now();

                // Get appropriate icon and color for toast type
                let iconClass, bgClass;
                switch (type) {
                    case 'high':
                    case 'danger':
                    case 'error':
                        iconClass = 'fas fa-exclamation-circle';
                        bgClass = 'bg-danger text-white';
                        break;
                    case 'medium':
                    case 'warning':
                        iconClass = 'fas fa-exclamation-triangle';
                        bgClass = 'bg-warning';
                        break;
                    case 'success':
                        iconClass = 'fas fa-check-circle';
                        bgClass = 'bg-success text-white';
                        break;
                    case 'low':
                    case 'info':
                    default:
                        iconClass = 'fas fa-info-circle';
                        bgClass = 'bg-info text-white';
                }

                // Create toast HTML
                const toastHtml = `
                                    <div id="${toastId}" class="toast" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="5000">
                                        <div class="toast-header ${bgClass}">
                                            <i class="${iconClass} me-2"></i>
                                            <strong class="me-auto">${title}</strong>
                                            <small>Just now</small>
                                            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
                                        </div>
                                        <div class="toast-body">
                                            ${message}
                                        </div>
                                    </div>
                                    `;

                // Add toast to container
                toastContainer.insertAdjacentHTML('beforeend', toastHtml);

                // Initialize and show toast
                const toastElement = document.getElementById(toastId);

                // Create Bootstrap toast
                try {
                    const bsToast = new bootstrap.Toast(toastElement);
                    bsToast.show();
                } catch (e) {
                    console.error("Error showing toast:", e);
                    // Fallback if Bootstrap's JavaScript isn't available
                    toastElement.style.display = 'block';
                    setTimeout(() => {
                        toastElement.remove();
                    }, 5000);
                }

                // Remove toast from DOM after it's hidden
                toastElement.addEventListener('hidden.bs.toast', () => {
                    toastElement.remove();
                });

                return toastElement;
            }
        }

        /**
         * Enhanced Sidebar Manager Class
         * Handles sidebar functionality, search system, and collapsible behavior
         */
        class EnhancedSidebarManager {
            constructor() {
                // Core elements
                this.sidebarWrapper = document.getElementById('sidebar-wrapper');
                this.sidebarToggle = document.getElementById('sidebarToggle');
                this.sidebarClose = document.getElementById('sidebarClose');
                this.sidebarOverlay = document.getElementById('sidebarOverlay');
                this.contentWrapper = document.querySelector('.content-wrapper');

                // Need to add burger toggle if it doesn't exist - but only on desktop
                this.removeDuplicateBurgers();
                if (!document.getElementById('sidebarBurger') && window.innerWidth >= 992) {
                    this.createSidebarBurger();
                }
                this.sidebarBurger = document.getElementById('sidebarBurger');

                // Search elements
                this.searchContainer = document.getElementById('searchContainer');
                this.searchInput = document.getElementById('globalSearch');
                this.searchResults = document.getElementById('searchResults');

                // Configuration
                this.breakpoint = 992; // Breakpoint for mobile view (matches Bootstrap lg)
                this.storageKey = 'medInventorySidebarState';
                this.collapsedWidth = 70; // Width in px when sidebar is collapsed
                this.expandedWidth = 260; // Width in px when sidebar is expanded

                // Mouse tracking configuration - ENABLED for hover functionality
                this.mouseTrackingEnabled = true;
                this.mouseLeaveDelay = 800; // Delay before collapsing on mouse leave (ms)
                this.mouseEnterDelay = 200; // Delay before expanding on mouse enter (ms)
                this.mouseLeaveTimer = null;
                this.mouseEnterTimer = null;
                this.isMouseInSidebar = false;
                this.isHoveringToggleZone = false;
                this.sidebarLocked = false; // Whether the sidebar is locked in its current state

                this.init();
            }

            init() {
                // Set up CSS variables for sidebar widths
                document.documentElement.style.setProperty('--sidebar-width', `${this.expandedWidth}px`);
                document.documentElement.style.setProperty('--collapsed-sidebar-width', `${this.collapsedWidth}px`);

                // Initialize event listeners
                this.initEventListeners();

                // Apply saved state (for desktop only)
                if (window.innerWidth >= this.breakpoint) {
                    this.applySavedState();
                }

                // Initialize mouse tracking for hover functionality (desktop only)
                if (this.mouseTrackingEnabled && window.innerWidth >= this.breakpoint) {
                    this.initMouseTracking();
                }

                // Initialize search functionality if search container exists
                if (this.searchContainer) {
                    this.initSearch();
                } else {
                    this.createSearchContainer();
                }

                // Add additional styles
                this.addAdditionalStyles();

                // Add nav link titles for tooltips
                this.addNavLinkTitles();

                // Handle window resize event - reattach listener to ensure it's not duplicated
                window.removeEventListener('resize', this.handleWindowResize.bind(this));
                window.addEventListener('resize', this.handleWindowResize.bind(this));
            }

            // Remove any duplicate burger buttons that might exist
            removeDuplicateBurgers() {
                const burgers = document.querySelectorAll('#sidebarBurger');
                if (burgers.length > 1) {
                    // Keep only the first one
                    for (let i = 1; i < burgers.length; i++) {
                        burgers[i].remove();
                    }
                }
            }

            createSidebarBurger() {
                // Create burger toggle button if it doesn't exist
                const navbar = document.querySelector('.top-navbar');
                if (navbar) {
                    const burgerBtn = document.createElement('button');
                    burgerBtn.id = 'sidebarBurger';
                    burgerBtn.className = 'btn btn-link sidebar-burger d-none d-lg-block'; // Hide on mobile
                    burgerBtn.setAttribute('title', 'Toggle Sidebar');
                    burgerBtn.innerHTML = '<i class="fas fa-bars"></i>';

                    // Find a good position to insert it
                    const pageTitle = navbar.querySelector('.page-title');
                    if (pageTitle) {
                        pageTitle.parentNode.insertBefore(burgerBtn, pageTitle);
                    } else {
                        navbar.appendChild(burgerBtn);
                    }

                    // Add styles to the head
                    const style = document.createElement('style');
                    style.textContent = `
                                        .sidebar-burger {
                                            padding: 0;
                                            color: var(--primary-color);
                                            margin-right: 10px;
                                            transition: transform 0.3s;
                                            z-index: 1100;
                                            position: relative;
                                        }
                                        /* Hide desktop burger on mobile */
                                        @media (max-width: 991.98px) {
                                            .sidebar-burger.d-none.d-lg-block {
                                                display: none !important;
                                            }
                                        }
                                        `;
                    document.head.appendChild(style);
                }
            }

            initEventListeners() {
                // Mobile sidebar toggle button
                if (this.sidebarToggle) {
                    // Remove existing listener to prevent duplicates
                    const newToggle = this.sidebarToggle.cloneNode(true);
                    if (this.sidebarToggle.parentNode) {
                        this.sidebarToggle.parentNode.replaceChild(newToggle, this.sidebarToggle);
                    }
                    this.sidebarToggle = newToggle;
                    this.sidebarToggle.addEventListener('click', () => this.toggleSidebar());
                }

                // Sidebar burger icon toggle with proper lock handling
                if (this.sidebarBurger) {
                    // Remove existing listener to prevent duplicates
                    const newBurger = this.sidebarBurger.cloneNode(true);
                    if (this.sidebarBurger.parentNode) {
                        this.sidebarBurger.parentNode.replaceChild(newBurger, this.sidebarBurger);
                    }
                    this.sidebarBurger = newBurger;
                    this.sidebarBurger.addEventListener('click', () => {
                        // Toggle the sidebar
                        this.toggleSidebar();
                        // Toggle locked state - must be done AFTER the toggle since toggleSidebar can change the lock
                        this.sidebarLocked = !this.sidebarLocked;
                        this.updateLockIndicator();
                        // Show feedback about locked state
                        if (this.sidebarLocked) {
                            window.app.showToast('Sidebar Locked', 'Sidebar will stay in this state until you click the burger icon again', 'info');
                        } else {
                            window.app.showToast('Sidebar Unlocked', 'Sidebar will now respond to hover', 'info');
                        }
                        // Save the locked state
                        this.saveState(this.sidebarWrapper.classList.contains('collapsed'));
                    });
                }

                // Close button in sidebar
                if (this.sidebarClose) {
                    this.sidebarClose.addEventListener('click', () => this.toggleSidebar(false));
                }

                // Overlay click to close sidebar
                if (this.sidebarOverlay) {
                    this.sidebarOverlay.addEventListener('click', () => this.toggleSidebar(false));
                }

                // Menu items handling
                this.initMenuEventListeners();

                // Escape key to close sidebar on mobile
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'Escape' && window.innerWidth < this.breakpoint &&
                        this.sidebarWrapper.classList.contains('show')) {
                        this.toggleSidebar(false);
                    }
                });
            }

            initMouseTracking() {
                if (!this.sidebarWrapper) return;

                console.log("Initializing mouse tracking for hover functionality");

                // Create left edge hover zone for collapsed sidebar
                this.createHoverZone();

                // Track mouse movement for auto expand/collapse
                const addHoverListeners = () => {
                    // First remove any existing listeners
                    this.sidebarWrapper.removeEventListener('mouseenter', this.onSidebarMouseEnter);
                    this.sidebarWrapper.removeEventListener('mouseleave', this.onSidebarMouseLeave);

                    // Define the handler functions with proper this binding
                    this.onSidebarMouseEnter = () => {
                        this.isMouseInSidebar = true;
                        clearTimeout(this.mouseLeaveTimer);
                        // Only auto-expand if sidebar is collapsed and not locked
                        if (this.sidebarWrapper.classList.contains('collapsed') && !this.sidebarLocked) {
                            this.mouseEnterTimer = setTimeout(() => {
                                this.toggleSidebar(true); // Expand
                            }, this.mouseEnterDelay);
                        }
                    };

                    this.onSidebarMouseLeave = () => {
                        this.isMouseInSidebar = false;
                        clearTimeout(this.mouseEnterTimer);
                        // Only auto-collapse if sidebar is expanded and not locked
                        if (!this.sidebarWrapper.classList.contains('collapsed') && !this.sidebarLocked) {
                            this.mouseLeaveTimer = setTimeout(() => {
                                if (!this.isMouseInSidebar && !this.isHoveringToggleZone) {
                                    this.toggleSidebar(false); // Collapse
                                }
                            }, this.mouseLeaveDelay);
                        }
                    };

                    // Add the listeners
                    this.sidebarWrapper.addEventListener('mouseenter', this.onSidebarMouseEnter);
                    this.sidebarWrapper.addEventListener('mouseleave', this.onSidebarMouseLeave);
                };

                // Add the hover listeners
                addHoverListeners();

                // Double-click sidebar header to lock/unlock
                const sidebarHeader = this.sidebarWrapper.querySelector('.sidebar-header');
                if (sidebarHeader) {
                    // Remove existing listener
                    const newHeader = sidebarHeader.cloneNode(true);
                    if (sidebarHeader.parentNode) {
                        sidebarHeader.parentNode.replaceChild(newHeader, sidebarHeader);
                    }
                    newHeader.addEventListener('dblclick', () => {
                        this.sidebarLocked = !this.sidebarLocked;
                        // Show feedback
                        if (this.sidebarLocked) {
                            window.app.showToast('Sidebar Locked', 'Sidebar will stay in this state until you double-click the header again', 'info');
                        } else {
                            window.app.showToast('Sidebar Unlocked', 'Sidebar will now respond to hover', 'info');
                        }
                        // Add visual indicator for locked state
                        this.updateLockIndicator();
                        // Save the locked state
                        this.saveState(this.sidebarWrapper.classList.contains('collapsed'));
                    });
                }
            }

            createHoverZone() {
                // Remove any existing hover zone
                const existingZone = document.querySelector('.sidebar-hover-zone');
                if (existingZone) existingZone.remove();

                // Create left edge hover zone for collapsed sidebar
                const hoverZone = document.createElement('div');
                hoverZone.className = 'sidebar-hover-zone';
                hoverZone.style.position = 'fixed';
                hoverZone.style.left = '0';
                hoverZone.style.top = '0';
                hoverZone.style.width = '15px';
                hoverZone.style.height = '100%';
                hoverZone.style.zIndex = '1000';
                document.body.appendChild(hoverZone);

                // Define event handlers with proper this binding
                const onHoverZoneEnter = () => {
                    this.isHoveringToggleZone = true;
                    // Only auto-expand if sidebar is collapsed and not locked
                    if (this.sidebarWrapper &&
                        this.sidebarWrapper.classList.contains('collapsed') &&
                        !this.sidebarLocked) {
                        this.mouseEnterTimer = setTimeout(() => {
                            this.toggleSidebar(true); // Expand
                        }, this.mouseEnterDelay);
                    }
                };

                const onHoverZoneLeave = () => {
                    this.isHoveringToggleZone = false;
                    clearTimeout(this.mouseEnterTimer);
                };

                // Add event listeners
                hoverZone.addEventListener('mouseenter', onHoverZoneEnter);
                hoverZone.addEventListener('mouseleave', onHoverZoneLeave);

                // Only show hover zone on desktop
                const style = document.createElement('style');
                style.textContent = `
                                    .sidebar-hover-zone {
                                        background: transparent;
                                        cursor: pointer;
                                        display: block;
                                    }
                                    @media (max-width: 991.98px) {
                                        .sidebar-hover-zone {
                                            display: none !important;
                                        }
                                    }
                                    `;
                document.head.appendChild(style);
            }

            updateLockIndicator() {
                if (!this.sidebarWrapper) return;

                // Remove any existing indicator
                const existingIndicator = this.sidebarWrapper.querySelector('.sidebar-lock-indicator');
                if (existingIndicator) existingIndicator.remove();

                if (this.sidebarLocked) {
                    // Add a visual indicator that sidebar is locked
                    const lockIndicator = document.createElement('div');
                    lockIndicator.className = 'sidebar-lock-indicator';
                    lockIndicator.innerHTML = '<i class="fas fa-lock"></i>';
                    lockIndicator.title = 'Sidebar is locked. Click burger icon or double-click header to unlock.';

                    // Style the indicator
                    lockIndicator.style.position = 'absolute';
                    lockIndicator.style.top = '10px';
                    lockIndicator.style.right = '10px';
                    lockIndicator.style.background = 'rgba(0,0,0,0.3)';
                    lockIndicator.style.color = 'white';
                    lockIndicator.style.padding = '5px';
                    lockIndicator.style.borderRadius = '50%';
                    lockIndicator.style.width = '24px';
                    lockIndicator.style.height = '24px';
                    lockIndicator.style.display = 'flex';
                    lockIndicator.style.alignItems = 'center';
                    lockIndicator.style.justifyContent = 'center';
                    lockIndicator.style.fontSize = '12px';
                    lockIndicator.style.zIndex = '1001';

                    this.sidebarWrapper.appendChild(lockIndicator);
                }
            }

            initMenuEventListeners() {
                // Collapse menu items click handling
                const menuToggles = document.querySelectorAll('.sidebar-menu-toggle');
                menuToggles.forEach(toggle => {
                    toggle.addEventListener('click', (e) => {
                        // Prevent closing on mobile when toggling menus
                        if (window.innerWidth < this.breakpoint) {
                            e.stopPropagation();
                        }

                        // Close other open submenus when opening a new one
                        const targetId = toggle.getAttribute('href');
                        const targetCollapse = document.querySelector(targetId);
                        const isExpanding = !targetCollapse.classList.contains('show');

                        if (isExpanding) {
                            // Use the utility method to close other submenus
                            AppManager.closeOtherSubmenus(targetCollapse.id);
                        }
                    });
                });

                // Menu item clicks on mobile - close sidebar
                const menuLinks = document.querySelectorAll('.sidebar-body .nav-link:not(.sidebar-menu-toggle)');
                menuLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        if (window.innerWidth < this.breakpoint) {
                            setTimeout(() => this.toggleSidebar(false), 100);
                        }
                    });
                });
            }

            toggleSidebar(show = undefined) {
                // Make sure the sidebar wrapper exists
                if (!this.sidebarWrapper) {
                    console.error("Sidebar wrapper not found");
                    return;
                }

                console.log("Toggling sidebar:", show);

                if (window.innerWidth < this.breakpoint) {
                    // Mobile behavior - slide in/out
                    const mobileShow = show === undefined ? !this.sidebarWrapper.classList.contains('show') : show;
                    if (mobileShow) {
                        this.sidebarWrapper.classList.add('show');
                        if (this.sidebarOverlay) this.sidebarOverlay.classList.add('show');
                        document.body.style.overflow = 'hidden'; // Prevent body scrolling
                    } else {
                        this.sidebarWrapper.classList.remove('show');
                        if (this.sidebarOverlay) this.sidebarOverlay.classList.remove('show');
                        document.body.style.overflow = ''; // Restore body scrolling

                        // Close all open submenus when closing the sidebar on mobile
                        setTimeout(() => {
                            AppManager.closeOtherSubmenus();
                        }, 300); // Small delay to ensure smooth animation
                    }
                } else {
                    // Desktop behavior - toggle between collapsed and expanded
                    const isCollapsed = this.sidebarWrapper.classList.contains('collapsed');

                    // If show is undefined (default), we toggle the current state
                    // If show is true, we make sure the sidebar is expanded
                    // If show is false, we make sure the sidebar is collapsed
                    const shouldCollapse = show === undefined ? !isCollapsed : !show;

                    if (shouldCollapse) {
                        // Collapse sidebar
                        this.sidebarWrapper.classList.add('collapsed');
                        // Adjust sidebar elements visibility
                        this.adjustSidebarForCollapsed(true);
                        // Update content wrapper
                        if (this.contentWrapper) {
                            this.contentWrapper.style.marginLeft = 'var(--collapsed-sidebar-width)';
                            this.contentWrapper.style.width = 'calc(100% - var(--collapsed-sidebar-width))';
                        }
                        // Update burger icon (now we use fa-bars for both states for consistency)
                        if (this.sidebarBurger) {
                            this.sidebarBurger.innerHTML = '<i class="fas fa-bars"></i>';
                            this.sidebarBurger.setAttribute('title', 'Expand Sidebar');
                        }
                    } else {
                        // Expand sidebar
                        this.sidebarWrapper.classList.remove('collapsed');
                        // Restore sidebar elements visibility
                        this.adjustSidebarForCollapsed(false);
                        // Update content wrapper
                        if (this.contentWrapper) {
                            this.contentWrapper.style.marginLeft = 'var(--sidebar-width)';
                            this.contentWrapper.style.width = 'calc(100% - var(--sidebar-width))';
                        }
                        // Update burger icon (now we use fa-bars for both states for consistency)
                        if (this.sidebarBurger) {
                            this.sidebarBurger.innerHTML = '<i class="fas fa-bars"></i>';
                            this.sidebarBurger.setAttribute('title', 'Collapse Sidebar');
                        }
                    }

                    // Save state to localStorage
                    if (show === undefined) {
                        this.saveState(shouldCollapse);
                    }
                }
            }

            adjustSidebarForCollapsed(isCollapsed) {
                if (!this.sidebarWrapper) return;

                // Adjust sidebar elements visibility and appearance when collapsed/expanded
                const sidebarBrand = this.sidebarWrapper.querySelector('.sidebar-brand');
                const sidebarLogoText = this.sidebarWrapper.querySelector('.sidebar-logo-text');
                const navLinks = this.sidebarWrapper.querySelectorAll('.nav-link');
                const submenuArrows = this.sidebarWrapper.querySelectorAll('.submenu-arrow');
                const userInfo = this.sidebarWrapper.querySelector('.user-info');

                if (isCollapsed) {
                    // Hide text elements, show only icons
                    if (sidebarLogoText) sidebarLogoText.style.display = 'none';
                    // Center the icons in nav links
                    navLinks.forEach(link => {
                        link.style.justifyContent = 'center';
                        link.style.padding = '0.7rem';
                        // Hide text in nav links
                        const text = link.querySelector('span');
                        if (text) text.style.display = 'none';
                    });
                    // Hide submenu arrows
                    submenuArrows.forEach(arrow => arrow.style.display = 'none');
                    // Hide or adjust user info
                    if (userInfo) {
                        const userDetails = userInfo.querySelector('.user-details');
                        const userAvatar = userInfo.querySelector('.user-avatar');
                        const logoutBtn = userInfo.querySelector('button, .btn');
                        if (userDetails) userDetails.style.display = 'none';
                        if (userAvatar) userAvatar.style.margin = '0 auto';
                        if (logoutBtn) {
                            logoutBtn.innerHTML = '<i class="fas fa-sign-out-alt"></i>';
                            logoutBtn.classList.add('btn-icon');
                        }
                    }
                    // Collapse all open submenus
                    const openSubmenus = this.sidebarWrapper.querySelectorAll('.collapse.show');
                    openSubmenus.forEach(submenu => {
                        submenu.classList.remove('show');
                        const toggle = document.querySelector(`[data-bs-toggle="collapse"][href="#${submenu.id}"]`);
                        if (toggle) toggle.setAttribute('aria-expanded', 'false');
                    });
                } else {
                    // Restore text elements
                    if (sidebarLogoText) sidebarLogoText.style.display = '';
                    // Restore nav links
                    navLinks.forEach(link => {
                        link.style.justifyContent = '';
                        link.style.padding = '';
                        // Show text in nav links
                        const text = link.querySelector('span');
                        if (text) text.style.display = '';
                    });
                    // Show submenu arrows
                    submenuArrows.forEach(arrow => arrow.style.display = '');
                    // Restore user info
                    if (userInfo) {
                        const userDetails = userInfo.querySelector('.user-details');
                        const userAvatar = userInfo.querySelector('.user-avatar');
                        const logoutBtn = userInfo.querySelector('button, .btn');
                        if (userDetails) userDetails.style.display = '';
                        if (userAvatar) userAvatar.style.margin = '';
                        if (logoutBtn) {
                            logoutBtn.innerHTML = '<i class="fas fa-sign-out-alt me-2"></i>Logout';
                            logoutBtn.classList.remove('btn-icon');
                        }
                    }
                }
            }

            handleWindowResize() {
                const isDesktop = window.innerWidth >= this.breakpoint;

                // Remove duplicate burgers on resize
                this.removeDuplicateBurgers();

                // Switching to desktop
                if (isDesktop) {
                    // Reset mobile styling
                    document.body.style.overflow = '';
                    if (this.sidebarOverlay) this.sidebarOverlay.classList.remove('show');
                    if (this.sidebarWrapper) this.sidebarWrapper.classList.remove('show');

                    // Ensure burger is only visible on desktop
                    if (this.sidebarBurger) {
                        this.sidebarBurger.classList.remove('d-block');
                        this.sidebarBurger.classList.add('d-none', 'd-lg-block');
                    }

                    // Create burger if it doesn't exist for desktop
                    if (!document.getElementById('sidebarBurger')) {
                        this.createSidebarBurger();
                        // Re-initialize event listeners
                        this.initEventListeners();
                    }

                    // Apply saved collapse state
                    this.applySavedState();

                    // Re-initialize mouse tracking for hover functionality
                    if (this.mouseTrackingEnabled) {
                        this.initMouseTracking();
                    }

                    // Show hover zone
                    const hoverZone = document.querySelector('.sidebar-hover-zone');
                    if (hoverZone) hoverZone.style.display = 'block';
                } else {
                    // Switching to mobile

                    // Remove collapsed state for mobile
                    if (this.sidebarWrapper) this.sidebarWrapper.classList.remove('collapsed');

                    // Reset sidebar styles for mobile view
                    this.adjustSidebarForCollapsed(false);

                    // Reset content wrapper
                    if (this.contentWrapper) {
                        this.contentWrapper.style.marginLeft = '';
                        this.contentWrapper.style.width = '';
                    }

                    // Hide desktop burger on mobile
                    if (this.sidebarBurger) {
                        this.sidebarBurger.classList.remove('d-lg-block');
                        this.sidebarBurger.classList.add('d-none');
                    }

                    // Hide hover zone on mobile
                    const hoverZone = document.querySelector('.sidebar-hover-zone');
                    if (hoverZone) hoverZone.style.display = 'none';
                }
            }

            // Save sidebar state (collapsed or not) to localStorage
            saveState(isCollapsed) {
                try {
                    localStorage.setItem(this.storageKey, JSON.stringify({
                        collapsed: isCollapsed,
                        timestamp: new Date().getTime(),
                        locked: this.sidebarLocked
                    }));
                } catch (e) {
                    console.warn('Failed to save sidebar state:', e);
                }
            }


            // Apply saved state from localStorage
            applySavedState() {
                try {
                    const savedState = JSON.parse(localStorage.getItem(this.storageKey));

                    // Check if state exists and is not too old (24 hours max)
                    const isStateValid = savedState &&
                        savedState.timestamp &&
                        (new Date().getTime() - savedState.timestamp < 24 * 60 * 60 * 1000);

                    if (isStateValid) {
                        // Apply locked state if saved
                        if (savedState.hasOwnProperty('locked')) {
                            this.sidebarLocked = savedState.locked;
                            this.updateLockIndicator();
                        }

                        // Apply collapsed state if saved
                        if (savedState.collapsed && this.sidebarWrapper) {
                            this.sidebarWrapper.classList.add('collapsed');
                            this.adjustSidebarForCollapsed(true);

                            if (this.contentWrapper) {
                                this.contentWrapper.style.marginLeft = 'var(--collapsed-sidebar-width)';
                                this.contentWrapper.style.width = 'calc(100% - var(--collapsed-sidebar-width))';
                            }

                            // Update burger icon if it exists
                            if (this.sidebarBurger) {
                                this.sidebarBurger.innerHTML = '<i class="fas fa-bars"></i>';
                                this.sidebarBurger.setAttribute('title', 'Expand Sidebar');
                            }
                        }
                    }
                } catch (e) {
                    console.warn('Failed to apply saved sidebar state:', e);
                    // Clear potentially corrupted storage
                    localStorage.removeItem(this.storageKey);
                }
            }


            // Add additional styling for the sidebar
            addAdditionalStyles() {
                // Only add styles if they don't already exist
                if (document.getElementById('enhanced-sidebar-styles')) return;

                const style = document.createElement('style');
                style.id = 'enhanced-sidebar-styles';
                style.textContent = `
                                    /* Enhanced Sidebar Styles */
                                    .sidebar-wrapper {
                                        transition: width 0.3s ease, transform 0.3s ease;
                                        width: var(--sidebar-width);
                                        overflow: hidden;
                                    }
                                    .sidebar-wrapper.collapsed {
                                        width: var(--collapsed-sidebar-width);
                                    }
                                    /* Tooltip for icons in collapsed sidebar */
                                    .sidebar-wrapper.collapsed .nav-link {
                                        position: relative;
                                    }
                                    .sidebar-wrapper.collapsed .nav-link:hover::after {
                                        content: attr(data-title);
                                        position: absolute;
                                        left: 100%;
                                        top: 50%;
                                        transform: translateY(-50%);
                                        background: var(--primary-color);
                                        color: white;
                                        padding: 5px 10px;
                                        border-radius: 4px;
                                        font-size: 0.8rem;
                                        white-space: nowrap;
                                        z-index: 1070;
                                        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
                                    }
                                    /* Hover zone styling */
                                    .sidebar-hover-zone {
                                        background: transparent;
                                        cursor: pointer;
                                    }
                                    /* Button icon styling for collapsed sidebar */
                                    .btn-icon {
                                        width: 36px;
                                        height: 36px;
                                        display: flex !important;
                                        align-items: center;
                                        justify-content: center;
                                        padding: 0 !important;
                                        border-radius: 50%;
                                    }
                                    /* Smooth transition for content wrapper */
                                    .content-wrapper {
                                        transition: margin-left 0.3s ease, width 0.3s ease;
                                    }
                                    /* Mobile sidebar behavior */
                                    @media (max-width: 991.98px) {
                                        .sidebar-wrapper {
                                            transform: translateX(-100%);
                                            position: fixed;
                                            height: 100%;
                                            z-index: 1050;
                                            box-shadow: none;
                                        }
                                        .sidebar-wrapper.show {
                                            transform: translateX(0);
                                            box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
                                        }
                                        .sidebar-overlay {
                                            position: fixed;
                                            top: 0;
                                            left: 0;
                                            width: 100%;
                                            height: 100%;
                                            background-color: rgba(0, 0, 0, 0.5);
                                            z-index: 1040;
                                            display: none;
                                        }
                                        .sidebar-overlay.show {
                                            display: block;
                                        }
                                        /* Hide burger on mobile as it's redundant with the mobile toggle */
                                        .sidebar-burger {
                                            display: none !important;
                                        }
                                    }
                                    /* Tooltip for submenu items */
                                    .submenu-link .tooltip-text {
                                        visibility: hidden;
                                        background-color: var(--primary-color);
                                        color: white;
                                        text-align: center;
                                        padding: 5px 10px;
                                        border-radius: 4px;
                                        position: absolute;
                                        z-index: 1070;
                                        left: 100%;
                                        top: 50%;
                                        transform: translateY(-50%);
                                        opacity: 0;
                                        transition: opacity 0.3s;
                                        width: max-content;
                                        max-width: 250px;
                                    }
                                    .sidebar-wrapper.collapsed .submenu-link:hover .tooltip-text {
                                        visibility: visible;
                                        opacity: 1;
                                    }
                                    /* Lock indicator animations */
                                    .sidebar-lock-indicator {
                                        animation: pulse 2s infinite;
                                    }
                                    @keyframes pulse {
                                        0% { opacity: 0.7; }
                                        50% { opacity: 1; }
                                        100% { opacity: 0.7; }
                                    }
                                    `;

                document.head.appendChild(style);
            }

            // Add data-title attributes to nav links for collapsed sidebar tooltips
            addNavLinkTitles() {
                if (!this.sidebarWrapper) return;

                const navLinks = this.sidebarWrapper.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    // Skip if already has data-title
                    if (link.hasAttribute('data-title')) return;
                    // Find the text inside the nav link
                    const span = link.querySelector('span');
                    if (span) {
                        const title = span.textContent.trim();
                        link.setAttribute('data-title', title);
                    }
                });
            }

            // Initialize search functionality
            initSearch() {
                if (!this.searchInput || !this.searchResults) return;

                // Add search input behavior
                this.searchInput.addEventListener('keyup', (e) => {
                    // Show clear button when search has content
                    const clearBtn = this.searchContainer.querySelector('.search-clear');
                    if (clearBtn) {
                        clearBtn.style.display = this.searchInput.value ? 'block' : 'none';
                    }
                    // Perform search on Enter
                    if (e.key === 'Enter') {
                        this.performSearch(this.searchInput.value);
                    }
                });

                // Live search as user types (debounced)
                let searchTimeout;
                this.searchInput.addEventListener('input', () => {
                    clearTimeout(searchTimeout);
                    if (this.searchInput.value.length > 2) {
                        searchTimeout = setTimeout(() => {
                            this.performSearch(this.searchInput.value);
                        }, 300);
                    } else if (!this.searchInput.value) {
                        this.searchResults.classList.remove('show');
                    }
                });

                // Clear search button
                const clearBtn = this.searchContainer.querySelector('.search-clear');
                if (clearBtn) {
                    clearBtn.addEventListener('click', () => {
                        this.searchInput.value = '';
                        this.searchResults.classList.remove('show');
                        clearBtn.style.display = 'none';
                        this.searchInput.focus();
                    });
                }

                // Hide search results when clicking outside
                document.addEventListener('click', (e) => {
                    if (!e.target.closest('#searchContainer') && !e.target.closest('#searchResults')) {
                        this.searchResults.classList.remove('show');
                    }
                });
            }

            // Create search container if it doesn't exist
            createSearchContainer() {
                // Create search container if it doesn't exist
                const searchHTML = `
                                    <div id="searchContainer" class="search-container position-relative">
                                        <div class="input-group">
                                            <span class="input-group-text bg-transparent border-end-0">
                                                <i class="fas fa-search text-muted"></i>
                                            </span>
                                            <input type="text" id="globalSearch" class="form-control border-start-0 ps-0"
                                                placeholder="Search medicines, reports, transactions..." aria-label="Search">
                                            <button class="btn btn-outline-secondary search-clear border-start-0" type="button">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                        <div id="searchResults" class="search-results shadow">
                                            <!-- Results will be populated by JavaScript -->
                                        </div>
                                    </div>
                                    `;

                // Add search container to the page
                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = searchHTML;
                    mainContent.parentNode.insertBefore(tempDiv.firstElementChild, mainContent);

                    // Add search styles
                    const style = document.createElement('style');
                    style.textContent = `
                                        .search-container {
                                            padding: 15px;
                                            background-color: rgba(255, 255, 255, 0.9);
                                            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                                        }
                                        .search-clear {
                                            display: none;
                                        }
                                        .search-results {
                                            position: absolute;
                                            top: 100%;
                                            left: 0;
                                            right: 0;
                                            background: white;
                                            border-radius: 0 0 0.5rem 0.5rem;
                                            max-height: 400px;
                                            overflow-y: auto;
                                            z-index: 1050;
                                            display: none;
                                            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
                                        }
                                        .search-results.show {
                                            display: block;
                                        }
                                        .search-result-group {
                                            padding: 10px 0;
                                            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
                                        }
                                        .search-result-heading {
                                            padding: 5px 15px;
                                            font-weight: 600;
                                            color: var(--primary-color);
                                            font-size: 0.9rem;
                                        }
                                        .search-result-item {
                                            display: flex;
                                            align-items: center;
                                            padding: 10px 15px;
                                            color: #333;
                                            text-decoration: none;
                                            transition: background-color 0.2s;
                                        }
                                        .search-result-item:hover {
                                            background-color: rgba(0, 0, 0, 0.05);
                                        }
                                        .search-result-icon {
                                            margin-right: 10px;
                                            color: var(--secondary-color);
                                            font-size: 1.2rem;
                                            width: 20px;
                                            text-align: center;
                                        }
                                        .search-result-details {
                                            flex: 1;
                                        }
                                        .search-result-title {
                                            font-weight: 500;
                                        }
                                        .search-result-type {
                                            font-size: 0.8rem;
                                            color: #666;
                                        }
                                        .search-no-results {
                                            padding: 15px;
                                            text-align: center;
                                            color: #666;
                                        }
                                        `;
                    document.head.appendChild(style);

                    // Update references to the newly created elements
                    this.searchContainer = document.getElementById('searchContainer');
                    this.searchInput = document.getElementById('globalSearch');
                    this.searchResults = document.getElementById('searchResults');

                    // Initialize search
                    this.initSearch();
                }
            }

            // Perform search and display results
            performSearch(query) {
                if (!query || !query.trim() || query.length < 2) return;
                query = query.trim().toLowerCase();
                if (!this.searchResults) return;

                // AJAX request to the server for search results
                fetch(`/api/search/?query=${encodeURIComponent(query)}`, {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'Content-Type': 'application/json'
                    }
                })
                    .then(response => response.json())
                    .then(results => {
                        this.displaySearchResults(results, query);
                    })
                    .catch(error => {
                        console.error('Search error:', error);
                        // Fallback with placeholder results
                        this.displaySearchResults({
                            medicines: [
                                { id: 123, name: 'Paracetamol', description: 'Pain reliever and fever reducer' },
                                { id: 456, name: 'Amoxicillin', description: 'Antibiotic medication' }
                            ],
                            reports: [
                                { id: 789, title: 'Monthly Stock Report', url: '/reports/stock', description: 'Inventory status as of this month' }
                            ],
                            transactions: [
                                { id: 101, title: 'Recent Transactions', url: '/transactions/', description: 'List of recent inventory transactions' }
                            ]
                        }, query);
                    });
            }

            // Display search results
            displaySearchResults(results, query) {
                // Clear previous results
                this.searchResults.innerHTML = '';

                // Check if any results
                const hasResults = Object.values(results).some(group => group.length > 0);

                if (!hasResults) {
                    this.searchResults.innerHTML = `
                                        <div class="search-no-results p-3 text-center">
                                            <i class="fas fa-search-minus mb-2" style="font-size: 1.5rem; color: var(--primary-color);"></i>
                                            <div>No results found for "${query}"</div>
                                            <div class="text-muted small mt-1">Try different keywords or check spelling</div>
                                        </div>
                                        `;
                } else {
                    // Build results HTML for each group
                    const groupLabels = {
                        'medicines': 'Medicines',
                        'reports': 'Reports',
                        'transactions': 'Transactions'
                    };

                    for (const [type, items] of Object.entries(results)) {
                        if (items.length === 0) continue;

                        const resultsHtml = items.map(item => {
                            const title = item.name || item.title;
                            const description = item.description || '';
                            const url = type === 'medicines' ? `/inventory/medicine/${item.id}/` : item.url;

                            return `
                                                <a href="${url}" class="search-result-item">
                                                    ${this.getIconForType(type)}
                                                    <div class="search-result-details">
                                                        <div class="search-result-title">${this.highlightQuery(title, query)}</div>
                                                        <div class="search-result-desc text-muted small">${this.highlightQuery(description, query)}</div>
                                                    </div>
                                                </a>
                                                `;
                        }).join('');

                        this.searchResults.innerHTML += `
                                            <div class="search-result-group">
                                                <div class="search-result-heading">
                                                    ${groupLabels[type] || type}
                                                    <span class="badge bg-secondary float-end">${items.length}</span>
                                                </div>
                                                ${resultsHtml}
                                            </div>
                                            `;
                    }

                    // Add total count
                    const totalResults = Object.values(results).reduce((total, group) => total + group.length, 0);
                    this.searchResults.innerHTML += `
                                        <div class="search-footer p-2 text-center">
                                            <small class="text-muted">Found ${totalResults} results for "${query}"</small>
                                        </div>
                                        `;
                }

                // Show results
                this.searchResults.classList.add('show');
            }

            // Highlight search query in text
            highlightQuery(text, query) {
                if (!query || !text) return text;
                const regex = new RegExp(`(${query})`, 'gi');
                return text.replace(regex, '<mark>$1</mark>');
            }

            // Get appropriate icon for search result type
            getIconForType(type) {
                let iconClass = '';
                switch (type) {
                    case 'medicines':
                        iconClass = 'fas fa-pills';
                        break;
                    case 'reports':
                        iconClass = 'fas fa-file-alt';
                        break;
                    case 'transactions':
                        iconClass = 'fas fa-exchange-alt';
                        break;
                    default:
                        iconClass = 'fas fa-search';
                }
                return `<i class="${iconClass} search-result-icon"></i>`;
            }
        }

        /**
         * Notification System Class
         * Handles fetching, displaying, and managing notifications from the server
         */

        /**
         * NotificationSystem - Handles the fetching, displaying and management of notifications
         */

        class NotificationSystem {
            constructor() {
                this.storageKey = 'notification_settings';
                this.notifications = []; // Store notifications data
                this.init();
                console.log('NotificationSystem initialized');
            }

            init() {
                this.initNotificationSystem();
                this.initNotificationModal();
                this.setupEventListeners();
            }

            initNotificationModal() {
                const modal = document.getElementById('notificationDetailModal');
                if (!modal) return;

                this.notificationModal = new bootstrap.Modal(modal);
                this.currentNotificationId = null;
            }

            initNotificationSystem() {
                const notificationCount = document.getElementById('notificationCount');
                if (!notificationCount) return;

                this.fetchNotifications();

                const notificationDropdown = document.getElementById('notificationsDropdown');
                if (notificationDropdown) {
                    notificationDropdown.addEventListener('click', (e) => {
                        e.preventDefault();
                        this.fetchNotifications();
                    });
                }

                setInterval(() => this.fetchNotifications(), 5 * 60 * 1000);
                this.addNotificationStyles();
            }

            setupEventListeners() {
                // Delegate event listener for notification items
                document.addEventListener('click', (e) => {
                    const notificationItem = e.target.closest('.notification-item');
                    if (notificationItem && !e.target.closest('.delete-notification')) {
                        e.preventDefault();
                        const notificationId = notificationItem.dataset.notificationId;
                        const medicineId = notificationItem.dataset.medicineId;
                        const url = notificationItem.dataset.url;

                        // If clicking the view details link, navigate directly
                        if (e.target.closest('.view-details-link')) {
                            if (medicineId) {
                                window.location.href = `/inventory/medicine/${medicineId}/`;
                            } else if (url && url !== '#') {
                                window.location.href = url;
                            }
                            return;
                        }

                        // Otherwise show the modal
                        const notification = this.notifications.find(n => n.id.toString() === notificationId);
                        if (notification) {
                            this.showNotificationDetail(notification);
                        }
                    }
                });
            }

            showNotificationDetail(notification) {
                const modal = document.getElementById('notificationDetailModal');
                if (!modal) return;

                // Update modal content
                modal.querySelector('.notification-title').textContent = notification.title;
                modal.querySelector('.notification-message').textContent = notification.message;
                modal.querySelector('.notification-time').textContent = this.formatTimeAgo(notification.created_at);
                modal.querySelector('.type-label').textContent = notification.notification_type;

                // Update notification icon
                const iconWrapper = modal.querySelector('.notification-modal-icon');
                if (iconWrapper) {
                    iconWrapper.className = 'notification-modal-icon';
                    iconWrapper.classList.add(`notification-type-${notification.notification_type.toLowerCase()}`);
                    iconWrapper.innerHTML = this.getNotificationIcon(notification.notification_type);
                }

                // Handle view details button
                const viewDetailsBtn = modal.querySelector('.view-details-btn');
                if (viewDetailsBtn) {
                    if (notification.medicine_id) {
                        viewDetailsBtn.style.display = 'inline-block';
                        viewDetailsBtn.href = `/inventory/medicine/${notification.medicine_id}/`;
                        viewDetailsBtn.onclick = null; // Allow default navigation
                    } else if (notification.url && notification.url !== '#') {
                        viewDetailsBtn.style.display = 'inline-block';
                        viewDetailsBtn.href = notification.url;
                        viewDetailsBtn.onclick = null; // Allow default navigation
                    } else {
                        viewDetailsBtn.style.display = 'none';
                        viewDetailsBtn.href = '#';
                        viewDetailsBtn.onclick = (e) => e.preventDefault(); // Prevent click on empty link
                    }
                }

                // Update delete button
                const deleteBtn = modal.querySelector('.delete-notification-btn');
                if (deleteBtn) {
                    deleteBtn.dataset.notificationId = notification.id;
                }

                // Store current notification ID
                this.currentNotificationId = notification.id;

                // Show modal
                this.notificationModal.show();

                // Mark as read if unread
                if (!notification.is_read) {
                    this.markNotificationAsRead(notification.id);
                }
            }

            // Add this method to handle view details button clicks
            handleViewDetailsClick(e, notification) {
                e.preventDefault();
                if (notification.medicine_id) {
                    window.location.href = `/inventory/medicine/${notification.medicine_id}/`;
                } else if (notification.url && notification.url !== '#') {
                    window.location.href = notification.url;
                }
            }


            getNotificationIcon(type) {
                const icons = {
                    'inventory': '<i class="fas fa-box fa-lg"></i>',
                    'expiry': '<i class="fas fa-calendar-times fa-lg"></i>',
                    'system': '<i class="fas fa-cogs fa-lg"></i>',
                    'default': '<i class="fas fa-bell fa-lg"></i>',
                    // Add more specific icons based on your notification types
                    'danger': '<i class="fas fa-exclamation-circle fa-lg"></i>',
                    'warning': '<i class="fas fa-exclamation-triangle fa-lg"></i>',
                    'success': '<i class="fas fa-check-circle fa-lg"></i>',
                    'info': '<i class="fas fa-info-circle fa-lg"></i>'
                };
                return icons[type.toLowerCase()] || icons.default;
            }

            fetchNotifications() {
                this.showLoadingState();

                fetch('/api/notifications/')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Network response error: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Debug log the received data
                        console.log('Received notifications:', data);

                        // Process and store notifications with default values
                        this.notifications = data.notifications.map(notification => ({
                            ...notification,
                            medicine_id: notification.medicine_id || null,
                            url: notification.url || '#'
                        }));

                        console.log('Processed notifications:', this.notifications);

                        // Update UI components
                        this.updateNotificationCount(data.unread_count);
                        this.updateNotificationDropdown(this.notifications);
                        this.addNotificationHandlers();
                    })
                    .catch(error => {
                        console.error('Error fetching notifications:', error);
                        this.showErrorState();
                    });
            }
            /**
             * Display loading state in notification dropdown
             */
            showLoadingState() {
                const notificationList = document.querySelector('.notification-list');
                if (!notificationList) return;

                notificationList.innerHTML = `
                                <div class="text-center p-4">
                                    <div class="spinner-border spinner-border-sm text-primary mb-2" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <p class="text-muted">Loading notifications...</p>
                                </div>`;
            }

            /**
             * Display error state in notification dropdown
             */
            showErrorState() {
                const notificationList = document.querySelector('.notification-list');
                if (!notificationList) return;

                notificationList.innerHTML = `
                                <div class="text-center p-4">
                                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-3"></i>
                                    <p class="text-muted">Could not load notifications</p>
                                    <button id="retryNotifications" class="btn btn-sm btn-outline-primary mt-2">
                                        <i class="fas fa-sync-alt me-1"></i> Retry
                                    </button>
                                </div>`;

                // Add retry button listener
                setTimeout(() => {
                    const retryBtn = document.getElementById('retryNotifications');
                    if (retryBtn) {
                        retryBtn.addEventListener('click', (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            this.fetchNotifications();
                        });
                    }
                }, 0);
            }

            /**
             * Update notification count badge
             * @param {number} unreadCount - Number of unread notifications
             */
            updateNotificationCount(unreadCount) {
                const notificationCount = document.querySelector('#notificationCount');
                if (!notificationCount) return;

                notificationCount.textContent = unreadCount;

                // Only display badge if there are unread notifications
                notificationCount.style.display = unreadCount > 0 ? '' : 'none';

                // Update page title to include notification count
                if (unreadCount > 0) {
                    document.title = `(${unreadCount}) ${document.title.replace(/^\(\d+\)\s/, '')}`;
                } else {
                    document.title = document.title.replace(/^\(\d+\)\s/, '');
                }
            }

            /**
             * Update notification dropdown content
             * @param {Array} notifications - Array of notification objects
             */
            updateNotificationDropdown(notifications) {
                // Store notifications for modal use
                this.notifications = notifications;

                const notificationList = document.querySelector('.notification-list');
                if (!notificationList) return;

                // Show empty state if no notifications
                if (!notifications || notifications.length === 0) {
                    notificationList.innerHTML = `
                                    <div class="text-center p-4">
                                        <i class="fas fa-bell-slash fa-2x text-muted mb-3"></i>
                                        <p class="text-muted">No notifications</p>
                                    </div>`;
                    return;
                }

                // Build notifications HTML
                notificationList.innerHTML = notifications.map(notification => `
                                <div class="notification-item ${notification.is_read ? 'read' : ''}"
                                    data-notification-id="${notification.id}">
                                    <a class="dropdown-item" href="${notification.url || '#'}">
                                        <div class="d-flex align-items-start">
                                            <div class="notification-indicator ${this.getPriorityClass(notification.notification_type)} me-2"></div>
                                            <div class="notification-content flex-grow-1">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <strong>${this.escapeHtml(notification.title)}</strong>
                                                    <small class="text-muted ms-2">${this.formatTimeAgo(notification.created_at)}</small>
                                                </div>
                                                <div class="notification-message mt-1">${this.escapeHtml(notification.message)}</div>
                                            </div>
                                        </div>
                                    </a>
                                </div>
                            `).join('');

                // Add mark all as read button if there are unread notifications
                const hasUnread = notifications.some(n => !n.is_read);
                if (hasUnread) {
                    notificationList.innerHTML += `
                                    <div class="dropdown-divider"></div>
                                    <div class="text-center p-2">
                                        <button id="markAllRead" class="btn btn-sm btn-light">
                                            <i class="fas fa-check-double me-1"></i> Mark all as read
                                        </button>
                                    </div>`;

                    // Add event listener for mark all as read button
                    setTimeout(() => {
                        const markAllReadBtn = document.getElementById('markAllRead');
                        if (markAllReadBtn) {
                            markAllReadBtn.addEventListener('click', (e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                this.markAllNotificationsAsRead();
                            });
                        }
                    }, 0);
                }

                // Add view all link
                notificationList.innerHTML += `
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item text-center" href="/notifications/">
                                    <i class="fas fa-bell me-1"></i> View all notifications
                                </a>`;
            }

            /**
             * Update the notification click handler
             */
            addNotificationHandlers() {
                document.querySelectorAll('.notification-item').forEach(item => {
                    const id = item.dataset.notificationId;

                    // Show modal on click
                    item.addEventListener('click', (e) => {
                        if (!e.target.closest('.delete-notification')) {
                            // Find the notification data
                            const notification = this.notifications.find(n => n.id.toString() === id);
                            if (notification) {
                                this.showNotificationDetail(notification);
                            }
                        }
                    });

                    // Delete notification
                    const deleteBtn = item.querySelector('.delete-notification');
                    if (deleteBtn) {
                        deleteBtn.addEventListener('click', (e) => {
                            e.stopPropagation();
                            this.deleteNotification(id);
                        });
                    }
                });
            }


            /**
* Show notification detail in modal
* @param {Object} notification - Notification data
*/
            showNotificationDetail(notification) {
                const modal = document.getElementById('notificationDetailModal');
                if (!modal) return;

                // Debug log at the start
                console.log('Notification data:', {
                    id: notification.id,
                    medicine_id: notification.medicine_id,
                    url: notification.url,
                    type: notification.notification_type
                });

                // Store current notification ID
                this.currentNotificationId = notification.id;

                // Get all modal elements first and check if they exist
                const elements = {
                    iconWrapper: modal.querySelector('.notification-modal-icon'),
                    title: modal.querySelector('.notification-title'),
                    message: modal.querySelector('.notification-message'),
                    time: modal.querySelector('.notification-time'),
                    typeLabel: modal.querySelector('.type-label'),
                    viewDetailsBtn: modal.querySelector('.view-details-btn'),
                    deleteBtn: modal.querySelector('.delete-notification-btn')
                };

                // Debug log for viewDetailsBtn
                console.log('View details button found:', !!elements.viewDetailsBtn);

                // Update modal content
                if (elements.iconWrapper) {
                    elements.iconWrapper.className = `notification-modal-icon notification-type-${notification.notification_type.toLowerCase()}`;
                    elements.iconWrapper.innerHTML = this.getNotificationIcon(notification.notification_type);
                }

                if (elements.title) elements.title.textContent = notification.title;
                if (elements.message) elements.message.textContent = notification.message;
                if (elements.time) elements.time.textContent = this.formatTimeAgo(notification.created_at);
                if (elements.typeLabel) elements.typeLabel.textContent = notification.notification_type;

                // Handle view details button visibility and URL
                if (elements.viewDetailsBtn) {
                    console.log('Checking view details conditions:', {
                        hasMedicineId: !!notification.medicine_id,
                        hasUrl: !!notification.url,
                        url: notification.url
                    });

                    elements.viewDetailsBtn.style.display = 'none'; // Reset display first

                    if (notification.medicine_id) {
                        elements.viewDetailsBtn.style.display = 'inline-block';
                        elements.viewDetailsBtn.href = `/inventory/medicine/${notification.medicine_id}/`;
                        console.log('Setting medicine URL:', elements.viewDetailsBtn.href);
                    } else if (notification.url && notification.url !== '#') {
                        elements.viewDetailsBtn.style.display = 'inline-block';
                        elements.viewDetailsBtn.href = notification.url;
                        console.log('Setting custom URL:', elements.viewDetailsBtn.href);
                    }

                    // Remove any existing click handlers
                    elements.viewDetailsBtn.onclick = null;
                }

                // Handle delete button
                if (elements.deleteBtn) {
                    elements.deleteBtn.dataset.notificationId = notification.id;
                    elements.deleteBtn.onclick = () => {
                        if (confirm('Are you sure you want to delete this notification?')) {
                            this.deleteNotification(notification.id);
                            this.notificationModal.hide();
                        }
                    };
                }

                // Show modal
                this.notificationModal.show();

                // Mark as read if unread
                if (!notification.is_read) {
                    this.markNotificationAsRead(notification.id);
                }
            }

            /**
             * Add notification styles to the document
             */
            addNotificationStyles() {
                if (document.getElementById('notification-styles')) return;

                const style = document.createElement('style');
                style.id = 'notification-styles';
                style.textContent = `
                                .notification-item {
                                    border-left: 3px solid transparent;
                                    transition: background-color 0.2s;
                                    padding: 0;
                                }

                                .notification-item:not(.read) {
                                    border-left-color: var(--primary-color, #3498db);
                                    background-color: rgba(52, 152, 219, 0.05);
                                }

                                .notification-item .dropdown-item {
                                    padding: 0.75rem 1rem;
                                    white-space: normal;
                                }

                                .notification-item:hover .dropdown-item {
                                    background-color: rgba(0, 0, 0, 0.03);
                                }

                                .notification-indicator {
                                    min-width: 8px;
                                    min-height: 8px;
                                    width: 8px;
                                    height: 8px;
                                    border-radius: 50%;
                                    margin-top: 6px;
                                    flex-shrink: 0;
                                }

                                .notification-message {
                                    font-size: 0.85rem;
                                    color: #666;
                                    word-break: break-word;
                                }

                                .notification-list {
                                    max-height: 350px;
                                    overflow-y: auto;
                                    min-width: 320px;
                                }

                                .bg-inventory {
                                    background-color: #3498db;
                                }

                                .bg-expiry {
                                    background-color: #e74c3c;
                                }

                                .bg-system {
                                    background-color: #2ecc71;
                                }

                                /* Improve scrollbar for notification list */
                                .notification-list::-webkit-scrollbar {
                                    width: 6px;
                                }

                                .notification-list::-webkit-scrollbar-track {
                                    background: #f1f1f1;
                                }

                                .notification-list::-webkit-scrollbar-thumb {
                                    background: #ccc;
                                    border-radius: 3px;
                                }

                                .notification-list::-webkit-scrollbar-thumb:hover {
                                    background: #999;
                                }
                            `;
                document.head.appendChild(style);
            }

            /**
             * Get CSS class for notification priority
             * @param {string} type - The notification type
             * @returns {string} - CSS class for notification indicator
             */
            getPriorityClass(type) {
                if (!type) return 'bg-info';

                switch (type.toLowerCase()) {
                    case 'danger':
                    case 'high':
                    case 'expiry':
                        return 'bg-danger';
                    case 'warning':
                    case 'medium':
                        return 'bg-warning';
                    case 'success':
                    case 'low':
                    case 'system':
                        return 'bg-success';
                    case 'inventory':
                        return 'bg-inventory';
                    default:
                        return 'bg-info';
                }
            }

            /**
             * Get notification icon based on type
             * @param {string} type - The notification type
             * @returns {string} - HTML for the icon
             */
            getNotificationIcon(type) {
                if (!type) return '<i class="fas fa-bell"></i>';

                switch (type.toLowerCase()) {
                    case 'danger':
                    case 'high':
                    case 'expiry':
                        return '<i class="fas fa-exclamation-circle"></i>';
                    case 'warning':
                    case 'medium':
                        return '<i class="fas fa-exclamation-triangle"></i>';
                    case 'success':
                    case 'low':
                        return '<i class="fas fa-check-circle"></i>';
                    case 'system':
                        return '<i class="fas fa-cog"></i>';
                    case 'inventory':
                        return '<i class="fas fa-box"></i>';
                    default:
                        return '<i class="fas fa-bell"></i>';
                }
            }

            /**
             * Format time ago
             * @param {string} timestamp - ISO timestamp
             * @returns {string} - Formatted time ago string
             */
            formatTimeAgo(timestamp) {
                if (!timestamp) return '';

                try {
                    const date = new Date(timestamp);
                    const now = new Date();
                    const seconds = Math.floor((now - date) / 1000);

                    // Check for invalid date
                    if (isNaN(date.getTime())) return '';

                    let interval = Math.floor(seconds / 31536000);
                    if (interval >= 1) return `${interval}y ago`;

                    interval = Math.floor(seconds / 2592000);
                    if (interval >= 1) return `${interval}mo ago`;

                    interval = Math.floor(seconds / 86400);
                    if (interval >= 1) return `${interval}d ago`;

                    interval = Math.floor(seconds / 3600);
                    if (interval >= 1) return `${interval}h ago`;

                    interval = Math.floor(seconds / 60);
                    if (interval >= 1) return `${interval}m ago`;

                    return 'just now';
                } catch (e) {
                    console.warn('Error formatting time ago:', e);
                    return '';
                }
            }

            /**
             * Escape HTML to prevent XSS
             * @param {string} unsafe - Unsafe string
             * @returns {string} - Escaped safe string
             */
            escapeHtml(unsafe) {
                if (!unsafe) return '';
                return unsafe
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }

            /**
             * Mark a notification as read
             * @param {string|number} id - Notification ID
             */
            markNotificationAsRead(id) {
                fetch(`/api/notifications/${id}/read/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCsrfToken()
                    }
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Server responded with ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Update UI
                        const notificationItem = document.querySelector(`.notification-item[data-notification-id="${id}"]`);
                        if (notificationItem) {
                            notificationItem.classList.add('read');
                        }

                        // Refresh notification count
                        this.fetchNotifications();
                    })
                    .catch(error => console.error('Error marking notification as read:', error));
            }

            /**
             * Mark all notifications as read
             */
            markAllNotificationsAsRead() {
                fetch('/api/notifications/mark-all-read/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCsrfToken()
                    }
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Server responded with ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Update UI - mark all notifications as read
                        document.querySelectorAll('.notification-item:not(.read)').forEach(item => {
                            item.classList.add('read');
                        });

                        // Update notification count
                        this.updateNotificationCount(0);

                        // Show success message
                        this.showToast('Success', 'All notifications marked as read', 'success');

                        // Close dropdown
                        this.closeNotificationDropdown();

                        // Refresh notifications after a short delay
                        setTimeout(() => this.fetchNotifications(), 500);
                    })
                    .catch(error => {
                        console.error('Error marking all notifications as read:', error);
                        this.showToast('Error', 'Failed to mark notifications as read', 'danger');
                    });
            }

            /**
             * Delete a notification
             * @param {string|number} id - Notification ID
             */
            deleteNotification(id) {
                fetch(`/api/notifications/${id}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRFToken': this.getCsrfToken()
                    }
                })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Server responded with ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        // Update UI - remove notification from list
                        const notificationItem = document.querySelector(`.notification-item[data-notification-id="${id}"]`);
                        if (notificationItem) {
                            notificationItem.remove();
                        }

                        // Show success message
                        this.showToast('Success', 'Notification deleted', 'success');

                        // Refresh notifications
                        this.fetchNotifications();
                    })
                    .catch(error => {
                        console.error('Error deleting notification:', error);
                        this.showToast('Error', 'Failed to delete notification', 'danger');
                    });
            }

            /**
             * Close the notification dropdown
             */
            closeNotificationDropdown() {
                const dropdownMenu = document.querySelector('.dropdown-menu.notification-list');
                if (dropdownMenu && typeof bootstrap !== 'undefined') {
                    const dropdown = bootstrap.Dropdown.getInstance(document.getElementById('notificationsDropdown'));
                    if (dropdown) {
                        dropdown.hide();
                    }
                }
            }

            /**
             * Show toast notification
             * @param {string} title - Toast title
             * @param {string} message - Toast message
             * @param {string} type - Toast type (success, danger, warning, info)
             */
            showToast(title, message, type = 'info') {
                // Check if app's showToast method exists
                if (window.app && typeof window.app.showToast === 'function') {
                    window.app.showToast(title, message, type);
                    return;
                }

                // Fallback toast implementation if app.showToast is not available
                const toastContainer = document.getElementById('toastContainer') || this.createToastContainer();

                const toast = document.createElement('div');
                toast.className = `toast show bg-${type === 'error' ? 'danger' : type} text-white`;
                toast.setAttribute('role', 'alert');
                toast.setAttribute('aria-live', 'assertive');
                toast.setAttribute('aria-atomic', 'true');

                toast.innerHTML = `
                                <div class="toast-header bg-${type === 'error' ? 'danger' : type} text-white">
                                    <strong class="me-auto">${title}</strong>
                                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                                </div>
                                <div class="toast-body">
                                    ${message}
                                </div>
                            `;

                toastContainer.appendChild(toast);

                // Auto-remove toast after 5 seconds
                setTimeout(() => {
                    toast.classList.remove('show');
                    setTimeout(() => toast.remove(), 500);
                }, 5000);

                // Add close button handler
                const closeBtn = toast.querySelector('.btn-close');
                if (closeBtn) {
                    closeBtn.addEventListener('click', () => {
                        toast.classList.remove('show');
                        setTimeout(() => toast.remove(), 500);
                    });
                }
            }

            /**
             * Create toast container if it doesn't exist
             * @returns {HTMLElement} - Toast container element
             */
            createToastContainer() {
                const container = document.createElement('div');
                container.id = 'toastContainer';
                container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
                container.style.zIndex = '1080';
                document.body.appendChild(container);
                return container;
            }

            /**
             * Get CSRF token from cookies
             * @returns {string|null} - CSRF token or null if not found
             */
            getCsrfToken() {
                const name = 'csrftoken';
                let cookieValue = null;

                if (document.cookie && document.cookie !== '') {
                    const cookies = document.cookie.split(';');
                    for (let i = 0; i < cookies.length; i++) {
                        const cookie = cookies[i].trim();
                        if (cookie.substring(0, name.length + 1) === (name + '=')) {
                            cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                            break;
                        }
                    }
                }

                return cookieValue;
            }


            /**
             * Escape HTML to prevent XSS attacks
             * @param {string} unsafe - Unsafe string that might contain HTML
             * @returns {string} - Escaped safe string
             */
            escapeHtml(unsafe) {
                if (!unsafe) return '';

                return unsafe
                    .replace(/&/g, "&amp;")
                    .replace(/</g, "&lt;")
                    .replace(/>/g, "&gt;")
                    .replace(/"/g, "&quot;")
                    .replace(/'/g, "&#039;");
            }
        }


        /**
         * Helper function to ensure burger exists - but only on desktop
         */
        function ensureSidebarBurger() {
            // Remove any duplicates first
            const burgers = document.querySelectorAll('#sidebarBurger');
            if (burgers.length > 1) {
                // Keep only the first one
                for (let i = 1; i < burgers.length; i++) {
                    burgers[i].remove();
                }
            }

            // Check if burger exists
            let burger = document.getElementById('sidebarBurger');

            // Only create burger on desktop
            if (!burger && window.innerWidth >= 992) {
                console.log("Creating missing sidebar burger icon for desktop");
                const navbar = document.querySelector('.top-navbar');
                if (navbar) {
                    // Create the burger button
                    const burgerBtn = document.createElement('button');
                    burgerBtn.id = 'sidebarBurger';
                    burgerBtn.className = 'btn btn-link sidebar-burger d-none d-lg-block';
                    burgerBtn.setAttribute('title', 'Toggle Sidebar');
                    burgerBtn.innerHTML = '<i class="fas fa-bars"></i>';

                    // Insert at the beginning of navbar
                    navbar.insertBefore(burgerBtn, navbar.firstChild);

                    // Add click event listener that uses sidebarManager if it exists
                    burgerBtn.addEventListener('click', function () {
                        if (window.app && window.app.sidebar) {
                            // Toggle sidebar locked state
                            window.app.sidebar.sidebarLocked = !window.app.sidebar.sidebarLocked;
                            // Toggle the sidebar
                            window.app.sidebar.toggleSidebar();
                            // Update lock indicator
                            window.app.sidebar.updateLockIndicator();
                            // Show feedback about locked state
                            if (window.app.sidebar.sidebarLocked) {
                                window.app.showToast('Sidebar Locked', 'Sidebar will stay in this state until you click the burger icon again', 'info');
                            } else {
                                window.app.showToast('Sidebar Unlocked', 'Sidebar will now respond to hover', 'info');
                            }
                            // Save state
                            window.app.sidebar.saveState(window.app.sidebar.sidebarWrapper.classList.contains('collapsed'));
                        } else {
                            // Fallback if the sidebar manager doesn't exist
                            const sidebarWrapper = document.getElementById('sidebar-wrapper');
                            if (sidebarWrapper) {
                                const isCollapsed = sidebarWrapper.classList.contains('collapsed');
                                sidebarWrapper.classList.toggle('collapsed');
                                // Adjust content wrapper
                                const contentWrapper = document.querySelector('.content-wrapper');
                                if (contentWrapper) {
                                    if (!isCollapsed) {
                                        contentWrapper.style.marginLeft = 'var(--collapsed-sidebar-width)';
                                        contentWrapper.style.width = 'calc(100% - var(--collapsed-sidebar-width))';
                                    } else {
                                        contentWrapper.style.marginLeft = 'var(--sidebar-width)';
                                        contentWrapper.style.width = 'calc(100% - var(--sidebar-width))';
                                    }
                                }
                            }
                        }
                    });

                    return burgerBtn;
                } else {
                    console.error("Cannot create burger: navbar not found");
                    return null;
                }
            }

            return burger;
        }

        /**
         * Fix for burger icon visibility - but only on desktop
         */
        function makeBurgerVisible() {
            const burger = document.getElementById('sidebarBurger');
            if (burger && window.innerWidth >= 992) {
                burger.style.display = 'block';
                burger.style.visibility = 'visible';
                burger.style.opacity = '1';
                burger.classList.remove('d-none');
                burger.classList.add('d-lg-block');
            } else if (burger && window.innerWidth < 992) {
                // Hide on mobile
                burger.style.display = 'none';
                burger.classList.add('d-none');
                burger.classList.remove('d-lg-block');
            }
        }

        /**
         * Ensure scrolling works
         */
        function enableScrolling() {
            document.documentElement.style.overflowY = 'auto';
            document.body.style.overflowY = 'auto';
            const contentWrapper = document.querySelector('.content-wrapper');
            if (contentWrapper) {
                contentWrapper.style.overflowY = 'auto';
            }
            const mainContent = document.querySelector('.main-content');
            if (mainContent) {
                mainContent.style.overflowY = 'auto';
            }
        }

        // Execute fixes on DOM ready
        document.addEventListener('DOMContentLoaded', function () {
            // Clean up duplicate burgers first
            const burgers = document.querySelectorAll('#sidebarBurger');
            if (burgers.length > 1) {
                for (let i = 1; i < burgers.length; i++) {
                    burgers[i].remove();
                }
            }


            ensureSidebarBurger();
            makeBurgerVisible();
            enableScrolling();

            // Re-check after a short delay (for dynamic loading scenarios)
            setTimeout(() => {
                ensureSidebarBurger();
                makeBurgerVisible();
            }, 500);

            // Add window resize listener to handle burger visibility
            window.addEventListener('resize', function () {
                makeBurgerVisible();
            });
        });

        // Utility to get the CSRF token from the page
        function getCsrfToken() {
            // First try to get from cookie
            const name = 'csrftoken';
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }

            // If not found in cookie, try to get from the form (Django usually includes this)
            if (!cookieValue) {
                const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
                if (csrfInput) {
                    cookieValue = csrfInput.value;
                }
            }

            return cookieValue;
        }

        // Utility function to format dates
        function formatDate(date) {
            if (!(date instanceof Date)) {
                date = new Date(date);
            }

            return new Intl.DateTimeFormat('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            }).format(date);
        }

        // Utility function to format currency (Peso)
        function formatPeso(amount) {
            if (isNaN(amount)) return '₱0.00';

            return new Intl.NumberFormat('en-PH', {
                style: 'currency',
                currency: 'PHP',
                minimumFractionDigits: 2
            }).format(amount);
        }

        // Utility function to format numbers with commas
        function formatNumber(number) {
            if (isNaN(number)) return '0';

            return new Intl.NumberFormat('en-US').format(number);
        }

        // Helper function to debounce function calls (for performance)
        function debounce(func, wait = 300) {
            let timeout;

            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };

                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Helper function to throttle function calls (for performance)
        function throttle(func, limit = 300) {
            let inThrottle;

            return function executedFunction(...args) {
                if (!inThrottle) {
                    func(...args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        }

        // Helper function to detect mobile devices
        function isMobileDevice() {
            return (window.innerWidth < 992) ||
                /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }


        // Function to add sorting functionality to tables
        function addTableSorting(tableSelector = '.table') {
            const tables = document.querySelectorAll(tableSelector);

            tables.forEach(table => {
                const headers = table.querySelectorAll('thead th');
                headers.forEach((header, index) => {
                    // Skip columns that have the 'no-sort' class
                    if (header.classList.contains('no-sort')) return;

                    // Add sorting indicators and cursor style
                    header.style.cursor = 'pointer';
                    header.innerHTML += '<span class="sort-indicator ms-1"></span>';

                    // Add click handler for sorting
                    header.addEventListener('click', function () {
                        const isAscending = !this.classList.contains('asc');

                        // Clear sort indicators on all headers
                        headers.forEach(h => {
                            h.classList.remove('asc', 'desc');
                            h.querySelector('.sort-indicator')?.remove();
                        });

                        // Add sort indicator to this header
                        this.classList.toggle('asc', isAscending);
                        this.classList.toggle('desc', !isAscending);

                        // Add visual indicator
                        const indicator = document.createElement('span');
                        indicator.className = 'sort-indicator ms-1';
                        indicator.innerHTML = isAscending ? '▲' : '▼';
                        this.appendChild(indicator);

                        // Sort the table rows
                        const rows = Array.from(table.querySelectorAll('tbody tr'));
                        rows.sort((a, b) => {
                            const cellA = a.cells[index].textContent.trim();
                            const cellB = b.cells[index].textContent.trim();

                            // Try to detect if the content is a number or date
                            const dateA = new Date(cellA);
                            const dateB = new Date(cellB);

                            if (!isNaN(dateA) && !isNaN(dateB)) {
                                // Date comparison
                                return isAscending ? dateA - dateB : dateB - dateA;
                            } else if (!isNaN(parseFloat(cellA)) && !isNaN(parseFloat(cellB))) {
                                // Numeric comparison
                                return isAscending
                                    ? parseFloat(cellA) - parseFloat(cellB)
                                    : parseFloat(cellB) - parseFloat(cellA);
                            } else {
                                // String comparison
                                return isAscending
                                    ? cellA.localeCompare(cellB)
                                    : cellB.localeCompare(cellA);
                            }
                        });

                        // Re-append sorted rows to the table
                        const tbody = table.querySelector('tbody');
                        rows.forEach(row => tbody.appendChild(row));
                    });
                });
            });

            // Add sorting styles
            if (!document.getElementById('table-sorting-styles')) {
                const style = document.createElement('style');
                style.id = 'table-sorting-styles';
                style.textContent = `
                                        th.asc .sort-indicator, th.desc .sort-indicator {
                                            display: inline-block;
                                            margin-left: 5px;
                                        }
                                        th.asc, th.desc {
                                            background-color: rgba(0, 0, 0, 0.05);
                                        }
                                    `;
                document.head.appendChild(style);
            }
        }

        // Initialize common UI enhancements
        function initializeUIEnhancements() {
            // Add table search to data tables
            addTableSearch();

            // Add table sorting
            addTableSorting();

            // Initialize Bootstrap tooltips
            if (typeof bootstrap !== 'undefined' && bootstrap.Tooltip) {
                document.querySelectorAll('[data-bs-toggle="tooltip"]').forEach(tooltip => {
                    new bootstrap.Tooltip(tooltip);
                });
            }

            // Initialize Bootstrap popovers
            if (typeof bootstrap !== 'undefined' && bootstrap.Popover) {
                document.querySelectorAll('[data-bs-toggle="popover"]').forEach(popover => {
                    new bootstrap.Popover(popover);
                });
            }

            // Add form validation styles
            document.querySelectorAll('form:not(.no-validation)').forEach(form => {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault();
                        event.stopPropagation();
                    }

                    form.classList.add('was-validated');
                });
            });
        }

        // Run UI enhancements when DOM is ready
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize UI enhancements after a short delay (to ensure all DOM elements are ready)
            setTimeout(initializeUIEnhancements, 100);
        });










    </script>

    {% block extra_js %}{% endblock %}



    <!-- Notification Detail Modal -->
    <div class="modal fade" id="notificationDetailModal" tabindex="-1" aria-labelledby="notificationDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header border-bottom">
                    <div class="d-flex align-items-center">
                        <div class="notification-modal-icon me-2">
                            <!-- Icon will be dynamically inserted here -->
                        </div>
                        <h5 class="modal-title" id="notificationDetailModalLabel">Notification Details</h5>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="notification-detail-content">
                        <h6 class="notification-title mb-2"></h6>
                        <p class="notification-message mb-3"></p>
                        <div class="notification-metadata text-muted small">
                            <div class="created-at mb-1">
                                <i class="fas fa-clock me-1"></i>
                                <span class="notification-time"></span>
                            </div>
                            <div class="notification-type">
                                <i class="fas fa-tag me-1"></i>
                                <span class="type-label"></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer border-top">
                    <div class="d-flex justify-content-between w-100">
                        <div>
                            <button type="button" class="btn btn-danger btn-sm delete-notification-btn" data-notification-id="">
                                <i class="fas fa-trash-alt me-1"></i>Delete
                            </button>
                        </div>
                        <div>
                            <button type="button" class="btn btn-secondary btn-sm me-2" data-bs-dismiss="modal">Close</button>
                            <a href="#" class="btn btn-primary btn-sm view-details-btn" style="display: none;">
                                <i class="fas fa-external-link-alt me-1"></i>View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

