# Generated by Django 4.2.9 on 2025-04-13 09:28

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('user_management', '0014_alter_userprofile_phone_number'),
    ]

    operations = [
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('customer_name', models.CharField(max_length=255)),
                ('patient_number', models.CharField(blank=True, max_length=255, null=True, unique=True)),
                ('customer_type', models.CharField(choices=[('in_patient', 'In Patient'), ('out_patient', 'Out Patient')], default='out_patient', max_length=20)),
                ('phone_number', models.CharField(blank=True, max_length=17, null=True, validators=[django.core.validators.RegexValidator(message='Please enter a valid Philippine phone number (e.g., +639123456789 or 09123456789).', regex='^(\\+639|09)\\d{9}$')])),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('address', models.TextField(blank=True, null=True)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_transaction_date', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Customer',
                'verbose_name_plural': 'Customers',
                'ordering': ['customer_name'],
                'indexes': [models.Index(fields=['customer_name'], name='user_manage_custome_13b987_idx'), models.Index(fields=['patient_number'], name='user_manage_patient_d5662d_idx')],
            },
        ),
    ]
