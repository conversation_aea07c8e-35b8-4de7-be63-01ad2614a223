{% extends 'base.html' %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary fw-bold">
                        <i class="fas fa-exchange-alt me-2"></i>Stock Movements
                    </h1>
                    <p class="text-muted mt-2">
                        Each movement records brand and supplier information to provide comprehensive tracking.
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Filters</h6>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-lg-3 col-md-6">
                    <label for="search" class="form-label">Search</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="search" name="search" value="{{ search }}" placeholder="Medicine name, category, brand...">
                        <button type="button" class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#searchModal">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="movement_type" class="form-label">Movement Type</label>
                    <select class="form-select" id="movement_type" name="movement_type">
                        <option value="all" {% if movement_type == 'all' %}selected{% endif %}>All Types</option>
                        {% for type_code, type_name in movement_types %}
                        <option value="{{ type_code }}" {% if movement_type == type_code %}selected{% endif %}>{{ type_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="date_filter" class="form-label">Date Range</label>
                    <select class="form-select" id="date_filter" name="date_filter">
                        <option value="all" {% if date_filter == 'all' %}selected{% endif %}>All Time</option>
                        <option value="today" {% if date_filter == 'today' %}selected{% endif %}>Today</option>
                        <option value="week" {% if date_filter == 'week' %}selected{% endif %}>Last 7 Days</option>
                        <option value="month" {% if date_filter == 'month' %}selected{% endif %}>Last 30 Days</option>
                        <option value="quarter" {% if date_filter == 'quarter' %}selected{% endif %}>Last 90 Days</option>
                    </select>
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="brand" class="form-label">Brand</label>
                    <select class="form-select" id="brand" name="brand">
                        <option value="">All Brands</option>
                        {% for brand_name in all_brands %}
                        <option value="{{ brand_name }}" {% if brand_filter == brand_name %}selected{% endif %}>{{ brand_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-lg-2 col-md-6">
                    <label for="supplier" class="form-label">Supplier</label>
                    <select class="form-select" id="supplier" name="supplier">
                        <option value="">All Suppliers</option>
                        {% for supplier_name in all_suppliers %}
                        <option value="{{ supplier_name }}" {% if supplier_filter == supplier_name %}selected{% endif %}>{{ supplier_name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-lg-1 col-md-6 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">Filter</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Movement Statistics -->
    <div class="row mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                Total Movements</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_movements }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exchange-alt fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                Total Quantity Moved</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_quantity }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-boxes fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-6 col-lg-12 col-md-12 mb-4">
            <div class="card shadow h-100">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Movement Types</h6>
                </div>
                <div class="card-body">
                    <div style="height: 250px;">
                        <canvas id="movementTypeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Movements Table -->
    <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex justify-content-between align-items-center">
            <h6 class="m-0 font-weight-bold text-primary">Stock Movements</h6>
            <div>
                <a href="{% url 'movement_analysis_dashboard' %}" class="btn btn-sm btn-info">
                    <i class="fas fa-chart-line me-1"></i>Movement Analysis
                </a>
                <a href="{% url 'update_movement_analyses' %}" class="btn btn-sm btn-secondary" onclick="return confirm('Update all movement analyses? This may take a moment.')">
                    <i class="fas fa-sync me-1"></i>Update Analyses
                </a>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>
                                <a href="?sort_by={% if sort_by == 'movement_date' %}-movement_date{% else %}movement_date{% endif %}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">
                                    Date
                                    {% if sort_by == 'movement_date' %}<i class="fas fa-sort-up"></i>{% elif sort_by == '-movement_date' %}<i class="fas fa-sort-down"></i>{% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by={% if sort_by == 'medicine__name' %}-medicine__name{% else %}medicine__name{% endif %}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">
                                    Medicine
                                    {% if sort_by == 'medicine__name' %}<i class="fas fa-sort-up"></i>{% elif sort_by == '-medicine__name' %}<i class="fas fa-sort-down"></i>{% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by={% if sort_by == 'movement_type' %}-movement_type{% else %}movement_type{% endif %}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">
                                    Type
                                    {% if sort_by == 'movement_type' %}<i class="fas fa-sort-up"></i>{% elif sort_by == '-movement_type' %}<i class="fas fa-sort-down"></i>{% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by={% if sort_by == 'quantity' %}-quantity{% else %}quantity{% endif %}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">
                                    Quantity
                                    {% if sort_by == 'quantity' %}<i class="fas fa-sort-up"></i>{% elif sort_by == '-quantity' %}<i class="fas fa-sort-down"></i>{% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by={% if sort_by == 'brand' %}-brand{% else %}brand{% endif %}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">
                                    Brand
                                    {% if sort_by == 'brand' %}<i class="fas fa-sort-up"></i>{% elif sort_by == '-brand' %}<i class="fas fa-sort-down"></i>{% endif %}
                                </a>
                            </th>
                            <th>
                                <a href="?sort_by={% if sort_by == 'supplier' %}-supplier{% else %}supplier{% endif %}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">
                                    Supplier
                                    {% if sort_by == 'supplier' %}<i class="fas fa-sort-up"></i>{% elif sort_by == '-supplier' %}<i class="fas fa-sort-down"></i>{% endif %}
                                </a>
                            </th>
                            <th>Reference</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in movements %}
                        <tr>
                            <td>{{ movement.movement_date|date:"M d, Y H:i" }}</td>
                            <td>
                                <a href="{% url 'medicine_detail' movement.medicine.id %}">{{ movement.medicine.name }}</a>
                                {% if movement.medicine.category %}
                                <br><small class="text-muted">Category: {{ movement.medicine.category }}</small>
                                {% endif %}
                                {% if movement.medicine.generic_medicine %}
                                <br><small class="text-muted">Generic: {{ movement.medicine.generic_medicine.name }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.movement_type == 'purchase' %}
                                <span class="badge bg-success">Purchase</span>
                                {% elif movement.movement_type == 'sale' %}
                                <span class="badge bg-danger">Sale</span>
                                {% elif movement.movement_type == 'adjustment' %}
                                <span class="badge bg-warning">Adjustment</span>
                                {% elif movement.movement_type == 'return' %}
                                <span class="badge bg-info">Return</span>
                                {% elif movement.movement_type == 'expired' %}
                                <span class="badge bg-dark">Expired</span>
                                {% elif movement.movement_type == 'transfer' %}
                                <span class="badge bg-primary">Transfer</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ movement.get_movement_type_display }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.quantity > 0 %}
                                <span class="text-success">+{{ movement.quantity }}</span>
                                {% else %}
                                <span class="text-danger">{{ movement.quantity }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.brand %}
                                <span class="badge bg-info">{{ movement.brand }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.supplier %}
                                <span class="badge bg-secondary">{{ movement.supplier }}</span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>{{ movement.reference_number|default:"-" }}</td>
                            <td>
                                <a href="{% url 'stock_movement_detail' movement.id %}" class="btn btn-sm btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{% url 'medicine_movement_analysis' movement.medicine.id %}" class="btn btn-sm btn-primary">
                                    <i class="fas fa-chart-line"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">No stock movements found.</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            {% if movements.has_other_pages %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center mt-4">
                    {% if movements.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1&sort_by={{ sort_by }}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">First</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ movements.previous_page_number }}&sort_by={{ sort_by }}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">Previous</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">First</span>
                    </li>
                    <li class="page-item disabled">
                        <span class="page-link">Previous</span>
                    </li>
                    {% endif %}

                    {% for i in movements.paginator.page_range %}
                    {% if movements.number == i %}
                    <li class="page-item active">
                        <span class="page-link">{{ i }}</span>
                    </li>
                    {% elif i > movements.number|add:'-3' and i < movements.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ i }}&sort_by={{ sort_by }}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">{{ i }}</a>
                    </li>
                    {% endif %}
                    {% endfor %}

                    {% if movements.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ movements.next_page_number }}&sort_by={{ sort_by }}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">Next</a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ movements.paginator.num_pages }}&sort_by={{ sort_by }}&search={{ search }}&movement_type={{ movement_type }}&date_filter={{ date_filter }}&brand={{ brand_filter }}&supplier={{ supplier_filter }}">Last</a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <span class="page-link">Next</span>
                    </li>
                    <li class="page-item disabled">
                        <span class="page-link">Last</span>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="searchModalLabel">Advanced Search</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="advancedSearchForm" method="get" action="{% url 'stock_movement_list' %}">
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="modal-medicine-name" class="form-label">Medicine Name</label>
                            <select class="form-select" id="modal-medicine-name" name="medicine_name">
                                <option value="">All Medicines</option>
                                {% for medicine in all_medicines %}
                                <option value="{{ medicine.name }}">{{ medicine.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="modal-category" class="form-label">Category</label>
                            <select class="form-select" id="modal-category" name="category">
                                <option value="">All Categories</option>
                                {% for category in all_categories %}
                                <option value="{{ category }}">{{ category }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="modal-brand" class="form-label">Brand</label>
                            <select class="form-select" id="modal-brand" name="brand">
                                <option value="">All Brands</option>
                                {% for brand_name in all_brands %}
                                <option value="{{ brand_name }}">{{ brand_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="modal-supplier" class="form-label">Supplier</label>
                            <select class="form-select" id="modal-supplier" name="supplier">
                                <option value="">All Suppliers</option>
                                {% for supplier_name in all_suppliers %}
                                <option value="{{ supplier_name }}">{{ supplier_name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="modal-generic-medicine" class="form-label">Generic Medicine</label>
                            <select class="form-select" id="modal-generic-medicine" name="generic_medicine">
                                <option value="">All Generic Medicines</option>
                                {% for generic in all_generics %}
                                <option value="{{ generic.name }}">{{ generic.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="modal-reference" class="form-label">Reference Number</label>
                            <input type="text" class="form-control" id="modal-reference" name="reference">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" id="applyAdvancedSearch">Apply Search</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Movement Type Chart
        var ctx = document.getElementById('movementTypeChart').getContext('2d');
        var movementTypeData = {{ movement_type_data|safe }};

        var backgroundColors = [
            'rgba(78, 115, 223, 0.7)',
            'rgba(28, 200, 138, 0.7)',
            'rgba(246, 194, 62, 0.7)',
            'rgba(231, 74, 59, 0.7)',
            'rgba(54, 185, 204, 0.7)',
            'rgba(133, 135, 150, 0.7)'
        ];

        var borderColors = [
            'rgba(78, 115, 223, 1)',
            'rgba(28, 200, 138, 1)',
            'rgba(246, 194, 62, 1)',
            'rgba(231, 74, 59, 1)',
            'rgba(54, 185, 204, 1)',
            'rgba(133, 135, 150, 1)'
        ];

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: movementTypeData.labels,
                datasets: [{
                    data: movementTypeData.data,
                    backgroundColor: backgroundColors,
                    borderColor: borderColors,
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: window.innerWidth < 768 ? 'bottom' : 'right',
                        labels: {
                            boxWidth: 12,
                            padding: 15
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.label + ': ' + context.parsed + ' movements';
                            }
                        }
                    }
                }
            }
        });

        // Function to get medicine details
        function getMedicineDetails(medicineId) {
            // Find the medicine in the list
            const medicineSelect = document.getElementById('modal-medicine-name');
            const selectedMedicine = medicineSelect.options[medicineSelect.selectedIndex].text;

            // Find the medicine's details from the data attributes
            {% for medicine in all_medicines %}
            if ("{{ medicine.name }}" === selectedMedicine) {
                // Set the brand, supplier, and category
                const brandSelect = document.getElementById('modal-brand');
                const supplierSelect = document.getElementById('modal-supplier');
                const categorySelect = document.getElementById('modal-category');
                const genericSelect = document.getElementById('modal-generic-medicine');

                // Set brand if available
                if ("{{ medicine.brand }}") {
                    for (let i = 0; i < brandSelect.options.length; i++) {
                        if (brandSelect.options[i].value === "{{ medicine.brand }}") {
                            brandSelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                // Set supplier if available
                if ("{{ medicine.supplier }}") {
                    for (let i = 0; i < supplierSelect.options.length; i++) {
                        if (supplierSelect.options[i].value === "{{ medicine.supplier }}") {
                            supplierSelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                // Set category if available
                if ("{{ medicine.category }}") {
                    for (let i = 0; i < categorySelect.options.length; i++) {
                        if (categorySelect.options[i].value === "{{ medicine.category }}") {
                            categorySelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                // Set generic medicine if available
                if ("{{ medicine.generic_medicine.id }}") {
                    for (let i = 0; i < genericSelect.options.length; i++) {
                        if (genericSelect.options[i].value === "{{ medicine.generic_medicine.name }}") {
                            genericSelect.selectedIndex = i;
                            break;
                        }
                    }
                }

                return;
            }
            {% endfor %}
        }

        // Add event listener to medicine select
        document.getElementById('modal-medicine-name').addEventListener('change', function() {
            getMedicineDetails(this.value);
        });

        // Advanced Search Modal
        document.getElementById('applyAdvancedSearch').addEventListener('click', function() {
            const form = document.getElementById('advancedSearchForm');
            const formData = new FormData(form);

            // Build search query
            let searchQuery = '';

            const medicineName = formData.get('medicine_name');
            if (medicineName) {
                searchQuery += medicineName + ' ';
            }

            const category = formData.get('category');
            if (category) {
                searchQuery += category + ' ';
            }

            const brand = formData.get('brand');
            if (brand) {
                searchQuery += brand + ' ';
            }

            const supplier = formData.get('supplier');
            if (supplier) {
                searchQuery += supplier + ' ';
            }

            const genericMedicine = formData.get('generic_medicine');
            if (genericMedicine) {
                searchQuery += genericMedicine + ' ';
            }

            const reference = formData.get('reference');
            if (reference) {
                searchQuery += reference + ' ';
            }

            // Set the search query to the main search input
            document.getElementById('search').value = searchQuery.trim();

            // Close the modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('searchModal'));
            modal.hide();

            // Submit the main form
            document.querySelector('form.row.g-3').submit();
        });
    });
</script>
{% endblock %}
