# Generated by Django 5.1.2 on 2025-02-14 02:36

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('inventory', '0006_audittrail_transaction_audit_trail'),
    ]

    operations = [
        migrations.AddField(
            model_name='audittrail',
            name='content_type',
            field=models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='object_id',
            field=models.PositiveIntegerField(default=0),
        ),
    ]
