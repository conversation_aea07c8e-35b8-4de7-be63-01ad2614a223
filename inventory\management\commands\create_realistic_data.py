import random
import math
from datetime import datetime, timedelta
from decimal import Decimal

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from inventory.models import Medicine, Transaction, EmailNotificationSetting
from django.db.models import Q
import logging
from django.core.mail import get_connection

logger = logging.getLogger(__name__)

# Customer types
CUSTOMER_TYPES = ['in_patient', 'out_patient', 'emergency', 'pharmacy']

# Customer names (realistic Filipino names)
CUSTOMER_NAMES = [
    "Juan Dela Cruz", "Maria Santos", "<PERSON> Reyes", "Ana Lim", "<PERSON> Garcia",
    "<PERSON> Mendoza", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
    "<PERSON>", "<PERSON>", "<PERSON> Cruz", "Sophia Tan", "Mateo Mendoza",
    "<PERSON> Aquino", "<PERSON>a <PERSON>", "<PERSON>", "Rosario Bautista",
    "<PERSON> Ramos", "Victoria Reyes", "Francisco Santos", "Luisa Gonzales"
]

def generate_patient_number():
    """Generate a realistic patient number"""
    year = random.randint(2023, 2025)
    number = random.randint(10000, 99999)
    return f"P-{year}-{number}"

def create_transaction(medicine, date, transaction_type, quantity):
    """Create a transaction for a medicine"""
    try:
        # For purchases, use a default customer type and name
        if transaction_type == 'purchase':
            customer_name = "Medical Supplier Inc."
            patient_number = None
            customer_type = 'pharmacy'  # Use pharmacy as default for purchases
        else:  # For sales
            customer_name = random.choice(CUSTOMER_NAMES)
            patient_number = generate_patient_number()
            customer_type = random.choice(CUSTOMER_TYPES)

        # Calculate total amount based on medicine price
        total_amount = quantity * medicine.price

        # Create the transaction
        transaction = Transaction.objects.create(
            medicine=medicine,
            transaction_date=date,
            quantity=quantity,
            transaction_type=transaction_type,
            customer_name=customer_name,
            patient_number=patient_number,
            customer_type=customer_type,
            total_amount=total_amount
        )

        # Update medicine quantity
        if transaction_type == 'purchase':
            medicine.quantity += quantity
        else:  # sale
            medicine.quantity = max(0, medicine.quantity - quantity)

        medicine.save()

        return transaction
    except Exception as e:
        logger.error(f"Error creating transaction for {medicine.name}: {str(e)}")
        return None

def generate_seasonal_pattern(base_demand, month, medicine_name, category):
    """Generate a seasonal pattern based on the medicine and month"""
    # Different medicines have different seasonal patterns based on category
    if category == 'Respiratory' or category == 'Antihistamines':
        # Respiratory/allergy medicines peak in spring (March-May) and fall (Sep-Nov)
        if month in [3, 4, 5, 9, 10, 11]:
            return base_demand * random.uniform(1.3, 1.8)
        return base_demand * random.uniform(0.7, 1.0)

    elif category == 'Analgesics' or category == 'Antibiotics':
        # Pain/fever/infection medicines peak in winter (Dec-Feb) and summer (Jun-Aug)
        if month in [12, 1, 2, 6, 7, 8]:
            return base_demand * random.uniform(1.2, 1.6)
        return base_demand * random.uniform(0.8, 1.1)

    elif category == 'Antidiabetic' or category == 'Cardiovascular':
        # Chronic disease medicines have stable demand with slight increase after holidays
        if month in [1, 2, 7, 8]:
            return base_demand * random.uniform(1.1, 1.3)
        return base_demand * random.uniform(0.9, 1.1)

    # Default pattern with slight randomness
    seasonal_factor = 1.0 + 0.2 * math.sin(month * math.pi / 6)
    return base_demand * seasonal_factor * random.uniform(0.9, 1.1)

class Command(BaseCommand):
    help = 'Generate realistic transaction data for existing medicines'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='Clear existing transactions before generating new data',
        )
        parser.add_argument(
            '--remove-test',
            action='store_true',
            help='Remove medicines with "test" in their name',
        )
        parser.add_argument(
            '--start-date',
            type=str,
            default='2023-01-01',
            help='Start date for transactions (YYYY-MM-DD)',
        )
        parser.add_argument(
            '--end-date',
            type=str,
            default='2025-06-01',
            help='End date for transactions (YYYY-MM-DD)',
        )
        parser.add_argument(
            '--frequency',
            type=float,
            default=0.7,
            help='Frequency of transactions (0.0-1.0)',
        )

    def handle(self, *args, **options):
        # Parse date arguments
        try:
            start_date = datetime.strptime(options['start_date'], '%Y-%m-%d')
            start_date = timezone.make_aware(start_date)

            end_date = datetime.strptime(options['end_date'], '%Y-%m-%d')
            end_date = timezone.make_aware(end_date)

            frequency = min(1.0, max(0.1, options['frequency']))
        except ValueError:
            self.stdout.write(self.style.ERROR('Invalid date format. Use YYYY-MM-DD.'))
            return

        # Temporarily disable email notifications to avoid flooding
        self.stdout.write(self.style.WARNING('Temporarily disabling email notifications...'))
        email_settings = EmailNotificationSetting.objects.filter(is_enabled=True)
        email_settings_ids = list(email_settings.values_list('id', flat=True))
        email_settings.update(is_enabled=False)

        # Remove test medicines if requested
        if options['remove_test']:
            self.stdout.write(self.style.WARNING('Removing test medicines...'))
            test_medicines = Medicine.objects.filter(
                Q(name__icontains='test') |
                Q(description__icontains='test')
            )
            count = test_medicines.count()
            test_medicines.delete()
            self.stdout.write(self.style.SUCCESS(f'Removed {count} test medicines.'))

        # Clear existing transactions if requested
        if options['clear']:
            self.stdout.write(self.style.WARNING('Clearing existing transactions...'))
            Transaction.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing transactions cleared.'))

            # Reset all medicine quantities to zero
            Medicine.objects.all().update(quantity=0)
            self.stdout.write(self.style.SUCCESS('Reset all medicine quantities to zero.'))

        # Get all real medicines
        medicines = list(Medicine.objects.all())
        if not medicines:
            self.stdout.write(self.style.ERROR('No medicines found in the database. Please add medicines first.'))
            return

        self.stdout.write(self.style.SUCCESS(f'Found {len(medicines)} medicines in the database.'))

        # Generate transactions from start_date to end_date
        current_date = start_date
        transaction_count = 0
        purchase_count = 0
        sale_count = 0

        # Use transaction atomic to speed up the process
        with transaction.atomic():
            while current_date <= end_date:
                # For each day, create some transactions
                for medicine in medicines:
                    # Determine if we create a transaction today (based on frequency)
                    if random.random() < frequency:
                        # Determine transaction type (80% sales, 20% purchases)
                        transaction_type = 'sale' if random.random() < 0.8 else 'purchase'

                        # Base quantity
                        base_quantity = random.randint(5, 15)

                        # Apply seasonal pattern based on medicine category
                        month = current_date.month
                        adjusted_quantity = int(generate_seasonal_pattern(
                            base_quantity,
                            month,
                            medicine.name,
                            medicine.category
                        ))

                        # For purchases, make larger quantities
                        if transaction_type == 'purchase':
                            adjusted_quantity *= random.randint(3, 6)
                            purchase_count += 1
                        else:
                            sale_count += 1

                        # Create the transaction with a random time during business hours
                        trans_time = timedelta(
                            hours=random.randint(8, 17),
                            minutes=random.randint(0, 59),
                            seconds=random.randint(0, 59)
                        )

                        trans = create_transaction(
                            medicine=medicine,
                            date=current_date + trans_time,
                            transaction_type=transaction_type,
                            quantity=adjusted_quantity
                        )

                        if trans:
                            transaction_count += 1

                # Move to next day
                current_date += timedelta(days=1)

                # Print progress every month
                if current_date.day == 1:
                    self.stdout.write(f"Generated transactions for {current_date.strftime('%B %Y')}")

        self.stdout.write(self.style.SUCCESS(f"Data generation complete!"))
        self.stdout.write(f"Created {transaction_count} transactions ({purchase_count} purchases, {sale_count} sales)")
        self.stdout.write(f"Updated stock levels for {len(medicines)} medicines")

        # Show current stock levels for a few medicines
        sample_size = min(5, len(medicines))
        sample_medicines = random.sample(medicines, sample_size)

        self.stdout.write("\nCurrent stock levels for sample medicines:")
        for med in sample_medicines:
            self.stdout.write(f"{med.name}: {med.quantity} units")
