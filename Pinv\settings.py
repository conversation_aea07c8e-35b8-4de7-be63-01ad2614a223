"""
Django settings for Pinv project.

Generated by 'django-admin startproject' using Django 5.1.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.1/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.1/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.1/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-xij+vafdahhl*zrv966m3u&kb^p+@&4t$kv1$lk_t4oe)agr^9'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
        'jazzmin',
        'widget_tweaks',
     'user_management', 
     'django.contrib.humanize',
     'inventory',
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
]

MIDDLEWARE = [

      'inventory.middleware.TimezoneMiddleware',
     'django.middleware.gzip.GZipMiddleware',
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'Pinv.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'Pinv.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.1/ref/settings/#databases

# Update the DATABASES configuration
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': 'pinv_db',  # Your database name
        'USER': 'postgres',  # Your PostgreSQL username
        'PASSWORD': 'admin',  # Your PostgreSQL password
        'HOST': 'localhost',
        'PORT': '5432',
        # Performance optimizations
        'CONN_MAX_AGE': 60,
        'OPTIONS': {
            'connect_timeout': 10,
            'client_encoding': 'UTF8'
        },
        'ATOMIC_REQUESTS': False,
        'AUTOCOMMIT': True,
    }
}


# Password validation
# https://docs.djangoproject.com/en/5.1/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.1/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'Asia/Manila'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.1/howto/static-files/

import os

STATIC_URL = 'static/'

STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'inventory', 'static'),
]

# Default primary key field type
# https://docs.djangoproject.com/en/5.1/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'unique-snowflake',
    }
}


# Add these settings for media files (profile pictures)
MEDIA_URL = '/media/'
MEDIA_ROOT = BASE_DIR / 'media'

# Authentication settings
LOGIN_URL = 'login'
LOGIN_REDIRECT_URL = 'home'
LOGOUT_REDIRECT_URL = 'login'


# Make sure you have the authentication backends configured
AUTHENTICATION_BACKENDS = [
    'django.contrib.auth.backends.ModelBackend',
]

ADMIN_URL = 'http://127.0.0.1:8000/admin/'  # Development
# ADMIN_URL = 'https://yourdomain.com/admin/'  # Production



SITE_URL = 'http://127.0.0.1:8000' 

# Email settings
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'bgso emus gqjg eioc'  
DEFAULT_FROM_EMAIL = 'BMC MedForecast <<EMAIL>>'


# Add logging configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'inventory': {  # This will catch all loggers in your inventory app
            'handlers': ['console'],
            'level': 'INFO',
        },
    },
}




JAZZMIN_SETTINGS = {
    # Custom files - specify the correct paths
    "custom_css": "admin/css/custom_admin.css",
    "custom_js": "admin/js/custom_admin.js",
    
    # Basic settings
    "site_title": "BMC Medforecast Admin",
    "site_header": "BMC Medforecast",
    "site_brand": "BMC Medforecast",
    "site_logo": "img/logo.png",
    "site_icon": "img/logo.png",
    "welcome_sign": "Welcome to BMC Medforecast Administration",
    "copyright": "BMC Medforecast",
    "search_model": ["inventory.Medicine", "auth.User"],
    
    # User avatar
    "user_avatar": lambda user: (
        user.profile.profile_picture.url 
        if hasattr(user, 'profile') and user.profile.profile_picture 
        else user.profile.get_initials() if hasattr(user, 'profile') 
        else user.get_full_name()[:2].upper() if user.get_full_name() 
        else user.username[:2].upper()
    ),

    # Footer and branding settings
    "show_ui_builder": False,
    "show_footer": True,  # Keep True to allow custom JS to modify it
    "footer_fixed": False,
    "show_powered_by": False,
    
    # Hide specific apps/models
    "hide_apps": [],
    "hide_models": [],
    
    # Additional settings
    "use_google_fonts_cdn": False,

   "topmenu_links": [
        {"name": "Home", "url": "admin:index", "permissions": ["auth.view_user"]},
        {"name": "View Site", "url": "/", "new_window": True, "icon": "fas fa-globe"},  # Added icon
        {"name": "Inventory", "url": "/inventory/", "permissions": ["inventory.view_medicine"]},
        {"model": "auth.User"},
    ],


    # Side Menu
    "show_sidebar": True,
    "navigation_expanded": True,
    "icons": {
        "auth": "fas fa-users-cog",
        "auth.user": "fas fa-user",
        "auth.Group": "fas fa-users",
        "inventory.Medicine": "fas fa-pills",
        "inventory.Transaction": "fas fa-exchange-alt",
        "inventory.Forecast": "fas fa-chart-line",
        "inventory.AuditTrail": "fas fa-history",
        "user_management.UserProfile": "fas fa-id-card",
    },
    "default_icon_parents": "fas fa-chevron-circle-right",
    "default_icon_children": "fas fa-circle",

    # Related Modal
    "related_modal_active": True,
}

# UI Customization
JAZZMIN_UI_TWEAKS = {
    "navbar_small_text": False,
    "footer_small_text": False,
    "body_small_text": False,
    "brand_small_text": False,
    "brand_colour": False,
    "accent": "accent-primary",
    "navbar": "navbar-dark",
    "no_navbar_border": False,
    "navbar_fixed": True,
    "layout_boxed": False,
    "footer_fixed": False,
    "sidebar_fixed": True,
    "sidebar": "sidebar-dark-primary",
    "sidebar_nav_small_text": False,
    "sidebar_disable_expand": False,
    "sidebar_nav_child_indent": True,
    "sidebar_nav_compact_style": False,
    "sidebar_nav_legacy_style": False,
    "sidebar_nav_flat_style": False,
    "theme": "default",
    "dark_mode_theme": None,
    "button_classes": {
        "primary": "btn-primary",
        "secondary": "btn-secondary",
        "info": "btn-info",
        "warning": "btn-warning",
        "danger": "btn-danger",
        "success": "btn-success"
    }
}