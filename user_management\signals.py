from django.db.models.signals import post_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from inventory.models import EmailNotificationSetting
from .models import UserProfile
import logging

User = get_user_model()
logger = logging.getLogger(__name__)

@receiver(post_save, sender=User)
def create_user_profile(sender, instance, created, **kwargs):
    """Create a UserProfile for every new User and add them to notification recipients."""
    logger.info(f"Signal triggered for user {instance.username}, created={created}")

    if created:
        try:
            # Check if profile already exists before creating
            if not hasattr(instance, 'profile'):
                UserProfile.objects.create(
                    user=instance,
                    first_name=instance.first_name or '',
                    last_name=instance.last_name or ''
                )
                logger.info(f"Created profile for user {instance.username}")

            # Add user to all notification types
            notification_types = [
                EmailNotificationSetting.LOW_STOCK,
                EmailNotificationSetting.OUT_OF_STOCK,
                EmailNotificationSetting.EXPIRING_SOON,
                EmailNotificationSetting.PRICE_CHANGE
            ]

            for notification_type in notification_types:
                notification_setting, notification_created = EmailNotificationSetting.objects.get_or_create(
                    notification_type=notification_type,
                    defaults={'is_enabled': True}
                )
                notification_setting.recipients.add(instance)
                logger.info(f"Added new user {instance.username} to {notification_type} notifications")
        except Exception as e:
            logger.error(f"Failed to create profile or add notifications for {instance.username}: {str(e)}")

@receiver(post_save, sender=User)
def save_user_profile(sender, instance, **kwargs):
    """Update UserProfile when User is updated."""
    try:
        if hasattr(instance, 'profile'):  # Note: changed from userprofile to profile
            instance.profile.save()
            logger.info(f"Updated profile for user {instance.username}")
    except Exception as e:
        logger.error(f"Failed to save profile for {instance.username}: {str(e)}")