<table class="table table-hover align-middle">
    <thead class="bg-light">
        <tr>
            <th>Medicine Name</th>
            <th>Category</th>
            <th>Current Stock</th>
            <th>Reorder Level</th>
            <th>Predicted Demand</th>
            <th>Recommended Order</th>
            <th>Trend</th>
            <th>Total Cost</th>
            <th>Actions</th>
        </tr>
    </thead>
    <tbody>
        {% for medicine in medicines %}
        <tr>
            <td>{{ medicine.name }}</td>
            <td>{{ medicine.category }}</td>
            <td>
                <span class="badge bg-{% if medicine.current_stock <= medicine.reorder_level %}danger{% else %}success{% endif %}">
                    {{ medicine.current_stock }}
                </span>
            </td>
            <td>{{ medicine.reorder_level }}</td>
            <td>{{ medicine.predicted_demand }}</td>
            <td>{{ medicine.recommended_order }}</td>
            <td>
                <span class="text-{% if medicine.trend_direction == 'up' %}danger{% elif medicine.trend_direction == 'down' %}success{% else %}warning{% endif %}">
                    <i class="fas fa-arrow-{{ medicine.trend_direction }}"></i>
                    {{ medicine.trend_percentage }}%
                </span>
            </td>
            <td>${{ medicine.total_cost|floatformat:2 }}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary view-forecast"
                        data-medicine-id="{{ medicine.id }}">
                    <i class="fas fa-chart-line"></i>
                </button>
            </td>
        </tr>
        {% endfor %}
    </tbody>
</table>