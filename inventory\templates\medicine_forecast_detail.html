{% extends 'base.html' %}
{% load static %}
{% load custom_filters %}

{% block title %}{{ medicine.name }} Forecast{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0">
            <i class="fas fa-chart-line text-primary me-2"></i>
            {{ medicine.name }} Forecast
        </h1>
        <div>
            <a href="{% url 'medicine_detail' medicine.id %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Medicine
            </a>
        </div>
    </div>

    <!-- Medicine Overview -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-pills me-2 text-primary"></i>Medicine Overview
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Current Stock</h6>
                                <div class="d-flex align-items-center">
                                    <h3 class="mb-0 me-2">{{ medicine.quantity }}</h3>
                                    <span class="badge {% if medicine.quantity <= medicine.reorder_level %}bg-danger{% else %}bg-success{% endif %}">
                                        {% if medicine.quantity <= medicine.reorder_level %}Low Stock{% else %}In Stock{% endif %}
                                    </span>
                                </div>
                            </div>
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Reorder Level</h6>
                                <h5 class="mb-0">{{ medicine.reorder_level }}</h5>
                            </div>
                            <div>
                                <h6 class="text-muted mb-2">Price</h6>
                                <h5 class="mb-0">₱{{ medicine.price|floatformat:2 }}</h5>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Category</h6>
                                <h5 class="mb-0">{{ medicine.category }}</h5>
                            </div>
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Brand</h6>
                                <h5 class="mb-0">{{ medicine.brand|default:"Generic" }}</h5>
                            </div>
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Supplier</h6>
                                <h5 class="mb-0">{{ medicine.supplier|default:"Unknown" }}</h5>
                            </div>
                            {% if medicine.generic_medicine %}
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Generic Medicine</h6>
                                <h5 class="mb-0">{{ medicine.generic_medicine.name }}</h5>
                            </div>
                            {% endif %}
                            <div>
                                <h6 class="text-muted mb-2">Forecast Method</h6>
                                <h5 class="mb-0">{{ forecast_method|default:"Auto" }}</h5>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>Forecast Metrics
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Data Quality</h6>
                                <div class="d-flex align-items-center">
                                    <div class="progress flex-grow-1 me-2" style="height: 8px;">
                                        <div class="progress-bar bg-primary" role="progressbar" style="width: {{ data_quality.completeness|default:'0%' }}"></div>
                                    </div>
                                    <span>{{ data_quality.completeness|default:'0%' }}</span>
                                </div>
                                <small class="text-muted">Based on {{ data_quality.points|default:0 }} data points over {{ data_quality.range_days|default:0 }} days</small>
                            </div>
                            <div class="mb-4">
                                <h6 class="text-muted mb-2">Trend</h6>
                                <div class="d-flex align-items-center">
                                    <h5 class="mb-0 me-2">{{ trend_direction|title }}</h5>
                                    <span class="badge {% if trend_direction == 'increasing' %}bg-success{% elif trend_direction == 'decreasing' %}bg-danger{% else %}bg-secondary{% endif %}">
                                        {% if trend_direction == 'increasing' %}<i class="fas fa-arrow-up"></i>
                                        {% elif trend_direction == 'decreasing' %}<i class="fas fa-arrow-down"></i>
                                        {% else %}<i class="fas fa-minus"></i>{% endif %}
                                    </span>
                                </div>
                                <small class="text-muted">Strength: {{ trend_strength|floatformat:2 }}</small>
                            </div>
                            <div>
                                <h6 class="text-muted mb-2">Seasonality</h6>
                                <h5 class="mb-0">{% if has_seasonality %}Yes{% else %}No{% endif %}</h5>
                                {% if has_seasonality %}
                                <small class="text-muted">Peak period: {{ peak_period }}</small>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <!-- Forecast Method -->
                            <div class="d-flex align-items-center h-100">
                                <div class="w-100">
                                    <h5 class="text-muted mb-3">Forecast Method</h5>
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="d-flex align-items-center justify-content-center w-100">
                                            <div class="text-center">
                                                <div class="rounded-circle bg-primary bg-opacity-10 d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                                                    <i class="fas fa-chart-line text-primary fs-1"></i>
                                                </div>
                                                <h4 class="mb-0 fw-bold text-primary">{{ forecast_method|default:"Auto"|upper }}</h4>
                                                <p class="text-muted small mt-2">{{ forecast.forecast_period|default:"30" }} day forecast period</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="alert alert-info alert-dismissible fade show mb-0" role="alert">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Forecast Recommendation:</strong>
                            {% if medicine.quantity <= medicine.reorder_level %}
                                Order <strong>{{ recommended_order|default:"15" }}</strong> units to maintain optimal stock levels.
                            {% else %}
                                No immediate action needed. Current stock is above reorder level.
                            {% endif %}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Seasonal Analysis -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h4 class="mb-0">
                        <i class="fas fa-calendar-alt me-2 text-primary"></i>Seasonal Analysis
                    </h4>
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-4">
                                <h5 class="text-muted mb-3">Quarterly Demand Patterns</h5>
                                <div class="chart-container" style="position: relative; height: 250px;">
                                    <canvas id="quarterlyChart"></canvas>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="text-muted mb-3">Quarterly Breakdown</h5>
                            <table class="table table-borderless">
                                <thead>
                                    <tr class="text-muted">
                                        <th>Quarter</th>
                                        <th class="text-end">Avg. Demand</th>
                                        <th class="text-end">Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% if seasonal_analysis and seasonal_analysis.seasonal_patterns %}
                                        <!-- Q1 -->
                                        <tr>
                                            <td class="fw-bold">Q1</td>
                                            <td class="text-end">{% if seasonal_analysis.seasonal_patterns.1 %}{{ seasonal_analysis.seasonal_patterns.1.mean|floatformat:1 }}{% else %}0.0{% endif %}</td>
                                            <td class="text-end">
                                                {% if seasonal_analysis.peak_quarter == 1 %}
                                                    <span class="badge bg-success rounded-pill px-2">Peak</span>
                                                {% elif seasonal_analysis.low_quarter == 1 %}
                                                    <span class="badge bg-danger rounded-pill px-2">Low</span>
                                                {% else %}
                                                    <span class="badge bg-secondary rounded-pill px-2">Normal</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <!-- Q2 -->
                                        <tr>
                                            <td class="fw-bold">Q2</td>
                                            <td class="text-end">{% if seasonal_analysis.seasonal_patterns.2 %}{{ seasonal_analysis.seasonal_patterns.2.mean|floatformat:1 }}{% else %}0.0{% endif %}</td>
                                            <td class="text-end">
                                                {% if seasonal_analysis.peak_quarter == 2 %}
                                                    <span class="badge bg-success rounded-pill px-2">Peak</span>
                                                {% elif seasonal_analysis.low_quarter == 2 %}
                                                    <span class="badge bg-danger rounded-pill px-2">Low</span>
                                                {% else %}
                                                    <span class="badge bg-secondary rounded-pill px-2">Normal</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <!-- Q3 -->
                                        <tr>
                                            <td class="fw-bold">Q3</td>
                                            <td class="text-end">{% if seasonal_analysis.seasonal_patterns.3 %}{{ seasonal_analysis.seasonal_patterns.3.mean|floatformat:1 }}{% else %}0.0{% endif %}</td>
                                            <td class="text-end">
                                                {% if seasonal_analysis.peak_quarter == 3 %}
                                                    <span class="badge bg-success rounded-pill px-2">Peak</span>
                                                {% elif seasonal_analysis.low_quarter == 3 %}
                                                    <span class="badge bg-danger rounded-pill px-2">Low</span>
                                                {% else %}
                                                    <span class="badge bg-secondary rounded-pill px-2">Normal</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <!-- Q4 -->
                                        <tr>
                                            <td class="fw-bold">Q4</td>
                                            <td class="text-end">{% if seasonal_analysis.seasonal_patterns.4 %}{{ seasonal_analysis.seasonal_patterns.4.mean|floatformat:1 }}{% else %}0.0{% endif %}</td>
                                            <td class="text-end">
                                                {% if seasonal_analysis.peak_quarter == 4 %}
                                                    <span class="badge bg-success rounded-pill px-2">Peak</span>
                                                {% elif seasonal_analysis.low_quarter == 4 %}
                                                    <span class="badge bg-danger rounded-pill px-2">Low</span>
                                                {% else %}
                                                    <span class="badge bg-secondary rounded-pill px-2">Normal</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    {% else %}
                                        <tr>
                                            <td colspan="3" class="text-center">No seasonal data available</td>
                                        </tr>
                                    {% endif %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Forecast Visualization -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h4 class="mb-0">
                            <i class="fas fa-chart-line me-2 text-primary"></i>Forecast Visualization
                        </h4>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-sm btn-outline-primary active" data-period="weekly">Weekly</button>
                            <button type="button" class="btn btn-sm btn-outline-primary" data-period="monthly">Monthly</button>
                            <button type="button" class="btn btn-sm btn-outline-primary" data-period="yearly">Yearly</button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="chart-container" style="position: relative; height: 300px;">
                        <canvas id="forecastChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Forecast Data Tables -->
    <div class="row g-4">
        <!-- Weekly Forecasts -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom-0 pt-4 pb-0">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary rounded-pill p-2 me-2">
                            <i class="fas fa-calendar-week"></i>
                        </span>
                        <h4 class="mb-0">Weekly Forecasts</h4>
                    </div>
                </div>
                <div class="card-body p-4">
                    {% if forecasts.weekly %}
                    <div class="table-responsive">
                        <table class="table table-borderless table-hover">
                            <thead>
                                <tr class="text-muted">
                                    <th>Week Ending</th>
                                    <th class="text-center">Forecast</th>
                                    <th class="text-end">Range</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for forecast in forecasts.weekly %}
                                <tr>
                                    <td class="fw-bold">{{ forecast.date }}</td>
                                    <td class="text-center">{{ forecast.value|floatformat:"0" }}</td>
                                    <td class="text-end">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <div class="me-2 small text-muted">±{{ forecast.value|subtract:forecast.lower|floatformat:"0" }}</div>
                                            <span class="badge bg-light text-dark" data-bs-toggle="tooltip"
                                                  title="95% Confidence Interval: {{ forecast.lower|floatformat:"0" }} - {{ forecast.upper|floatformat:"0" }}">
                                                <i class="fas fa-info-circle"></i>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center text-muted">
                            <i class="fas fa-calendar-times fs-1"></i>
                            <p class="mt-2">No weekly forecasts available</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Monthly Forecasts -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom-0 pt-4 pb-0">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary rounded-pill p-2 me-2">
                            <i class="fas fa-calendar-alt"></i>
                        </span>
                        <h4 class="mb-0">Monthly Forecasts</h4>
                    </div>
                </div>
                <div class="card-body p-4">
                    {% if forecasts.monthly %}
                    <div class="table-responsive">
                        <table class="table table-borderless table-hover">
                            <thead>
                                <tr class="text-muted">
                                    <th>Month</th>
                                    <th class="text-center">Forecast</th>
                                    <th class="text-end">Range</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for forecast in forecasts.monthly %}
                                <tr>
                                    <td class="fw-bold">{{ forecast.date }}</td>
                                    <td class="text-center">{{ forecast.value|floatformat:"0" }}</td>
                                    <td class="text-end">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <div class="me-2 small text-muted">±{{ forecast.value|subtract:forecast.lower|floatformat:"0" }}</div>
                                            <span class="badge bg-light text-dark" data-bs-toggle="tooltip"
                                                  title="95% Confidence Interval: {{ forecast.lower|floatformat:"0" }} - {{ forecast.upper|floatformat:"0" }}">
                                                <i class="fas fa-info-circle"></i>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center text-muted">
                            <i class="fas fa-calendar-times fs-1"></i>
                            <p class="mt-2">No monthly forecasts available</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Yearly Forecasts -->
        <div class="col-md-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom-0 pt-4 pb-0">
                    <div class="d-flex align-items-center">
                        <span class="badge bg-primary rounded-pill p-2 me-2">
                            <i class="fas fa-calendar-day"></i>
                        </span>
                        <h4 class="mb-0">Yearly Forecasts</h4>
                    </div>
                </div>
                <div class="card-body p-4">
                    {% if forecasts.yearly %}
                    <div class="table-responsive">
                        <table class="table table-borderless table-hover">
                            <thead>
                                <tr class="text-muted">
                                    <th>Year</th>
                                    <th class="text-center">Forecast</th>
                                    <th class="text-end">Range</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for forecast in forecasts.yearly %}
                                <tr>
                                    <td class="fw-bold">{{ forecast.date }}</td>
                                    <td class="text-center">{{ forecast.value|floatformat:"0" }}</td>
                                    <td class="text-end">
                                        <div class="d-flex align-items-center justify-content-end">
                                            <div class="me-2 small text-muted">±{{ forecast.value|subtract:forecast.lower|floatformat:"0" }}</div>
                                            <span class="badge bg-light text-dark" data-bs-toggle="tooltip"
                                                  title="95% Confidence Interval: {{ forecast.lower|floatformat:"0" }} - {{ forecast.upper|floatformat:"0" }}">
                                                <i class="fas fa-info-circle"></i>
                                            </span>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="d-flex justify-content-center align-items-center h-100">
                        <div class="text-center text-muted">
                            <i class="fas fa-calendar-times fs-1"></i>
                            <p class="mt-2">No yearly forecasts available</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });

        // Prepare forecast data with error handling
        const forecastData = {
            weekly: {
                labels: [{% if forecasts.weekly %}{% for forecast in forecasts.weekly %}"{{ forecast.date }}",{% endfor %}{% endif %}],
                values: [{% if forecasts.weekly %}{% for forecast in forecasts.weekly %}{{ forecast.value }},{% endfor %}{% endif %}],
                lower: [{% if forecasts.weekly %}{% for forecast in forecasts.weekly %}{{ forecast.lower }},{% endfor %}{% endif %}],
                upper: [{% if forecasts.weekly %}{% for forecast in forecasts.weekly %}{{ forecast.upper }},{% endfor %}{% endif %}]
            },
            monthly: {
                labels: [{% if forecasts.monthly %}{% for forecast in forecasts.monthly %}"{{ forecast.date }}",{% endfor %}{% endif %}],
                values: [{% if forecasts.monthly %}{% for forecast in forecasts.monthly %}{{ forecast.value }},{% endfor %}{% endif %}],
                lower: [{% if forecasts.monthly %}{% for forecast in forecasts.monthly %}{{ forecast.lower }},{% endfor %}{% endif %}],
                upper: [{% if forecasts.monthly %}{% for forecast in forecasts.monthly %}{{ forecast.upper }},{% endfor %}{% endif %}]
            },
            yearly: {
                labels: [{% if forecasts.yearly %}{% for forecast in forecasts.yearly %}"{{ forecast.date }}",{% endfor %}{% endif %}],
                values: [{% if forecasts.yearly %}{% for forecast in forecasts.yearly %}{{ forecast.value }},{% endfor %}{% endif %}],
                lower: [{% if forecasts.yearly %}{% for forecast in forecasts.yearly %}{{ forecast.lower }},{% endfor %}{% endif %}],
                upper: [{% if forecasts.yearly %}{% for forecast in forecasts.yearly %}{{ forecast.upper }},{% endfor %}{% endif %}]
            }
        };

        // Create chart
        const ctx = document.getElementById('forecastChart').getContext('2d');
        let currentPeriod = 'weekly';
        let forecastChart;

        function createChart(period) {
            // Destroy existing chart if it exists
            if (forecastChart) {
                forecastChart.destroy();
            }

            // Create new chart
            forecastChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: forecastData[period].labels,
                    datasets: [
                        {
                            label: 'Forecast',
                            data: forecastData[period].values,
                            borderColor: 'rgba(75, 192, 192, 1)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            borderWidth: 2,
                            tension: 0.1,
                            fill: false
                        },
                        {
                            label: 'Lower Bound',
                            data: forecastData[period].lower,
                            borderColor: 'rgba(75, 192, 192, 0.3)',
                            backgroundColor: 'transparent',
                            borderWidth: 1,
                            borderDash: [5, 5],
                            tension: 0.1,
                            fill: false,
                            pointRadius: 0
                        },
                        {
                            label: 'Upper Bound',
                            data: forecastData[period].upper,
                            borderColor: 'rgba(75, 192, 192, 0.3)',
                            backgroundColor: 'transparent',
                            borderWidth: 1,
                            borderDash: [5, 5],
                            tension: 0.1,
                            fill: '-1',
                            pointRadius: 0,
                            backgroundColor: 'rgba(75, 192, 192, 0.1)'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Demand'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: period.charAt(0).toUpperCase() + period.slice(1)
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        },
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                boxWidth: 6
                            }
                        }
                    }
                }
            });
        }

        // Initialize chart with weekly data
        createChart(currentPeriod);

        // Handle period button clicks
        const periodButtons = document.querySelectorAll('[data-period]');
        periodButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Update active button
                periodButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                // Update chart
                currentPeriod = this.getAttribute('data-period');
                createChart(currentPeriod);
            });
        });

        // Create quarterly chart
        const quarterlyChartCtx = document.getElementById('quarterlyChart').getContext('2d');

        // Prepare quarterly data
        const quarterlyData = {
            labels: ['Q1', 'Q2', 'Q3', 'Q4'],
            values: [0, 0, 0, 0] // Default values
        };

        // Try to populate with actual data if available
        {% if seasonal_analysis and seasonal_analysis.seasonal_patterns %}
            // Use simple approach without custom filters
            {% if seasonal_analysis.seasonal_patterns.1 %}
                quarterlyData.values[0] = {{ seasonal_analysis.seasonal_patterns.1.mean|default:0 }};
            {% endif %}
            {% if seasonal_analysis.seasonal_patterns.2 %}
                quarterlyData.values[1] = {{ seasonal_analysis.seasonal_patterns.2.mean|default:0 }};
            {% endif %}
            {% if seasonal_analysis.seasonal_patterns.3 %}
                quarterlyData.values[2] = {{ seasonal_analysis.seasonal_patterns.3.mean|default:0 }};
            {% endif %}
            {% if seasonal_analysis.seasonal_patterns.4 %}
                quarterlyData.values[3] = {{ seasonal_analysis.seasonal_patterns.4.mean|default:0 }};
            {% endif %}
        {% endif %}

        // Create quarterly chart
        new Chart(quarterlyChartCtx, {
            type: 'bar',
            data: {
                labels: quarterlyData.labels,
                datasets: [{
                    label: 'Average Demand',
                    data: quarterlyData.values,
                    backgroundColor: ['rgba(0, 123, 255, 0.7)', 'rgba(0, 123, 255, 0.7)', 'rgba(0, 123, 255, 0.7)', 'rgba(0, 123, 255, 0.7)'],
                    borderColor: ['rgb(0, 123, 255)', 'rgb(0, 123, 255)', 'rgb(0, 123, 255)', 'rgb(0, 123, 255)'],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Average Demand'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Quarter'
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return `Demand: ${context.raw.toFixed(1)}`;
                            }
                        }
                    }
                }
            }
        });

        // Accuracy gauge code has been removed and replaced with a progress bar
    });
</script>
{% endblock %}

{% endblock %}
