from django.urls import path
from django.contrib.auth.views import <PERSON>goutView
from . import views
from django.contrib import admin
from django.contrib.auth import views as auth_views

urlpatterns = [
   path('login/', views.CustomLoginView.as_view(), name='login'),
    path('logout/', LogoutView.as_view(next_page='login'), name='logout'),
    path('register/', views.register, name='register'),
    path('profile/', views.profile, name='profile'),
    path('users/', views.user_list, name='user_list'),
    path('users/<int:user_id>/', views.user_detail, name='user_detail'),
    path('users/<int:user_id>/toggle/', views.toggle_user_status, name='toggle_user_status'),
    path('users/<int:user_id>/approve-changes/', views.approve_profile_changes, name='approve_profile_changes'),
    path('users/<int:user_id>/reject-changes/', views.reject_profile_changes, name='reject_profile_changes'),
    path('notifications/', views.notification_list, name='notifications'),
    path('notifications/<int:notification_id>/mark-read/',
         views.mark_notification_read, name='mark_notification_read'),
    path('groups/', views.permission_groups, name='permission_groups'),

    path('forgot-password/', views.forgot_password, name='forgot_password'),
    path('reset-password/<str:email>/', views.reset_password, name='reset_password'),
    path('verify-login-otp/', views.verify_login_otp, name='verify_login_otp'),
    path('verify-totp-setup/', views.verify_totp_setup, name='verify_totp_setup'),
    path('verify-email/', views.verify_email, name='verify_email'),
    path('verify-email-prompt/', views.verify_email_prompt, name='verify_email_prompt'),
    path('resend-verification/', views.resend_verification, name='resend_verification'),

      # API endpoints
    path('api/notifications/', views.get_notifications, name='api_notifications'),
    path('api/notifications/<int:notification_id>/read/',
         views.mark_notification_read, name='api_mark_notification_read'),
    path('api/notifications/mark-all-read/',
         views.mark_all_notifications_read, name='api_mark_all_notifications_read'),
        path('api/notifications/<int:notification_id>/delete/',
         views.delete_notification, name='api_delete_notification'),

 path('profile/delete-picture/', views.delete_profile_picture, name='delete_profile_picture'),

    # Registration validation endpoints
    path('check-username/', views.check_username, name='check_username'),
    path('check-email/', views.check_email, name='check_email'),

    # Customer management
    path('customers/', views.customer_list, name='customer_list'),
    path('customers/<int:customer_id>/', views.customer_detail, name='customer_detail'),
    path('customers/add/', views.add_customer, name='add_customer'),
    path('customers/<int:customer_id>/edit/', views.edit_customer, name='edit_customer'),
    path('customers/<int:customer_id>/delete/', views.delete_customer, name='delete_customer'),
]