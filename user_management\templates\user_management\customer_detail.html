{% extends 'base.html' %}
{% load static %}

{% block title %}{{ customer.customer_name }} - Customer Details{% endblock %}

{% block page_title %}Customer Details{% endblock %}

{% block extra_css %}
<style>
    .customer-header {
        background: linear-gradient(135deg, #2c7be5 0%, #1a68d1 100%);
        color: white;
        padding: 2rem;
        border-radius: 0.5rem;
        margin-bottom: 1.5rem;
    }
    .customer-avatar {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        font-weight: bold;
        margin-right: 1.5rem;
    }
    .info-card {
        height: 100%;
        transition: all 0.2s ease;
    }
    .info-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
    }
    .info-icon {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
    }
    .transaction-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }
    .transaction-row:hover {
        background-color: rgba(0, 123, 255, 0.05);
    }
    .customer-type-in {
        color: #2c7be5;
        background-color: rgba(44, 123, 229, 0.1);
    }
    .customer-type-out {
        color: #00864e;
        background-color: rgba(0, 134, 78, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-3">
    {% if messages %}
    <div class="row animate__animated animate__fadeIn">
        <div class="col-12">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show shadow-sm border-0" role="alert">
                <i class="fas fa-info-circle me-2"></i> {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}

    <!-- Back Button -->
    <div class="row mb-3">
        <div class="col-12">
            <a href="{% url 'customer_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Customer List
            </a>
        </div>
    </div>

    <!-- Customer Header -->
    <div class="customer-header shadow-sm">
        <div class="d-flex align-items-center">
            <div class="customer-avatar">
                {{ customer.customer_name|slice:":1" }}
            </div>
            <div>
                <h1 class="h3 mb-1">{{ customer.customer_name }}</h1>
                <div class="d-flex align-items-center mb-2">
                    <span class="badge rounded-pill {% if customer.customer_type == 'in_patient' %}customer-type-in{% else %}customer-type-out{% endif %} me-2">
                        {{ customer.get_customer_type_display }}
                    </span>
                    {% if customer.patient_number %}
                    <span class="badge bg-light text-dark">
                        <i class="fas fa-id-card me-1"></i>{{ customer.patient_number }}
                    </span>
                    {% endif %}
                </div>
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-sm btn-light me-2" data-bs-toggle="modal" data-bs-target="#editCustomerModal">
                        <i class="fas fa-edit me-1"></i>Edit
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteCustomerModal">
                        <i class="fas fa-trash-alt me-1"></i>Delete
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Information -->
    <div class="row mb-4">
        <div class="col-md-4 mb-3">
            <div class="card shadow-sm border-0 info-card h-100">
                <div class="card-body">
                    <h5 class="card-title mb-3">Contact Information</h5>
                    <div class="d-flex align-items-center mb-3">
                        <div class="info-icon bg-primary bg-opacity-10 text-primary">
                            <i class="fas fa-phone-alt"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">Phone Number</small>
                            <span>{{ customer.phone_number|default:"Not provided" }}</span>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <div class="info-icon bg-info bg-opacity-10 text-info">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">Email</small>
                            <span>{{ customer.email|default:"Not provided" }}</span>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="info-icon bg-success bg-opacity-10 text-success">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">Address</small>
                            <span>{{ customer.address|default:"Not provided" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card shadow-sm border-0 info-card h-100">
                <div class="card-body">
                    <h5 class="card-title mb-3">Customer Details</h5>
                    <div class="d-flex align-items-center mb-3">
                        <div class="info-icon bg-warning bg-opacity-10 text-warning">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">Created On</small>
                            <span>{{ customer.created_at|date:"F d, Y" }}</span>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <div class="info-icon bg-danger bg-opacity-10 text-danger">
                            <i class="fas fa-history"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">Last Transaction</small>
                            <span>{{ customer.last_transaction_date|date:"F d, Y"|default:"No transactions" }}</span>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="info-icon bg-secondary bg-opacity-10 text-secondary">
                            <i class="fas fa-sticky-note"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">Notes</small>
                            <span>{{ customer.notes|default:"No notes" }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card shadow-sm border-0 info-card h-100">
                <div class="card-body">
                    <h5 class="card-title mb-3">Transaction Summary</h5>
                    <div class="d-flex align-items-center mb-3">
                        <div class="info-icon bg-primary bg-opacity-10 text-primary">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">Total Transactions</small>
                            <span>{{ transactions.count }}</span>
                        </div>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <div class="info-icon bg-success bg-opacity-10 text-success">
                            <i class="fas fa-pills"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">Total Medicines</small>
                            <span>{{ total_medicines }}</span>
                        </div>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="info-icon bg-info bg-opacity-10 text-info">
                            <i class="fas fa-money-bill-wave"></i>
                        </div>
                        <div>
                            <small class="text-muted d-block">Total Amount</small>
                            <span>₱{{ total_amount|floatformat:2 }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Transaction History -->
    <div class="card shadow-sm border-0 rounded-3 mb-4">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0 text-primary">Transaction History</h5>
                </div>
                <div class="col text-end">
                    <a href="{% url 'create_transaction' %}?customer={{ customer.id }}" class="btn btn-sm btn-primary">
                        <i class="fas fa-plus me-1"></i>New Transaction
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table align-middle mb-0">
                    <thead class="bg-light">
                        <tr>
                            <th class="ps-4">Date</th>
                            <th>Medicine</th>
                            <th class="text-center">Quantity</th>
                            <th class="text-end">Amount</th>
                            <th class="text-end pe-4">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for transaction in transactions %}
                        <tr class="transaction-row">
                            <td class="ps-4">
                                <div>{{ transaction.transaction_date|date:"M d, Y" }}</div>
                                <small class="text-muted">{{ transaction.transaction_date|time:"g:i A" }}</small>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-capsule text-primary me-2"></i>
                                    <div>
                                        <div class="fw-medium">{{ transaction.medicine.name }}</div>
                                        <small class="text-muted">{{ transaction.medicine.get_medicine_type_display }}</small>
                                    </div>
                                </div>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-light text-dark px-2 py-1 rounded-pill">{{ transaction.quantity }}</span>
                            </td>
                            <td class="text-end fw-medium">₱{{ transaction.total_amount|floatformat:2 }}</td>
                            <td class="text-end pe-4">
                                <a href="{% url 'transaction_detail' transaction.id %}" class="btn btn-sm btn-outline-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="5" class="text-center py-5">
                                <div class="py-4">
                                    <i class="fas fa-shopping-cart text-muted" style="font-size: 3rem;"></i>
                                    <h5 class="mt-3">No transactions found</h5>
                                    <p class="text-muted">This customer has no transaction history</p>
                                    <a href="{% url 'create_transaction' %}?customer={{ customer.id }}" class="btn btn-primary mt-2">
                                        <i class="fas fa-plus me-2"></i>Create Transaction
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
        {% if transactions.has_other_pages %}
        <div class="card-footer bg-white py-3">
            <nav aria-label="Transaction pagination">
                <ul class="pagination justify-content-center mb-0">
                    {% if transactions.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transactions.previous_page_number }}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in transactions.paginator.page_range %}
                        {% if transactions.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                        {% elif num > transactions.number|add:'-3' and num < transactions.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if transactions.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transactions.next_page_number }}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ transactions.paginator.num_pages }}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
        </div>
        {% endif %}
    </div>
</div>

<!-- Edit Customer Modal -->
<div class="modal fade" id="editCustomerModal" tabindex="-1" aria-labelledby="editCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="editCustomerModalLabel">
                    <i class="fas fa-edit me-2"></i>Edit Customer
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'edit_customer' customer.id %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="customer_name" class="form-label">Customer Name <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="customer_name" name="customer_name" value="{{ customer.customer_name }}" required>
                        </div>
                        <div class="col-md-6">
                            <label for="patient_number" class="form-label">Patient Number</label>
                            <input type="text" class="form-control" id="patient_number" name="patient_number" value="{{ customer.patient_number|default:'' }}">
                        </div>
                        <div class="col-md-6">
                            <label for="customer_type" class="form-label">Customer Type</label>
                            <select class="form-select" id="customer_type" name="customer_type">
                                <option value="out_patient" {% if customer.customer_type == 'out_patient' %}selected{% endif %}>Out Patient</option>
                                <option value="in_patient" {% if customer.customer_type == 'in_patient' %}selected{% endif %}>In Patient</option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="phone_number" class="form-label">Phone Number</label>
                            <input type="tel" class="form-control" id="phone_number" name="phone_number" value="{{ customer.phone_number|default:'' }}" placeholder="e.g., 09123456789">
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ customer.email|default:'' }}">
                        </div>
                        <div class="col-12">
                            <label for="address" class="form-label">Address</label>
                            <textarea class="form-control" id="address" name="address" rows="2">{{ customer.address|default:'' }}</textarea>
                        </div>
                        <div class="col-12">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" name="notes" rows="2">{{ customer.notes|default:'' }}</textarea>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-2"></i>Update Customer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete Customer Modal -->
<div class="modal fade" id="deleteCustomerModal" tabindex="-1" aria-labelledby="deleteCustomerModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteCustomerModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Delete Customer
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete <strong>{{ customer.customer_name }}</strong>?</p>
                <p class="text-danger">This action cannot be undone. All transaction history for this customer will remain but will no longer be linked to a customer record.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form method="post" action="{% url 'delete_customer' customer.id %}">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash-alt me-2"></i>Delete Customer
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
