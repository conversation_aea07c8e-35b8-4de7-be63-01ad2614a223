﻿{% extends 'base.html' %}

{% block title %}Audit Trail - MedInventory{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-primary fw-bold">
                    <i class="fas fa-history me-2"></i>Audit Trail
                </h1>
                <div class="d-flex gap-2">
                    <a href="{% url 'home' %}" class="btn btn-outline-secondary rounded-pill">
                        <i class="fas fa-arrow-left me-1"></i>Back to Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Audit Trail Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h5 class="mb-0 text-primary">Activity Log</h5>
                        </div>
                        <div class="col-md-6">
                            <form id="searchForm" method="get">
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fas fa-search text-muted"></i>
                                    </span>
                                    <input type="text" id="searchInput" name="search" class="form-control border-start-0"
                                           placeholder="Search audit records..." value="{{ search_query }}">
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0">Action</th>
                                    <th class="border-0">Item Type</th>
                                    <th class="border-0">Item ID</th>
                                    <th class="border-0">Details</th>
                                    <th class="border-0">Date & Time</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if audit_trails %}
                                {% for audit in audit_trails %}
                                <tr>
                                    <td>
                                        {% if audit.action == 'create' %}
                                        <span class="badge bg-success rounded-pill px-3 py-2">
                                            <i class="fas fa-plus-circle me-1"></i>Create
                                        </span>
                                        {% elif audit.action == 'update' %}
                                        <span class="badge bg-warning rounded-pill px-3 py-2">
                                            <i class="fas fa-edit me-1"></i>Update
                                        </span>
                                        {% elif audit.action == 'delete' %}
                                        <span class="badge bg-danger rounded-pill px-3 py-2">
                                            <i class="fas fa-trash-alt me-1"></i>Delete
                                        </span>
                                        {% elif audit.action == 'add_quantity' %}
                                        <span class="badge bg-info rounded-pill px-3 py-2">
                                            <i class="fas fa-plus me-1"></i>Add Quantity
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary rounded-pill px-3 py-2">
                                            {{ audit.get_action_display }}
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td>{{ audit.content_type.model|title }}</td>
                                    <td>{{ audit.object_id }}</td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            {% if audit.content_type.model == 'medicine' %}
                                            <i class="fas fa-pills text-primary me-2"></i>
                                            {% elif audit.content_type.model == 'supplier' %}
                                            <i class="fas fa-truck text-primary me-2"></i>
                                            {% else %}
                                            <i class="fas fa-clipboard-list text-primary me-2"></i>
                                            {% endif %}
                                            <a href="#" class="text-decoration-none" data-bs-toggle="modal" data-bs-target="#detailModal{{ audit.id }}">
                                                View Details
                                            </a>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span>{{ audit.created_at|date:"F d, Y" }}</span>
                                            <small class="text-muted">{{ audit.created_at|time:"h:i A" }}</small>
                                        </div>
                                    </td>
                                </tr>

                                <!-- Detail Modal for each record -->
                                <div class="modal fade" id="detailModal{{ audit.id }}" tabindex="-1" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <div class="modal-header">
                                                <h5 class="modal-title">
                                                    {{ audit.get_action_display }} {{ audit.content_type.model|title }}
                                                </h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <h6 class="text-muted">Basic Information</h6>
                                                    <ul class="list-group list-group-flush">
                                                        <li class="list-group-item d-flex justify-content-between px-0">
                                                            <span>Action:</span>
                                                            <span class="fw-bold">{{ audit.get_action_display }}</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between px-0">
                                                            <span>Item Type:</span>
                                                            <span class="fw-bold">{{ audit.content_type.model|title }}</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between px-0">
                                                            <span>Item ID:</span>
                                                            <span class="fw-bold">{{ audit.object_id }}</span>
                                                        </li>
                                                        <li class="list-group-item d-flex justify-content-between px-0">
                                                            <span>Date & Time:</span>
                                                            <span class="fw-bold">{{ audit.created_at|date:"F d, Y" }} at {{ audit.created_at|time:"h:i A" }}</span>
                                                        </li>
                                                    </ul>
                                                </div>

                                                {% if audit.content_object %}
                                                <div>
                                                    <h6 class="text-muted">Item Details</h6>
                                                    <div class="card bg-light">
                                                        <div class="card-body">
                                                            {% if audit.content_type.model == 'medicine' %}
                                                            <p><strong>Name:</strong> {{ audit.content_object.name }}</p>
                                                            <p><strong>Category:</strong> {{ audit.content_object.category }}</p>
                                                            <p><strong>Price:</strong> ₱{{ audit.content_object.price }}</p>
                                                            <p><strong>Current Stock:</strong> {{ audit.content_object.quantity }}</p>
                                                            {% elif audit.content_type.model == 'supplier' %}
                                                            <p><strong>Name:</strong> {{ audit.content_object.name }}</p>
                                                            <p><strong>Contact:</strong> {{ audit.content_object.contact_number }}</p>
                                                            {% else %}
                                                            <p>Details not available for this item type.</p>
                                                            {% endif %}
                                                        </div>
                                                    </div>
                                                </div>
                                                {% else %}
                                                <div class="alert alert-info">
                                                    <i class="fas fa-info-circle me-2"></i>The referenced item may have been deleted.
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                {% endfor %}
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="py-5">
                                            <i class="fas fa-history text-muted mb-3" style="font-size: 3rem;"></i>
                                            <h5 class="text-muted mb-2">No Audit Records Found</h5>
                                            <p class="text-muted">System activity will appear here as changes are made.</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <p class="mb-0 text-muted">
                                Showing <span class="fw-bold">{{ audit_trails|length|default:"0" }}</span> of
                                <span class="fw-bold">{{ audit_trail_count|default:"0" }}</span> records
                            </p>
                        </div>
                        <div class="col-md-6">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-end mb-0">
                                    {% if page_obj.has_previous %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}

                                    {% for i in page_obj.paginator.page_range %}
                                    {% if page_obj.number == i %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ i }}</span>
                                    </li>
                                    {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ i }}{% if search_query %}&search={{ search_query }}{% endif %}">{{ i }}</a>
                                    </li>
                                    {% endif %}
                                    {% endfor %}

                                    {% if page_obj.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* MEDUSA-like styling for Audit Trail */
    :root {
        --primary-color: #2c3e50;
        --secondary-color: #3498db;
        --success-color: #2ecc71;
        --warning-color: #f39c12;
        --danger-color: #e74c3c;
        --info-color: #3498db;
        --light-color: #f8f9fa;
        --dark-color: #343a40;
    }

    .card {
        border-radius: 8px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

        .card:hover {
            box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
        }

    .table th {
        font-weight: 600;
        color: var(--primary-color);
        font-size: 0.85rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .table td {
        vertical-align: middle;
        padding: 0.75rem 1rem;
    }

    .table-hover tbody tr:hover {
        background-color: rgba(52, 152, 219, 0.05);
    }

    .badge {
        font-weight: 500;
        letter-spacing: 0.3px;
    }

    .rounded-pill {
        border-radius: 50rem !important;
    }

    .form-control, .form-select {
        height: 38px;
        border-radius: 4px;
        border-color: #e2e8f0;
        font-size: 0.95rem;
    }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

    .form-label {
        font-size: 0.9rem;
        font-weight: 500;
        color: var(--primary-color);
        margin-bottom: 0.25rem;
    }

    .page-link {
        color: var(--secondary-color);
        border-color: #e2e8f0;
    }

    .page-item.active .page-link {
        background-color: var(--secondary-color);
        border-color: var(--secondary-color);
    }

    .modal-content {
        border-radius: 8px;
        border: none;
    }

    .modal-header {
        border-bottom: 1px solid #e2e8f0;
    }

    .modal-footer {
        border-top: 1px solid #e2e8f0;
    }

    @media (max-width: 768px) {
        .table {
            min-width: 650px;
        }
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Search functionality with debounce
        const searchInput = document.getElementById('searchInput');
        const searchForm = document.getElementById('searchForm');

        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        if (searchInput) {
            searchInput.addEventListener('input', debounce(function (e) {
                searchForm.submit();
            }, 500));
        }

        // Tooltip initialization
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        if (typeof bootstrap !== 'undefined') {
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Auto-hide alert messages
        const alerts = document.querySelectorAll('.alert:not(.alert-important)');
        alerts.forEach(alert => {
            setTimeout(() => {
                alert.classList.add('fade');
                setTimeout(() => {
                    alert.remove();
                }, 500);
            }, 5000);
        });
    });
</script>
{% endblock %}