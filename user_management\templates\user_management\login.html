
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - BMC MedForecast: Inventory Optimization and Monitoring Management System using Forecasting Algorithm</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.rawgit.com/davidshimjs/qrcodejs/gh-pages/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcodejs/1.0.0/qrcode.min.js"></script>
    <style>
        /* Enhanced Professional Medical Background Styles */
        @keyframes gradientBackground {
            0% {
                background-position: 0% 50%;
            }

            50% {
                background-position: 100% 50%;
            }

            100% {
                background-position: 0% 50%;
            }
        }

        /* Professional Medical Background Styles with Dual Images */
        body {
            background: linear-gradient(to right, rgba(255, 255, 255, 0.7), rgba(255, 255, 255, 0.7)), linear-gradient(to right, url('https://img.freepik.com/free-photo/medicine-capsules-global-health-with-geometric-pattern-digital-remix_53876-126742.jpg') 0% 0% / 50% 100% no-repeat, url('https://img.freepik.com/free-photo/medical-banner-with-stethoscope_23-**********.jpg') 100% 0% / 50% 100% no-repeat);
            background-attachment: fixed;
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }


        /* Professional Medical Background Styles with Original Images - Improved Blending */
        body {
            position: relative;
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            overflow-x: hidden;
            background: none; /* Remove default background */
        }

            /* First background (left side) */
            body::before {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                height: 100%;
                width: 55%; /* Slightly wider to create overlap */
                z-index: -2;
                background-image: url('https://img.freepik.com/free-photo/medicine-capsules-global-health-with-geometric-pattern-digital-remix_53876-126742.jpg');
                background-size: cover;
                background-position: center right;
                mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 80%, rgba(0, 0, 0, 0) 100%);
                -webkit-mask-image: linear-gradient(to right, rgba(0, 0, 0, 1) 80%, rgba(0, 0, 0, 0) 100%);
            }

            /* Second background (right side) */
            body::after {
                content: '';
                position: fixed;
                top: 0;
                right: 0;
                height: 100%;
                width: 55%; /* Slightly wider to create overlap */
                z-index: -2;
                background-image: url('https://img.freepik.com/free-photo/medical-banner-with-stethoscope_23-**********.jpg');
                background-size: cover;
                background-position: center left;
                mask-image: linear-gradient(to left, rgba(0, 0, 0, 1) 80%, rgba(0, 0, 0, 0) 100%);
                -webkit-mask-image: linear-gradient(to left, rgba(0, 0, 0, 1) 80%, rgba(0, 0, 0, 0) 100%);
            }

        /* Additional overlay for center blending - using a special pseudo-element technique */
        .background-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.7);
            z-index: -1;
        }

        /* Mobile optimization for backgrounds */
        @media (max-width: 768px) {
            body::before, body::after {
                opacity: 0.3; /* Reduce background image opacity on mobile */
            }
            .background-overlay {
                background-color: rgba(255, 255, 255, 0.85); /* Increase overlay opacity */
            }
            .medical-bg .medical-element {
                opacity: 0.4; /* Reduce opacity of floating medical elements */
            }
        }

            /* For browsers that don't support mask-image properly, add a central gradient overlay */
            .background-overlay::after {
                content: '';
                position: absolute;
                top: 0;
                left: 40%;
                width: 20%;
                height: 100%;
                background: radial-gradient(ellipse at center, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0) 70%);
                pointer-events: none;
            }

        /* Card styling with blur effect */
        .card {
            border-radius: 1.25rem;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1), 0 5px 15px rgba(0, 0, 0, 0.05);
            border: none;
            overflow: hidden;
            backdrop-filter: blur(10px);
            background-color: rgba(255, 255, 255, 0.9);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            z-index: 1;
        }

            .card:hover {
                transform: translateY(-5px);
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.12), 0 8px 20px rgba(0, 0, 0, 0.06);
            }

            /* Touch-friendly active states for mobile */
            .card:active {
                transform: translateY(-3px); /* Smaller transform on touch */
                transition: transform 0.1s ease;
            }

            .btn:active {
                transform: translateY(1px); /* Button press effect */
                transition: transform 0.1s ease;
            }

        .card-header {
            border-top-left-radius: 1.25rem !important;
            border-top-right-radius: 1.25rem !important;
            background: linear-gradient(135deg, #0143a3 0%, #0077d4 100%);
            padding: 1.5rem 1rem;
            border: none;
        }

        /* System name styling */
        .system-name {
            color: #1a3c6e;
            font-weight: 700;
            margin-bottom: 2rem;
            text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.1);
            letter-spacing: 0.5px;
        }

        /* Enhanced mobile typography */
        @media (max-width: 576px) {
            .system-name {
                font-size: 1.8rem; /* Smaller heading on mobile */
                margin-bottom: 1.5rem;
            }
            .card-header h4 {
                font-size: 1.2rem; /* Smaller card header on mobile */
            }
            .card-title {
                font-size: 1.1rem;
            }
            .btn {
                font-size: 0.95rem;
            }
            .form-label {
                font-size: 0.9rem;
            }
        }

        /* Form controls */
        .form-control {
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            border: 1px solid rgba(0, 0, 0, 0.1);
            background-color: rgba(255, 255, 255, 0.9);
            transition: all 0.3s ease;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
        }

        /* Improve form responsiveness on small screens */
        @media (max-width: 576px) {
            .form-control, .input-group-text, .btn {
                padding: 0.6rem 0.8rem; /* Slightly smaller padding on very small screens */
                font-size: 0.95rem; /* Slightly smaller font size */
            }
            .card {
                margin: 0 10px; /* Add some margin on small screens */
                border-radius: 1rem; /* Slightly smaller border radius */
            }
            .card-body {
                padding: 1.25rem; /* Slightly less padding in card body */
            }
            .input-group-text {
                padding-left: 0.6rem;
                padding-right: 0.6rem;
            }
        }

            .form-control:focus {
                box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.15);
                border-color: #0d6efd;
                background-color: #fff;
            }

        /* Button styling */
        .btn {
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: linear-gradient(135deg, #0143a3 0%, #0077d4 100%);
            border: none;
        }

            .btn-primary:hover, .btn-primary:focus {
                background: linear-gradient(135deg, #013584 0%, #0066b3 100%);
                transform: translateY(-2px);
                box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            }

        .btn-outline-secondary {
            border: 2px solid #6c757d;
            color: #495057;
        }

            .btn-outline-secondary:hover {
                background-color: #f8f9fa;
                color: #0143a3;
                border-color: #0143a3;
            }

        .input-group-text {
            background-color: rgba(13, 110, 253, 0.1);
            border: 1px solid rgba(0, 0, 0, 0.1);
            color: #0d6efd;
        }

        .footer {
            margin-top: 3rem;
            color: #6c757d;
            font-size: 0.9rem;
        }

        .alert {
            border-radius: 0.5rem;
            font-weight: 500;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            border-left: 4px solid;
        }

        .alert-danger {
            border-left-color: #dc3545;
            background-color: rgba(220, 53, 69, 0.05);
        }

        .alert-warning {
            border-left-color: #ffc107;
            background-color: rgba(255, 193, 7, 0.05);
        }

        .alert-success {
            border-left-color: #28a745;
            background-color: rgba(40, 167, 69, 0.05);
        }

        .alert-info {
            border-left-color: #17a2b8;
            background-color: rgba(23, 162, 184, 0.05);
        }

        #login-error-container, #otp-error-container {
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
        }

        /* OTP Verification Div Display */
        #otpVerificationDiv {
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
            z-index: 1050;
            display: none; /* Hidden by default, JS will show it */
            max-width: 450px;
            height: fit-content;
            animation: fadeIn 0.4s ease forwards;
        }

        /* Overlay for when OTP is shown */
        .auth-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(3px);
            z-index: 1040;
            display: none;
        }

        /* Simple fade in animation */
        @keyframes fadeIn {
            from {
                opacity: 0;
            }

            to {
                opacity: 1;
            }
        }

        .animate-card {
            animation: fadeIn 0.5s ease forwards;
        }

        /* Alternative solution for browsers without good mask support */
        @supports not (mask-image: linear-gradient(to right, black, transparent)) {
            body::before {
                mask-image: none;
                -webkit-mask-image: none;
                width: 50%;
                opacity: 0.99;
            }

            body::after {
                mask-image: none;
                -webkit-mask-image: none;
                width: 50%;
                opacity: 0.99;
            }
            /* Add a middle section with both images blended */
            .background-overlay::before {
                content: '';
                position: absolute;
                top: 0;
                left: 45%;
                width: 10%;
                height: 100%;
                background: linear-gradient(to right, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.95) 50%, rgba(255, 255, 255, 0.9) 100%);
                z-index: 1;
            }
        }



        /* OTP styling */
        .digit-placeholder-container {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 1.5rem 0;
        }

        .digit-placeholder {
            width: 45px;
            height: 45px;
            border-radius: 8px;
            background-color: rgba(13, 110, 253, 0.05);
            border: 1px solid rgba(13, 110, 253, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            transition: all 0.3s ease;
            font-size: 1.2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* Improved OTP Verification Div Display */
        #otpVerificationDiv {
            position: fixed;
            left: 0;
            right: 0;
            top: 0;
            bottom: 0;
            margin: auto;
            z-index: 1050;
            display: none; /* Hidden by default, JS will show it */
            max-width: 450px;
            height: fit-content;
            animation: fadeIn 0.4s ease forwards;
        }

        /* Timer styling */
        #otpTimer {
            font-size: 0.9rem;
            font-weight: 500;
            color: #6c757d;
            text-align: center;
            display: block;
            margin-top: 0.5rem;
        }

        /* Form view states */
        .view-hidden {
            display: none !important;
        }

        .view-visible {
            display: block !important;
        }

        /* Overlay for when OTP is shown */
        .auth-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(3px);
            z-index: 1040;
            display: none;
        }

        /* OTP icon styling */
        .otp-icon {
            font-size: 3rem;
            color: #0143a3;
            background-color: rgba(13, 110, 253, 0.1);
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            margin: 0 auto 1.5rem;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
        }


        .envelope-open {
            animation: openEnvelope 1s ease-in-out;
        }

        .clock-tick {
            animation: clockTick 2s linear infinite;
        }

        @keyframes openEnvelope {
            0% {
                transform: scale(1);
            }

            50% {
                transform: scale(1.2);
            }

            100% {
                transform: scale(1);
            }
        }

        @keyframes clockTick {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        /* Enhanced loading animation */
        @keyframes buttonLoad {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        .btn-loading {
            background-size: 200% 200%;
            background-image: linear-gradient(45deg, #0143a3, #0077d4, #0143a3, #0077d4);
            animation: buttonLoad 2s ease infinite;
            color: white !important;
            position: relative;
            overflow: hidden;
        }

        .btn-loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% {
                left: -100%;
            }
            100% {
                left: 100%;
            }
        }


    </style>
</head>
<body>
    <div class="background-overlay"></div>
    <!-- Enhanced Medical Background Elements -->
    <div class="medical-bg">
        <div class="medical-element stethoscope"></div>
        <div class="medical-element pill-bottle"></div>
        <div class="medical-element medical-cross"></div>
        <div class="medical-element dna"></div>
        <div class="medical-element prescription"></div>
        <div class="medical-element microscope"></div>
        <div class="medical-element heart-rate"></div>
        <div class="medical-element medical-vial"></div>
    </div>

    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-12 text-center mb-4">
                <h1 class="system-name">
                    <i class="fas fa-capsules me-2"></i>
                    BMC MedForecast
                </h1>
            </div>
            <div class="col-md-7 col-lg-5">
                <!-- Login Form Card -->
                <div class="card shadow border-0 animate-card" id="loginFormCard">
                    <div class="card-header text-white text-center">
                        <h4 class="mb-0"><i class="fas fa-sign-in-alt me-2"></i>Login to Your Account</h4>
                    </div>
                    <div class="card-body p-4">
                        <div id="login-error-container">
                            {% if form.errors %}
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle me-2"></i>Your username and password didn't match. Please try again.
                            </div>
                            {% endif %}

                            {% if messages %}
                            {% for message in messages %}
                            <div class="alert alert-{{ message.tags }}">
                                {{ message }}
                            </div>
                            {% endfor %}
                            {% endif %}
                        </div>

                        <!-- Admin login bypass:
                             1. username='admin', password='admin' will bypass OTP verification
                             2. Any username containing 'admin' will bypass email verification
                        -->
                        <form method="post" class="needs-validation" novalidate id="loginForm" autocomplete="off">
                            {% csrf_token %}
                            <input type="hidden" name="next" value="{{ next }}">
                            <div class="mb-3">
                                <label for="id_username" class="form-label fw-bold">Username</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text"
                                           name="username"
                                           class="form-control"
                                           id="id_username"
                                           placeholder="Enter your username"
                                           required
                                           autocomplete="off">
                                    <div class="invalid-feedback">
                                        Please enter your username.
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label for="id_password" class="form-label fw-bold">Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password"
                                           name="password"
                                           class="form-control"
                                           id="id_password"
                                           placeholder="Enter your password"
                                           required
                                           autocomplete="new-password">
                                    <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <div class="invalid-feedback">
                                        Please enter your password.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="d-flex justify-content-end">
                                    <a href="#" class="text-primary" data-bs-toggle="modal" data-bs-target="#forgotPasswordModal">
                                        <i class="fas fa-key me-1"></i>Forgot Password?
                                    </a>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary" id="loginButton">
                                    <i class="fas fa-sign-in-alt me-2"></i>Log In
                                </button>
                                <a href="{% url 'register' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-user-plus me-2"></i>Create New Account
                                </a>
                            </div>


                            <div class="mb-3">
                                <label class="form-label text-center d-block"></label>
                                <div class="btn-group w-100" role="group">
                                    <input type="radio" class="btn-check" name="otp_type" id="otp_email" value="email" checked>
                                    <label class="btn btn-outline-primary" for="otp_email">
                                        <i class="fas fa-envelope me-2"></i>Email OTP
                                    </label>

                                    <input type="radio" class="btn-check" name="otp_type" id="otp_totp" value="totp">
                                    <label class="btn btn-outline-primary" for="otp_totp">
                                        <i class="fas fa-clock me-2"></i>Time-based OTP
                                    </label>
                                </div>
                            </div>

                        </form>
                    </div>
                </div>
                <!-- OTP Verification Card -->
                <div class="card shadow border-0 animate-card" id="otpVerificationDiv" style="display: none;">
                    <div class="card-header text-white text-center">
                        <h4 class="mb-0"><i class="fas fa-shield-alt me-2"></i>Two-Factor Authentication</h4>
                    </div>
                    <div class="card-body p-4">
                        <div class="text-center mb-3">
                            <i class="fas fa-lock text-primary" style="font-size: 3rem;"></i>
                            <h5 class="mt-3">Security Verification</h5>
                            <p class="text-muted otp-email-text">We've sent a 6-digit code to your registered email address</p>
                            <p class="text-muted otp-totp-text" style="display: none;">Enter the 6-digit code from your authenticator app</p>
                        </div>

                        <div id="otp-error-container"></div>
                        <form id="otpVerificationForm" method="post" action="{% url 'verify_login_otp' %}" class="needs-validation" novalidate>
                            {% csrf_token %}
                            <div class="mb-4">
                                <label for="otp" class="form-label fw-bold">Verification Code</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-key"></i></span>
                                    <input type="text" name="otp" class="form-control" id="otp"
                                           placeholder="Enter 6-digit code" required maxlength="6"
                                           pattern="\d{6}" inputmode="numeric">
                                    <div class="invalid-feedback">
                                        Please enter the valid 6-digit code.
                                    </div>
                                </div>

                                <!-- Code entry helper (optional visual element) -->
                                <div class="d-flex justify-content-between mt-3 mb-3">
                                    <div class="digit-placeholder"></div>
                                    <div class="digit-placeholder"></div>
                                    <div class="digit-placeholder"></div>
                                    <div class="digit-placeholder"></div>
                                    <div class="digit-placeholder"></div>
                                    <div class="digit-placeholder"></div>
                                </div>

                                <div class="text-center mb-3">
                                    <span class="text-muted" id="otpTimer">Code expires in: 05:00</span>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary" id="verifyOtpButton">
                                    <i class="fas fa-check-circle me-2"></i>Verify & Login
                                </button>
                                <button type="button" class="btn btn-outline-secondary" id="resendOtpButton">
                                    <i class="fas fa-redo-alt me-2"></i>Resend Code
                                </button>
                                <button type="button" class="btn btn-link text-muted" id="backToLoginButton">
                                    <i class="fas fa-arrow-left me-2"></i>Back to Login
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <div class="text-center footer">
                    <p>&copy; 2025 Medicine Inventory Management System. All rights reserved.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>

    <script>

        /**
         * Authentication Module
         * Handles login, OTP verification, and password reset functionality
         * Includes support for both Email OTP and Time-based OTP (TOTP)
         */
        document.addEventListener('DOMContentLoaded', function () {
            // Cache DOM elements
            const elements = {
                // Login elements
                loginForm: document.getElementById('loginForm'),
                loginButton: document.getElementById('loginButton'),
                togglePassword: document.getElementById('togglePassword'),
                passwordInput: document.getElementById('id_password'),
                loginFormCard: document.getElementById('loginFormCard'),
                // OTP verification elements
                otpVerificationDiv: document.getElementById('otpVerificationDiv'),
                otpVerificationForm: document.getElementById('otpVerificationForm'),
                otpInput: document.getElementById('otp'),
                digitPlaceholders: document.querySelectorAll('.digit-placeholder'),
                verifyOtpButton: document.getElementById('verifyOtpButton'),
                backToLoginButton: document.getElementById('backToLoginButton'),
                resendOtpButton: document.getElementById('resendOtpButton'),
                otpTimer: document.getElementById('otpTimer'),
                // Password reset elements
                resetForm: document.getElementById('resetForm'),
                resetButton: document.getElementById('resetButton'),
                messageDiv: document.getElementById('forgotPasswordMessages'),
                emailStep: document.getElementById('emailStep'),
                otpStep: document.getElementById('otpStep'),
                otpForm: document.getElementById('otpForm'),
                submitOtpButton: document.getElementById('submitOtpButton'),
                toggleNewPassword: document.getElementById('toggleNewPassword'),
                // TOTP specific elements
                otpEmailText: document.querySelector('.otp-email-text'),
                otpTotpText: document.querySelector('.otp-totp-text')
            };

            // OTP timer variables
            let timeLeft = 300; // 5 minutes in seconds for email OTP
            let timerInterval;

            // TOTP specific variables
            let totpTimeLeft = 30; // TOTP default period is 30 seconds
            let totpInterval;
            let currentOtpType = 'email'; // Default OTP type

            // ===============================
            // Password Visibility Toggles
            // ===============================
            // Toggle main password visibility
            if (elements.togglePassword) {
                elements.togglePassword.addEventListener('click', function () {
                    togglePasswordVisibility(elements.passwordInput, this.querySelector('i'));
                });
            }

            // Toggle new password visibility (for password reset)
            if (elements.toggleNewPassword) {
                elements.toggleNewPassword.addEventListener('click', function () {
                    const passwordInput = document.getElementById('new_password');
                    togglePasswordVisibility(passwordInput, this.querySelector('i'));
                });
            }

            function togglePasswordVisibility(inputElement, iconElement) {
                const type = inputElement.getAttribute('type') === 'password' ? 'text' : 'password';
                inputElement.setAttribute('type', type);
                iconElement.classList.toggle('fa-eye');
                iconElement.classList.toggle('fa-eye-slash');
            }

            // ===============================
            // OTP Verification Functionality
            // ===============================
            // OTP input handling with visual placeholder updates
            if (elements.otpInput) {
                elements.otpInput.addEventListener('input', function (e) {
                    const value = e.target.value;
                    // Replace non-numeric characters and limit to 6 digits
                    this.value = this.value.replace(/[^0-9]/g, '').slice(0, 6);
                    // Validate input length
                    if (this.value.length === 6) {
                        this.classList.add('is-valid');
                        this.classList.remove('is-invalid');
                    } else {
                        this.classList.remove('is-valid');
                        this.classList.add('is-invalid');
                    }
                    // Update visual placeholders
                    updateOtpPlaceholders(this.value);
                });
            }

            function updateOtpPlaceholders(value) {
                if (!elements.digitPlaceholders) return;
                // Clear all placeholders
                elements.digitPlaceholders.forEach((placeholder, index) => {
                    placeholder.textContent = '';
                    placeholder.style.backgroundColor = 'rgba(13, 110, 253, 0.05)';
                    placeholder.style.borderColor = 'rgba(13, 110, 253, 0.2)';
                });
                // Fill placeholders with entered digits
                Array.from(value).forEach((digit, index) => {
                    if (index < elements.digitPlaceholders.length) {
                        elements.digitPlaceholders[index].textContent = digit;
                        elements.digitPlaceholders[index].style.backgroundColor = 'rgba(13, 110, 253, 0.1)';
                        elements.digitPlaceholders[index].style.borderColor = 'rgba(13, 110, 253, 0.5)';
                    }
                });
            }

            // Email OTP timer countdown functionality
            function startOtpTimer() {
                // Clear any existing timer
                if (timerInterval) {
                    clearInterval(timerInterval);
                }
                // Clear TOTP timer if running
                if (totpInterval) {
                    clearInterval(totpInterval);
                }
                // Reset timer values
                timeLeft = 300;
                if (elements.otpTimer) {
                    elements.otpTimer.style.color = '#6c757d';
                }
                if (elements.verifyOtpButton) {
                    elements.verifyOtpButton.disabled = false;
                }
                // Start new timer
                timerInterval = setInterval(updateTimer, 1000);
                updateTimer();
                // Set current OTP type
                currentOtpType = 'email';
            }

            function updateTimer() {
                if (!elements.otpTimer) return;
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                elements.otpTimer.textContent = `Code expires in: ${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                if (timeLeft <= 0) {
                    elements.otpTimer.textContent = 'Code expired. Please request a new one.';
                    elements.otpTimer.style.color = '#dc3545';
                    clearInterval(timerInterval);
                    if (elements.verifyOtpButton) {
                        elements.verifyOtpButton.disabled = true;
                    }
                } else {
                    timeLeft--;
                }
            }

            // TOTP timer countdown functionality
            function startTotpTimer(period = 30) {
                // Clear any existing TOTP timer
                if (totpInterval) {
                    clearInterval(totpInterval);
                }
                // Clear email OTP timer if running
                if (timerInterval) {
                    clearInterval(timerInterval);
                }
                totpTimeLeft = period;
                if (elements.otpTimer) {
                    elements.otpTimer.style.color = '#6c757d';
                }
                if (elements.verifyOtpButton) {
                    elements.verifyOtpButton.disabled = false;
                }
                totpInterval = setInterval(updateTotpTimer, 1000);
                updateTotpTimer();
                // Set current OTP type
                currentOtpType = 'totp';
            }

            function updateTotpTimer() {
                if (!elements.otpTimer) return;
                elements.otpTimer.textContent = `Code expires in: ${totpTimeLeft} seconds`;
                if (totpTimeLeft <= 0) {
                    elements.otpTimer.textContent = 'Code expired. Please generate a new one.';
                    elements.otpTimer.style.color = '#dc3545';
                    clearInterval(totpInterval);
                    if (elements.verifyOtpButton) {
                        elements.verifyOtpButton.disabled = true;
                    }
                } else {
                    totpTimeLeft--;
                }
            }

            // Function to clear error messages
            function clearErrorMessages() {
                const errorContainers = ['login-error-container', 'otp-error-container'];
                errorContainers.forEach(containerId => {
                    const container = document.getElementById(containerId);
                    if (container) {
                        container.innerHTML = '';
                    }
                });
            }

            // Back to login button functionality
            if (elements.backToLoginButton) {
                elements.backToLoginButton.addEventListener('click', function () {
                    if (elements.otpVerificationDiv) {
                        elements.otpVerificationDiv.style.display = 'none';
                    }
                    if (elements.loginFormCard) {
                        elements.loginFormCard.style.display = 'block';
                    }
                    // Clean up timers when going back to login
                    clearAllTimers();
                    // Clear any error messages
                    clearErrorMessages();
                });
            }

            // Resend OTP button functionality
            if (elements.resendOtpButton) {
                elements.resendOtpButton.addEventListener('click', function () {
                    const button = this;
                    button.disabled = true;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';

                    // Get CSRF token
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;

                    fetch(window.location.href, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest',
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            action: 'resend_otp',
                            username: elements.loginForm.querySelector('[name="username"]').value
                        }),
                        credentials: 'same-origin'
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                showAlert('success', '<i class="fas fa-check-circle me-2"></i>New verification code sent!');
                                // Handle different OTP types
                                if (data.otp_type === 'totp') {
                                    startTotpTimer(data.expires_in || 30);
                                    if (elements.otpEmailText) elements.otpEmailText.style.display = 'none';
                                    if (elements.otpTotpText) elements.otpTotpText.style.display = 'block';
                                } else {
                                    startOtpTimer();
                                    if (elements.otpEmailText) elements.otpEmailText.style.display = 'block';
                                    if (elements.otpTotpText) elements.otpTotpText.style.display = 'none';
                                }
                                // Clear input and placeholders
                                if (elements.otpInput) {
                                    elements.otpInput.value = '';
                                    updateOtpPlaceholders('');
                                }
                            } else {
                                showAlert('danger', data.message || 'Failed to send new code');
                            }
                            button.disabled = false;
                            button.innerHTML = '<i class="fas fa-redo-alt me-2"></i>Resend Code';
                        })
                        .catch(error => {
                            handleError(error, button, '<i class="fas fa-redo-alt me-2"></i>Resend Code');
                        });
                });
            }

            // ===============================
            // TOTP Setup Functionality
            // ===============================
            /**
             * Handles the TOTP setup process
             * @param {Object} data - The data containing QR code URI and secret key
             */
            function handleTotpSetup(data) {
                // Enhanced debug logging
                console.log('Full TOTP Setup Data:', data);

                // Show TOTP setup modal
                const modalElement = document.getElementById('totpSetupModal');
                const modal = new bootstrap.Modal(modalElement);
                modal.show();

                // Generate and display QR code
                const qrCodeContainer = document.getElementById('qrCodeContainer');
                if (!qrCodeContainer) {
                    console.error('QR code container not found');
                    return;
                }

                // Clear existing content
                qrCodeContainer.innerHTML = '';

                // Display manual entry secret code first
                const manualSecretInput = document.getElementById('manualSecretCode');
                if (manualSecretInput && data.secret) {
                    manualSecretInput.value = data.secret;
                    // Add click-to-copy functionality
                    manualSecretInput.addEventListener('click', function () {
                        this.select();
                    });
                }

                // Ensure the QR URI is properly formatted
                if (!data.qr_uri) {
                    console.error('QR URI is missing from data');
                    qrCodeContainer.innerHTML = '<div class="alert alert-danger">Missing QR code data</div>';
                    return;
                }

                // Create QR code with loading indicator

                // Delay QR code generation slightly to ensure DOM is ready
                setTimeout(() => {
                    try {
                        new QRCode(qrCodeContainer, {
                            text: data.qr_uri,
                            width: 200,
                            height: 200,
                            colorDark: "#000000",
                            colorLight: "#ffffff",
                            correctLevel: QRCode.CorrectLevel.M,
                            quietZone: 15,
                            quietZoneColor: '#FFFFFF'
                        });





                        // Add a download button for the QR code
                        const qrImage = qrCodeContainer.querySelector('img');
                        if (qrImage) {
                            // Create the download button
                            const downloadBtn = document.createElement('a');
                            downloadBtn.className = 'text-muted small fw-semibold mt-2 d-block';
                            downloadBtn.style.cursor = 'pointer';
                            downloadBtn.style.transition = 'all 0.2s ease';
                            downloadBtn.style.color = '#0078BD'; // Medical blue color
                            downloadBtn.style.padding = '8px 14px';
                            downloadBtn.style.borderRadius = '6px';
                            downloadBtn.style.backgroundColor = '#F5FAFC';
                            downloadBtn.style.textDecoration = 'none';
                            downloadBtn.style.fontFamily = "'Roboto', 'Helvetica Neue', sans-serif";
                            downloadBtn.style.display = 'inline-flex';
                            downloadBtn.style.alignItems = 'center';
                            downloadBtn.style.border = '1px solid #E1EEF4';
                            downloadBtn.style.minWidth = '180px';

                            // Create SVG container for the icon
                            const svgIcon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
                            svgIcon.setAttribute('width', '20');
                            svgIcon.setAttribute('height', '20');
                            svgIcon.setAttribute('viewBox', '0 0 24 24');
                            svgIcon.setAttribute('fill', 'none');
                            svgIcon.setAttribute('stroke', '#0078BD');
                            svgIcon.setAttribute('stroke-width', '1.5');
                            svgIcon.setAttribute('stroke-linecap', 'round');
                            svgIcon.setAttribute('stroke-linejoin', 'round');
                            svgIcon.style.marginRight = '8px';
                            svgIcon.style.transition = 'opacity 0.3s';

                            // Create text span for flexible text changing
                            const textSpan = document.createElement('span');
                            textSpan.textContent = 'Download QR Code';
                            textSpan.style.transition = 'opacity 0.3s';

                            // Add elements to button
                            downloadBtn.appendChild(svgIcon);
                            downloadBtn.appendChild(textSpan);

                            // Define medical icons as SVG path data
                            const medicalIcons = [
                                // Heartbeat/ECG
                                {
                                    type: 'path',
                                    attributes: {
                                        d: 'M3,12h3l2-6l4,12l3-6h6'
                                    }
                                },
                                // Medicine pill/capsule
                                {
                                    type: 'path',
                                    attributes: {
                                        d: 'M17.5 6.5l-11 11c-1.5 1.5-3.5 1.5-5 0v0c-1.5-1.5-1.5-3.5 0-5l11-11c1.5-1.5 3.5-1.5 5 0v0c1.5 1.5 1.5 3.5 0 5z'
                                    },
                                    extras: [
                                        {
                                            type: 'line',
                                            attributes: {
                                                x1: '9.5',
                                                y1: '14.5',
                                                x2: '14.5',
                                                y2: '9.5'
                                            }
                                        }
                                    ]
                                },
                                // Medical cross
                                {
                                    type: 'g',
                                    extras: [
                                        {
                                            type: 'circle',
                                            attributes: {
                                                cx: '12',
                                                cy: '12',
                                                r: '9'
                                            }
                                        },
                                        {
                                            type: 'line',
                                            attributes: {
                                                x1: '12',
                                                y1: '8',
                                                x2: '12',
                                                y2: '16'
                                            }
                                        },
                                        {
                                            type: 'line',
                                            attributes: {
                                                x1: '8',
                                                y1: '12',
                                                x2: '16',
                                                y2: '12'
                                            }
                                        }
                                    ]
                                },
                                // Stethoscope
                                {
                                    type: 'path',
                                    attributes: {
                                        d: 'M4.7,19c0,0,0-5,4.3-5c4.3,0,4.3,5,4.3,5 M4.7,14c0-2.5,3-3,3-6c0-2,1-3,3-3h0.8C13.3,5,14,6,14,7c0,3,3,3.5,3,6'
                                    },
                                    extras: [
                                        {
                                            type: 'circle',
                                            attributes: {
                                                cx: '17',
                                                cy: '15',
                                                r: '2'
                                            }
                                        }
                                    ]
                                },
                                // Syringe
                                {
                                    type: 'path',
                                    attributes: {
                                        d: 'M6,15l8-8 M11,10l3,3 M14,7l3,3 M17,4l3,3'
                                    },
                                    extras: [
                                        {
                                            type: 'path',
                                            attributes: {
                                                d: 'M7,14L4,17l3,3l3-3L7,14z'
                                            }
                                        },
                                        {
                                            type: 'line',
                                            attributes: {
                                                x1: '8.5',
                                                y1: '12.5',
                                                x2: '11.5',
                                                y2: '15.5'
                                            }
                                        }
                                    ]
                                }
                            ];

                            // Current index for icon rotation
                            let currentIconIndex = 0;
                            let isLoading = false;
                            let animationInterval;

                            // Function to clear the SVG icon
                            function clearSVGIcon() {
                                while (svgIcon.firstChild) {
                                    svgIcon.removeChild(svgIcon.firstChild);
                                }
                            }

                            // Function to render a specific icon
                            function renderIcon(iconData) {
                                clearSVGIcon();

                                // Create main element (path, group, etc)
                                const mainElement = document.createElementNS('http://www.w3.org/2000/svg', iconData.type);

                                // Add attributes to the main element
                                if (iconData.attributes) {
                                    Object.entries(iconData.attributes).forEach(([key, value]) => {
                                        mainElement.setAttribute(key, value);
                                    });
                                }

                                svgIcon.appendChild(mainElement);

                                // Add any extra elements
                                if (iconData.extras) {
                                    iconData.extras.forEach(extra => {
                                        const extraElement = document.createElementNS('http://www.w3.org/2000/svg', extra.type);
                                        Object.entries(extra.attributes).forEach(([key, value]) => {
                                            extraElement.setAttribute(key, value);
                                        });
                                        svgIcon.appendChild(extraElement);
                                    });
                                }
                            }

                            // Function to start the loading animation
                            function startLoading() {
                                if (isLoading) return;

                                isLoading = true;
                                textSpan.textContent = 'Downloading...';

                                // Rotate through icons every 800ms
                                animationInterval = setInterval(() => {
                                    // Fade out
                                    svgIcon.style.opacity = 0;

                                    // After fade out, change the icon
                                    setTimeout(() => {
                                        currentIconIndex = (currentIconIndex + 1) % medicalIcons.length;
                                        renderIcon(medicalIcons[currentIconIndex]);

                                        // Fade in the new icon
                                        svgIcon.style.opacity = 1;
                                    }, 300);
                                }, 1000);
                            }

                            // Function to stop the loading animation
                            function stopLoading() {
                                if (!isLoading) return;

                                clearInterval(animationInterval);
                                textSpan.textContent = 'Download Complete';
                                svgIcon.style.opacity = 0;

                                // Show checkmark icon
                                setTimeout(() => {
                                    clearSVGIcon();
                                    const checkmark = document.createElementNS('http://www.w3.org/2000/svg', 'path');
                                    checkmark.setAttribute('d', 'M5,12l5,5l10-10');
                                    svgIcon.appendChild(checkmark);
                                    svgIcon.style.opacity = 1;

                                    // Reset after 2 seconds
                                    setTimeout(() => {
                                        isLoading = false;
                                        textSpan.textContent = 'Download QR Code';
                                        renderIcon(medicalIcons[0]);
                                    }, 2000);
                                }, 300);
                            }

                            // Initialize with the first icon
                            renderIcon(medicalIcons[0]);

                            // Simulate download on click
                            downloadBtn.addEventListener('click', (e) => {
                                e.preventDefault();
                                startLoading();

                                // Simulate download completion after 5 seconds
                                setTimeout(() => {
                                    stopLoading();
                                }, 5000);
                            });

                            // Hover effects
                            downloadBtn.addEventListener('mouseover', () => {
                                if (!isLoading) {
                                    downloadBtn.style.color = '#005A8C';
                                    downloadBtn.style.backgroundColor = '#E1EEF4';
                                    svgIcon.setAttribute('stroke', '#005A8C');
                                }
                            });

                            downloadBtn.addEventListener('mouseout', () => {
                                if (!isLoading) {
                                    downloadBtn.style.color = '#0078BD';
                                    downloadBtn.style.backgroundColor = '#F5FAFC';
                                    svgIcon.setAttribute('stroke', '#0078BD');
                                }
                            });

                            // For testing, export to a container
                            document.body.appendChild(downloadBtn);
                            downloadBtn.onclick = () => {
                                const link = document.createElement('a');
                                link.download = 'totp-qr-code.png';
                                link.href = qrImage.src;
                                link.click();
                            };
                            qrCodeContainer.appendChild(downloadBtn);
                        }

                        console.log('QR code generated successfully');
                    } catch (error) {
                        console.error('QR Code generation error:', error);
                        qrCodeContainer.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Failed to generate QR code. Please use the manual code above.
                        </div>`;
                    }
                }, 100);

                // Enhanced copy functionality
                window.copySecretCode = function () {
                    const secretInput = document.getElementById('manualSecretCode');
                    const copyButton = secretInput.nextElementSibling;

                    navigator.clipboard.writeText(secretInput.value)
                        .then(() => {
                            // Show success feedback
                            copyButton.innerHTML = '<i class="fas fa-check"></i>';
                            copyButton.classList.remove('btn-outline-secondary');
                            copyButton.classList.add('btn-success');

                            setTimeout(() => {
                                copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                                copyButton.classList.remove('btn-success');
                                copyButton.classList.add('btn-outline-secondary');
                            }, 2000);
                        })
                        .catch(err => {
                            // Fallback to old method if clipboard API fails
                            secretInput.select();
                            document.execCommand('copy');

                            copyButton.innerHTML = '<i class="fas fa-check"></i>';
                            setTimeout(() => {
                                copyButton.innerHTML = '<i class="fas fa-copy"></i>';
                            }, 2000);
                        });
                };

                // Setup verification code input handler
                const verificationInput = document.getElementById('totpVerificationCode');
                if (verificationInput) {
                    verificationInput.value = ''; // Clear any existing value
                    verificationInput.addEventListener('input', function (e) {
                        this.value = this.value.replace(/[^0-9]/g, '').slice(0, 6);

                        // Add visual feedback
                        if (this.value.length === 6) {
                            this.classList.add('is-valid');
                            this.classList.remove('is-invalid');
                            document.getElementById('verifyTotpButton').disabled = false;
                        } else {
                            this.classList.remove('is-valid');
                            this.classList.add('is-invalid');
                            document.getElementById('verifyTotpButton').disabled = true;
                        }
                    });
                }

                // Enhanced verification handler
                window.verifyTotpSetup = function () {
                    const verifyButton = document.getElementById('verifyTotpButton');
                    const codeInput = document.getElementById('totpVerificationCode');
                    const code = codeInput.value.trim();

                    if (!code || code.length !== 6 || !/^\d{6}$/.test(code)) {
                        showAlert('danger', '<i class="fas fa-exclamation-circle me-2"></i>Please enter a valid 6-digit code', document.querySelector('.modal-body'));
                        return;
                    }

                    verifyButton.disabled = true;
                    verifyButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Verifying...';

                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                    const loginForm = document.getElementById('loginForm');
                    const username = loginForm ? loginForm.querySelector('[name="username"]').value : '';

                    fetch('{% url "verify_totp_setup" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                            'X-CSRFToken': csrfToken,
                            'X-Requested-With': 'XMLHttpRequest'
                        },
                        body: `otp=${encodeURIComponent(code)}&username=${encodeURIComponent(username)}`,
                        credentials: 'same-origin'
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                const modal = bootstrap.Modal.getInstance(document.getElementById('totpSetupModal'));
                                modal.hide();
                                showAlert('success', '<i class="fas fa-check-circle me-2"></i>Two-factor authentication setup complete!');
                                if (data.redirect_url) {
                                    window.location.href = data.redirect_url;
                                }
                            } else {
                                showAlert('danger', data.message || 'Verification failed', document.querySelector('.modal-body'));
                                codeInput.value = '';
                                codeInput.classList.remove('is-valid');
                                codeInput.classList.add('is-invalid');
                                verifyButton.disabled = true;
                                codeInput.focus();
                            }
                        })
                        .catch(error => {
                            console.error('Verification error:', error);
                            showAlert('danger', '<i class="fas fa-exclamation-triangle me-2"></i>An error occurred during verification', document.querySelector('.modal-body'));
                            codeInput.value = '';
                            codeInput.classList.remove('is-valid');
                            codeInput.classList.add('is-invalid');
                            verifyButton.disabled = true;
                        })
                        .finally(() => {
                            verifyButton.disabled = false;
                            verifyButton.innerHTML = '<i class="fas fa-check me-2"></i>Verify Setup';
                        });
                };
            }

            // Helper function to show alerts
            function showAlert(type, message, container = document.body) {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;

                container.insertBefore(alertDiv, container.firstChild);

                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alertDiv);
                    bsAlert.close();
                }, 5000);
            }

            // Enhanced function to display error messages in dedicated containers
            function displayErrorMessage(data, containerId) {
                // Get error details
                let errorIcon = 'exclamation-circle';
                let errorClass = 'danger';

                // Customize error display based on error type
                if (data.error_type) {
                    switch(data.error_type) {
                        // Login errors
                        case 'user_not_found':
                            errorIcon = 'user-times';
                            break;
                        case 'wrong_password':
                            errorIcon = 'lock';
                            break;
                        case 'unverified_email':
                            errorIcon = 'envelope';
                            break;
                        case 'inactive_account':
                            errorIcon = 'user-slash';
                            break;

                        // OTP errors
                        case 'invalid_otp':
                            errorIcon = 'key';
                            break;
                        case 'expired_otp':
                            errorIcon = 'clock';
                            break;
                        case 'invalid_totp':
                            errorIcon = 'shield-alt';
                            break;
                        case 'totp_not_configured':
                            errorIcon = 'cog';
                            break;
                        case 'invalid_session':
                            errorIcon = 'user-clock';
                            break;
                        case 'profile_not_found':
                            errorIcon = 'user-slash';
                            break;

                        default:
                            errorIcon = 'exclamation-circle';
                    }
                }

                // Create a more visually appealing error message
                const errorHTML = `<i class="fas fa-${errorIcon} me-2"></i>${data.message}`;

                // Display the error in the dedicated container
                const errorContainer = document.getElementById(containerId);
                if (errorContainer) {
                    errorContainer.innerHTML = `
                        <div class="alert alert-${errorClass} alert-dismissible fade show">
                            ${errorHTML}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    `;

                    // Scroll to the error container
                    errorContainer.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                } else {
                    // Fallback to general alert if container not found
                    showAlert('danger', errorHTML);
                }
            }
            // ===============================
            // Form Submissions
            // ===============================
            // Main Login Form Handler
            if (elements.loginForm) {
                elements.loginForm.addEventListener('submit', function (event) {
                    event.preventDefault();
                    if (!this.checkValidity()) {
                        event.stopPropagation();
                        this.classList.add('was-validated');
                        return;
                    }

                    // Clear any previous error messages
                    clearErrorMessages();

                    const formData = new FormData(this);
                    const submitButton = elements.loginButton;
                    submitButton.disabled = true;
                    submitButton.classList.add('btn-loading');
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Logging in...';

                    fetch(this.action, {
                        method: 'POST',
                        body: formData,
                        credentials: 'same-origin'
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                // Direct login success (admin/superuser bypass)
                                showAlert('success', '<i class="fas fa-check-circle me-2"></i>Login successful. Redirecting...');
                                setTimeout(() => {
                                    window.location.href = data.redirect_url;
                                }, 1000);
                            } else if (data.status === 'totp_setup_required') {
                                handleTotpSetup(data);
                                submitButton.disabled = false;
                                submitButton.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Log In';
                            } else if (data.status === 'otp_required') {
                                elements.loginFormCard.style.display = 'none';
                                elements.otpVerificationDiv.style.display = 'block';
                                // Update UI based on OTP type
                                if (data.otp_type === 'totp') {
                                    if (elements.otpEmailText) elements.otpEmailText.style.display = 'none';
                                    if (elements.otpTotpText) elements.otpTotpText.style.display = 'block';
                                    startTotpTimer(data.expires_in || 30);
                                } else {
                                    if (elements.otpEmailText) elements.otpEmailText.style.display = 'block';
                                    if (elements.otpTotpText) elements.otpTotpText.style.display = 'none';
                                    startOtpTimer();
                                }
                                showAlert('success', data.message);
                            } else {
                                // Use the centralized error display function
                                displayErrorMessage(data, 'login-error-container');

                                submitButton.disabled = false;
                                submitButton.classList.remove('btn-loading');
                                submitButton.innerHTML = '<i class="fas fa-sign-in-alt me-2"></i>Log In';
                            }
                        })
                        .catch(error => handleError(error, submitButton, '<i class="fas fa-sign-in-alt me-2"></i>Log In', 'login-error-container'));
                });
            }

            // OTP Verification Form Handler
            if (elements.otpVerificationForm) {
                elements.otpVerificationForm.addEventListener('submit', function (event) {
                    event.preventDefault();
                    if (!this.checkValidity()) {
                        event.stopPropagation();
                        this.classList.add('was-validated');
                        return;
                    }

                    // Clear any previous error messages
                    clearErrorMessages();

                    const verifyButton = elements.verifyOtpButton;
                    verifyButton.disabled = true;
                    verifyButton.classList.add('btn-loading');
                    verifyButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Verifying...';

                    const formData = new FormData(this);
                    // Add the current OTP type to the form data
                    formData.append('otp_type', currentOtpType);

                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;

                    fetch(this.action, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': csrfToken,
                        },
                        body: formData,
                        credentials: 'same-origin'
                    })
                        .then(response => response.json())
                        .then(data => {
                            if (data.status === 'success') {
                                showAlert('success', 'OTP verified successfully. Redirecting...');
                                setTimeout(() => {
                                    window.location.href = data.redirect_url;
                                }, 1500);
                            } else {
                                // Use the centralized error display function
                                displayErrorMessage(data, 'otp-error-container');

                                verifyButton.disabled = false;
                                verifyButton.classList.remove('btn-loading');
                                verifyButton.innerHTML = '<i class="fas fa-check-circle me-2"></i>Verify & Login';
                            }
                        })
                        .catch(error => {
                            handleError(error, verifyButton, '<i class="fas fa-check-circle me-2"></i>Verify & Login', 'otp-error-container');
                        });
                });
            }

            // Password Reset Initial Form
            if (elements.resetForm) {
                elements.resetForm.addEventListener('submit', function (event) {
                    event.preventDefault();
                    if (!this.checkValidity()) {
                        event.stopPropagation();
                        this.classList.add('was-validated');
                        return;
                    }

                    const resetButton = elements.resetButton;
                    resetButton.disabled = true;
                    resetButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Sending...';

                    const formData = new FormData(this);
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;

                    // Note: This URL would need to be updated to use a proper variable or string
                    fetch('/forgot-password/', {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': csrfToken,
                        },
                        body: formData,
                        credentials: 'same-origin'
                    })
                        .then(response => response.json())
                        .then(data => {
                            showAlert(data.status, data.message);
                            if (data.status === 'success') {
                                elements.emailStep.style.display = 'none';
                                elements.otpStep.style.display = 'block';
                                if (elements.otpForm) {
                                    elements.otpForm.action = data.redirect_url;
                                }
                                const otpInputField = document.getElementById('otp');
                                if (otpInputField) otpInputField.focus();
                                startOtpTimer();
                            }
                            resetButton.disabled = false;
                            resetButton.innerHTML = '<i class="fas fa-paper-plane me-2"></i>Send Reset OTP';
                        })
                        .catch(error => handleError(error, resetButton, '<i class="fas fa-paper-plane me-2"></i>Send Reset OTP'));
                });
            }

            // Password Reset OTP Form
            if (elements.otpForm) {
                elements.otpForm.addEventListener('submit', function (event) {
                    event.preventDefault();
                    if (!this.checkValidity()) {
                        event.stopPropagation();
                        this.classList.add('was-validated');
                        return;
                    }

                    const submitButton = elements.submitOtpButton;
                    submitButton.disabled = true;
                    submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Resetting...';

                    const formData = new FormData(this);
                    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value;

                    fetch(this.action, {
                        method: 'POST',
                        headers: {
                            'X-CSRFToken': csrfToken,
                        },
                        body: formData,
                        credentials: 'same-origin'
                    })
                        .then(response => response.json())
                        .then(data => {
                            const iconClass = data.status === 'success' ? 'check-circle' : 'exclamation-circle';
                            if (elements.messageDiv) {
                                elements.messageDiv.innerHTML = `
                            <div class="alert alert-${data.status} alert-dismissible fade show" role="alert">
                                <i class="fas fa-${iconClass} me-2"></i>
                                ${data.message}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                            `;
                            }

                            if (data.status === 'success') {
                                setTimeout(() => {
                                    window.location.href = data.redirect_url;
                                }, 2000);
                            } else {
                                submitButton.disabled = false;
                                submitButton.innerHTML = '<i class="fas fa-save me-2"></i>Reset Password';
                            }
                        })
                        .catch(error => handleError(error, submitButton, '<i class="fas fa-save me-2"></i>Reset Password'));
                });
            }

            // ===============================
            // Utility Functions
            // ===============================
            /**
             * Shows an alert message
             * @param {string} type - Alert type (success, danger, warning, info)
             * @param {string} message - Alert message
             * @param {Element} container - Container to append alert to (optional)
             */
            function showAlert(type, message, container = null) {
                const alertDiv = document.createElement('div');
                alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
                alertDiv.innerHTML = `
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                const targetContainer = container || elements.messageDiv || document.querySelector('.alert-container');
                if (targetContainer) {
                    targetContainer.appendChild(alertDiv);
                    setTimeout(() => alertDiv.remove(), 5000);
                }
            }

            /**
             * Handles fetch errors and resets button state
             * @param {Error} error - The error that occurred
             * @param {Element} button - The button to reset
             * @param {string} originalHtml - The original button HTML
             * @param {string} containerId - Optional ID of error container to display message in
             */
            function handleError(error, button, originalHtml, containerId = null) {
                console.error('Error:', error);

                const errorData = {
                    status: 'error',
                    error_type: 'network_error',
                    message: 'A network error occurred. Please check your connection and try again.'
                };

                if (containerId) {
                    // Use the centralized error display function if a container is specified
                    displayErrorMessage(errorData, containerId);
                } else {
                    // Fallback to general alert
                    showAlert('danger', '<i class="fas fa-wifi me-2"></i>' + errorData.message);
                }

                if (button) {
                    button.disabled = false;
                    button.classList.remove('btn-loading');
                    button.innerHTML = originalHtml || button.getAttribute('data-original-text') || 'Submit';
                }
            }

            /**
             * Clears all active timers
             */
            function clearAllTimers() {
                if (timerInterval) clearInterval(timerInterval);
                if (totpInterval) clearInterval(totpInterval);
            }

            // Start appropriate timer if OTP verification is visible
            if (elements.otpVerificationDiv && elements.otpVerificationDiv.style.display === 'block') {
                const otpType = elements.otpTotpText &&
                    elements.otpTotpText.style.display === 'block' ? 'totp' : 'email';
                if (otpType === 'totp') {
                    startTotpTimer();
                } else {
                    startOtpTimer();
                }
            }

            // Cleanup on page unload
            window.addEventListener('unload', clearAllTimers);
        });




        document.querySelectorAll('input[name="otp_type"]').forEach(radio => {
            radio.addEventListener('change', function () {
                const emailIcon = document.querySelector('label[for="otp_email"] i');
                const clockIcon = document.querySelector('label[for="otp_totp"] i');

                if (this.value === 'totp') {
                    clockIcon.classList.add('clock-tick');
                    emailIcon.classList.remove('envelope-open');
                    emailIcon.classList.remove('fa-envelope-open');
                    emailIcon.classList.add('fa-envelope');
                } else {
                    clockIcon.classList.remove('clock-tick');
                    emailIcon.classList.add('envelope-open');
                    emailIcon.classList.remove('fa-envelope');
                    emailIcon.classList.add('fa-envelope-open');
                }
            });
        });

        // Initialize the animations based on default selection
        window.addEventListener('load', function () {
            const emailIcon = document.querySelector('label[for="otp_email"] i');
            emailIcon.classList.add('envelope-open');
            emailIcon.classList.remove('fa-envelope');
            emailIcon.classList.add('fa-envelope-open');
        });


    </script>


    <!-- Modal structure -->
    <div class="modal fade" id="forgotPasswordModal" tabindex="-1" aria-labelledby="forgotPasswordModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header text-white text-center" style="background: linear-gradient(135deg, #2563eb, #1d4ed8);">
                    <h4 class="modal-title" id="forgotPasswordModalLabel">
                        <i class="fas fa-key me-2"></i>Reset Password
                    </h4>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-4">
                    <div id="forgotPasswordMessages"></div>
                    <!-- Email Form -->
                    <div id="emailStep">
                        <form id="resetForm" method="post" action="{% url 'forgot_password' %}" class="needs-validation" novalidate>
                            {% csrf_token %}
                            <div class="mb-4">
                                <label for="reset_email" class="form-label fw-bold">Email Address</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-envelope"></i>
                                    </span>
                                    <input type="email" name="email" class="form-control" id="reset_email"
                                           placeholder="Enter your registered email address" required>
                                    <div class="invalid-feedback">
                                        Please enter a valid email address.
                                    </div>
                                </div>
                                <small class="form-text text-muted mt-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    We'll send a one-time password (OTP) to your email.
                                </small>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="resetButton">
                                    <i class="fas fa-paper-plane me-2"></i>Send Reset OTP
                                </button>
                            </div>
                        </form>
                    </div>

                    <!-- OTP and New Password Form -->
                    <div id="otpStep" style="display: none;">
                        <form id="otpForm" method="post" class="needs-validation" novalidate>
                            {% csrf_token %}
                            <div class="mb-3">
                                <label for="otp" class="form-label fw-bold">Enter OTP</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-shield-alt"></i></span>
                                    <input type="text" name="otp" class="form-control" id="otp"
                                           placeholder="Enter 6-digit OTP" required maxlength="6" pattern="\d{6}">
                                    <div class="invalid-feedback">
                                        Please enter the 6-digit OTP.
                                    </div>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="new_password" class="form-label fw-bold">New Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" name="new_password" class="form-control" id="new_password"
                                           placeholder="Enter new password" required>
                                    <button class="btn btn-outline-secondary" type="button" id="toggleNewPassword">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="mb-4">
                                <label for="confirm_password" class="form-label fw-bold">Confirm Password</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" name="confirm_password" class="form-control" id="confirm_password"
                                           placeholder="Confirm new password" required>
                                </div>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary" id="submitOtpButton">
                                    <i class="fas fa-save me-2"></i>Reset Password
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>



    <div class="modal fade" id="totpSetupModal" tabindex="-1" aria-labelledby="totpSetupModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="totpSetupModalLabel">
                        <i class="fas fa-shield-alt me-2"></i>Set Up Two-Factor Authentication
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body p-3">
                    <div class="row g-3">
                        <!-- QR Code Section -->
                        <div class="col-12">
                            <div class="text-center">

                                <h5 class="mb-1">Scan QR Code</h5>
                                <p class="text-muted small mb-2">Use an authenticator app like Google Authenticator</p>
                            </div>
                        </div>

                        <!-- QR Code Container -->
                        <div class="col-12">
                            <div class="d-flex flex-column align-items-center">
                                <div id="qrCodeContainer" class="text-center mb-2" style="min-height: 200px; width: 200px;">
                                    <!-- QR code will be inserted here -->
                                    Download
                                </div>
                            </div>
                        </div>

                        <!-- Manual Entry Section -->
                        <div class="col-12">
                            <label class="form-label fw-bold small">Manual Entry Code</label>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-key"></i></span>
                                <input type="text" class="form-control" id="manualSecretCode" readonly>
                                <button class="btn btn-outline-secondary" type="button" onclick="copySecretCode()">
                                    <i class="fas fa-copy"></i>
                                </button>
                            </div>
                            <small class="text-muted d-block mt-1">
                                Can't scan? Enter this code manually in your authenticator app
                            </small>
                        </div>

                        <!-- Verification Code Section -->
                        <div class="col-12">
                            <label for="totpVerificationCode" class="form-label fw-bold small">Verification Code</label>
                            <div class="input-group input-group-sm">
                                <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                <input type="text"
                                       class="form-control"
                                       id="totpVerificationCode"
                                       placeholder="Enter 6-digit code"
                                       maxlength="6"
                                       inputmode="numeric"
                                       pattern="\d{6}"
                                       required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-sm btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Cancel
                    </button>
                    <button type="button" class="btn btn-sm btn-primary" onclick="verifyTotpSetup()" id="verifyTotpButton">
                        <i class="fas fa-check me-1"></i>Verify Setup
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>