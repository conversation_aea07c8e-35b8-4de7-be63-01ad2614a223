import re
import random
import csv
from datetime import datetime, timedelta
from decimal import Decimal

from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from django.db.models import Sum
from inventory.models import Medicine, Transaction
from user_management.models import Customer

class Command(BaseCommand):
    help = 'Import medicines from the summary file and generate realistic transactions'

    def add_arguments(self, parser):
        parser.add_argument('--csv-file', type=str, default='backup/medicines_backup_20250329_200259.csv',
                            help='Path to the medicine CSV file')
        parser.add_argument('--limit', type=int, default=20,
                            help='Limit the number of medicines to import')
        parser.add_argument('--start-date', type=str, default='2023-01-01',
                            help='Start date for transactions (YYYY-MM-DD)')
        parser.add_argument('--end-date', type=str, default='2025-04-14',
                            help='End date for transactions (YYYY-MM-DD)')
        parser.add_argument('--clear', action='store_true',
                            help='Clear existing medicines and transactions before importing')

    def handle(self, *args, **options):
        csv_file = options['csv_file']
        limit = options['limit']
        start_date_str = options['start_date']
        end_date_str = options['end_date']
        clear_existing = options['clear']

        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
        except ValueError:
            self.stdout.write(self.style.ERROR('Invalid date format. Use YYYY-MM-DD'))
            return

        # Clear existing data if requested
        if clear_existing:
            self.stdout.write(self.style.WARNING('Clearing existing medicines and transactions...'))
            Transaction.objects.all().delete()
            Medicine.objects.all().delete()
            self.stdout.write(self.style.SUCCESS('Existing data cleared'))

        # Parse the CSV file and import medicines
        medicines = self.parse_csv_file(csv_file, limit)

        if not medicines:
            self.stdout.write(self.style.ERROR('No medicines found in the CSV file'))
            return

        # Create medicines in the database
        created_medicines = self.create_medicines(medicines)

        # Generate transactions for the medicines
        self.generate_transactions(created_medicines, start_date, end_date)

        self.stdout.write(self.style.SUCCESS(f'Successfully imported {len(created_medicines)} medicines and generated transactions'))

    def parse_csv_file(self, file_path, limit):
        """Parse the medicine CSV file and extract medicine data"""
        medicines = []
        medicine_dict = {}  # To track unique medicines

        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                csv_reader = csv.reader(f)
                headers = next(csv_reader)  # Skip header row

                # Process each row
                for row in csv_reader:
                    if len(row) < 4:  # Skip incomplete rows
                        continue

                    # Extract medicine name
                    name = row[1].strip()
                    if not name:  # Skip rows without a name
                        continue

                    # Skip if we already processed this medicine
                    if name in medicine_dict:
                        continue

                    # Get price from amount column
                    try:
                        amount = float(row[3].strip())
                        quantity = float(row[2].strip())
                        price = amount / quantity if quantity > 0 else 0
                    except (ValueError, IndexError, ZeroDivisionError):
                        price = random.uniform(10, 100)  # Random price if calculation fails

                    # Generate reorder levels
                    reorder_level = random.randint(20, 50)
                    reorder_quantity = random.randint(50, 100)

                    # Determine category and medicine type
                    category = self.determine_category(name)
                    medicine_type = self.determine_medicine_type(name)

                    # Create medicine data
                    medicine_data = {
                        'name': name,
                        'description': f'{name} - Imported from sales data',
                        'quantity': 0,  # Start with zero, will be updated by transactions
                        'price': Decimal(str(round(price, 2))),
                        'reorder_level': reorder_level,
                        'reorder_quantity': reorder_quantity,
                        'category': category,
                        'medicine_type': medicine_type,
                    }

                    # Add to medicines list and dictionary
                    medicines.append(medicine_data)
                    medicine_dict[name] = True

                    self.stdout.write(f"Parsed medicine: {name}")

                    # Stop if we've reached the limit
                    if len(medicines) >= limit:
                        break

            return medicines

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error parsing CSV file: {str(e)}'))
            return []

    def determine_medicine_type(self, name):
        """Determine medicine type based on name"""
        name_lower = name.lower()

        if any(term in name_lower for term in ['tab', 'tablet']):
            return 'tablet'
        elif any(term in name_lower for term in ['cap', 'capsule']):
            return 'capsule'
        elif any(term in name_lower for term in ['syrup', 'susp', 'suspension']):
            return 'syrup'
        elif any(term in name_lower for term in ['inj', 'amp', 'vial']):
            return 'injection'
        elif any(term in name_lower for term in ['cream', 'oint', 'ointment']):
            return 'cream'
        elif any(term in name_lower for term in ['drop']):
            return 'drops'
        elif any(term in name_lower for term in ['inhaler', 'nebule', 'nebulizer']):
            return 'inhaler'
        else:
            return 'other'

    def determine_category(self, name):
        """Determine category based on name"""
        name_lower = name.lower()

        if any(term in name_lower for term in ['amoxicillin', 'ampicillin', 'cefuroxime', 'ceftriaxone', 'antibiotic']):
            return 'Antibiotics'
        elif any(term in name_lower for term in ['paracetamol', 'ibuprofen', 'mefenamic', 'ketorolac', 'tramadol']):
            return 'Analgesics'
        elif any(term in name_lower for term in ['salbutamol', 'ipratropium', 'budesonide']):
            return 'Respiratory'
        elif any(term in name_lower for term in ['amlodipine', 'losartan', 'enalapril', 'metoprolol']):
            return 'Cardiovascular'
        elif any(term in name_lower for term in ['omeprazole', 'ranitidine', 'pantoprazole']):
            return 'Gastrointestinal'
        elif any(term in name_lower for term in ['insulin', 'metformin', 'glimepiride']):
            return 'Antidiabetic'
        elif any(term in name_lower for term in ['cetirizine', 'loratadine', 'diphenhydramine']):
            return 'Antihistamine'
        elif any(term in name_lower for term in ['dexamethasone', 'prednisone', 'hydrocortisone']):
            return 'Corticosteroids'
        elif any(term in name_lower for term in ['vitamin', 'ascorbic', 'multivitamin']):
            return 'Vitamins'
        else:
            return 'Others'

    def create_medicines(self, medicines_data):
        """Create medicines in the database"""
        created_medicines = []

        with transaction.atomic():
            for medicine_data in medicines_data:
                # Generate a random expiration date 1-3 years in the future
                days_to_expiry = random.randint(365, 365*3)
                expiration_date = timezone.now().date() + timedelta(days=days_to_expiry)
                medicine_data['expiration_date'] = expiration_date

                # Create the medicine
                medicine = Medicine.objects.create(**medicine_data)
                created_medicines.append(medicine)

                self.stdout.write(self.style.SUCCESS(f"Created medicine: {medicine.name}"))

        return created_medicines

    def generate_transactions(self, medicines, start_date, end_date):
        """Generate realistic transactions for the medicines using existing customers"""
        # Get all customers from the database
        customers = list(Customer.objects.all())

        if not customers:
            self.stdout.write(self.style.WARNING('No customers found in the database. Using default customer data.'))
            # Fallback to default customer types if no customers exist
            customer_types = ['in_patient', 'out_patient']
        else:
            self.stdout.write(self.style.SUCCESS(f'Using {len(customers)} existing customers for transactions'))

        # Transaction types with weights
        transaction_types = ['sale', 'purchase']
        transaction_weights = [70, 30]  # 70% sales, 30% purchases

        # Track total transactions created
        total_transactions = 0

        # Create a dictionary to track medicine seasonality patterns
        medicine_seasonality = {}
        for medicine in medicines:
            # Randomly assign seasonality pattern (0=none, 1=winter, 2=summer, 3=spring, 4=fall)
            medicine_seasonality[medicine.id] = random.randint(0, 4)

        # Generate transactions in batches by month
        current_date = start_date
        batch_size = 0
        transactions_to_create = []

        while current_date <= end_date:
            # Determine month for seasonality
            month = current_date.month

            # Process each medicine
            for medicine in medicines:
                # Skip some medicines on some days for realism
                if random.random() > 0.3:  # 30% chance of transaction on any given day
                    continue

                # Get medicine's seasonality pattern
                seasonality = medicine_seasonality[medicine.id]

                # Calculate seasonality factor (1.0 is baseline)
                seasonality_factor = 1.0

                # Apply seasonality patterns
                if seasonality == 1:  # Winter seasonality (higher in Dec-Feb)
                    if month in [12, 1, 2]:
                        seasonality_factor = 1.5
                elif seasonality == 2:  # Summer seasonality (higher in Jun-Aug)
                    if month in [6, 7, 8]:
                        seasonality_factor = 1.4
                elif seasonality == 3:  # Spring seasonality (higher in Mar-May)
                    if month in [3, 4, 5]:
                        seasonality_factor = 1.3
                elif seasonality == 4:  # Fall seasonality (higher in Sep-Nov)
                    if month in [9, 10, 11]:
                        seasonality_factor = 1.2

                # Determine transaction type based on weights
                transaction_type = random.choices(transaction_types, weights=transaction_weights, k=1)[0]

                # Calculate quantity based on transaction type and medicine properties
                base_quantity = max(1, int(medicine.reorder_level * 0.3 * random.uniform(0.5, 1.5)))

                if transaction_type == 'sale':
                    quantity = int(base_quantity * seasonality_factor)
                else:  # purchase
                    # Purchases are typically larger
                    quantity = int(base_quantity * seasonality_factor * random.randint(3, 8))

                # Calculate total amount
                total_amount = quantity * medicine.price

                # Create transaction with random time on the current date
                random_hour = random.randint(8, 17)  # Business hours
                random_minute = random.randint(0, 59)
                transaction_datetime = current_date.replace(hour=random_hour, minute=random_minute)

                # Create transaction object
                transaction_data = {
                    'medicine': medicine,
                    'transaction_type': transaction_type,
                    'quantity': quantity,
                    'total_amount': total_amount,
                    'transaction_date': transaction_datetime,
                }

                # Add customer info for sales
                if transaction_type == 'sale':
                    if customers:
                        # Use a random customer from the database
                        customer = random.choice(customers)
                        transaction_data['customer_type'] = customer.customer_type
                        transaction_data['customer_name'] = customer.customer_name
                        transaction_data['patient_number'] = customer.patient_number

                        # Update customer's last transaction date
                        if not customer.last_transaction_date or customer.last_transaction_date < transaction_datetime:
                            customer.last_transaction_date = transaction_datetime
                            customer.save(update_fields=['last_transaction_date'])
                    else:
                        # Fallback to random data if no customers exist
                        transaction_data['customer_type'] = random.choice(customer_types)
                        transaction_data['customer_name'] = f"Customer {random.randint(1000, 9999)}"
                        transaction_data['patient_number'] = f"P-{random.randint(2023, 2025)}-{random.randint(10000, 99999)}"

                # Add to batch
                transactions_to_create.append(Transaction(**transaction_data))
                batch_size += 1

                # Create transactions in batches of 1000
                if batch_size >= 1000:
                    with transaction.atomic():
                        Transaction.objects.bulk_create(transactions_to_create)
                        total_transactions += batch_size
                        self.stdout.write(f"Created {batch_size} transactions up to {current_date.date()}")
                        transactions_to_create = []
                        batch_size = 0

            # Move to next day
            current_date += timedelta(days=1)

        # Create any remaining transactions
        if transactions_to_create:
            with transaction.atomic():
                Transaction.objects.bulk_create(transactions_to_create)
                total_transactions += batch_size
                self.stdout.write(f"Created final batch of {batch_size} transactions")

        self.stdout.write(self.style.SUCCESS(f"Total transactions created: {total_transactions}"))

        # Update medicine quantities based on transactions
        self.update_medicine_quantities(medicines)

    def update_medicine_quantities(self, medicines):
        """Update medicine quantities based on transaction history"""
        updated_count = 0

        with transaction.atomic():
            for medicine in medicines:
                # Calculate net quantity from transactions
                sales = Transaction.objects.filter(
                    medicine=medicine,
                    transaction_type='sale'
                ).aggregate(total=Sum('quantity'))['total'] or 0

                purchases = Transaction.objects.filter(
                    medicine=medicine,
                    transaction_type='purchase'
                ).aggregate(total=Sum('quantity'))['total'] or 0

                # Calculate new quantity
                new_quantity = purchases - sales

                # Ensure quantity is not negative
                new_quantity = max(0, new_quantity)

                # Update medicine quantity
                Medicine.objects.filter(id=medicine.id).update(quantity=new_quantity)
                updated_count += 1

        self.stdout.write(self.style.SUCCESS(f"Updated quantities for {updated_count} medicines"))
