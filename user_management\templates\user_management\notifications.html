{% extends 'base.html' %}
{% load static %}

{% block title %}Notifications | Medicine Inventory Management System{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Page Header -->
    <div class="page-header mb-4">
        <div class="d-flex align-items-center">
            <div>

            </div>
            <div class="ms-auto">
                <button id="markAllAsRead" class="btn btn-outline-primary">
                    <i class="fas fa-check-double me-2"></i>Mark All as Read
                </button>
            </div>
        </div>
    </div>

    <!-- Notifications Container -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">All Notifications</h5>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" id="notificationFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-filter me-1"></i>Filter
                            </button>
                            <ul class="dropdown-menu" aria-labelledby="notificationFilterDropdown">
                                <li><a class="dropdown-item active" href="#" data-filter="all">All Notifications</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="unread">Unread</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="read">Read</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" data-filter="inventory">Inventory</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="expiry">Expiry</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="system">System</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="INFO">Price Changes</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if notifications %}
                    <div class="list-group list-group-flush notification-list">
                        {% for notification in notifications %}
                        <div class="list-group-item notification-item {% if not notification.read %}unread{% endif %}"
                             data-notification-id="{{ notification.id }}"
                             data-notification-type="{{ notification.notification_type }}">
                            <div class="d-flex">
                                <!-- Notification Icon -->
                                <div class="notification-icon me-3">
                                    {% if notification.notification_type == 'inventory' %}
                                    <div class="icon-wrapper bg-primary-light text-primary">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    {% elif notification.notification_type == 'expiry' %}
                                    <div class="icon-wrapper bg-warning-light text-warning">
                                        <i class="fas fa-calendar-times"></i>
                                    </div>
                                    {% elif notification.notification_type == 'system' %}
                                    <div class="icon-wrapper bg-info-light text-info">
                                        <i class="fas fa-cogs"></i>
                                    </div>
                                    {% elif notification.notification_type == 'INFO' %}
                                    <div class="icon-wrapper bg-info-light text-info">
                                        <i class="fas fa-tag"></i>
                                    </div>
                                    {% else %}
                                    <div class="icon-wrapper bg-secondary-light text-secondary">
                                        <i class="fas fa-bell"></i>
                                    </div>
                                    {% endif %}
                                </div>

                                <!-- Notification Content -->
                                <div class="notification-content flex-grow-1">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="mb-1">{{ notification.title }}</h6>
                                        <small class="text-muted notification-time">{{ notification.created_at|timesince }} ago</small>
                                    </div>
                                    <p class="mb-1 notification-message">{{ notification.message }}</p>
                                    {% if notification.url %}
                                    <a href="{{ notification.url }}" class="notification-link">
                                        <small>View Details <i class="fas fa-arrow-right ms-1"></i></small>
                                    </a>
                                    {% endif %}
                                </div>

                                <!-- Notification Actions -->
                                <div class="notification-actions ms-2">
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-icon" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end">
                                            {% if not notification.read %}
                                            <li>
                                                <button class="dropdown-item mark-as-read" data-notification-id="{{ notification.id }}">
                                                    <i class="fas fa-check me-2"></i>Mark as Read
                                                </button>
                                            </li>
                                            {% else %}
                                            <li>
                                                <button class="dropdown-item mark-as-unread" data-notification-id="{{ notification.id }}">
                                                    <i class="fas fa-times me-2"></i>Mark as Unread
                                                </button>
                                            </li>
                                            {% endif %}
                                            <li><hr class="dropdown-divider"></li>
                                            <li>
                                                <button class="dropdown-item text-danger delete-notification" data-notification-id="{{ notification.id }}">
                                                    <i class="fas fa-trash-alt me-2"></i>Delete
                                                </button>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Pagination -->
                    {% if is_paginated %}
                    <div class="d-flex justify-content-center py-3">
                        <nav aria-label="Notifications pagination">
                            <ul class="pagination">
                                {% if page_obj.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page=1" aria-label="First">
                                        <span aria-hidden="true">&laquo;&laquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}" aria-label="Previous">
                                        <span aria-hidden="true">&laquo;</span>
                                    </a>
                                </li>
                                {% endif %}

                                {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                <li class="page-item"><a class="page-link" href="?page={{ num }}">{{ num }}</a></li>
                                {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.next_page_number }}" aria-label="Next">
                                        <span aria-hidden="true">&raquo;</span>
                                    </a>
                                </li>
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}" aria-label="Last">
                                        <span aria-hidden="true">&raquo;&raquo;</span>
                                    </a>
                                </li>
                                {% endif %}
                            </ul>
                        </nav>
                    </div>
                    {% endif %}

                    {% else %}
                    <!-- No Notifications State -->
                    <div class="text-center p-5">
                        <div class="mb-3">
                            <i class="fas fa-bell-slash fa-4x text-muted"></i>
                        </div>
                        <h4 class="fw-light text-muted">No Notifications</h4>
                        <p class="text-muted">You don't have any notifications at the moment.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    /* Notification Styling */
    .notification-list {
        max-height: 70vh;
        overflow-y: auto;
    }

    .notification-item {
        transition: all 0.2s ease;
        border-left: 3px solid transparent;
    }

        .notification-item.unread {
            background-color: rgba(52, 152, 219, 0.05);
            border-left-color: var(--primary-color);
        }

        .notification-item:hover {
            background-color: rgba(52, 152, 219, 0.08);
        }

    .notification-icon .icon-wrapper {
        width: 45px;
        height: 45px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.25rem;
    }

    .bg-primary-light {
        background-color: rgba(52, 152, 219, 0.15);
    }

    .bg-warning-light {
        background-color: rgba(243, 156, 18, 0.15);
    }

    .bg-danger-light {
        background-color: rgba(231, 76, 60, 0.15);
    }

    .bg-success-light {
        background-color: rgba(46, 204, 113, 0.15);
    }

    .bg-info-light {
        background-color: rgba(52, 152, 219, 0.15);
    }

    .bg-secondary-light {
        background-color: rgba(127, 140, 141, 0.15);
    }

    .notification-link {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        display: inline-block;
        margin-top: 0.5rem;
        transition: all 0.2s ease;
    }

        .notification-link:hover {
            color: var(--secondary-color);
        }

    .notification-time {
        font-size: 0.75rem;
        white-space: nowrap;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Elements
        const markAllButton = document.getElementById('markAllAsRead');
        const notificationItems = document.querySelectorAll('.notification-item');
        const filterButtons = document.querySelectorAll('[data-filter]');

        // Mark single notification as read
        document.querySelectorAll('.mark-as-read').forEach(button => {
            button.addEventListener('click', function () {
                const notificationId = this.getAttribute('data-notification-id');
                markNotificationAsRead(notificationId);
            });
        });

        // Mark single notification as unread
        document.querySelectorAll('.mark-as-unread').forEach(button => {
            button.addEventListener('click', function () {
                const notificationId = this.getAttribute('data-notification-id');
                markNotificationAsUnread(notificationId);
            });
        });

        // Delete notification
        document.querySelectorAll('.delete-notification').forEach(button => {
            button.addEventListener('click', function () {
                const notificationId = this.getAttribute('data-notification-id');
                deleteNotification(notificationId);
            });
        });

        // Mark all as read
        if (markAllButton) {
            markAllButton.addEventListener('click', function () {
                markAllNotificationsAsRead();
            });
        }

        // Filtering
        filterButtons.forEach(button => {
            button.addEventListener('click', function (e) {
                e.preventDefault();

                // Update active state
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');

                const filterType = this.getAttribute('data-filter');
                filterNotifications(filterType);
            });
        });

        // Function to mark notification as read
        function markNotificationAsRead(notificationId) {
            // Send AJAX request to mark as read
            fetch(`/api/notifications/${notificationId}/read/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
                .then(response => {
                    if (response.ok) {
                        // Update UI
                        const notificationItem = document.querySelector(`.notification-item[data-notification-id="${notificationId}"]`);
                        notificationItem.classList.remove('unread');

                        // Update dropdown action
                        const actionDropdown = notificationItem.querySelector('.dropdown-menu');
                        actionDropdown.innerHTML = `
                        <li>
                            <button class="dropdown-item mark-as-unread" data-notification-id="${notificationId}">
                                <i class="fas fa-times me-2"></i>Mark as Unread
                            </button>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <button class="dropdown-item text-danger delete-notification" data-notification-id="${notificationId}">
                                <i class="fas fa-trash-alt me-2"></i>Delete
                            </button>
                        </li>
                    `;

                        // Re-attach event listeners
                        const newUnreadButton = notificationItem.querySelector('.mark-as-unread');
                        newUnreadButton.addEventListener('click', function () {
                            markNotificationAsUnread(notificationId);
                        });

                        const newDeleteButton = notificationItem.querySelector('.delete-notification');
                        newDeleteButton.addEventListener('click', function () {
                            deleteNotification(notificationId);
                        });

                        // Show success message
                        showToast('Success', 'Notification marked as read');

                        // Update notification counter in base.html
                        updateNotificationCounter();
                    }
                })
                .catch(error => {
                    console.error('Error marking notification as read:', error);
                    showToast('Error', 'Could not mark notification as read', 'error');
                });
        }

        // Function to mark notification as unread
        function markNotificationAsUnread(notificationId) {
            // Send AJAX request to mark as unread
            fetch(`/api/notifications/${notificationId}/unread/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
                .then(response => {
                    if (response.ok) {
                        // Update UI
                        const notificationItem = document.querySelector(`.notification-item[data-notification-id="${notificationId}"]`);
                        notificationItem.classList.add('unread');

                        // Update dropdown action
                        const actionDropdown = notificationItem.querySelector('.dropdown-menu');
                        actionDropdown.innerHTML = `
                        <li>
                            <button class="dropdown-item mark-as-read" data-notification-id="${notificationId}">
                                <i class="fas fa-check me-2"></i>Mark as Read
                            </button>
                        </li>
                        <li><hr class="dropdown-divider"></li>
                        <li>
                            <button class="dropdown-item text-danger delete-notification" data-notification-id="${notificationId}">
                                <i class="fas fa-trash-alt me-2"></i>Delete
                            </button>
                        </li>
                    `;

                        // Re-attach event listeners
                        const newReadButton = notificationItem.querySelector('.mark-as-read');
                        newReadButton.addEventListener('click', function () {
                            markNotificationAsRead(notificationId);
                        });

                        const newDeleteButton = notificationItem.querySelector('.delete-notification');
                        newDeleteButton.addEventListener('click', function () {
                            deleteNotification(notificationId);
                        });

                        // Show success message
                        showToast('Success', 'Notification marked as unread');

                        // Update notification counter in base.html
                        updateNotificationCounter();
                    }
                })
                .catch(error => {
                    console.error('Error marking notification as unread:', error);
                    showToast('Error', 'Could not mark notification as unread', 'error');
                });
        }

        // Function to delete notification
        function deleteNotification(notificationId) {
            if (confirm('Are you sure you want to delete this notification?')) {
                // Send AJAX request to delete
                fetch(`/api/notifications/${notificationId}/delete/`, {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    }
                })
                    .then(response => {
                        if (response.ok) {
                            // Remove item from DOM
                            const notificationItem = document.querySelector(`.notification-item[data-notification-id="${notificationId}"]`);
                            notificationItem.style.height = notificationItem.offsetHeight + 'px';

                            // Animate removal
                            setTimeout(() => {
                                notificationItem.style.height = '0';
                                notificationItem.style.opacity = '0';
                                notificationItem.style.padding = '0';
                                notificationItem.style.margin = '0';
                                notificationItem.style.overflow = 'hidden';

                                setTimeout(() => {
                                    notificationItem.remove();

                                    // Check if no notifications left
                                    checkEmptyState();

                                    // Update notification counter in base.html
                                    updateNotificationCounter();
                                }, 300);
                            }, 10);

                            // Show success message
                            showToast('Success', 'Notification deleted');
                        }
                    })
                    .catch(error => {
                        console.error('Error deleting notification:', error);
                        showToast('Error', 'Could not delete notification', 'error');
                    });
            }
        }

        // Function to mark all notifications as read
        function markAllNotificationsAsRead() {
            // Send AJAX request to mark all as read
            fetch('/api/notifications/mark-all-read/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
                .then(response => {
                    if (response.ok) {
                        // Update UI for all unread notifications
                        document.querySelectorAll('.notification-item.unread').forEach(item => {
                            item.classList.remove('unread');

                            // Update each dropdown
                            const notificationId = item.getAttribute('data-notification-id');
                            const actionDropdown = item.querySelector('.dropdown-menu');
                            actionDropdown.innerHTML = `
                            <li>
                                <button class="dropdown-item mark-as-unread" data-notification-id="${notificationId}">
                                    <i class="fas fa-times me-2"></i>Mark as Unread
                                </button>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <button class="dropdown-item text-danger delete-notification" data-notification-id="${notificationId}">
                                    <i class="fas fa-trash-alt me-2"></i>Delete
                                </button>
                            </li>
                        `;
                        });

                        // Re-attach event listeners to all buttons
                        document.querySelectorAll('.mark-as-unread').forEach(button => {
                            const notificationId = button.getAttribute('data-notification-id');
                            button.addEventListener('click', function () {
                                markNotificationAsUnread(notificationId);
                            });
                        });

                        document.querySelectorAll('.delete-notification').forEach(button => {
                            const notificationId = button.getAttribute('data-notification-id');
                            button.addEventListener('click', function () {
                                deleteNotification(notificationId);
                            });
                        });

                        // Show success message
                        showToast('Success', 'All notifications marked as read');

                        // Update notification counter in base.html
                        updateNotificationCounter(0);
                    }
                })
                .catch(error => {
                    console.error('Error marking all notifications as read:', error);
                    showToast('Error', 'Could not mark all notifications as read', 'error');
                });
        }

        // Function to filter notifications
        function filterNotifications(filterType) {
            notificationItems.forEach(item => {
                if (filterType === 'all') {
                    item.style.display = 'block';
                } else if (filterType === 'read') {
                    item.style.display = item.classList.contains('unread') ? 'none' : 'block';
                } else if (filterType === 'unread') {
                    item.style.display = item.classList.contains('unread') ? 'block' : 'none';
                } else {
                    // Filter by notification type
                    const itemType = item.getAttribute('data-notification-type');
                    item.style.display = (itemType === filterType) ? 'block' : 'none';
                }
            });

            // Check if any visible items
            const visibleItems = Array.from(notificationItems).filter(item =>
                item.style.display !== 'none'
            );

            if (visibleItems.length === 0) {
                showEmptyFilterState(filterType);
            } else {
                hideEmptyFilterState();
            }
        }

        // Show empty state for filters
        function showEmptyFilterState(filterType) {
            // Remove existing empty state if any
            hideEmptyFilterState();

            // Create empty state message
            const emptyState = document.createElement('div');
            emptyState.className = 'text-center p-5 empty-filter-state';

            let filterName = 'matching';
            switch (filterType) {
                case 'read': filterName = 'read'; break;
                case 'unread': filterName = 'unread'; break;
                case 'inventory': filterName = 'inventory'; break;
                case 'expiry': filterName = 'expiry'; break;
                case 'system': filterName = 'system'; break;
            }

            emptyState.innerHTML = `
                <div class="mb-3">
                    <i class="fas fa-filter fa-4x text-muted"></i>
                </div>
                <h4 class="fw-light text-muted">No ${filterName} notifications</h4>
                <p class="text-muted">Try a different filter or check back later.</p>
            `;

            // Add to DOM
            const notificationList = document.querySelector('.notification-list');
            notificationList.appendChild(emptyState);
        }

        // Hide empty filter state
        function hideEmptyFilterState() {
            const emptyState = document.querySelector('.empty-filter-state');
            if (emptyState) {
                emptyState.remove();
            }
        }

        // Check if no notifications left and show empty state
        function checkEmptyState() {
            const remainingNotifications = document.querySelectorAll('.notification-item');
            if (remainingNotifications.length === 0) {
                const notificationList = document.querySelector('.notification-list');
                if (notificationList) {
                    notificationList.innerHTML = `
                        <div class="text-center p-5">
                            <div class="mb-3">
                                <i class="fas fa-bell-slash fa-4x text-muted"></i>
                            </div>
                            <h4 class="fw-light text-muted">No Notifications</h4>
                            <p class="text-muted">You don't have any notifications at the moment.</p>
                        </div>
                    `;
                }
            }
        }

        // Update notification counter in base.html dropdown
        function updateNotificationCounter(count = null) {
            // If count is provided, use it, otherwise count unread notifications
            const unreadCount = count !== null ? count :
                document.querySelectorAll('.notification-item.unread').length;

            // Update badge in navbar
            const navbarBadge = document.querySelector('#notificationsDropdown .badge');
            if (navbarBadge) {
                if (unreadCount > 0) {
                    navbarBadge.textContent = unreadCount;
                    navbarBadge.style.display = 'inline-block';
                } else {
                    navbarBadge.style.display = 'none';
                }
            }

            // If application object exists, update its notifications
            if (typeof app !== 'undefined' && app.refreshNotificationDropdown) {
                app.refreshNotificationDropdown();
            }
        }

        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }

        // Toast notification functionality (uses app.showToast from base.html if available)
        function showToast(title, message, type = 'success') {
            if (typeof app !== 'undefined' && app.showToast) {
                app.showToast(title, message, type);
            } else {
                // Fallback toast implementation
                console.log(`${title}: ${message}`);
            }
        }
    });
</script>
{% endblock %}