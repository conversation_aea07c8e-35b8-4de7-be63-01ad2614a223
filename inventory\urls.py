from django.urls import path
from . import views, redirect_views, api_views, views_simulation
from .views import get_medicine_quantity, get_medicine_details, search_medicines, update_cart_quantity, audit_trail_list, transaction_audit_trail, check_duplicate_customers, update_medicine_price
from .test_excel_view import test_excel_view


urlpatterns = [

    path('', views.home, name='home'),
    path('medicines/', views.medicine_list, name='medicine_list'),
    path('medicine/<int:pk>/', views.medicine_detail, name='medicine_detail'),
    path('transactions/', views.transaction_list, name='transaction_list'),
    path('transaction/<int:pk>/', views.transaction_detail, name='transaction_detail'),
    path('inventory/', views.inventory_list, name='inventory_list'),
    path('calculate_forecasts/', views.calculate_forecasts, name='calculate_forecasts'),
    path('forecasts/', views.forecast_list, name='forecast_list'),
    path('medicine/create/', views.create_medicine, name='create_medicine'),
    path('transaction/create/', views.create_transaction, name='create_transaction'),
    path('transaction/save/', views.save_transaction, name='save_transaction'),
    path('transaction/cancel/', views.cancel_transaction, name='cancel_transaction'),
    path('transaction/remove/<int:index>/', views.remove_from_cart, name='remove_from_cart'),
    path('transaction/update_quantity/', update_cart_quantity, name='update_cart_quantity'),
    path('forecast/<int:pk>/', views.forecast_detail, name='forecast_detail'),
    path('medicine/<int:pk>/add_quantity/', views.add_quantity, name='add_quantity'),
    path('audit_trail/', audit_trail_list, name='audit_trail_list'),
    path('audit_trail/transaction/<int:pk>/', transaction_audit_trail, name='transaction_audit_trail'),
    path('get_medicine_quantity/<int:pk>/', get_medicine_quantity, name='get_medicine_quantity'),

    path('medicines/download/', views.download_medicines_excel, name='download_medicines_excel'),

    path('forecasts/<int:pk>/', views.forecast_detail, name='forecast_detail'),

    path('test-excel/', test_excel_view, name='test_excel_view'),
    path('forecast-excel/', views.forecast_excel_view, name='forecast_excel_view'),
    path('direct-forecast-excel/', views.direct_forecast_excel, name='direct_forecast_excel'),
    path('simple-excel/', views.simple_excel_view, name='simple_excel_view'),


    path('medicine-analysis/', views.medicine_analysis_view, name='medicine_analysis'),

    path('medicine/<int:medicine_id>/forecast/', views.medicine_forecast_detail, name='medicine_forecast_detail'),

    path('medicine-analysis/', views.medicine_analysis_view, name='medicine_analysis'),
    path('medicine-analysis/<int:medicine_id>/', views.medicine_analysis_view, name='medicine_analysis_detail'),


          path('update-forecasts/', views.generate_forecasts, name='update_forecasts'),
              path('generate-forecasts/', views.generate_forecasts, name='generate_forecasts'),




 path('forecast/arima/', views.arima_forecast_view, name='arima_forecast'),

 path('reports/', views.reports_dashboard, name='reports_dashboard'),


 path('about/', views.about_us, name='about_us'),

   path('api/search_customer/', views.search_customer, name='search_customer'),
   path('api/check_duplicate_customers/', check_duplicate_customers, name='check_duplicate_customers'),
   path('api/medicine_details/<int:pk>/', get_medicine_details, name='get_medicine_details'),
   path('api/search_medicines/', search_medicines, name='search_medicines'),
   path('api/update_medicine_price/', update_medicine_price, name='update_medicine_price'),

   # File upload URLs
   path('medicines/upload/', views.upload_medicine_form, name='upload_medicine_form'),
   path('medicines/upload/process/', views.upload_medicine, name='upload_medicine'),
   path('medicines/upload/template/', views.download_medicine_template, name='download_medicine_template'),
   path('transactions/upload/', views.upload_transaction_form, name='upload_transaction_form'),
   path('transactions/upload/process/', views.upload_transaction, name='upload_transaction'),
   path('transactions/upload/template/', views.download_transaction_template, name='download_transaction_template'),

   # Stock Movement URLs
   path('stock-movements/', views.stock_movement_list, name='stock_movement_list'),

   path('stock-movement/<int:pk>/', views.stock_movement_detail, name='stock_movement_detail'),
   path('medicine/<int:pk>/movement-analysis/', views.medicine_movement_analysis, name='medicine_movement_analysis'),
   path('movement-analysis/', views.movement_analysis_dashboard, name='movement_analysis_dashboard'),
   path('api/medicine-details/<int:pk>/', views.get_medicine_details_api, name='get_medicine_details_api'),
   path('update-movement-analyses/', views.update_all_movement_analyses, name='update_movement_analyses'),

   # API endpoints
   path('api/medicines/', api_views.get_medicines, name='api_get_medicines'),

   # Simulation views
   path('forecast-simulation/', views_simulation.forecast_simulation, name='forecast_simulation'),
   path('forecast-simulation/<int:medicine_id>/', views_simulation.forecast_simulation, name='forecast_simulation_detail'),
   path('download-forecast-simulation/', views_simulation.download_forecast_simulation, name='download_forecast_simulation'),
   path('download-forecast-simulation/<int:medicine_id>/', views_simulation.download_forecast_simulation, name='download_forecast_simulation_detail'),
]