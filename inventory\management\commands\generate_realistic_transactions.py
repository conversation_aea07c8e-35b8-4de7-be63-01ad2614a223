import random
from datetime import datetime, timedelta
from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from inventory.models import Medicine, Transaction, User
from django.db.models import Sum, F

class Command(BaseCommand):
    help = 'Generate realistic transaction data for medicines from 2023 to April 14, 2025'

    def add_arguments(self, parser):
        parser.add_argument('--start-date', type=str, default='2023-01-01',
                            help='Start date for transactions (YYYY-MM-DD)')
        parser.add_argument('--end-date', type=str, default='2025-04-14',
                            help='End date for transactions (YYYY-MM-DD)')
        parser.add_argument('--clear', action='store_true',
                            help='Clear existing transactions before generating new ones')

    def handle(self, *args, **options):
        start_date_str = options['start_date']
        end_date_str = options['end_date']
        clear_existing = options['clear']

        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
        except ValueError:
            self.stdout.write(self.style.ERROR('Invalid date format. Use YYYY-MM-DD'))
            return

        # Get all medicines
        medicines = Medicine.objects.all()
        if not medicines.exists():
            self.stdout.write(self.style.ERROR('No medicines found in the database'))
            return

        # Get admin user for transactions
        try:
            admin_user = User.objects.filter(is_staff=True).first()
            if not admin_user:
                admin_user = User.objects.first()
                if not admin_user:
                    self.stdout.write(self.style.ERROR('No users found in the database'))
                    return
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Error getting admin user: {str(e)}'))
            return

        # Clear existing transactions if requested
        if clear_existing:
            with transaction.atomic():
                # Get the current quantities before deleting transactions
                medicine_quantities = {m.id: m.quantity for m in medicines}

                # Delete all transactions
                deleted_count = Transaction.objects.all().delete()[0]
                self.stdout.write(self.style.SUCCESS(f'Deleted {deleted_count} existing transactions'))

                # Reset medicine quantities to their current values
                for medicine_id, quantity in medicine_quantities.items():
                    Medicine.objects.filter(id=medicine_id).update(quantity=quantity)

        # Generate transactions
        self.generate_transactions(start_date, end_date, medicines, admin_user)

        self.stdout.write(self.style.SUCCESS('Successfully generated realistic transaction data'))

    def generate_transactions(self, start_date, end_date, medicines, admin_user):
        """Generate realistic transactions for each medicine"""

        # Customer types and payment methods
        customer_types = ['Regular', 'Walk-in', 'Insurance', 'Hospital', 'Pharmacy']
        payment_methods = ['Cash', 'Credit Card', 'Insurance', 'Mobile Payment']

        # Transaction types with weights (more sales than purchases)
        transaction_types = ['sale', 'purchase', 'adjustment']
        transaction_weights = [70, 25, 5]  # 70% sales, 25% purchases, 5% adjustments

        # Track total transactions created
        total_transactions = 0

        # Create a dictionary to track medicine seasonality patterns
        medicine_seasonality = {}
        for medicine in medicines:
            # Randomly assign seasonality pattern (0=none, 1=winter, 2=summer, 3=spring, 4=fall)
            medicine_seasonality[medicine.id] = random.randint(0, 4)

        # Create a dictionary to track medicine popularity (affects transaction frequency)
        medicine_popularity = {}
        for medicine in medicines:
            # Assign popularity score (1-10)
            medicine_popularity[medicine.id] = random.randint(1, 10)

        # Create a dictionary to track medicine price trends
        medicine_price_trends = {}
        for medicine in medicines:
            # Assign price trend (-0.1 to 0.3) - slight decrease to moderate increase
            medicine_price_trends[medicine.id] = random.uniform(-0.1, 0.3)

        # Generate transactions in batches by month
        current_date = start_date
        batch_size = 0
        transactions_to_create = []

        while current_date <= end_date:
            # Determine month and year for seasonality
            month = current_date.month
            year = current_date.year

            # Determine if it's a weekend
            is_weekend = current_date.weekday() >= 5

            # Process each medicine
            for medicine in medicines:
                # Skip some medicines on some days for realism
                if random.random() > 0.7:
                    continue

                # Get medicine's seasonality pattern
                seasonality = medicine_seasonality[medicine.id]

                # Calculate seasonality factor (1.0 is baseline)
                seasonality_factor = 1.0

                # Apply seasonality patterns
                if seasonality == 1:  # Winter seasonality (higher in Dec-Feb)
                    if month in [12, 1, 2]:
                        seasonality_factor = 1.5
                elif seasonality == 2:  # Summer seasonality (higher in Jun-Aug)
                    if month in [6, 7, 8]:
                        seasonality_factor = 1.4
                elif seasonality == 3:  # Spring seasonality (higher in Mar-May)
                    if month in [3, 4, 5]:
                        seasonality_factor = 1.3
                elif seasonality == 4:  # Fall seasonality (higher in Sep-Nov)
                    if month in [9, 10, 11]:
                        seasonality_factor = 1.2

                # Apply weekend factor (fewer transactions on weekends)
                weekend_factor = 0.6 if is_weekend else 1.0

                # Apply yearly growth factor (transactions increase over years)
                yearly_growth = 1.0 + (year - start_date.year) * 0.15

                # Apply medicine popularity
                popularity_factor = medicine_popularity[medicine.id] / 5.0

                # Calculate final transaction probability
                transaction_probability = 0.3 * seasonality_factor * weekend_factor * yearly_growth * popularity_factor

                # Determine if we create a transaction for this medicine on this day
                if random.random() <= transaction_probability:
                    # Determine transaction type based on weights
                    transaction_type = random.choices(transaction_types, weights=transaction_weights, k=1)[0]

                    # Calculate quantity based on transaction type and medicine properties
                    base_quantity = max(1, int(medicine.reorder_level * 0.3 * random.uniform(0.5, 1.5)))

                    if transaction_type == 'sale':
                        quantity = base_quantity
                    elif transaction_type == 'purchase':
                        # Purchases are typically larger
                        quantity = base_quantity * random.randint(3, 8)
                    else:  # adjustment
                        # Adjustments can be positive or negative
                        quantity = base_quantity * random.choice([-1, 1])

                    # Calculate price with trend over time
                    years_passed = year - start_date.year
                    months_passed = (year - start_date.year) * 12 + (month - start_date.month)
                    price_trend_factor = 1.0 + (medicine_price_trends[medicine.id] * years_passed)

                    # Base price is medicine's unit price or a default value
                    base_price = getattr(medicine, 'unit_price', random.uniform(5, 100))
                    unit_price = base_price * price_trend_factor

                    # Calculate total amount
                    total_amount = unit_price * quantity

                    # Create transaction with random time on the current date
                    random_hour = random.randint(8, 17)  # Business hours
                    random_minute = random.randint(0, 59)
                    transaction_datetime = current_date.replace(hour=random_hour, minute=random_minute)

                    # Create transaction object
                    transaction_data = {
                        'medicine': medicine,
                        'transaction_type': transaction_type,
                        'quantity': quantity,
                        'total_amount': total_amount,
                        'transaction_date': transaction_datetime,
                    }

                    # Add customer info for sales
                    if transaction_type == 'sale':
                        transaction_data['customer_type'] = random.choice(customer_types)
                        transaction_data['customer_name'] = f"Customer {random.randint(1000, 9999)}"
                        transaction_data['patient_number'] = f"P-{random.randint(2023, 2025)}-{random.randint(10000, 99999)}"

                    # Add to batch
                    transactions_to_create.append(Transaction(**transaction_data))
                    batch_size += 1

                    # Create transactions in batches of 1000
                    if batch_size >= 1000:
                        with transaction.atomic():
                            Transaction.objects.bulk_create(transactions_to_create)
                            total_transactions += batch_size
                            self.stdout.write(f"Created {batch_size} transactions up to {current_date.date()}")
                            transactions_to_create = []
                            batch_size = 0

            # Move to next day
            current_date += timedelta(days=1)

        # Create any remaining transactions
        if transactions_to_create:
            with transaction.atomic():
                Transaction.objects.bulk_create(transactions_to_create)
                total_transactions += batch_size
                self.stdout.write(f"Created final batch of {batch_size} transactions")

        self.stdout.write(self.style.SUCCESS(f"Total transactions created: {total_transactions}"))

        # Update medicine quantities based on transactions
        self.update_medicine_quantities()

    def update_medicine_quantities(self):
        """Update medicine quantities based on transaction history"""
        medicines = Medicine.objects.all()
        updated_count = 0

        with transaction.atomic():
            for medicine in medicines:
                # Calculate net quantity from transactions
                sales = Transaction.objects.filter(
                    medicine=medicine,
                    transaction_type='sale'
                ).aggregate(total=Sum('quantity'))['total'] or 0

                purchases = Transaction.objects.filter(
                    medicine=medicine,
                    transaction_type='purchase'
                ).aggregate(total=Sum('quantity'))['total'] or 0

                adjustments = Transaction.objects.filter(
                    medicine=medicine,
                    transaction_type='adjustment'
                ).aggregate(total=Sum('quantity'))['total'] or 0

                # Calculate new quantity
                new_quantity = purchases - sales + adjustments

                # Ensure quantity is not negative
                new_quantity = max(0, new_quantity)

                # Update medicine quantity
                Medicine.objects.filter(id=medicine.id).update(quantity=new_quantity)
                updated_count += 1

        self.stdout.write(self.style.SUCCESS(f"Updated quantities for {updated_count} medicines"))
