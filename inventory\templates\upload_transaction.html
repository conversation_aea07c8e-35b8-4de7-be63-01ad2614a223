{% extends 'base.html' %}

{% block title %}Upload Transactions - MedInventory{% endblock %}

{% block content %}
<div class="container-fluid px-4 py-3">
    <!-- Header Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h1 class="h3 mb-0 text-primary fw-bold">
                    <i class="fas fa-file-upload me-2"></i>Upload Transactions
                </h1>
                <a href="{% url 'transaction_list' %}" class="btn btn-outline-secondary rounded-pill">
                    <i class="fas fa-arrow-left me-1"></i>Back to List
                </a>
            </div>
        </div>
    </div>

    <!-- Upload Card -->
    <div class="row">
        <div class="col-lg-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="text-primary mb-3">Upload Transaction Data</h5>
                            <p class="text-muted">
                                Upload a CSV file containing transaction data to add multiple transactions at once.
                                Make sure your CSV file follows the required format.
                            </p>

                            <div class="alert alert-info">
                                <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>CSV Format Requirements</h6>
                                <p class="mb-0">Your CSV file should include the following columns (note: the column header in the template is <strong>medicine_name</strong>, not medicine_id):</p>
                                <ul class="mb-0">
                                    <li><strong>medicine_name</strong> - Medicine name (required) - Enter the medicine name, not the ID number</li>
                                    <li><strong>transaction_type</strong> - 'purchase' (adds to inventory) or 'sale' (deducts from inventory) (required)</li>
                                    <li><strong>quantity</strong> - Transaction quantity (required)</li>
                                    <li><strong>customer_name</strong> - Customer name (required)</li>
                                    <li><strong>patient_number</strong> - Patient number (optional)</li>
                                    <li><strong>customer_type</strong> - 'in_patient' or 'out_patient' (optional, default: out_patient)</li>
                                    <li><strong>transaction_date</strong> - Date in DD/MM/YYYY format (optional, default: today)</li>
                                    <li><strong>transaction_time</strong> - Time in HH:MM:SS format (optional, default: now)</li>
                                </ul>
                            </div>

                            <div class="alert alert-primary mt-3">
                                <h6 class="alert-heading"><i class="fas fa-chart-line me-2"></i>Daily Forecast Generation</h6>
                                <p class="mb-0">
                                    <strong>Important:</strong> Forecasts are generated automatically at 7:00 PM daily.
                                </p>
                                <ul class="mb-0 mt-2">
                                    <li>Transactions uploaded before 7:00 PM will be included in today's forecast</li>
                                    <li>Transactions uploaded after 7:00 PM will be included in tomorrow's forecast</li>
                                    <li>This schedule ensures consistent and reliable forecasting results</li>
                                </ul>
                                <p class="mb-0 mt-2">
                                    The forecasting system evaluates multiple algorithms including Holtwinters, Moving Average, Linear Regression,
                                    Polynomial Regression, ARIMA, Ensemble methods, and Gradient Boosting to select the best model for each medicine.
                                </p>
                            </div>

                            <div class="alert alert-warning mt-3">
                                <h6 class="alert-heading"><i class="fas fa-calendar-alt me-2"></i>Historical Data Handling</h6>
                                <p class="mb-0">
                                    <strong>Important:</strong> The system handles historical data based on age:
                                </p>
                                <ul class="mb-0 mt-2">
                                    <li><strong>Current data (0-7 days):</strong> Used for inventory and forecasting</li>
                                    <li><strong>Active historical (7 days - 1 year):</strong> Used for forecasting calculations but not inventory</li>
                                    <li><strong>Reference historical (1-3 years):</strong> Stored as reference only, not used in active forecasting</li>
                                    <li><strong>Ancient data (>3 years):</strong> Not imported</li>
                                    <li>Duplicate transactions will be automatically skipped</li>
                                </ul>
                            </div>

                            <div class="alert alert-info mt-3">
                                <h6 class="alert-heading"><i class="fas fa-exchange-alt me-2"></i>Transaction Types Explained</h6>
                                <p class="mb-0">
                                    <strong>Understanding transaction types:</strong>
                                </p>
                                <ul class="mb-0 mt-2">
                                    <li><strong>'purchase':</strong> Normally adds to inventory, but in this upload form ALL transactions will DEDUCT from inventory</li>
                                    <li><strong>'sale':</strong> Deducts from inventory (sales, dispensing, adjustments that decrease stock)</li>
                                </ul>
                                <p class="mt-2 mb-0 text-danger fw-bold">
                                    <i class="fas fa-exclamation-triangle me-1"></i> IMPORTANT: For consistency with the Create Transaction form,
                                    ALL transactions uploaded here will DEDUCT from inventory regardless of transaction type.
                                </p>
                                <p class="mt-2 mb-0">
                                    <strong>Example:</strong> If you have 100 units of a medicine and upload any transaction for 20 units,
                                    the inventory will be reduced to 80 units.
                                </p>
                            </div>

                            <div class="mt-3">
                                <a href="{% url 'download_transaction_template' %}" class="btn btn-outline-primary">
                                    <i class="fas fa-download me-2"></i>Download Template
                                </a>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <div class="upload-container p-4 border rounded bg-light">
                                <form method="post" enctype="multipart/form-data" id="upload-form" action="{% url 'upload_transaction' %}">
                                    {% csrf_token %}

                                    <div class="mb-4 text-center">
                                        <div class="upload-icon mb-3">
                                            <i class="fas fa-file-csv fa-3x text-primary"></i>
                                        </div>
                                        <h5 class="text-secondary">Select CSV File</h5>
                                        <p class="text-muted small">Maximum file size: 5MB</p>
                                    </div>

                                    <div class="mb-3">
                                        <div class="custom-file-upload">
                                            <input type="file" name="csv_file" id="csv_file" class="form-control" accept=".csv" required>
                                        </div>
                                        <div class="form-text">Only CSV files are accepted</div>
                                    </div>

                                    <div class="progress mb-3 d-none" id="upload-progress">
                                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                                    </div>

                                    <div id="upload-result" class="mb-3"></div>

                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary" id="upload-button">
                                            <i class="fas fa-upload me-2"></i>Upload Transactions
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Forecasting Impact Section -->
    <div class="row mt-4">
        <div class="col-lg-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-light py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-chart-line me-2"></i>Impact on Forecasting <span class="badge bg-primary ms-2">7:00 PM Daily</span>
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h6 class="fw-bold text-primary mb-3">How Forecasting Works</h6>
                            <p>
                                The BMC Medicine Forecasting System uses advanced algorithms to predict future demand for each medicine.
                                When you upload transaction data, the system automatically analyzes the patterns and updates the forecasts.
                            </p>
                            <div class="d-flex flex-wrap gap-2 mt-3">
                                <span class="badge bg-primary p-2"><i class="fas fa-chart-line me-1"></i> Holtwinters</span>
                                <span class="badge bg-primary p-2"><i class="fas fa-chart-line me-1"></i> Moving Average</span>
                                <span class="badge bg-primary p-2"><i class="fas fa-chart-line me-1"></i> Linear Regression</span>
                                <span class="badge bg-primary p-2"><i class="fas fa-chart-line me-1"></i> Polynomial Regression</span>
                                <span class="badge bg-primary p-2"><i class="fas fa-chart-line me-1"></i> ARIMA</span>
                                <span class="badge bg-primary p-2"><i class="fas fa-chart-line me-1"></i> Ensemble Methods</span>
                                <span class="badge bg-success p-2"><i class="fas fa-star me-1"></i> Gradient Boosting</span>
                            </div>
                        </div>
                    </div>

                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="fas fa-lightbulb me-2"></i>Benefits of Bulk Upload</h6>
                                    <ul class="mb-0">
                                        <li class="mb-2">Quickly add historical transaction data</li>
                                        <li class="mb-2">Improve forecast accuracy with more data points</li>
                                        <li class="mb-2">Save time compared to manual entry</li>
                                        <li class="mb-2">Ensure consistent data format</li>
                                        <li>Automatically trigger forecast updates</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100 border-0 shadow-sm">
                                <div class="card-body">
                                    <h6 class="card-title text-primary"><i class="fas fa-check-circle me-2"></i>Best Practices</h6>
                                    <ul class="mb-0">
                                        <li class="mb-2">Include transactions from different time periods</li>
                                        <li class="mb-2">Ensure data accuracy before uploading</li>
                                        <li class="mb-2">Include both 'in' and 'out' transactions</li>
                                        <li class="mb-2">Review forecasts after uploading large datasets</li>
                                        <li>Create seasonal patterns for better predictions</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-success mt-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-robot fa-2x"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading">Automatic Model Selection</h6>
                                <p class="mb-0">
                                    The system automatically selects the best forecasting model for each medicine based on its unique demand pattern.
                                    Gradient Boosting is particularly effective for medicines with complex seasonal patterns and trends.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-info mt-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-calendar-check fa-2x"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading">Flexible Date Formats</h6>
                                <p class="mb-0">
                                    The system accepts various date formats including YYYY-MM-DD, DD/MM/YYYY, MM/DD/YYYY, and more.
                                    You don't need to worry about specific formatting - the system will automatically detect and parse dates.
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="alert alert-secondary mt-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-shield-alt fa-2x"></i>
                            </div>
                            <div>
                                <h6 class="alert-heading">Duplicate Protection</h6>
                                <p class="mb-0">
                                    The system automatically detects and skips duplicate transactions to prevent data redundancy.
                                    This ensures that your inventory and forecasting data remain accurate even if you accidentally
                                    upload the same file multiple times.
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const form = document.getElementById('upload-form');
        const fileInput = document.getElementById('csv_file');
        const uploadButton = document.getElementById('upload-button');
        const progressBar = document.getElementById('upload-progress');
        const progressBarInner = progressBar.querySelector('.progress-bar');
        const resultContainer = document.getElementById('upload-result');

        // File input change handler
        fileInput.addEventListener('change', function() {
            const file = this.files[0];
            if (file) {
                // Check file type
                if (!file.name.endsWith('.csv')) {
                    showError('Please select a CSV file.');
                    this.value = '';
                    return;
                }

                // Check file size (5MB max)
                if (file.size > 5 * 1024 * 1024) {
                    showError('File size exceeds 5MB limit.');
                    this.value = '';
                    return;
                }

                // Show file name
                const fileName = file.name;
                resultContainer.innerHTML = `<div class="alert alert-info">
                    <i class="fas fa-file-csv me-2"></i>Selected file: <strong>${fileName}</strong>
                </div>`;
            }
        });

        // Form submit handler
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            if (!fileInput.files[0]) {
                showError('Please select a file to upload.');
                return;
            }

            // Show progress bar
            progressBar.classList.remove('d-none');
            progressBarInner.style.width = '0%';

            // Disable button during upload
            uploadButton.disabled = true;
            uploadButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';

            // Create FormData object
            const formData = new FormData(this);

            // Simulate progress (since we can't get real progress for small files)
            let progress = 0;
            const progressInterval = setInterval(() => {
                progress += 5;
                if (progress > 90) {
                    clearInterval(progressInterval);
                }
                progressBarInner.style.width = `${progress}%`;
            }, 100);

            // Send AJAX request
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Clear progress interval
                clearInterval(progressInterval);

                // Complete progress bar
                progressBarInner.style.width = '100%';

                // Show results
                if (data.error) {
                    showError(data.error);
                } else {
                    showSuccess(data);
                }

                // Re-enable button
                uploadButton.disabled = false;
                uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>Upload Transactions';

                // Redirect to transaction list after 3 seconds if successful
                if (!data.error && data.created > 0) {
                    setTimeout(() => {
                        window.location.href = "{% url 'transaction_list' %}";
                    }, 3000);
                }
            })
            .catch(error => {
                // Clear progress interval
                clearInterval(progressInterval);

                // Show error
                showError('An error occurred during upload. Please try again.');
                console.error('Upload error:', error);

                // Re-enable button
                uploadButton.disabled = false;
                uploadButton.innerHTML = '<i class="fas fa-upload me-2"></i>Upload Transactions';
            });
        });

        // Function to show error message
        function showError(message) {
            resultContainer.innerHTML = `<div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>${message}
            </div>`;
        }

        // Function to show success message
        function showSuccess(data) {
            let html = `<div class="alert alert-success">
                <i class="fas fa-check-circle me-2"></i>Upload successful!
                <ul class="mb-0 mt-2">
                    <li>Created: ${data.created} transactions</li>
                </ul>
            </div>`;

            // Add errors if any
            if (data.errors && data.errors.length > 0) {
                html += `<div class="alert alert-warning mt-2">
                    <i class="fas fa-exclamation-triangle me-2"></i>Warnings:
                    <ul class="mb-0 mt-2">
                        ${data.errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>`;
            }

            resultContainer.innerHTML = html;
        }
    });
</script>
{% endblock %}
