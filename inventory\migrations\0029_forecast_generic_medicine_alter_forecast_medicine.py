# Generated by Django 5.2 on 2025-04-26 15:25

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('inventory', '0028_genericmedicine_medicine_brand_medicine_supplier_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='forecast',
            name='generic_medicine',
            field=models.ForeignKey(blank=True, help_text='Generic medicine category for forecasting', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='forecasts', to='inventory.genericmedicine'),
        ),
        migrations.AlterField(
            model_name='forecast',
            name='medicine',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='inventory.medicine'),
        ),
    ]
