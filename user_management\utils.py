import secrets
from django.utils import timezone

def generate_verification_token(user):
    """
    Generate a unique verification token for phone lock verification
    
    Args:
        user: User instance to generate token for
        
    Returns:
        str: Generated verification token
    """
    token = secrets.token_urlsafe(32)
    user.profile.phone_lock_verification_token = token
    user.profile.phone_lock_verification_token_created = timezone.now()
    user.profile.save()
    return token

def generate_verification_token_with_expiry(user, expiry_hours=24):
    """
    Generate a verification token with expiry time
    
    Args:
        user: User instance to generate token for
        expiry_hours: Number of hours until token expires (default: 24)
        
    Returns:
        str: Generated verification token
    """
    token = secrets.token_urlsafe(32)
    user.profile.verification_token = token
    user.profile.verification_token_created = timezone.now()
    user.profile.verification_token_expires = timezone.now() + timezone.timedelta(hours=expiry_hours)
    user.profile.save()
    return token