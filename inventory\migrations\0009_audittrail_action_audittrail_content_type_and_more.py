# Generated by Django 5.1.2 on 2025-02-14 08:46

import datetime
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('inventory', '0008_remove_audittrail_content_type_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='audittrail',
            name='action',
            field=models.CharField(choices=[('create_medicine', 'Create Medicine'), ('add_quantity', 'Add Quantity'), ('create_transaction', 'Create Transaction'), ('create_forecast', 'Create Forecast'), ('unknown', 'Unknown')], default='unknown', max_length=50),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='content_type',
            field=models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='audittrail',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=datetime.datetime(2025, 2, 14, 8, 46, 13, 490049, tzinfo=datetime.timezone.utc)),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='audittrail',
            name='object_id',
            field=models.PositiveIntegerField(default=0),
        ),
    ]
