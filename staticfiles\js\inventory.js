document.addEventListener('DOMContentLoaded', function () {
    // Table row hover effect
    const tableRows = document.querySelectorAll('tbody tr');
    tableRows.forEach(row => {
        row.addEventListener('mouseenter', function () {
            this.style.backgroundColor = '#f8f9fa';
            this.style.transition = 'background-color 0.3s ease';
        });
        row.addEventListener('mouseleave', function () {
            this.style.backgroundColor = '';
        });
    });

    // Stock status badge tooltips
    const badges = document.querySelectorAll('.badge');
    badges.forEach(badge => {
        new bootstrap.Tooltip(badge);
    });

    // Modal enhancement
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const modalBody = this.querySelector('.modal-body');

            // Add entrance animation
            modalBody.classList.add('fade-enter');
            setTimeout(() => {
                modalBody.classList.add('fade-enter-active');
            }, 10);
        });

        modal.addEventListener('hidden.bs.modal', function () {
            const modalBody = this.querySelector('.modal-body');
            modalBody.classList.remove('fade-enter', 'fade-enter-active');
        });
    });

    // Table search functionality
    function addTableSearch() {
        const searchInput = document.createElement('input');
        searchInput.type = 'text';
        searchInput.className = 'form-control mb-3';
        searchInput.placeholder = 'Search inventory...';

        const table = document.querySelector('.table');
        table.parentNode.insertBefore(searchInput, table);

        searchInput.addEventListener('keyup', function () {
            const searchText = this.value.toLowerCase();
            const rows = document.querySelectorAll('tbody tr');

            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(searchText) ? '' : 'none';
            });
        });
    }
    addTableSearch();

    // Add sorting functionality
    function addTableSorting() {
        const headers = document.querySelectorAll('thead th');
        headers.forEach((header, index) => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                const rows = Array.from(document.querySelectorAll('tbody tr'));
                const sortDirection = header.classList.contains('asc') ? -1 : 1;

                rows.sort((a, b) => {
                    const aVal = a.children[index].textContent;
                    const bVal = b.children[index].textContent;
                    return aVal.localeCompare(bVal) * sortDirection;
                });

                headers.forEach(h => h.classList.remove('asc', 'desc'));
                header.classList.toggle('asc', sortDirection === 1);
                header.classList.toggle('desc', sortDirection === -1);

                const tbody = document.querySelector('tbody');
                tbody.innerHTML = '';
                rows.forEach(row => tbody.appendChild(row));
            });
        });
    }
    addTableSorting();

    // Action button handler
    const actionButtons = document.querySelectorAll('.modal-footer .btn-primary, .modal-footer .btn-warning, .modal-footer .btn-danger');
    actionButtons.forEach(button => {
        button.addEventListener('click', function () {
            // Add your action handling logic here
            alert('Action button clicked! Add your custom logic here.');
        });
    });
});